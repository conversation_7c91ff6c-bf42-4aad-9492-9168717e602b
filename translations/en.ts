// File: en.ts
// Những file liên quan đến file này: vi.ts, App.tsx, context/LocalizationContext.tsx

export default {
  // Common
  common: {
    cancel: "Cancel",
    delete: "Delete",
    save: "Save",
    edit: "Edit",
    update: "Update",
    loading: "Loading...",
    error: "An error occurred",
    success: "Success",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    retry: "Try again",
    notification: "Notification",
    optional: "Optional",
    done: "Done",
    add: "Add",
    hidden: "Hidden",
    close: "Close",
    reset: "Reset", // New entry
  },

  // App
  appSubtitle: "Smart personal finance management",

  // Date and Time
  dateTime: {
    today: "Today",
    yesterday: "Yesterday",
    dayFormat: "dddd, MMMM D, YYYY",
  },

  // Categories
  categoryNames: {
    // Expense categories
    "Ăn uống": "Food & Drink",
    "Di chuyển": "Transportation",
    "Mua sắm": "Shopping",
    "Hóa đơn": "Bills",
    "Nhà cửa": "Housing",
    "Giải trí": "Entertainment",
    "Sức khỏe": "Health",
    "Giáo dục": "Education",
    Khác: "Other",

    // Income categories
    Lương: "Salary",
    Thưởng: "Bonus",
    "Đầu tư": "Investment",
    "Thu nhập": "Income",
    "Tiền thưởng": "Bonus Payment",
  },

  // Auth screens
  auth: {
    login: "Login",
    signup: "Sign up",
    email: "Email",
    password: "Password",
    forgotPassword: "Forgot password?",
    noAccount: "Don't have an account?",
    hasAccount: "Already have an account?",
    resetPassword: "Reset Password",
    user: "User",
    logoutError: "Cannot log out. Please try again later.",
    enterEmail: "Enter your email",
    enterPassword: "Enter your password",
    loginRequiredFields: "Please enter all required information",
    forgotPasswordEmailRequired: "Please enter your email to reset password",
    createNewAccount: "Create new account",
    name: "Full name",
    enterName: "Enter your full name",
    register: "Register",
    passwordResetInstructions:
      "Enter your email and we will send password reset instructions.",
    passwordResetEmailSent:
      "Password reset instructions have been sent to your email.",
    backToLogin: "Back to Login",
    confirmPassword: "Confirm password",
    enterConfirmPassword: "Re-enter your password",
    passwordMismatch: "Passwords do not match",
    registerAllFieldsRequired: "Please fill in all fields",
    registerPolicyText: "By registering, you agree to our ",
    terms: "Terms",
    and: "and",
    privacyPolicy: "Privacy Policy",
    emailAlreadyExists:
      "This email is already in use. Please use a different email.",
    registerSuccess:
      "Your account has been created successfully. Next, let's set up your first wallet.",
    registerError: "An error occurred during registration.",
    loginFailed: "Login Failed",
    invalidCredentials: "Invalid email or password.",
    loginError: "Failed to login. Please try again later.",
    passwordResetError:
      "An error occurred while sending the password reset email.",
    emailRequired: "Email is required",
    invalidEmail: "Please enter a valid email address",
    passwordRequired: "Password is required",
    passwordTooShort: "Password must be at least 6 characters",
    rememberMe: "Remember me",
    nameRequired: "Name is required",
    confirmPasswordRequired: "Please confirm your password",
  },

  // Setup screens
  setup: {
    welcome: "Welcome to AI Money!",
    setupDescription:
      "Let's set up the app to start managing your personal finances effectively.",
    stepsHeader: "You need to complete the following steps:",
    createFirstWallet: "Create your first wallet",
    chooseCategories: "Choose categories that suit you",
    getStarted: "Get Started",
    congratulations: "Congratulations!",
    setupComplete: "You have successfully completed the setup.",
    startUsing: "Start Using AI Money",
    createWallet: "Create Wallet",
    initialSetup: "Initial Setup",
    walletCreation: "Wallet Creation",
    addMoreWallets: "Add More Wallets",
    continue: "Continue",
    skipForNow: "Skip for now",
    categoriesSetup: "Categories Setup",
    chooseExpenseCategories: "Choose expense categories",
    chooseIncomeCategories: "Choose income categories",
    selectAtLeastOne: "Please select at least one category",
    finish: "Finish",
  },

  // Currency screens
  currency: {
    title: "Currency",
    confirmChange: "Confirm Change",
    changeConfirmMessage: "Do you want to change the currency to {{code}}?",
    updateSuccess: "Currency has been updated to {{code}}",
    infoMessage:
      "The currency will be applied throughout the app. Note that changing the currency will not automatically convert the values of existing transactions.",
  },

  // Transactions
  transactions: {
    transactionDetails: "Transaction Details",
    transactionNotFound: "Transaction not found",
    details: "Details",
    time: "Time",
    type: "Type",
    noNotes: "No notes",
    undefinedWallet: "Undefined",
    deleteTransaction: "Delete Transaction",
    deleteConfirm: "Are you sure you want to delete this transaction?",
    transactionDeleted: "Transaction deleted successfully",
    deleteError: "Cannot delete transaction. Please try again later.",
    transactionDescription: "Transaction Description",
    enterDescription: "Enter transaction description",
    editTransaction: "Edit Transaction",
    updateTransaction: "Update Transaction",
    transactionUpdated: "Transaction updated successfully",
    updateError: "Cannot update transaction. Please try again later.",
    loadError: "Cannot load transaction information. Please try again later.",
    transactionList: "Transaction List",
    searchTransactions: "Search transactions...",
    noTransactions: "No transactions found",
    filterAll: "All",
  },

  // Tabs
  tabs: {
    home: "Overview",
    chatbot: "Chatbot",
    add: "Add",
    stats: "Statistics",
    settings: "Settings",
    more: "More", // New entry for More tab
	main: "Main", 
  },

  // Home screen
  home: {
    greeting: "Hello,",
    yourBalance: "Your Balance",
    totalBalance: "Total Balance",
    myWallets: "My Wallets",
    allWallets: "All Wallets",
    recentTransactions: "Recent Transactions",
    viewAll: "View All",
    addTransaction: "Add Transaction",
    viewReport: "View Report",
    thisMonthIncome: "This Month's Income",
    thisMonthExpense: "This Month's Expense",
    emptyTransactionMessage: "No transactions yet. Add your first transaction.",
    edit: "Edit",
    delete: "Delete",
    noTransaction: "No transactions found",
    displayingXofYWallets: "Displaying {{x}}/{{y}} wallets", // New entry
  },

  // Stats screen
  stats: {
    title: "Statistics",
    expense: "Expense",
    income: "Income",
    week: "Week",
    month: "Month",
    year: "Year",
    totalExpense: "Total Expense",
    totalIncome: "Total Income",
    expenseByCategory: "Expense by Category",
    incomeBySource: "Income by Source",
    noExpenseData: "No expense data",
    noIncomeData: "No income data",
    categoryDetails: "Category Details",
    amount: "Amount:",
  },

  // Add Transaction screen
  addTransaction: {
    title: "Add Transaction",
    transferTitle: "Transfer Money",
    expenseTab: "Expense",
    incomeTab: "Income",
    transferTab: "Transfer",
    amount: "Amount",
    date: "Date",
    category: "Category",
    note: "Note",
    wallet: "Wallet",
    fromWallet: "From Wallet",
    toWallet: "To Wallet",
    photo: "Add Photo",
    addTransaction: "Add Transaction",
    selectCategory: "Select Category",
    selectWallet: "Select Wallet",
    selectFromWallet: "Select source wallet",
    selectToWallet: "Select destination wallet",
    closeButton: "Close",
    transactionAdded: "Transaction added successfully",
    transactionName: "Transaction Name",
    save: "Save",
    createTransaction: "Create Transaction",
    createTransfer: "Create Transfer",
    transferTo: "Transfer to",
    transferFrom: "Transfer from",
    addNew: "Add New",
    noCategoriesMessage: "No categories. Please add categories first.",
    noWalletsMessage: "No wallets. Please add wallets first.",
    transactionDate: "Transaction Date",
    loading: "Loading data...",
    error: {
      title: "Error",
      nameRequired: "Please enter a transaction name",
      amountRequired: "Please enter a valid amount",
      categoryRequired: "Please select a category",
      walletRequired: "Please select a wallet",
      toWalletRequired: "Please select a destination wallet",
      sameWallet: "Cannot transfer to the same wallet",
      loginRequired: "You need to be logged in to perform this action",
      saveFailed: "Cannot save transaction. Please try again later.",
      loadFailed: "Cannot load data. Please try again later.",
      insufficientBalance:
        "Insufficient balance in wallet to complete this transaction",
    },
    success: {
      title: "Success",
      message: "New transaction created successfully",
    },
  },

  // Settings screen
  settings: {
    title: "Settings",
    account: "Account",
    profile: "Personal Information",
    changePassword: "Change Password",
    updateProfile: "Update and view personal information",
    updatePassword: "Update account password",
    interface: "Interface",
    darkMode: "Dark Mode",
    language: "Language",
    on: "On",
    off: "Off",
    general: "General",
    manageWallets: "Manage Wallets",
    categories: "Categories",
    currency: "Currency",
    walletsDescription: "Add, edit, delete wallets",
    categoriesDescription: "Customize income/expense categories",
    logout: "Logout",
    logoutConfirm: "Are you sure you want to logout?",
    version: "Version",
    walletLabel: "wallets",
    transactionLabel: "transactions",
    displayingXofYWallets: "Displaying {{x}}/{{y}} wallets",
    customizeHomeScreen: "Customize Home Screen",
    displaySettings: "Display Settings",
    limitWalletDisplay: "Limit wallet display",
    limitTransactionDisplay: "Limit transaction display",
    hideWallets: "Hide wallets",
    showTopWallets: "Show top wallets",
    showRecentTransactions: "Show recent transactions",

    // New entries for Home Options feature
    homeOptions: "Home Options",
    homeOptionsDescription:
      "Customize number of wallets and transactions shown",
    walletDisplay: "Wallet Display",
    walletDisplayDescription:
      "Customize how many wallets to show on the home screen. Select 0 to show all wallets.",
    transactionDisplay: "Transaction Display",
    transactionDisplayDescription:
      "Customize how many recent transactions to show on the home screen.",
    all: "All",
    walletsLabel: "wallets",
    transactionsLabel: "transactions",
    allWallets: "All wallets",
    resetDefaults: "Reset to Default",
    resetDefaultsConfirm: "Are you sure you want to restore default settings?",
    resetSuccess: "Settings have been reset to default",
    resetError: "Could not reset settings. Please try again later.",
    loadSettingsError: "Could not load settings. Please try again later.",
    saveSettingsSuccess: "Settings saved successfully",
    saveSettingsError: "Could not save settings. Please try again later.",
    homeOptionsExplain:
      "These settings will be applied to the home screen. You can still view all wallets and transactions in the Wallets and Transactions sections.",
      
    // Add update check settings
    checkUpdate: "Check for updates",
    checkUpdateDescription: "Check for new app versions",
    lastChecked: "Last checked",
  },

  // Category screens
  categories: {
    title: "Categories",
    expense: "Expense",
    income: "Income",
    addCategory: "Add Category",
    editCategory: "Edit Category",
    categoryName: "Category Name",
    categoryType: "Category Type",
    color: "Color",
    icon: "Icon",
    categoryAdded: "Category added successfully",
    categoryUpdated: "Category updated successfully",
    categoryDeleted: "Category deleted successfully",
    deleteConfirm: "Are you sure you want to delete this category?",
    noCategories: "No categories yet",
    nameRequired: "Please enter a category name",
    enterCategoryName: "Enter category name",
    categoryDetails: "Category Details",
    categoryNotFound: "Category not found",
    expenseCategories: "Expense Categories",
    incomeCategories: "Income Categories",
    categoryStats: "Category Statistics",
    usageAmount: "Usage Amount",
    manageCategories: "Manage Categories",
    editMode: "Edit Mode",
    viewMode: "View Mode",
    gridView: "Grid View",
    listView: "List View",
    // New keys for custom and template tabs
    custom: "Custom",
    templates: "Templates",
    templateRequired: "Please select a category",
  },

  // Wallet screens
  wallets: {
    title: "Wallets",
    addWallet: "Add Wallet",
    editWallet: "Edit Wallet",
    walletName: "Wallet Name",
    walletType: "Wallet Type",
    balance: "Balance",
    initialBalance: "Initial Balance",
    walletAdded: "Wallet added successfully",
    walletUpdated: "Wallet updated successfully",
    walletDeleted: "Wallet deleted successfully",
    deleteConfirm:
      "Are you sure you want to delete this wallet? All transactions related to this wallet will also be deleted.",
    deleteDefaultWalletConfirm:
      "Are you sure you want to delete this default wallet? All transactions related to this wallet will also be deleted, and you'll need to set a new default wallet.",
    noWallets: "No wallets yet",
    totalBalance: "Total Balance",
    cashType: "Cash",
    bankType: "Bank",
    ewalletType: "E-Wallet",
    loadError: "Cannot load wallets. Please try again later.",
    deleteError: "Cannot delete wallet. Please try again later.",
    nameRequired: "Please enter a wallet name",
    balanceRequired: "Please enter a valid balance",
    addError: "Cannot create new wallet. Please try again later.",
    enterWalletName: "Enter wallet name",
    enterInitialBalance: "Enter initial balance",
    enterBalance: "Enter balance",
    updateError: "Cannot update wallet. Please try again later.",
    walletDetails: "Wallet Details",
    walletNotFound: "Wallet not found",
    yourBalance: "Your Balance",
    walletList: "Wallet List",
    add: "Add",
    sort: "Sort",
    done: "Done",
    edit: "Edit",
    alreadyDefault: "This wallet is already the default",
    defaultWalletSet: "Default wallet set successfully",
    defaultWalletError: "Cannot set default wallet. Please try again later.",
    sortOrderSaved: "Wallet order saved",
    useSortArrows: "Use arrows to sort wallets",
    thisMonthIncome: "This Month's Income",
    thisMonthExpense: "This Month's Expense",
    setAsDefault: "Set as Default",
    deleteWallet: "Delete Wallet",
    deleteWalletQuestion: "Are you sure you want to delete this wallet?",
    isDefaultWallet: "This is the default wallet",
    noTransactions: "No transactions yet",
    addTransaction: "Add Transaction",
    addTransactionHint: "Add transactions for this wallet",
    selectBank: "Select a bank",
    bankSelector: "Bank Selection",
    chooseBank: "Choose a bank",
    // New entries for wallet icons
    icon: "Icon",
    chooseIcon: "Choose Icon",
    customIcon: "Custom Icon",
    defaultIcon: "Default Icon",
    selectTemplate: "Select Template",
    bankAndEwallet: "Banks / E-Wallets",
    cashWallets: "Cash Wallets",
    searchIcon: "Search icons...",
    noIconsFound: "No icons found",
    iconSelected: "Icon selected successfully",
	sortAllWallets: "Sort All Wallets",
  },

  // Notifications
  notifications: {
    clearTitle: "Clear Notifications",
    clearConfirm: "Are you sure you want to clear all notifications?",
  },

  // Chatbot
  chatbot: {
    greeting: "Hi! I'm your AI Money financial assistant. How can I help you?",
    suggestions:
      'You can try:\n- "View my last 5 transactions"\n- "View all my wallets"\n- "Add lunch transaction 150k"\n\nPress ℹ️ for more.',
    today: "Today",
    yesterday: "Yesterday",
    twoDaysAgo: "2 days ago",
    daysAgo: "{days} days ago",
    quickSuggestions: {
      recentTransactions: "View my last 5 transactions",
      addLunchTransaction: "Add lunch transaction 150k",
      monthSummary: "View month's income/expenses",
      newWallet: "Create new MB Bank wallet",
      viewCategories: "View expense categories",
    },
    helpTitle: "AI Money Chatbot Instructions:",
    transactionsHelp: "TRANSACTIONS:",
    walletsHelp: "WALLETS:",
    categoriesHelp: "CATEGORIES:",
    reportsHelp: "REPORTS:",
    noteHelp:
      "Note: AI Money will ask for confirmation before making important changes.",
    // New entries for message templates feature
    templates: "Message Templates",
    selectTemplate: "Select Message Template",
    noTemplates: "No message templates yet",
    noTemplatesDescription: "Please add templates in the Transaction Templates screen",
    loadingTemplates: "Loading templates...",
    templateSelected: "Template selected",
  },

  // Profile
  profile: {
    title: "Personal Information",
    changePhoto: "Change Photo",
    name: "Name",
    email: "Email",
    phone: "Phone",
    saveChanges: "Save Changes",
    phoneRequired: "Please enter a valid phone number",
    nameRequired: "Please enter your name",
    updateSuccess: "Personal information has been updated",
    uploadError: "Cannot process image. Please try again.",
    photoPermissionRequired:
      "The app needs permission to access your photo library to select a profile picture",
    avatarUpdateSuccess: "Profile picture has been updated",
    avatarUpdateError: "Cannot upload profile picture",
    changePassword: "Change Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    currentPasswordRequired: "Please enter your current password",
    newPasswordRequired: "Please enter a new password",
    passwordLengthError: "New password must be at least 6 characters",
    passwordMismatch: "Password confirmation doesn't match",
    passwordUpdateSuccess: "Password has been changed successfully",
    passwordRequirements: "Password must meet the following requirements:",
    passwordLength: "At least 6 characters",
    passwordNumber: "At least 1 number",
    deleteAccount: "Delete Account",
    deleteAccountConfirm:
      "Are you sure you want to delete your account? This action cannot be undone and all your data will be lost.",
    accountDeletedSuccess: "Your account has been successfully deleted",
  },

  // More section (new)
  more: {
    title: "More Features",
    wallets: "Wallets",
    walletsDescription: "Manage your wallets",
    categories: "Categories",
    categoriesDescription: "Customize income/expense categories",
    currency: "Currency",
    currencyDescription: "Change your currency",
    backups: "Backups",
    backupsDescription: "Backup and restore your data",
    sharing: "Sharing",
    sharingDescription: "Share reports and expenses",
    settings: "Settings",
    settingsDescription: "App settings and preferences",
    statsDescription: "View reports and expense statistics",
    switchApp: "Switch App",
    switchAppDescription: "Switch to another application",
    movingFeature: "Moving",
    movingDescription: "Import data from other financial apps",
    templates: "Transaction Templates",
    templatesDescription: "Manage templates for chatbot",
  },

  // Moving screen
  moving: {
    title: "Moving",
    intro: "Import data from other financial apps",
    introText:
      "Take a screenshot of your financial app, AI will automatically detect and create corresponding wallets in AI Money.",
    multipleText: "You can select up to {max} images to analyze at once.",
    selectImage: "Select Image",
    takePhoto: "Take Photo",
    processingImage: "Analyzing image...",
    processingMultiple: "Analyzing image... ({current}/{total})",
    noWalletsFound: "No wallets found",
    noWalletsFoundText:
      "Could not detect wallets in the selected images. Please try again with different images or make sure the images clearly show wallet names and balances.",
    detectedWallets: "Detected Wallets ({count})",
    selectAll: "Select All",
    deselectAll: "Deselect All",
    creatingWallets: "Creating wallets...",
    createSelectedWallets: "Create Selected Wallets",
    selectWalletPrompt: "Please select at least one wallet to create.",
    successTitle: "Success",
    successMessage: "Created {count} new wallets from other apps.",
    errorTitle: "Error",
    errorMessage: "Could not create wallets. Please try again.",
    addMoreImages: "Add more images ({remaining} left)",
    tipsTitle: "Tips",
    tip1: "Take screenshots of wallet/account lists from your old app",
    tip2: "Select multiple images to import wallets from different screens simultaneously",
    tip3: "Make sure wallet names and balances are clearly visible in the images",
    tip4: "Check and edit information before creating wallets",
    permissionDenied: "Permission denied",
    cameraPermissionText: 
      "AI Money needs camera access to let you take screenshots of other financial apps. These images are analyzed with AI technology to extract information about your financial accounts (name and balance), helping you easily transfer data to AI Money without manual entry. Images are only used for this purpose and are processed securely.",
    galleryPermissionText:
      "AI Money needs access to your photo library to let you select screenshots from other financial apps. We'll analyze these images to automatically detect and create wallets matching the names and balances shown in the image. This permission is only used to read images you specifically select, and we don't collect or store other images from your library.",
    tooManyImages: "Too many images",
    tooManyImagesText:
      "You can only select up to {max} images. Only the first {max} images will be processed.",
    imageProcessingError: "Error processing images",
    imageProcessingErrorText:
      "Could not process images. Please try again with different images.",
    walletTypes: {
      bank: "Bank",
      ewallet: "E-Wallet",
      cash: "Cash",
    },
    walletName: "Wallet Name",
    balance: "Balance",
    limitReached: "Daily Limit Reached",
    limitReachedText: "You have reached the daily limit of 2 uses for the Moving feature. Please try again tomorrow.",
    remainingUses: "{count} uses remaining today"
  },
  
  // Templates
  templates: {
    title: "Transaction Templates",
    addTemplate: "Add Template",
    editTemplate: "Edit Template",
    deleteTemplate: "Delete Template",
    templateContent: "Template Content",
    deleteConfirm: "Are you sure you want to delete this template?",
    templateDeleted: "Template deleted successfully",
    templateAdded: "Template added successfully",
    templateUpdated: "Template updated successfully",
    noTemplates: "No transaction templates yet",
    addTemplateHint: "Add transaction templates to use in the chatbot",
    enterTemplate: "Enter transaction template content",
    contentRequired: "Please enter template content",
  },
  
  // Add standalone purpose strings
  permissions: {
    camera: "AI Money needs camera access to let you take screenshots of other financial apps. These images are analyzed with AI technology to extract information about your financial accounts (name and balance), helping you easily transfer data to AI Money without manual entry. Images are only used for this purpose and are processed securely.",
    photoLibrary: "AI Money needs access to your photo library to let you select screenshots from other financial apps. We'll analyze these images to automatically detect and create wallets matching the names and balances shown in the image. This permission is only used to read images you specifically select, and we don't collect or store other images from your library.",
    photoLibraryAdd: "AI Money needs permission to save analysis images to your library when requested. These images can help you reference information that's been imported about your financial accounts."
  },
  
  // Update feature
  update: {
    title: "Check for Updates",
    checking: "Checking for updates...",
    available: "Update available",
    notAvailable: "App is up to date",
    error: "Unable to check for updates",
    lastChecked: "Last checked: {date}",
    checkNow: "Check now",
    requiredUpdateTitle: "Required Update",
    newUpdateTitle: "New Version Available",
    currentVersion: "Current version",
    newVersion: "New version",
    releaseNotes: "Release notes",
    noReleaseNotes: "No release notes available",
    requiredMessage: "Please update to continue using the app",
    recommendedMessage: "We recommend updating for the best experience",
    updateNow: "Update now",
    remindLater: "Remind later",
    close: "Close",
    installSuccess: "Update installed successfully",
    installError: "Unable to install update",
    openStore: "Open app store",
  },
};