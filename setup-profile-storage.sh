#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Supabase storage for profiles...${NC}"

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo -e "${RED}Error: Supabase CLI is not installed.${NC}"
    echo "Please install it first: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Set up storage bucket and policies
echo -e "${YELLOW}Setting up storage buckets and policies...${NC}"
cat "supabase/storage.sql" | supabase db sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully set up storage policies for profiles.${NC}"
else
    echo -e "${RED}Error setting up storage policies.${NC}"
    exit 1
fi

# Update profiles table to add avatar_url and phone columns if not exists
echo -e "${YELLOW}Updating profiles table...${NC}"
cat << EOF | supabase db sql
-- Add avatar_url column if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'avatar_url'
    ) THEN
        ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
    END IF;
    
    -- Add phone column if not exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'phone'
    ) THEN
        ALTER TABLE profiles ADD COLUMN phone TEXT;
    END IF;
END \$\$;
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully updated profiles table.${NC}"
else
    echo -e "${RED}Error updating profiles table.${NC}"
    exit 1
fi

echo -e "${GREEN}Profile storage setup completed successfully!${NC}"
echo -e "${YELLOW}You can now upload profile pictures.${NC}" 