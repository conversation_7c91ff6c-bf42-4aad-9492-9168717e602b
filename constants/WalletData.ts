// File: constants/WalletData.ts
// File tập trung quản lý dữ liệu ví, ngân hàng và các template

import { ImageRequireSource } from "react-native";

// Định nghĩa interface cho ví mẫu
export interface WalletTemplate {
  id: string;
  name: string;
  icon: string;
  color: string;
  type: string;
}

// Định nghĩa interface cho loại ví
export interface WalletType {
  id: string;
  name: string;
  icon: string;
}

// Định nghĩa các màu mặc định cho ví
export const WALLET_COLORS = [
  "#4CAF50",
  "#2196F3",
  "#9C27B0",
  "#F44336",
  "#FF9800",
  "#795548",
  "#607D8B",
  "#E91E63",
  "#3F51B5",
];

// Định nghĩa các loại ví
export const WALLET_TYPES: WalletType[] = [
  { id: "cash", name: "Tiền mặt", icon: "cash-outline" },
  { id: "bank", name: "<PERSON><PERSON> hàng", icon: "card-outline" },
  { id: "ewallet", name: "<PERSON><PERSON> điện tử", icon: "wallet-outline" },
];

// Mapping các icon ngân hàng với file hình ảnh
export const BankLogos: Record<string, ImageRequireSource> = {
  // Ngân hàng và ví điện tử (56 hình ảnh từ danh sách)
  "abbank.png": require("@/assets/images/bank-logos/abbank.png"),
  "acb.png": require("@/assets/images/bank-logos/acb.png"),
  "agribank.png": require("@/assets/images/bank-logos/agribank.png"),
  "appota.png": require("@/assets/images/bank-logos/appota.png"),
  "bacabank.png": require("@/assets/images/bank-logos/bacabank.png"),
  "baokim.png": require("@/assets/images/bank-logos/baokim.png"),
  "bidv.png": require("@/assets/images/bank-logos/bidv.png"),
  "binance.png": require("@/assets/images/bank-logos/binance.png"),
  "cake.png": require("@/assets/images/bank-logos/cake.png"),
  "cbbank.png": require("@/assets/images/bank-logos/cbbank.png"),
  "default.png": require("@/assets/images/bank-logos/default.png"),
  "gold.png": require("@/assets/images/bank-logos/gold.png"),
  "grabpay.png": require("@/assets/images/bank-logos/grabpay.png"),
  "hlbank.png": require("@/assets/images/bank-logos/hlbank.png"),
  "hsbc.png": require("@/assets/images/bank-logos/hsbc.png"),
  "liobank.png": require("@/assets/images/bank-logos/liobank.png"),
  "lpbbank.png": require("@/assets/images/bank-logos/lpbbank.png"),
  "mbbank.png": require("@/assets/images/bank-logos/mbbank.png"),
  "momo.png": require("@/assets/images/bank-logos/momo.png"),
  "ncb.png": require("@/assets/images/bank-logos/ncb.png"),
  "nextpay.png": require("@/assets/images/bank-logos/nextpay.png"),
  "nganluong.png": require("@/assets/images/bank-logos/nganluong.png"),
  "ocb.png": require("@/assets/images/bank-logos/ocb.png"),
  "oceanbank.png": require("@/assets/images/bank-logos/oceanbank.png"),
  "oneu.png": require("@/assets/images/bank-logos/oneu.png"),
  "onus.png": require("@/assets/images/bank-logos/onus.png"),
  "payme.png": require("@/assets/images/bank-logos/payme.png"),
  "payoo.png": require("@/assets/images/bank-logos/payoo.png"),
  "paypal.png": require("@/assets/images/bank-logos/paypal.png"),
  "pgbank.png": require("@/assets/images/bank-logos/pgbank.png"),
  "pvcombank.png": require("@/assets/images/bank-logos/pvcombank.png"),
  "sacombank.png": require("@/assets/images/bank-logos/sacombank.png"),
  "scb.png": require("@/assets/images/bank-logos/scb.png"),
  "seabank.png": require("@/assets/images/bank-logos/seabank.png"),
  "shinhanbank.png": require("@/assets/images/bank-logos/shinhanbank.png"),
  "shopeepay.png": require("@/assets/images/bank-logos/shopeepay.png"),
  "techcombank.png": require("@/assets/images/bank-logos/techcombank.png"),
  "tienmat.png": require("@/assets/images/bank-logos/tienmat.png"),
  "tiki.png": require("@/assets/images/bank-logos/tiki.png"),
  "timo.png": require("@/assets/images/bank-logos/timo.png"),
  "tpbank.png": require("@/assets/images/bank-logos/tpbank.png"),
  "truemoney.png": require("@/assets/images/bank-logos/truemoney.png"),
  "vib.png": require("@/assets/images/bank-logos/vib.png"),
  "vietcombank.png": require("@/assets/images/bank-logos/vietcombank.png"),
  "vietinbank.png": require("@/assets/images/bank-logos/vietinbank.png"),
  "viettelmoney.png": require("@/assets/images/bank-logos/viettelmoney.png"),
  "viettelpay.png": require("@/assets/images/bank-logos/viettelpay.png"),
  "vimo.png": require("@/assets/images/bank-logos/vimo.png"),
  "vnpay.png": require("@/assets/images/bank-logos/vnpay.png"),
  "vnptmoney.png": require("@/assets/images/bank-logos/vnptmoney.png"),
  "vpbank.png": require("@/assets/images/bank-logos/vpbank.png"),
  "vrb.png": require("@/assets/images/bank-logos/vrb.png"),
  "vtcpay.png": require("@/assets/images/bank-logos/vtcpay.png"),
  "wooribank.png": require("@/assets/images/bank-logos/wooribank.png"),
  "xm.png": require("@/assets/images/bank-logos/xm.png"),
  "zalopay.png": require("@/assets/images/bank-logos/zalopay.png"),
  
  // Giữ lại phần dự phòng với các hình ảnh thay thế cho trường hợp không tìm thấy
  "eximbank.png": require("@/assets/images/bank-logos/default.png"),
  "hdbank.png": require("@/assets/images/bank-logos/default.png"),
  "ibk.png": require("@/assets/images/bank-logos/default.png"),
  "indovinabank.png": require("@/assets/images/bank-logos/default.png"),
  "kienlongbank.png": require("@/assets/images/bank-logos/default.png"),
  "lienvietpostbank.png": require("@/assets/images/bank-logos/default.png"),
  "msb.png": require("@/assets/images/bank-logos/default.png"),
  "namabank.png": require("@/assets/images/bank-logos/default.png"),
  "publicbank.png": require("@/assets/images/bank-logos/default.png"),
  "shb.png": require("@/assets/images/bank-logos/default.png"),
  "vietabank.png": require("@/assets/images/bank-logos/default.png"),
  "vietcapitalbank.png": require("@/assets/images/bank-logos/default.png"),
  "baovietbank.png": require("@/assets/images/bank-logos/default.png"),
};

// Danh sách các ví mẫu (cả ngân hàng và ví điện tử) - 48 ví
export const BANK_TEMPLATES: WalletTemplate[] = [
  // Ngân hàng và ví điện tử (56 ví tổng cộng, trừ 8 ví tiền mặt = 48 ví)
  {
    id: "abbank",
    name: "ABBank",
    icon: "abbank.png",
    color: "#00A651",
    type: "bank",
  },
  { id: "acb", name: "ACB", icon: "acb.png", color: "#0033A0", type: "bank" },
  {
    id: "agribank",
    name: "Agribank",
    icon: "agribank.png",
    color: "#00538b",
    type: "bank",
  },
  {
    id: "appota",
    name: "Appota",
    icon: "appota.png",
    color: "#37A651",
    type: "ewallet",
  },
  {
    id: "bacabank",
    name: "BacABank",
    icon: "bacabank.png",
    color: "#F15A22",
    type: "bank",
  },
  {
    id: "baokim",
    name: "Bảo Kim",
    icon: "baokim.png",
    color: "#48A53C",
    type: "ewallet",
  },
  {
    id: "bidv",
    name: "BIDV",
    icon: "bidv.png",
    color: "#1A488D",
    type: "bank",
  },
  {
    id: "binance",
    name: "Binance",
    icon: "binance.png",
    color: "#F0B90B",
    type: "ewallet",
  },
  {
    id: "cake",
    name: "CAKE",
    icon: "cake.png",
    color: "#FF4094",
    type: "ewallet",
  },
  {
    id: "cbbank",
    name: "CBBank",
    icon: "cbbank.png",
    color: "#13477D",
    type: "bank",
  },
  {
    id: "grabpay",
    name: "GrabPay",
    icon: "grabpay.png",
    color: "#00B14F",
    type: "ewallet",
  },
  {
    id: "hlbank",
    name: "HLBank",
    icon: "hlbank.png",
    color: "#C41230",
    type: "bank",
  },
  {
    id: "hsbc",
    name: "HSBC",
    icon: "hsbc.png",
    color: "#DB0011",
    type: "bank",
  },
  {
    id: "liobank",
    name: "LioBank",
    icon: "liobank.png",
    color: "#000000",
    type: "bank",
  },
  {
    id: "lpbbank",
    name: "LPBank",
    icon: "lpbbank.png",
    color: "#05489D",
    type: "bank",
  },
  {
    id: "mbbank",
    name: "MB Bank",
    icon: "mbbank.png",
    color: "#1D4289",
    type: "bank",
  },
  {
    id: "momo",
    name: "Ví MoMo",
    icon: "momo.png",
    color: "#A50064",
    type: "ewallet",
  },
  { id: "ncb", name: "NCB", icon: "ncb.png", color: "#E41E26", type: "bank" },
  {
    id: "nextpay",
    name: "NextPay",
    icon: "nextpay.png",
    color: "#005AAB",
    type: "ewallet",
  },
  {
    id: "nganluong",
    name: "Ngân Lượng",
    icon: "nganluong.png",
    color: "#F37021",
    type: "ewallet",
  },
  { id: "ocb", name: "OCB", icon: "ocb.png", color: "#D4A448", type: "bank" },
  {
    id: "oceanbank",
    name: "OceanBank",
    icon: "oceanbank.png",
    color: "#29ABE2",
    type: "bank",
  },
  {
    id: "oneu",
    name: "OneU",
    icon: "oneu.png",
    color: "#ED4F78",
    type: "ewallet",
  },
  {
    id: "onus",
    name: "ONUS",
    icon: "onus.png",
    color: "#005EFF",
    type: "ewallet",
  },
  {
    id: "payme",
    name: "PayMe",
    icon: "payme.png",
    color: "#21C17C",
    type: "ewallet",
  },
  {
    id: "payoo",
    name: "Payoo",
    icon: "payoo.png",
    color: "#00AEEF",
    type: "ewallet",
  },
  {
    id: "paypal",
    name: "PayPal",
    icon: "paypal.png",
    color: "#003087",
    type: "ewallet",
  },
  {
    id: "pgbank",
    name: "PGBank",
    icon: "pgbank.png",
    color: "#00AEEF",
    type: "bank",
  },
  {
    id: "pvcombank",
    name: "PVcomBank",
    icon: "pvcombank.png",
    color: "#F89938",
    type: "bank",
  },
  {
    id: "sacombank",
    name: "Sacombank",
    icon: "sacombank.png",
    color: "#004B9C",
    type: "bank",
  },
  { id: "scb", name: "SCB", icon: "scb.png", color: "#DC0032", type: "bank" },
  {
    id: "seabank",
    name: "SeABank",
    icon: "seabank.png",
    color: "#0C2577",
    type: "bank",
  },
  {
    id: "shinhanbank",
    name: "ShinhanBank",
    icon: "shinhanbank.png",
    color: "#0091CF",
    type: "bank",
  },
  {
    id: "shopeepay",
    name: "ShopeePay",
    icon: "shopeepay.png",
    color: "#EE4D2D",
    type: "ewallet",
  },
  {
    id: "techcombank",
    name: "Techcombank",
    icon: "techcombank.png",
    color: "#E4032E",
    type: "bank",
  },
  {
    id: "tiki",
    name: "Tiki",
    icon: "tiki.png",
    color: "#1A94FF",
    type: "ewallet",
  },
  {
    id: "timo",
    name: "Timo",
    icon: "timo.png",
    color: "#00317C",
    type: "bank",
  },
  {
    id: "tpbank",
    name: "TPBank",
    icon: "tpbank.png",
    color: "#7C1282",
    type: "bank",
  },
  {
    id: "truemoney",
    name: "TrueMoney",
    icon: "truemoney.png",
    color: "#FF6200",
    type: "ewallet",
  },
  { id: "vib", name: "VIB", icon: "vib.png", color: "#412F8A", type: "bank" },
  {
    id: "vietcombank",
    name: "Vietcombank",
    icon: "vietcombank.png",
    color: "#007B49",
    type: "bank",
  },
  {
    id: "vietinbank",
    name: "VietinBank",
    icon: "vietinbank.png",
    color: "#005BAA",
    type: "bank",
  },
  {
    id: "viettelmoney",
    name: "ViettelMoney",
    icon: "viettelmoney.png",
    color: "#EE202E",
    type: "ewallet",
  },
  {
    id: "viettelpay",
    name: "ViettelPay",
    icon: "viettelpay.png",
    color: "#EE202E",
    type: "ewallet",
  },
  {
    id: "vimo",
    name: "Vimo",
    icon: "vimo.png",
    color: "#005EB8",
    type: "ewallet",
  },
  {
    id: "vnpay",
    name: "VNPAY",
    icon: "vnpay.png",
    color: "#0065A9",
    type: "ewallet",
  },
  {
    id: "vnptmoney",
    name: "VNPT Money",
    icon: "vnptmoney.png",
    color: "#2A87D7",
    type: "ewallet",
  },
  {
    id: "vpbank",
    name: "VPBank",
    icon: "vpbank.png",
    color: "#030082",
    type: "bank",
  },
  { id: "vrb", name: "VRB", icon: "vrb.png", color: "#233E94", type: "bank" },
  {
    id: "vtcpay",
    name: "VTCPay",
    icon: "vtcpay.png",
    color: "#004D9D",
    type: "ewallet",
  },
  {
    id: "wooribank",
    name: "WooriBank",
    icon: "wooribank.png",
    color: "#0082C9",
    type: "bank",
  },
  { id: "xm", name: "XM", icon: "xm.png", color: "#000000", type: "ewallet" },
  {
    id: "zalopay",
    name: "ZaloPay",
    icon: "zalopay.png",
    color: "#0068FF",
    type: "ewallet",
  },
];

// Ví tiền mặt mẫu - 8 ví
export const CASH_TEMPLATES: WalletTemplate[] = [
  {
    id: "cash_home",
    name: "Tiền mặt tại nhà",
    icon: "home-outline",
    color: "#4CAF50",
    type: "cash",
  },
  {
    id: "cash_wallet",
    name: "Tiền trong ví",
    icon: "wallet-outline",
    color: "#2196F3",
    type: "cash",
  },
  {
    id: "cash_savings",
    name: "Tiền tiết kiệm",
    icon: "save-outline",
    color: "#9C27B0",
    type: "cash",
  },
  {
    id: "cash_office",
    name: "Tiền tại công ty",
    icon: "briefcase-outline",
    color: "#FF9800",
    type: "cash",
  },
  {
    id: "cash_emergency",
    name: "Tiền dự phòng",
    icon: "medkit-outline",
    color: "#F44336",
    type: "cash",
  },
  {
    id: "cash_travel",
    name: "Tiền du lịch",
    icon: "airplane-outline",
    color: "#009688",
    type: "cash",
  },
  {
    id: "cash_gift",
    name: "Tiền lì xì/quà tặng",
    icon: "gift-outline",
    color: "#E91E63",
    type: "cash",
  },
  {
    id: "cash_education",
    name: "Tiền học tập",
    icon: "school-outline",
    color: "#3F51B5",
    type: "cash",
  },
];

// Hàm tiện ích để kiểm tra xem icon có phải là file ảnh PNG từ BankLogos không
export const isImageIcon = (iconName: string): boolean => {
  if (!iconName) return false;
  return iconName.endsWith('.png') && BankLogos.hasOwnProperty(iconName);
};

// Hàm lấy danh sách template theo loại
export const getWalletTemplatesByType = (type: string): WalletTemplate[] => {
  return type === 'bank' ? BANK_TEMPLATES : CASH_TEMPLATES;
};

// Hàm lọc danh sách templates theo từ khóa tìm kiếm
export const getFilteredTemplates = (templateType: string, searchQuery: string): WalletTemplate[] => {
  const templates = templateType === 'bank' ? BANK_TEMPLATES : CASH_TEMPLATES;
  
  if (!searchQuery.trim()) {
    return templates;
  }
  
  const normalizedQuery = searchQuery.toLowerCase().trim();
  return templates.filter(template => 
    template.name.toLowerCase().includes(normalizedQuery)
  );
};

// Lấy tên loại ví từ type id
export const getWalletTypeName = (typeId: string, t: any): string => {
  switch (typeId) {
    case "cash":
      return t ? t("wallets.cashType") : "Tiền mặt";
    case "bank":
      return t ? t("wallets.bankType") : "Ngân hàng";
    case "ewallet":
      return t ? t("wallets.ewalletType") : "Ví điện tử";
    default:
      return typeId;
  }
};

// Format tiền với dấu ngăn cách hàng nghìn
export const formatMoney = (amount: string | number): string => {
  if (!amount) return "";

  // Loại bỏ tất cả các dấu chấm hiện có
  const cleanAmount = amount.toString().replace(/\./g, "");

  // Thêm dấu chấm ngăn cách hàng nghìn
  return cleanAmount.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

// Chuyển đổi từ số có dấu chấm ngăn cách về số nguyên
export const parseMoney = (formattedAmount: string): string => {
  if (!formattedAmount) return "0";

  // Loại bỏ tất cả các dấu chấm
  return formattedAmount.toString().replace(/\./g, "");
};

export default {
  WALLET_COLORS,
  WALLET_TYPES,
  BankLogos,
  BANK_TEMPLATES,
  CASH_TEMPLATES,
  isImageIcon,
  getWalletTemplatesByType,
  getFilteredTemplates,
  getWalletTypeName,
  formatMoney,
  parseMoney
};