/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#007AFF";
const tintColorDark = "#007AFF";

export const Colors = {
  light: {
    text: "#333333",
    background: "#FFFFFF",
    tint: tintColorLight,
    icon: "#687076",
    tabIconDefault: "#687076",
    tabIconSelected: tintColorLight,
    card: "#FFFFFF",
    border: "#EEEEEE",
    notification: "#FF3B30",
    inputBackground: "#F5F5F5",
    placeholder: "#999999",
  },
  dark: {
    text: "#FFFFFF",
    background: "#121212",
    tint: tintColorDark,
    icon: "#9BA1A6",
    tabIconDefault: "#9BA1A6",
    tabIconSelected: tintColorDark,
    card: "#1E1E1E",
    border: "#333333",
    notification: "#FF453A",
    inputBackground: "#333333",
    placeholder: "#777777",
  },
  categories: {
    expense: "#F44336",
    income: "#4CAF50",
    food: "#FF9800",
    transport: "#9C27B0",
    shopping: "#F44336",
    bills: "#2196F3",
    home: "#FF5722",
    entertainment: "#673AB7",
    health: "#4CAF50",
    education: "#009688",
  },
};
