// File: lib/helpers/chat-helpers.ts
// Những file liên quan đến file này: 
// 1. app/(tabs)/chatbot.tsx
// 2. lib/services/ai-service.ts
// 3. lib/services/chatbot-actions.ts
// 4. lib/types/chatbot-types.ts
// 5. lib/services/ai-learning-service.ts (MỚI)

import { CategoryModel } from "@/lib/models/category";
import { WalletModel } from "@/lib/models/wallet";
import { TransactionModel } from "@/lib/models/transaction";
import { 
  TransferRequest,
  TransactionResult,
  UserPronoun 
} from "@/lib/types/chatbot-types";
import { AILearningService } from "@/lib/services/ai-learning-service";

// Danh sách cách gọi người dùng và AI sẽ xưng
export const USER_PRONOUNS: UserPronoun[] = [
  { calling: "Bạn", selfCalling: "Tôi" },
  { calling: "<PERSON>h", selfCalling: "Em" },
  { calling: "Chị", selfCalling: "Em" },
  { calling: "Em", selfCalling: "Anh" },
  { calling: "Chú", selfCalling: "Cháu" },
  { calling: "Cô", selfCalling: "Cháu" },
  { calling: "Bố", selfCalling: "Con" },
  { calling: "Ba", selfCalling: "Con" },
  { calling: "Mẹ", selfCalling: "Con" },
  { calling: "Má", selfCalling: "Con" },
  { calling: "Bà", selfCalling: "Tui" },
];

// Format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Format date to Vietnamese format
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Get Vietnamese relative date (today, yesterday, etc.)
export const getRelativeDate = (dateString: string): string => {
  const date = new Date(dateString);
  const today = new Date();

  // Reset time part for comparison
  const dateWithoutTime = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate()
  );
  const todayWithoutTime = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate()
  );

  const diff = todayWithoutTime.getTime() - dateWithoutTime.getTime();
  const daysDiff = diff / (1000 * 60 * 60 * 24);

  if (daysDiff === 0) return "Hôm nay";
  if (daysDiff === 1) return "Hôm qua";
  if (daysDiff === 2) return "Hôm kia";
  if (daysDiff >= 3 && daysDiff < 7) return `${daysDiff} ngày trước`;

  return formatDate(dateString);
};

// Tạo định dạng thời gian đầy đủ cả ngày và giờ cho giao dịch mới
export const getFullDateTime = (): string => {
  // Lấy cả ngày và giờ hiện tại
  const now = new Date();
  // Trả về định dạng ISO đầy đủ, ví dụ: "2023-05-13T15:30:00.000Z"
  return now.toISOString();
};

// Trích xuất phần ngày từ chuỗi ISO datetime
export const getDateFromISOString = (isoString: string): string => {
  return isoString.split("T")[0]; // Lấy phần "yyyy-mm-dd" từ "yyyy-mm-ddThh:mm:ss.sssZ"
};

// Parse amount from text
export const parseAmount = (text: string): number | null => {
  // Look for patterns like "150k", "15tr", "150,000", "15 triệu", etc.
  const amountPatterns = [
    /(\d+[.,]?\d*)\s*k\b/i, // 150k
    /(\d+[.,]?\d*)\s*tr\b/i, // 15tr
    /(\d+[.,]?\d*)\s*triệu\b/i, // 15 triệu
    /(\d+[.,]?\d*)\s*ngàn\b/i, // 150 ngàn
    /(\d+[.,]?\d*)[.,](\d{3})[.,]?(\d{3})?\b/i, // 150.000 or 1.500.000
    /(\d+[.,]?\d*)\s*đồng\b/i, // 150000 đồng
    /(\d+[.,]?\d*)\s*d\b/i, // 150000d
    /(\d+[.,]?\d*)\s*vnd\b/i, // 150000vnd
  ];

  for (const pattern of amountPatterns) {
    const match = text.match(pattern);
    if (match) {
      let amount = parseFloat(match[1].replace(",", "."));

      // Convert based on unit
      if (pattern.source.includes("k\\b")) {
        amount *= 1000;
      } else if (
        pattern.source.includes("tr\\b") ||
        pattern.source.includes("triệu\\b")
      ) {
        amount *= 1000000;
      } else if (pattern.source.includes("ngàn\\b")) {
        amount *= 1000;
      }

      return amount;
    }
  }

  return null;
};

// Hàm phân loại danh mục mới: SỬ DỤNG AI LEARNING SERVICE
export const findCategoryByDescription = async (
  description: string,
  type: "income" | "expense"
): Promise<string | null> => {
  try {
    // Sử dụng AI Learning Service thay vì logic cũ
    return await AILearningService.findCategoryByDescription(description, type);
  } catch (error) {
    console.error("Lỗi khi tìm danh mục phù hợp:", error);
    return null;
  }
};

// Tìm ví theo tên
export const findWalletByName = async (walletName: string): Promise<string | null> => {
  try {
    const wallets = await WalletModel.getAll();
    if (!wallets || wallets.length === 0) return null;

    // Chuẩn hóa tên ví cần tìm
    const normalizedName = walletName.toLowerCase().trim();

    // Tìm kiếm ví có tên tương đồng
    for (const wallet of wallets) {
      if (wallet.name.toLowerCase().includes(normalizedName)) {
        return wallet.id;
      }
    }

    // Các tên phổ biến và biệt danh có thể có của ví
    const commonWalletAliases: { [key: string]: string[] } = {
      vcb: ["vietcombank", "vcb"],
      vtb: ["vietinbank", "vtb"],
      bidv: ["bidv", "đầu tư và phát triển"],
      agri: ["agribank", "nông nghiệp", "agri"],
      sacom: ["sacombank", "sacom"],
      tech: ["techcombank", "tech", "tcb"],
      acb: ["acb", "á châu"],
      mb: ["mb", "quân đội", "mbbank"],
      vpbank: ["vpbank", "vp"],
      tpbank: ["tpbank", "tp"],
      ocb: ["ocb", "phương đông"],
      msb: ["msb", "hàng hải"],
      hdbank: ["hdbank", "hd"],
      vib: ["vib", "quốc tế"],
      scb: ["scb", "sài gòn"],
      abbank: ["abbank", "an bình"],
      "bac a": ["bac a", "bắc á"],
      vietabank: ["vietabank", "việt á"],
      seabank: ["seabank", "đông nam á"],
      lpbank: ["lpbank", "lp", "liên việt"],
      shb: ["shb", "sài gòn hà nội"],
      eximbank: ["eximbank", "exim"],
      pgbank: ["pgbank", "pg", "xăng dầu"],
      oceanbank: ["oceanbank", "ocean"],
      namabank: ["namabank", "nam á"],
      baovietbank: ["baovietbank", "bảo việt"],
      gpbank: ["gpbank", "gp", "dầu khí toàn cầu"],
      vietbank: ["vietbank", "việt nam ngân hàng"],
      ncb: ["ncb", "quốc dân"],
      momo: ["momo", "ví momo"],
      zalopay: ["zalopay", "zalo"],
      vnpay: ["vnpay", "vn"],
      airpay: ["airpay", "shopee pay", "shopee"],
      viettelpay: ["viettelpay", "viettel"],
      moca: ["moca", "grab moca", "grabpay"],
      "tiền mặt": ["tiền mặt", "mặt", "cash"],
    };

    // Tìm kiếm theo biệt danh
    for (const [key, aliases] of Object.entries(commonWalletAliases)) {
      if (aliases.some((alias) => normalizedName.includes(alias))) {
        // Tìm ví có tên gần giống với biệt danh
        for (const wallet of wallets) {
          const walletNameLower = wallet.name.toLowerCase();
          if (
            aliases.some((alias) => walletNameLower.includes(alias)) ||
            walletNameLower.includes(key)
          ) {
            return wallet.id;
          }
        }
      }
    }

    // Nếu không tìm thấy, trả về null
    return null;
  } catch (error) {
    console.error("Lỗi khi tìm ví theo tên:", error);
    return null;
  }
};

// Phân tích yêu cầu chuyển tiền từ tin nhắn
export const parseTransferRequest = async (message: string): Promise<TransferRequest | null> => {
  // Các mẫu regex để phân tích câu chuyển tiền
  // Mẫu 1: "X (đơn vị tiền) từ V1 sang/chuyển/qua V2"
  // Mẫu 2: "chuyển X (đơn vị tiền) từ V1 sang/cho/đến V2"
  // Mẫu 3: "X (đơn vị tiền) V1 sang/chuyển/qua V2"
  // Mẫu 4: "X (đơn vị tiền) V1 cho V2"
  // Mẫu 5: "chuyển X (đơn vị tiền) V1 sang/cho/đến V2"

  try {
    // Chuyển đổi message về chữ thường để dễ xử lý
    const msg = message.toLowerCase();

    // Regex để nhận dạng số tiền
    // Nhận dạng: 500k, 500 k, 500K, 500 ngàn, 500.000, 500000, 1tr, 1 tr, 1 triệu, 1triệu
    const amountPattern =
      /(\d+[.,]?\d*)\s*(k|ngàn|nghìn|tr|triệu|đồng|d|vnd)?/i;

    // Regex nhận dạng các từ khóa chuyển tiền
    const transferKeywords = /(chuyển|sang|qua|cho|đến|vào|từ)/i;

    if (!transferKeywords.test(msg)) {
      return null; // Không phải yêu cầu chuyển tiền
    }

    // Tìm số tiền trong tin nhắn
    const amountMatch = msg.match(amountPattern);
    if (!amountMatch) return null;

    let amount = parseFloat(amountMatch[1].replace(",", "."));
    const unit = amountMatch[2] ? amountMatch[2].toLowerCase() : "";

    // Chuyển đổi đơn vị sang VND
    if (unit === "k" || unit === "ngàn" || unit === "nghìn") {
      amount *= 1000;
    } else if (unit === "tr" || unit === "triệu") {
      amount *= 1000000;
    }

    // Nếu không tìm thấy số tiền hoặc số tiền không hợp lệ
    if (isNaN(amount) || amount <= 0) return null;

    // Tìm các ví được đề cập trong tin nhắn
    // Cắt bỏ phần có số tiền để dễ phân tích hơn
    const textWithoutAmount = msg.replace(amountMatch[0], "");

    // Tách câu thành các phần nhỏ để tìm tên ví
    const parts = textWithoutAmount.split(/\s+/);

    let sourceWalletName = "";
    let destWalletName = "";
    let foundTransferKeyword = false;

    // Phân tích cấu trúc câu để tìm ví nguồn và ví đích
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      // Kiểm tra nếu từ hiện tại là từ khóa chuyển tiền
      if (/chuyển|sang|qua|cho|đến|vào/.test(part)) {
        foundTransferKeyword = true;

        // Nếu chưa tìm thấy ví nguồn, giả sử các từ trước từ khóa này là ví nguồn
        if (!sourceWalletName && i > 0) {
          sourceWalletName = parts.slice(0, i).join(" ").trim();
        }

        // Các từ sau từ khóa này là ví đích
        if (i < parts.length - 1) {
          destWalletName = parts
            .slice(i + 1)
            .join(" ")
            .trim();
          break;
        }
      } else if (part === "từ" && i < parts.length - 1) {
        // Xử lý trường hợp "từ V1 sang V2"
        sourceWalletName = "";

        // Tìm từ khóa chuyển tiếp theo
        for (let j = i + 1; j < parts.length; j++) {
          if (/sang|qua|cho|đến|vào/.test(parts[j])) {
            sourceWalletName = parts
              .slice(i + 1, j)
              .join(" ")
              .trim();
            destWalletName = parts
              .slice(j + 1)
              .join(" ")
              .trim();
            foundTransferKeyword = true;
            break;
          }
        }

        // Nếu không tìm thấy từ khóa chuyển tiếp theo, mặc định lấy tất cả các từ còn lại
        if (!sourceWalletName) {
          sourceWalletName = parts
            .slice(i + 1)
            .join(" ")
            .trim();
        }

        break;
      }
    }

    // Nếu không tìm thấy từ khóa chuyển tiền rõ ràng, thử phương pháp đơn giản
    if (!foundTransferKeyword || (!sourceWalletName && !destWalletName)) {
      // Tìm các phần tử còn lại sau khi bỏ số tiền và chia làm 2
      const remainingParts = parts.filter((p) => p.trim() !== "");

      if (remainingParts.length >= 2) {
        // Giả sử phần tử đầu tiên là ví nguồn và cuối cùng là ví đích
        sourceWalletName = remainingParts[0];
        destWalletName = remainingParts[remainingParts.length - 1];
      }
    }

    // Làm sạch tên ví (loại bỏ các từ khóa chuyển tiền)
    sourceWalletName = sourceWalletName
      .replace(/(chuyển|sang|qua|cho|đến|vào|từ)/g, "")
      .trim();
    destWalletName = destWalletName
      .replace(/(chuyển|sang|qua|cho|đến|vào|từ)/g, "")
      .trim();

    // Nếu không tìm thấy tên ví
    if (!sourceWalletName || !destWalletName) {
      return null;
    }

    // Tìm ID ví từ tên ví
    const sourceWalletId = await findWalletByName(sourceWalletName);
    const destWalletId = await findWalletByName(destWalletName);

    // Nếu không tìm thấy ít nhất một trong hai ví
    if (!sourceWalletId || !destWalletId) {
      return {
        isTransfer: true,
        isComplete: false,
        amount,
        sourceWalletName,
        destWalletName,
        sourceWalletId,
        destWalletId,
        message: "Không tìm thấy ví",
      };
    }

    // Nếu cả hai ví là một
    if (sourceWalletId === destWalletId) {
      return {
        isTransfer: true,
        isComplete: false,
        amount,
        sourceWalletName,
        destWalletName,
        sourceWalletId,
        destWalletId,
        message: "Không thể chuyển tiền cho cùng một ví",
      };
    }

    return {
      isTransfer: true,
      isComplete: true,
      amount,
      sourceWalletName,
      destWalletName,
      sourceWalletId,
      destWalletId,
      description: `Chuyển tiền từ ${sourceWalletName} sang ${destWalletName}`,
    };
  } catch (error) {
    console.error("Lỗi khi phân tích yêu cầu chuyển tiền:", error);
    return null;
  }
};

// Phân tích tên ví từ tin nhắn
export const parseWalletFromMessage = async (
  message: string
): Promise<string | null> => {
  try {
    // Lấy danh sách tất cả các ví
    const wallets = await WalletModel.getAll();
    if (!wallets || wallets.length === 0) return null;

    // Chuyển đổi tin nhắn về chữ thường để dễ so sánh
    const lowerMessage = message.toLowerCase();

    // Tạo mẫu regex để tìm tên ví ở cuối câu
    // Ví dụ: "ăn sáng 100k vib" sẽ tìm "vib" ở cuối
    const words = lowerMessage.split(/\s+/);

    // Lấy từ cuối cùng - có thể là tên ví
    const lastWord = words[words.length - 1].trim();

    // Nếu từ cuối cùng có chứa số, có thể là một phần của số tiền -> bỏ qua
    if (/\d/.test(lastWord)) return null;

    // Kiểm tra xem từ cuối có phải là tên ví không
    const walletId = await findWalletByName(lastWord);

    return walletId;
  } catch (error) {
    console.error("Lỗi khi phân tích tên ví từ tin nhắn:", error);
    return null;
  }
};

// Helper function to create sample data for testing
export const createSampleData = async () => {
  try {
    // Check if wallets exist
    const wallets = await WalletModel.getAll();
    if (wallets.length === 0) {
      // Create a sample wallet
      await WalletModel.create({
        name: "Ví tiền mặt",
        balance: 1000000,
        type: "cash",
        icon: "cash-outline",
        color: "#2ecc71",
        is_default: true,
      });
      console.log("Created sample wallet");
    }

    // Check if categories exist
    const expenseCategories = await CategoryModel.getByType("expense");
    if (expenseCategories.length === 0) {
      // Create sample expense categories - Lưu ý: Danh mục "Chi tiêu khác" được tạo đầu tiên
      await CategoryModel.create({
        name: "Chi tiêu khác",
        type: "expense",
        icon: "cart-outline",
        color: "#607d8b", // Màu xám
      });
      
      await CategoryModel.create({
        name: "Y tế",
        type: "expense",
        icon: "medical-outline",
        color: "#9b59b6", // Màu tím
      });
      
      await CategoryModel.create({
        name: "Sinh hoạt",
        type: "expense",
        icon: "home-outline",
        color: "#f39c12", // Màu cam
      });
      
      await CategoryModel.create({
        name: "Mua sắm",
        type: "expense",
        icon: "bag-outline",
        color: "#16a085", // Màu xanh ngọc
      });
      
      await CategoryModel.create({
        name: "Di chuyển",
        type: "expense",
        icon: "car-outline",
        color: "#3498db", // Màu xanh dương
      });
      
      await CategoryModel.create({
        name: "Ăn uống",
        type: "expense",
        icon: "restaurant-outline",
        color: "#e74c3c", // Màu đỏ
      });
      
      console.log("Created sample expense categories");
    }

    // Đảm bảo có danh mục "Chi tiêu khác" nếu chưa có
    const hasOtherCategory = expenseCategories.some(
      cat => cat.name.toLowerCase().includes("khác")
    );
    
    if (!hasOtherCategory && expenseCategories.length > 0) {
      await CategoryModel.create({
        name: "Chi tiêu khác",
        type: "expense",
        icon: "cart-outline",
        color: "#607d8b", // Màu xám
      });
      console.log("Created 'Chi tiêu khác' category");
    }

    const incomeCategories = await CategoryModel.getByType("income");
    if (incomeCategories.length === 0) {
      // Create sample income category
      await CategoryModel.create({
        name: "Lương",
        type: "income",
        icon: "cash-outline",
        color: "#2ecc71" // Màu xanh lá
      });
      
      await CategoryModel.create({
        name: "Thu nhập khác",
        type: "income",
        icon: "cash-outline",
        color: "#3498db" // Màu xanh dương
      });
      
      console.log("Created sample income categories");
    }

    return true;
  } catch (error) {
    console.error("Error creating sample data:", error);
    return false;
  }
};

// Đảm bảo có danh mục mặc định trong hệ thống
export const ensureDefaultCategories = async () => {
  try {
    const expenseCategories = await CategoryModel.getByType("expense");
    
    // Kiểm tra xem có danh mục "Chi tiêu khác" không
    const hasOtherCategory = expenseCategories.some(
      cat => cat.name.toLowerCase().includes("khác")
    );
    
    // Nếu chưa có danh mục chi tiêu nào hoặc không có "Chi tiêu khác"
    if (expenseCategories.length === 0 || !hasOtherCategory) {
      return await createSampleData();
    }
    
    return true;
  } catch (error) {
    console.error("Error ensuring default categories:", error);
    return false;
  }
};