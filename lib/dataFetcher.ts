import NetInfo from "@react-native-community/netinfo";
import { Alert } from "react-native";
import { supabase } from "./supabase";

// Cache for storing fetched data
const cache: Record<string, { data: any; timestamp: number }> = {};

// Default cache expiration time (1 minute)
const DEFAULT_CACHE_EXPIRATION = 60 * 1000;

// Check if network is available
const isNetworkAvailable = async (): Promise<boolean> => {
  const networkState = await NetInfo.fetch();
  return networkState.isConnected ?? false;
};

// Refresh Supabase session
const refreshSession = async (): Promise<boolean> => {
  try {
    // Check network status first
    const networkAvailable = await isNetworkAvailable();
    if (!networkAvailable) {
      console.warn("Cannot refresh session: No network connection");
      return false;
    }

    // Refresh session
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error("Error refreshing session:", error);
      return false;
    }

    if (data.session) {
      console.log("Session refreshed successfully");
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error in refreshSession:", error);
    return false;
  }
};

// Generic data fetcher with caching, retry, and session refresh
export async function fetchData<T>({
  key,
  fetcher,
  forceRefresh = false,
  cacheExpiration = DEFAULT_CACHE_EXPIRATION,
  maxRetries = 3,
  showErrorAlert = false,
  errorMessage = "Không thể tải dữ liệu. Vui lòng thử lại sau.",
}: {
  key: string;
  fetcher: () => Promise<{ data: T | null; error: any }>;
  forceRefresh?: boolean;
  cacheExpiration?: number;
  maxRetries?: number;
  showErrorAlert?: boolean;
  errorMessage?: string;
}): Promise<T | null> {
  // Check cache first if not forcing refresh
  if (
    !forceRefresh &&
    cache[key] &&
    Date.now() - cache[key].timestamp < cacheExpiration
  ) {
    console.log(`Using cached data for ${key}`);
    return cache[key].data;
  }

  // Check network availability
  const networkAvailable = await isNetworkAvailable();
  if (!networkAvailable) {
    console.warn("Network unavailable, using cached data if available");
    if (cache[key]) {
      return cache[key].data;
    }

    if (showErrorAlert) {
      Alert.alert(
        "Lỗi kết nối",
        "Không có kết nối mạng. Vui lòng kiểm tra kết nối và thử lại."
      );
    }
    return null;
  }

  // Attempt to fetch data with retries
  let retries = 0;
  while (retries <= maxRetries) {
    try {
      const { data, error } = await fetcher();

      if (error) {
        console.error(
          `Error fetching data (attempt ${retries + 1}/${maxRetries + 1}):`,
          error
        );

        // Check if error is related to authentication
        if (
          error.message?.includes("JWT expired") ||
          error.message?.includes("invalid token") ||
          error.message?.includes("not authenticated") ||
          error.status === 401
        ) {
          console.log(
            "Authentication error detected, attempting to refresh session"
          );
          const refreshed = await refreshSession();
          if (refreshed) {
            console.log("Session refreshed, retrying query");
            retries++;
            continue;
          }
        }

        // If we've reached max retries, show error and return null
        if (retries === maxRetries) {
          if (showErrorAlert) {
            Alert.alert("Lỗi", errorMessage);
          }
          return null;
        }

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, 1000));
        retries++;
        continue;
      }

      // Cache successful result
      if (data) {
        cache[key] = {
          data,
          timestamp: Date.now(),
        };
      }

      return data;
    } catch (error) {
      console.error(
        `Unexpected error (attempt ${retries + 1}/${maxRetries + 1}):`,
        error
      );

      // If we've reached max retries, show error and return null
      if (retries === maxRetries) {
        if (showErrorAlert) {
          Alert.alert("Lỗi", errorMessage);
        }
        return null;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
      retries++;
    }
  }

  return null;
}

// Clear cache for a specific key
export function clearCache(key: string): void {
  if (cache[key]) {
    delete cache[key];
  }
}

// Clear all cache
export function clearAllCache(): void {
  Object.keys(cache).forEach((key) => {
    delete cache[key];
  });
}
