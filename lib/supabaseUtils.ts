import NetInfo from "@react-native-community/netinfo";
import { Alert } from "react-native";
import { supabase } from "./supabase";

// Network status tracking
let isConnected = true;

// Initialize network listener
export const initNetworkListener = () => {
  // Subscribe to network status changes
  const unsubscribe = NetInfo.addEventListener((state) => {
    isConnected = state.isConnected ?? false;
    console.log(
      "Network connection status:",
      isConnected ? "Connected" : "Disconnected"
    );
  });

  return unsubscribe;
};

// Check if device is connected to the internet
export const checkNetworkStatus = async (): Promise<boolean> => {
  try {
    const networkState = await NetInfo.fetch();
    isConnected = networkState.isConnected ?? false;
    return isConnected;
  } catch (error) {
    console.error("Error checking network status:", error);
    return false;
  }
};

// Refresh Supabase session
export const refreshSession = async (): Promise<boolean> => {
  try {
    // Check network status first
    const networkAvailable = await checkNetworkStatus();
    if (!networkAvailable) {
      console.warn("Cannot refresh session: No network connection");
      return false;
    }

    // Get current session
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();

    if (sessionError) {
      console.error("Error getting session:", sessionError);
      return false;
    }

    if (!sessionData.session) {
      console.warn("No active session to refresh");
      return false;
    }

    // Refresh session
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error("Error refreshing session:", error);
      return false;
    }

    if (data.session) {
      console.log("Session refreshed successfully");
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error in refreshSession:", error);
    return false;
  }
};

// Wrapper for Supabase queries with retry logic
export const executeSupabaseQuery = async <T>(
  queryFn: () => Promise<{ data: T | null; error: any }>,
  options: {
    maxRetries?: number;
    retryDelay?: number;
    showErrorAlert?: boolean;
    errorMessage?: string;
  } = {}
): Promise<T | null> => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    showErrorAlert = false,
    errorMessage = "Không thể tải dữ liệu. Vui lòng thử lại sau.",
  } = options;

  let retries = 0;

  while (retries <= maxRetries) {
    try {
      // Check network status
      if (retries > 0) {
        const networkAvailable = await checkNetworkStatus();
        if (!networkAvailable) {
          console.warn("Network unavailable, waiting before retry");
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
          retries++;
          continue;
        }
      }

      // Execute the query
      const { data, error } = await queryFn();

      if (error) {
        console.error(
          `Supabase query error (attempt ${retries + 1}/${maxRetries + 1}):`,
          error
        );

        // Check if error is related to authentication
        if (
          error.message?.includes("JWT expired") ||
          error.message?.includes("invalid token") ||
          error.message?.includes("not authenticated") ||
          error.status === 401
        ) {
          console.log(
            "Authentication error detected, attempting to refresh session"
          );
          const refreshed = await refreshSession();
          if (refreshed) {
            console.log("Session refreshed, retrying query");
            retries++;
            continue;
          }
        }

        // If we've reached max retries, show error and return null
        if (retries === maxRetries) {
          if (showErrorAlert) {
            Alert.alert("Lỗi", errorMessage);
          }
          return null;
        }

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
        retries++;
        continue;
      }

      return data;
    } catch (error) {
      console.error(
        `Unexpected error in Supabase query (attempt ${retries + 1}/${
          maxRetries + 1
        }):`,
        error
      );

      // If we've reached max retries, show error and return null
      if (retries === maxRetries) {
        if (showErrorAlert) {
          Alert.alert("Lỗi", errorMessage);
        }
        return null;
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, retryDelay));
      retries++;
    }
  }

  return null;
};
