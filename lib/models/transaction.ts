import { supabase } from "../supabase";

export interface Transaction {
  id: string;
  user_id: string;
  wallet_id: string;
  category_id: string;
  amount: number;
  type: "income" | "expense";
  description: string;
  date: string;
  created_at: string;
  updated_at: string;

  // Joined fields
  category?: {
    name: string;
    color: string;
    icon: string;
  };
  wallet?: {
    name: string;
  };
}

export type TransactionCreateInput = Pick<
  Transaction,
  "wallet_id" | "category_id" | "amount" | "type" | "description" | "date"
>;

export type TransactionUpdateInput = Partial<TransactionCreateInput>;

export const TransactionModel = {
  // Get all transactions for current user with category and wallet details
  async getAll(): Promise<Transaction[]> {
    const { data, error } = await supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(name, color, icon),
        wallet:wallets(name)
      `
      )
      .order("date", { ascending: false });

    if (error) {
      console.error("Error fetching transactions:", error);
      throw error;
    }

    return data as Transaction[];
  },

  // Get recent transactions (limited number)
  async getRecent(limit: number = 5): Promise<Transaction[]> {
    const { data, error } = await supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(name, color, icon),
        wallet:wallets(name)
      `
      )
      .order("date", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching recent transactions:", error);
      throw error;
    }

    return data as Transaction[];
  },

  // Get transactions by wallet ID
  async getByWallet(walletId: string): Promise<Transaction[]> {
    const { data, error } = await supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(name, color, icon),
        wallet:wallets(name)
      `
      )
      .eq("wallet_id", walletId)
      .order("date", { ascending: false });

    if (error) {
      console.error("Error fetching wallet transactions:", error);
      throw error;
    }

    return data as Transaction[];
  },

  // Get transactions by category ID
  async getByCategory(categoryId: string): Promise<Transaction[]> {
    const { data, error } = await supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(name, color, icon),
        wallet:wallets(name)
      `
      )
      .eq("category_id", categoryId)
      .order("date", { ascending: false });

    if (error) {
      console.error("Error fetching category transactions:", error);
      throw error;
    }

    return data as Transaction[];
  },

  // Get transaction by ID
  async getById(id: string): Promise<Transaction | null> {
    const { data, error } = await supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(name, color, icon),
        wallet:wallets(name)
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return null;
      }
      console.error("Error fetching transaction:", error);
      throw error;
    }

    return data as Transaction;
  },

  // Create a new transaction
  async create(transactionData: TransactionCreateInput): Promise<Transaction> {
    // Get current user ID
    const { data: sessionData } = await supabase.auth.getSession();
    const user_id = sessionData.session?.user.id;

    if (!user_id) {
      throw new Error("User not logged in");
    }

    const { data, error } = await supabase
      .from("transactions")
      .insert({ ...transactionData, user_id })
      .select()
      .single();

    if (error) {
      console.error("Error creating transaction:", error);
      throw error;
    }

    return data as Transaction;
  },

  // Update a transaction
  async update(
    id: string,
    transactionData: TransactionUpdateInput
  ): Promise<Transaction> {
    const { data, error } = await supabase
      .from("transactions")
      .update(transactionData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating transaction:", error);
      throw error;
    }

    return data as Transaction;
  },

  // Delete a transaction
  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("transactions").delete().eq("id", id);

    if (error) {
      console.error("Error deleting transaction:", error);
      throw error;
    }
  },

  // Get monthly statistics (income and expense)
  async getMonthlyStats(
    month: number,
    year: number
  ): Promise<{ income: number; expense: number }> {
    const startDate = new Date(year, month - 1, 1).toISOString();
    const endDate = new Date(year, month, 0).toISOString();

    const { data, error } = await supabase
      .from("transactions")
      .select("amount, type")
      .gte("date", startDate)
      .lte("date", endDate);

    if (error) {
      console.error("Error fetching monthly stats:", error);
      throw error;
    }

    // Calculate totals
    const result = (data as Transaction[]).reduce(
      (acc, transaction) => {
        if (transaction.amount > 0) {
          acc.income += transaction.amount;
        } else {
          acc.expense += Math.abs(transaction.amount);
        }
        return acc;
      },
      { income: 0, expense: 0 }
    );

    return result;
  },
};
