// lib/models/wallet_position.ts
import { supabase } from "../supabase";

export interface WalletPosition {
  id: string;
  user_id: string;
  wallet_id: string;
  position: number;
  created_at: string;
  updated_at: string;
}

export const WalletPositionModel = {
  // L<PERSON>y tất cả vị trí ví của người dùng hiện tại
  async getAll(): Promise<WalletPosition[]> {
    const { data: sessionData } = await supabase.auth.getSession();
    const user_id = sessionData.session?.user.id;

    if (!user_id) {
      throw new Error("Người dùng chưa đăng nhập");
    }

    const { data, error } = await supabase
      .from("wallet_positions")
      .select("*")
      .eq("user_id", user_id)
      .order("position", { ascending: true });

    if (error) {
      console.error("Error fetching wallet positions:", error);
      throw error;
    }

    return data as WalletPosition[];
  },

  // <PERSON><PERSON><PERSON> vị trí nhiề<PERSON> ví cùng lúc
  async savePositions(walletIds: string[]): Promise<void> {
    const { data: sessionData } = await supabase.auth.getSession();
    const user_id = sessionData.session?.user.id;

    if (!user_id) {
      throw new Error("Người dùng chưa đăng nhập");
    }

    // Xóa tất cả vị trí cũ của user trước khi thêm mới
    const { error: deleteError } = await supabase
      .from("wallet_positions")
      .delete()
      .eq("user_id", user_id);

    if (deleteError) {
      console.error("Error deleting old positions:", deleteError);
      throw deleteError;
    }

    // Tạo các bản ghi vị trí mới
    const positions = walletIds.map((wallet_id, position) => ({
      user_id,
      wallet_id,
      position
    }));

    // Thêm các vị trí mới
    const { error: insertError } = await supabase
      .from("wallet_positions")
      .insert(positions);

    if (insertError) {
      console.error("Error inserting new positions:", insertError);
      throw insertError;
    }
  }
};