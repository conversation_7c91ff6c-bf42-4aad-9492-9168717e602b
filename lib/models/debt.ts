// File: lib/models/debt.ts
// File này xử lý các thao tác database cho tính năng Sổ Nợ
// File này liên quan đến: lib/supabase.ts, app/(more)/debt.tsx, lib/services/NotificationService.ts

import { supabase } from "@/lib/supabase";
import { executeSupabaseQuery } from "@/lib/supabaseUtils";
import { NotificationService, DebtNotificationData } from "@/lib/services/NotificationService";

// Định nghĩa kiểu dữ liệu khoản nợ
export interface Debt {
  id: string;
  user_id: string;
  name: string;
  amount: number;
  remaining: number;
  type: 'payable' | 'receivable'; // phải trả | cho vay
  description?: string;
  due_date?: string; // ngày đáo hạn (ISO string)
  wallet_id?: string;
  contact_info?: string;
  reminder: number; // 0: không, 1: có
  reminder_date?: string; // ngày nhắc nhở (ISO string)
  status: 'active' | 'paid' | 'canceled';
  created_at: string;
  updated_at?: string;
}

// Định nghĩa kiểu dữ liệu giao dịch nợ
export interface DebtTransaction {
  id: string;
  debt_id: string;
  amount: number; // dương: thanh toán, âm: vay thêm
  transaction_date: string;
  note?: string;
  transaction_id?: string; // ID giao dịch liên kết (nếu có)
  created_at: string;
}

// Định nghĩa kiểu dữ liệu để tạo khoản nợ mới
export interface CreateDebtData {
  name: string;
  amount: number;
  type: 'payable' | 'receivable';
  description?: string;
  due_date?: string;
  wallet_id?: string;
  contact_info?: string;
  reminder?: number;
  reminder_date?: string;
}

// Định nghĩa kiểu dữ liệu để cập nhật khoản nợ
export interface UpdateDebtData {
  name?: string;
  amount?: number;
  remaining?: number;
  type?: 'payable' | 'receivable';
  description?: string;
  due_date?: string;
  wallet_id?: string;
  contact_info?: string;
  reminder?: number;
  reminder_date?: string;
  status?: 'active' | 'paid' | 'canceled';
}

// Định nghĩa kiểu dữ liệu để tạo giao dịch nợ
export interface CreateDebtTransactionData {
  debt_id: string;
  amount: number;
  transaction_date?: string;
  note?: string;
  transaction_id?: string;
}

export class DebtModel {
  /**
   * Lấy tất cả khoản nợ của người dùng hiện tại
   */
  static async getAll(): Promise<Debt[]> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .order('created_at', { ascending: false }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải danh sách sổ nợ'
      }
    );

    return result || [];
  }

  /**
   * Lấy thông tin chi tiết một khoản nợ
   */
  static async getById(id: string): Promise<Debt | null> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .eq('id', id)
        .single(),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải thông tin khoản nợ'
      }
    );

    return result;
  }

  /**
   * Tạo khoản nợ mới
   */
  static async create(data: CreateDebtData): Promise<Debt | null> {
    // Lấy user hiện tại
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('Người dùng chưa đăng nhập');
    }

    // Tạo ID unique
    const id = `debt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    const debtData = {
      id,
      user_id: user.id,
      name: data.name,
      amount: data.amount,
      remaining: data.amount, // Ban đầu số tiền còn lại = tổng số tiền
      type: data.type,
      description: data.description || null,
      due_date: data.due_date || null,
      wallet_id: data.wallet_id || null,
      contact_info: data.contact_info || null,
      reminder: data.reminder || 0,
      reminder_date: data.reminder_date || null,
      status: 'active' as const,
      created_at: now,
      updated_at: now,
    };

    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .insert([debtData])
        .select()
        .single(),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tạo khoản nợ mới'
      }
    );

    // Lên lịch thông báo nếu tạo thành công
    if (result) {
      await this.scheduleNotificationsForDebt(result);
    }

    return result;
  }

  /**
   * Cập nhật thông tin khoản nợ
   */
  static async update(id: string, data: UpdateDebtData): Promise<Debt | null> {
    const updateData = {
      ...data,
      updated_at: new Date().toISOString(),
    };

    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .update(updateData)
        .eq('id', id)
        .select()
        .single(),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể cập nhật khoản nợ'
      }
    );

    // Cập nhật thông báo nếu update thành công
    if (result) {
      await this.updateNotificationsForDebt(result);
    }

    return result;
  }

  /**
   * Cập nhật trạng thái khoản nợ
   */
  static async updateStatus(id: string, status: 'active' | 'paid' | 'canceled'): Promise<Debt | null> {
    const result = await this.update(id, { status });
    
    // Hủy thông báo nếu đã trả xong hoặc hủy
    if (result && (status === 'paid' || status === 'canceled')) {
      await NotificationService.cancelDebtNotifications(id);
    }

    return result;
  }

  /**
   * Xóa khoản nợ
   */
  static async delete(id: string): Promise<boolean> {
    // Hủy thông báo trước khi xóa
    await NotificationService.cancelDebtNotifications(id);

    // Trước khi xóa nợ, cần xóa tất cả giao dịch liên quan
    await executeSupabaseQuery(
      () => supabase
        .from('debt_transactions')
        .delete()
        .eq('debt_id', id),
      {
        maxRetries: 2,
        showErrorAlert: false
      }
    );

    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .delete()
        .eq('id', id),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể xóa khoản nợ'
      }
    );

    return result !== null;
  }

  /**
   * Lấy danh sách khoản nợ theo loại
   */
  static async getByType(type: 'payable' | 'receivable'): Promise<Debt[]> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .eq('type', type)
        .order('created_at', { ascending: false }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: `Không thể tải danh sách ${type === 'payable' ? 'nợ phải trả' : 'khoản cho vay'}`
      }
    );

    return result || [];
  }

  /**
   * Lấy danh sách khoản nợ theo trạng thái
   */
  static async getByStatus(status: 'active' | 'paid' | 'canceled'): Promise<Debt[]> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .eq('status', status)
        .order('created_at', { ascending: false }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải danh sách theo trạng thái'
      }
    );

    return result || [];
  }

  /**
   * Lấy danh sách khoản nợ quá hạn
   */
  static async getOverdue(): Promise<Debt[]> {
    const today = new Date().toISOString().split('T')[0];
    
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .eq('status', 'active')
        .not('due_date', 'is', null)
        .lt('due_date', today)
        .order('due_date', { ascending: true }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải danh sách khoản nợ quá hạn'
      }
    );

    return result || [];
  }

  /**
   * Lấy tổng thống kê nợ
   */
  static async getStatistics(): Promise<{
    totalPayable: number;
    totalReceivable: number;
    activePayable: number;
    activeReceivable: number;
    overdueCount: number;
  }> {
    const debts = await this.getAll();
    
    const totalPayable = debts
      .filter(d => d.type === 'payable')
      .reduce((sum, d) => sum + d.amount, 0);
    
    const totalReceivable = debts
      .filter(d => d.type === 'receivable')
      .reduce((sum, d) => sum + d.amount, 0);
    
    const activePayable = debts
      .filter(d => d.type === 'payable' && d.status === 'active')
      .reduce((sum, d) => sum + d.remaining, 0);
    
    const activeReceivable = debts
      .filter(d => d.type === 'receivable' && d.status === 'active')
      .reduce((sum, d) => sum + d.remaining, 0);
    
    const today = new Date();
    const overdueCount = debts.filter(d => 
      d.status === 'active' && 
      d.due_date && 
      new Date(d.due_date) < today
    ).length;

    return {
      totalPayable,
      totalReceivable,
      activePayable,
      activeReceivable,
      overdueCount
    };
  }

  // ================= DEBT TRANSACTIONS =================

  /**
   * Lấy tất cả giao dịch của một khoản nợ
   */
  static async getTransactions(debtId: string): Promise<DebtTransaction[]> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_transactions')
        .select('*')
        .eq('debt_id', debtId)
        .order('transaction_date', { ascending: false }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải lịch sử giao dịch'
      }
    );

    return result || [];
  }

  /**
   * Tạo giao dịch thanh toán nợ
   */
  static async createTransaction(data: CreateDebtTransactionData): Promise<DebtTransaction | null> {
    // Tạo ID unique
    const id = `debt_trans_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date().toISOString();

    const transactionData = {
      id,
      debt_id: data.debt_id,
      amount: data.amount,
      transaction_date: data.transaction_date || now,
      note: data.note || null,
      transaction_id: data.transaction_id || null,
      created_at: now,
    };

    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_transactions')
        .insert([transactionData])
        .select()
        .single(),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tạo giao dịch thanh toán'
      }
    );

    if (result) {
      // Cập nhật số tiền còn lại của khoản nợ
      await this.updateRemainingAmount(data.debt_id);
    }

    return result;
  }

  /**
   * Xóa giao dịch thanh toán
   */
  static async deleteTransaction(id: string, debtId: string): Promise<boolean> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_transactions')
        .delete()
        .eq('id', id),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể xóa giao dịch'
      }
    );

    if (result !== null) {
      // Cập nhật lại số tiền còn lại của khoản nợ
      await this.updateRemainingAmount(debtId);
      return true;
    }

    return false;
  }

  /**
   * Cập nhật số tiền còn lại dựa trên tổng giao dịch
   */
  static async updateRemainingAmount(debtId: string): Promise<void> {
    // Lấy thông tin khoản nợ
    const debt = await this.getById(debtId);
    if (!debt) return;

    // Lấy tất cả giao dịch
    const transactions = await this.getTransactions(debtId);
    
    // Tính tổng đã thanh toán
    const totalPaid = transactions.reduce((sum, trans) => sum + trans.amount, 0);
    
    // Cập nhật số tiền còn lại
    const remaining = Math.max(0, debt.amount - totalPaid);
    
    await this.update(debtId, { remaining });

    // Nếu đã trả hết, tự động cập nhật trạng thái
    if (remaining === 0 && debt.status === 'active') {
      await this.updateStatus(debtId, 'paid');
    }
  }

  /**
   * Thanh toán một phần hoặc toàn bộ khoản nợ
   */
  static async makePayment(
    debtId: string, 
    amount: number, 
    note?: string,
    transactionId?: string
  ): Promise<DebtTransaction | null> {
    return this.createTransaction({
      debt_id: debtId,
      amount,
      note,
      transaction_id: transactionId
    });
  }

  /**
   * Lấy danh sách khoản nợ sắp đến hạn (trong vòng 7 ngày)
   */
  static async getUpcoming(days: number = 7): Promise<Debt[]> {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + days);
    
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .eq('status', 'active')
        .not('due_date', 'is', null)
        .gte('due_date', today.toISOString().split('T')[0])
        .lte('due_date', futureDate.toISOString().split('T')[0])
        .order('due_date', { ascending: true }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tải danh sách khoản nợ sắp đến hạn'
      }
    );

    return result || [];
  }

  /**
   * Tìm kiếm khoản nợ theo tên hoặc thông tin liên hệ
   */
  static async search(query: string): Promise<Debt[]> {
    const result = await executeSupabaseQuery(
      () => supabase
        .from('debt_debts')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%,contact_info.ilike.%${query}%`)
        .order('created_at', { ascending: false }),
      {
        maxRetries: 3,
        showErrorAlert: true,
        errorMessage: 'Không thể tìm kiếm khoản nợ'
      }
    );

    return result || [];
  }

  // ================= NOTIFICATION HELPERS =================

  /**
   * Lên lịch thông báo cho khoản nợ mới
   */
  private static async scheduleNotificationsForDebt(debt: Debt): Promise<void> {
    try {
      if (debt.status !== 'active') return;

      const notificationData: DebtNotificationData = {
        debtId: debt.id,
        debtName: debt.name,
        amount: debt.remaining,
        type: debt.type,
        dueDate: debt.due_date || '',
      };

      // Lên lịch thông báo nhắc nhở
      if (debt.reminder === 1 && debt.reminder_date) {
        const reminderDate = new Date(debt.reminder_date);
        reminderDate.setHours(9, 0, 0, 0); // 9h sáng

        await NotificationService.scheduleDebtReminder(
          `debt_reminder_${debt.id}`,
          reminderDate,
          notificationData
        );
      }

      // Lên lịch thông báo quá hạn
      if (debt.due_date) {
        const dueDate = new Date(debt.due_date);
        await NotificationService.scheduleOverdueNotification(
          `debt_reminder_${debt.id}`,
          dueDate,
          notificationData
        );
      }
    } catch (error) {
      console.error('Lỗi lên lịch thông báo cho nợ:', error);
    }
  }

  /**
   * Cập nhật thông báo khi thay đổi thông tin nợ
   */
  private static async updateNotificationsForDebt(debt: Debt): Promise<void> {
    try {
      if (debt.status !== 'active') {
        // Hủy thông báo nếu không còn active
        await NotificationService.cancelDebtNotifications(debt.id);
        return;
      }

      const notificationData: DebtNotificationData = {
        debtId: debt.id,
        debtName: debt.name,
        amount: debt.remaining,
        type: debt.type,
        dueDate: debt.due_date || '',
      };

      const reminderDate = debt.reminder === 1 && debt.reminder_date 
        ? new Date(debt.reminder_date) 
        : undefined;
      
      const dueDate = debt.due_date ? new Date(debt.due_date) : undefined;

      if (reminderDate) {
        reminderDate.setHours(9, 0, 0, 0);
      }

      await NotificationService.updateDebtNotifications(
        debt.id,
        reminderDate,
        dueDate,
        notificationData
      );
    } catch (error) {
      console.error('Lỗi cập nhật thông báo cho nợ:', error);
    }
  }

  /**
   * Kiểm tra và gửi thông báo cho các khoản nợ sắp đến hạn
   */
  static async checkAndSendDueNotifications(): Promise<void> {
    try {
      const upcomingDebts = await this.getUpcoming(1); // Nợ đến hạn trong 24h
      
      if (upcomingDebts.length > 0) {
        await NotificationService.sendImmediateNotification(
          '⏰ Nhắc nhở thanh toán',
          `Bạn có ${upcomingDebts.length} khoản nợ sắp đến hạn trong 24h tới`,
          { type: 'upcoming-debts', count: upcomingDebts.length }
        );
      }
    } catch (error) {
      console.error('Lỗi kiểm tra thông báo đến hạn:', error);
    }
  }
}