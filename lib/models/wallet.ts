import { supabase } from "../supabase";

export interface Wallet {
  id: string;
  user_id: string;
  name: string;
  balance: number;
  type: "cash" | "bank" | "ewallet";
  icon: string;
  color: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export type WalletCreateInput = Pick<
  Wallet,
  "name" | "balance" | "type" | "color" | "icon"
> & {
  is_default?: boolean;
};

export type WalletUpdateInput = Partial<WalletCreateInput>;

export const WalletModel = {
  // Lấy tất cả ví của người dùng hiện tại
  async getAll(): Promise<Wallet[]> {
    const { data, error } = await supabase
      .from("wallets")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching wallets:", error);
      throw error;
    }

    return data as Wallet[];
  },

  // Lấy ví theo ID
  async getById(id: string): Promise<Wallet | null> {
    const { data, error } = await supabase
      .from("wallets")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // PGRST116 là mã lỗi khi không tìm thấy kết quả
        return null;
      }
      console.error("Error fetching wallet:", error);
      throw error;
    }

    return data as Wallet;
  },

  // Tạo ví mới
  async create(walletData: WalletCreateInput): Promise<Wallet> {
    // Nếu là ví đầu tiên, đặt mặc định là true
    const existingWallets = await this.getAll();
    if (existingWallets.length === 0) {
      walletData.is_default = true;
    }

    // Lấy session hiện tại để có user_id
    const { data: sessionData } = await supabase.auth.getSession();
    const user_id = sessionData.session?.user.id;

    if (!user_id) {
      throw new Error("Người dùng chưa đăng nhập");
    }

    const { data, error } = await supabase
      .from("wallets")
      .insert({ ...walletData, user_id })
      .select()
      .single();

    if (error) {
      console.error("Error creating wallet:", error);
      throw error;
    }

    // Nếu đặt ví này là mặc định, hãy bỏ mặc định các ví khác
    if (walletData.is_default) {
      await this.updateDefaultWallet(data.id);
    }

    return data as Wallet;
  },

  // Cập nhật ví
  async update(id: string, walletData: WalletUpdateInput): Promise<Wallet> {
    const { data, error } = await supabase
      .from("wallets")
      .update(walletData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating wallet:", error);
      throw error;
    }

    // Nếu đặt ví này là mặc định, hãy bỏ mặc định các ví khác
    if (walletData.is_default) {
      await this.updateDefaultWallet(id);
    }

    return data as Wallet;
  },

  // Xóa ví
  async delete(id: string): Promise<void> {
    const wallet = await this.getById(id);

    // Kiểm tra xem ví có phải là mặc định không
    const isDefault = wallet?.is_default;

    const { error } = await supabase.from("wallets").delete().eq("id", id);

    if (error) {
      console.error("Error deleting wallet:", error);
      throw error;
    }

    // Nếu ví vừa xóa là mặc định, chọn một ví khác làm mặc định
    if (isDefault) {
      const wallets = await this.getAll();
      if (wallets.length > 0) {
        await this.update(wallets[0].id, { is_default: true });
      }
    }
  },

  // Đặt ví mặc định (và bỏ mặc định các ví khác)
  async updateDefaultWallet(id: string): Promise<void> {
    // Bỏ mặc định tất cả ví khác
    const { error } = await supabase
      .from("wallets")
      .update({ is_default: false })
      .neq("id", id);

    if (error) {
      console.error("Error updating default wallets:", error);
      throw error;
    }
  },

  // Lấy ví mặc định
  async getDefault(): Promise<Wallet | null> {
    const { data, error } = await supabase
      .from("wallets")
      .select("*")
      .eq("is_default", true)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // Không tìm thấy ví mặc định
        return null;
      }
      console.error("Error fetching default wallet:", error);
      throw error;
    }

    return data as Wallet;
  },
};
