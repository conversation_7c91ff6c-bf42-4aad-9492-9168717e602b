import { supabase } from "../supabase";

export interface Category {
  id: string;
  user_id: string;
  name: string;
  type: "income" | "expense";
  icon: string;
  color: string;
  created_at: string;
  updated_at: string;
}

export type CategoryCreateInput = Pick<
  Category,
  "name" | "type" | "icon" | "color"
>;

export type CategoryUpdateInput = Partial<CategoryCreateInput>;

export const CategoryModel = {
  // Lấy tất cả danh mục của người dùng hiện tại
  async getAll(): Promise<Category[]> {
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("type", { ascending: true })
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }

    return data as Category[];
  },

  // L<PERSON>y danh mục theo loại (thu nhập hoặc chi tiêu)
  async getByType(type: "income" | "expense"): Promise<Category[]> {
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("type", type)
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching categories by type:", error);
      throw error;
    }

    return data as Category[];
  },

  // Lấy danh mục theo ID
  async getById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // PGRST116 là mã lỗi khi không tìm thấy kết quả
        return null;
      }
      console.error("Error fetching category:", error);
      throw error;
    }

    return data as Category;
  },

  // Tạo danh mục mới
  async create(categoryData: CategoryCreateInput): Promise<Category> {
    // Gọi function insert_category với quyền SECURITY DEFINER
    const { data, error } = await supabase.rpc("insert_category", {
      p_name: categoryData.name,
      p_icon: categoryData.icon,
      p_color: categoryData.color,
      p_type: categoryData.type,
    });

    if (error) {
      console.error("Error creating category:", error);
      throw error;
    }

    // Lấy category vừa được tạo
    const { data: newCategory, error: fetchError } = await supabase
      .from("categories")
      .select("*")
      .eq("id", data)
      .single();

    if (fetchError) {
      console.error("Error fetching created category:", fetchError);
      throw fetchError;
    }

    return newCategory as Category;
  },

  // Cập nhật danh mục
  async update(
    id: string,
    categoryData: CategoryUpdateInput
  ): Promise<Category> {
    const { data, error } = await supabase
      .from("categories")
      .update(categoryData)
      .eq("id", id)
      .select()
      .single();

    if (error) {
      console.error("Error updating category:", error);
      throw error;
    }

    return data as Category;
  },

  // Xóa danh mục
  async delete(id: string): Promise<void> {
    const { error } = await supabase.from("categories").delete().eq("id", id);

    if (error) {
      console.error("Error deleting category:", error);
      throw error;
    }
  },

  // Tạo nhiều danh mục cùng lúc
  async createMany(categoriesData: CategoryCreateInput[]): Promise<Category[]> {
    if (categoriesData.length === 0) return [];

    // Xóa tất cả danh mục hiện tại
    await this.deleteAll();

    // Thêm từng danh mục một sử dụng hàm insert_category
    const results: Category[] = [];

    for (const category of categoriesData) {
      try {
        const { data, error } = await supabase.rpc("insert_category", {
          p_name: category.name,
          p_icon: category.icon,
          p_color: category.color,
          p_type: category.type,
        });

        if (error) {
          console.error("Error adding category:", category.name, error);
          throw error;
        }

        // Lấy category vừa được tạo
        const { data: newCategory, error: fetchError } = await supabase
          .from("categories")
          .select("*")
          .eq("id", data)
          .single();

        if (fetchError) {
          console.error("Error fetching created category:", fetchError);
          continue;
        }

        results.push(newCategory as Category);
      } catch (err) {
        console.error("Error creating category:", category.name, err);
      }
    }

    return results;
  },

  // Xóa tất cả danh mục của người dùng hiện tại
  async deleteAll(): Promise<void> {
    // Gọi function delete_all_categories
    const { error } = await supabase.rpc("delete_all_categories");

    if (error) {
      console.error("Error deleting all categories:", error);
      throw error;
    }
  },
};
