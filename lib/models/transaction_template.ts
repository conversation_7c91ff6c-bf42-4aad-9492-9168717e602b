// File: lib/models/transaction_template.ts
// Những file liên quan đến file này: AsyncStorage của react-native

import AsyncStorage from "@react-native-async-storage/async-storage";

// <PERSON><PERSON><PERSON> lưu trữ trong AsyncStorage
const STORAGE_KEY = "moneyup_transaction_templates";

// Định nghĩa interface cho mẫu giao dịch
export interface TransactionTemplate {
  id: string;
  content: string;
  created_at: string;
}

// Model quản lý mẫu giao dịch sử dụng AsyncStorage
const TransactionTemplateModel = {
  // Lấy tất cả mẫu giao dịch
  async getAll() {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEY);
      if (jsonValue) {
        return JSON.parse(jsonValue);
      }
      return [];
    } catch (error) {
      console.error("Error loading templates from storage:", error);
      return [];
    }
  },

  // Tạo mẫu giao dịch mới
  async create(templateData) {
    try {
      // <PERSON><PERSON>y danh sách hiện tại
      const templates = await this.getAll();
      
      // Tạo mẫu mới với ID và timestamp
      const newTemplate = {
        id: Date.now().toString(),
        content: templateData.content,
        created_at: new Date().toISOString(),
      };
      
      // Thêm vào danh sách
      templates.unshift(newTemplate);
      
      // Lưu lại vào AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(templates));
      
      return newTemplate;
    } catch (error) {
      console.error("Error creating template:", error);
      throw error;
    }
  },

  // Cập nhật mẫu giao dịch
  async update(id, templateData) {
    try {
      // Lấy danh sách hiện tại
      const templates = await this.getAll();
      
      // Tìm vị trí của mẫu cần cập nhật
      const index = templates.findIndex(template => template.id === id);
      
      if (index !== -1) {
        // Cập nhật mẫu
        templates[index] = {
          ...templates[index],
          content: templateData.content,
        };
        
        // Lưu lại vào AsyncStorage
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(templates));
        
        return templates[index];
      }
      
      throw new Error("Template not found");
    } catch (error) {
      console.error("Error updating template:", error);
      throw error;
    }
  },

  // Xoá mẫu giao dịch
  async delete(id) {
    try {
      // Lấy danh sách hiện tại
      const templates = await this.getAll();
      
      // Lọc bỏ mẫu cần xoá
      const updatedTemplates = templates.filter(template => template.id !== id);
      
      // Lưu lại vào AsyncStorage
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updatedTemplates));
    } catch (error) {
      console.error("Error deleting template:", error);
      throw error;
    }
  }
};

export { TransactionTemplateModel };
export default TransactionTemplateModel;