import * as FileSystem from "expo-file-system";
import { supabase } from "../supabase";
import { supabaseAdmin } from "../supabaseAdmin";

export interface UserProfile {
  id: string;
  full_name: string;
  avatar_url?: string;
  phone?: string;
  email?: string;
  created_at: string;
  updated_at: string;
}

export class UserModel {
  /**
   * Get the current user's profile, creating it if it doesn't exist
   */
  static async getCurrentProfile(): Promise<UserProfile | null> {
    try {
      const { data: user } = await supabase.auth.getUser();

      if (!user.user) return null;

      // Try to get existing profile
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.user.id)
        .single();

      // If profile exists, return it with email
      if (data) {
        return {
          ...data,
          email: user.user.email,
        } as UserProfile;
      }

      // If no profile exists, create one
      if (error && error.code === "PGRST116") {
        console.log(
          "No profile found, creating new profile for user",
          user.user.id
        );

        // Get user metadata for name if available
        const userMeta = user.user.user_metadata;
        const fullName = userMeta?.full_name || userMeta?.name || "User";

        // Create new profile
        const { data: newProfile, error: createError } = await supabase
          .from("profiles")
          .insert([
            {
              id: user.user.id,
              full_name: fullName,
            },
          ])
          .select()
          .single();

        if (createError) throw createError;

        return {
          ...newProfile,
          email: user.user.email,
        } as UserProfile;
      }

      // If there was a different error, throw it
      if (error) throw error;

      return null;
    } catch (error) {
      console.error("Error getting current profile:", error);
      return null;
    }
  }

  /**
   * Update user profile information
   */
  static async updateProfile(
    profile: Partial<UserProfile>
  ): Promise<UserProfile | null> {
    try {
      const { data: user } = await supabase.auth.getUser();

      if (!user.user) throw new Error("No authenticated user");

      // Make sure we don't try to update email field in profiles table
      const { email, ...profileData } = profile;

      const { data, error } = await supabase
        .from("profiles")
        .update(profileData)
        .eq("id", user.user.id)
        .select()
        .single();

      if (error) throw error;

      return {
        ...data,
        email: user.user.email,
      } as UserProfile;
    } catch (error) {
      console.error("Error updating profile:", error);
      return null;
    }
  }

  /**
   * Upload avatar image to Supabase storage
   */
  static async uploadAvatar(uri: string): Promise<string | null> {
    try {
      const { data: user } = await supabase.auth.getUser();

      if (!user.user) return null;

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error("File does not exist");
      }

      // Generate a unique filename without user ID in the path
      const fileExt = uri.split(".").pop(); // Get file extension
      const fileName = `avatar_${Date.now()}.${fileExt || "jpg"}`;
      const filePath = `avatars/${fileName}`;

      // Convert image URI to base64
      const fileBase64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Upload to Supabase (using base64)
      const { error: uploadError } = await supabase.storage
        .from("profiles")
        .upload(filePath, decode(fileBase64), {
          contentType: `image/${fileExt || "jpeg"}`,
          upsert: true,
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data } = supabase.storage.from("profiles").getPublicUrl(filePath);

      if (!data.publicUrl) {
        throw new Error("Failed to get public URL");
      }

      // Make sure we perform a direct update to ensure avatar_url is saved
      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: data.publicUrl })
        .eq("id", user.user.id);

      if (updateError) {
        console.error("Error updating avatar_url in profile:", updateError);
        throw updateError;
      }

      // Fetch the updated profile
      const updatedProfile = await UserModel.getCurrentProfile();
      return data.publicUrl;
    } catch (error) {
      console.error("Error uploading avatar:", error);
      return null;
    }
  }

  /**
   * Upload avatar image directly from base64 data to Supabase storage
   */
  static async uploadAvatarBase64(base64Data: string): Promise<string | null> {
    try {
      const { data: user } = await supabase.auth.getUser();

      if (!user.user) return null;

      // Generate a unique filename without user ID in the path
      const fileName = `avatar_${Date.now()}.jpg`;
      const filePath = `avatars/${fileName}`;

      // Upload to Supabase (using base64)
      const { error: uploadError } = await supabase.storage
        .from("profiles")
        .upload(filePath, decode(base64Data), {
          contentType: "image/jpeg",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data } = supabase.storage.from("profiles").getPublicUrl(filePath);

      if (!data.publicUrl) {
        throw new Error("Failed to get public URL");
      }

      // Make sure we perform a direct update to ensure avatar_url is saved
      const { error: updateError } = await supabase
        .from("profiles")
        .update({ avatar_url: data.publicUrl })
        .eq("id", user.user.id);

      if (updateError) {
        console.error("Error updating avatar_url in profile:", updateError);
        throw updateError;
      }

      return data.publicUrl;
    } catch (error) {
      console.error("Error uploading avatar from base64:", error);
      return null;
    }
  }

  /**
   * Delete the current user account directly
   * NOTE: This approach is for development/testing only and requires setting
   * the service role key in the supabase client
   */
  static async deleteAccount(): Promise<boolean> {
    try {
      // Get the current user
      const { data: user } = await supabase.auth.getUser();

      if (!user.user) return false;

      const userId = user.user.id;

      // First, delete user data to maintain referential integrity
      // Delete the user's transactions
      const { error: transactionsError } = await supabase
        .from("transactions")
        .delete()
        .eq("user_id", userId);

      if (transactionsError) {
        console.error("Error deleting transactions:", transactionsError);
        // Continue with deletion even if transaction removal fails
      }

      // Delete the user's wallets
      const { error: walletsError } = await supabase
        .from("wallets")
        .delete()
        .eq("user_id", userId);

      if (walletsError) {
        console.error("Error deleting wallets:", walletsError);
        // Continue with deletion even if wallet removal fails
      }

      // Delete the user's profile
      const { error: profileError } = await supabase
        .from("profiles")
        .delete()
        .eq("id", userId);

      if (profileError) {
        console.error("Error deleting profile:", profileError);
        // Continue with deletion even if profile removal fails
      }

      // Finally, delete the user account itself using admin client
      const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(
        userId
      );

      if (deleteError) {
        console.error("Error deleting user account:", deleteError);
        throw deleteError;
      }

      return true;
    } catch (error) {
      console.error("Error deleting account:", error);
      return false;
    }
  }
}

// Helper function to convert base64 to Uint8Array
function decode(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

// Helper function for browsers that might not have atob
function atob(base64: string): string {
  if (typeof window !== "undefined" && window.atob) {
    return window.atob(base64);
  }

  // If we're not in a browser or window.atob is not available
  const buffer = Buffer.from(base64, "base64");
  return buffer.toString("binary");
}
