import { createClient } from "@supabase/supabase-js";
import "react-native-url-polyfill/auto";

// Thay thế các giá trị này bằng URL và service role key của dự án Supabase của bạn
const supabaseUrl = "https://wziiirvfbfjruflqyyaa.supabase.co";

// CẢNH BÁO: CHỈ SỬ DỤNG SERVICE ROLE KEY TRONG MÔI TRƯỜNG PHÁT TRIỂN
// KHÔNG BAO GIỜ đưa service role key vào ứng dụng production - chỉ sử dụng trên server
const supabaseServiceKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6aWlpcnZmYmZqcnVmbHF5eWFhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTcyNywiZXhwIjoyMDYyNjM1NzI3fQ.nuhqqjfgVX12XTqTOs9Qau5kWPSvQXue981ZyStvNrM";

// Tạo client admin với service role key
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
