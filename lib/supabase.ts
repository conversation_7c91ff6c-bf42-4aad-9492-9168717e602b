// File này ở lib/supabase.ts
// Những file liên quan: AuthContext.tsx, register.tsx, login.tsx

import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import "react-native-url-polyfill/auto";

// Thay thế các giá trị này bằng URL và khóa của dự án Supabase của bạn
// Bạn có thể lấy các giá trị này từ phần "Project Settings" trên Supabase dashboard
const supabaseUrl = "https://wziiirvfbfjruflqyyaa.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6aWlpcnZmYmZqcnVmbHF5eWFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk3MjcsImV4cCI6MjA2MjYzNTcyN30.vuXfr85VKgDbMGo9PAbRswzmRK3rhzzMAwKK6fToOJg";

// AsyncStorage adapter for Supabase auth
const AsyncStorageAdapter = {
  getItem: (key: string) => {
    return AsyncStorage.getItem(key);
  },
  setItem: (key: string, value: string) => {
    return AsyncStorage.setItem(key, value);
  },
  removeItem: (key: string) => {
    return AsyncStorage.removeItem(key);
  },
};

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorageAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Khởi tạo một biến để theo dõi listeners
export let activeAuthListeners: Array<{ subscription: { unsubscribe: () => void } }> = [];

// Hàm đăng ký listener an toàn - sử dụng khi cần lắng nghe sự kiện auth
export const safeAuthListener = (callback: (event: string, session: any) => void) => {
  // Xóa tất cả listeners hiện tại trước khi tạo mới
  cleanupAuthListeners();
  
  // Tạo listener mới
  const { data } = supabase.auth.onAuthStateChange(callback);
  
  // Lưu trữ reference để có thể dọn dẹp sau này
  activeAuthListeners.push(data);
  
  return data;
};

// Hàm xóa tất cả listeners để tránh memory leaks
export const cleanupAuthListeners = () => {
  activeAuthListeners.forEach(listener => {
    try {
      if (listener && listener.subscription && listener.subscription.unsubscribe) {
        listener.subscription.unsubscribe();
      }
    } catch (error) {
      console.log('[Supabase] Error unsubscribing listener:', error);
    }
  });
  
  // Reset mảng
  activeAuthListeners = [];
};