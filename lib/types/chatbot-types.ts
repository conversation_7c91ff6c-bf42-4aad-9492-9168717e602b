// File: lib/types/chatbot-types.ts
// Những file liên quan đến file này: 
// 1. app/(tabs)/chatbot.tsx
// 2. lib/services/ai-service.ts
// 3. lib/services/chatbot-actions.ts
// 4. lib/helpers/chat-helpers.ts

// Import các types cần thiết từ các model
import { Transaction } from "@/lib/models/transaction";
import { Wallet } from "@/lib/models/wallet";
import { Category } from "@/lib/models/category";

// Type cho tin nhắn trong chatbot
export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
  data?: any; // Cho transactions, categories, wallets data
  buttons?: ChatButton[];
  transaction_id?: string; // Thêm transaction_id để có thể xóa giao dịch
  showDeleteButton?: boolean; // Hiển thị nút xóa giao dịch
}

// Type cho nút trong tin nhắn
export interface ChatButton {
  text: string;
  onPress: () => void;
  type: "confirm" | "cancel" | "neutral";
}

// Type cho kết quả phân tích của AI
export interface AIAnalysisResult {
  action: ChatbotAction;
  data: any;
  confidence: number;
}

// Các hành động mà chatbot có thể thực hiện
export type ChatbotAction = 
  | "create_transaction" 
  | "update_transaction"
  | "create_wallet"
  | "update_wallet"
  | "view_wallets"
  | "view_transactions"
  | "view_statistics"
  | "view_categories"
  | "transfer"
  | "confirm"
  | "unknown"
  | "api_key_missing"
  | "error";

// Type cho yêu cầu chuyển tiền
export interface TransferRequest {
  isTransfer: boolean;
  isComplete: boolean;
  amount: number;
  sourceWalletName: string;
  destWalletName: string;
  sourceWalletId: string | null;
  destWalletId: string | null;
  description?: string;
  message?: string;
}

// Type cho dữ liệu giao dịch mới
export interface TransactionData {
  description: string;
  amount: number;
  type: "income" | "expense";
  date: string;
  wallet_id?: string;
  category_id?: string;
  wallet_keyword?: string;
  category_keyword?: string;
}

// Type cho dữ liệu ví mới
export interface WalletData {
  name: string;
  balance: number;
  type: "cash" | "bank" | "ewallet";
  icon?: string;
  color?: string;
  is_default?: boolean;
}

// Type cho kết quả xử lý giao dịch
export interface TransactionResult {
  message: string;
  transactionId: string | null;
}

// Type cho dữ liệu người xưng hô
export interface UserPronoun {
  calling: string;
  selfCalling: string;
}

// Type cho thống kê hàng tháng
export interface MonthlyStats {
  income: number;
  expense: number;
}