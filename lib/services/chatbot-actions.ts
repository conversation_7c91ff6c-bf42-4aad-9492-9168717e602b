// File: lib/services/chatbot-actions.ts
// Những file liên quan đến file này: 
// 1. app/(tabs)/chatbot.tsx
// 2. lib/services/ai-service.ts
// 3. lib/helpers/chat-helpers.ts
// 4. lib/types/chatbot-types.ts
// 5. lib/models/transaction.ts
// 6. lib/models/wallet.ts
// 7. lib/models/category.ts
// 8. lib/services/ai-learning-service.ts (MỚI)

import { CategoryModel } from "@/lib/models/category";
import { WalletModel } from "@/lib/models/wallet";
import { TransactionModel } from "@/lib/models/transaction";
import { 
  TransactionResult, 
  TransferRequest,
  TransactionData,
  WalletData,
  MonthlyStats
} from "@/lib/types/chatbot-types";
import { 
  findCategoryByDescription, 
  findWalletByName, 
  formatCurrency,
  getFullDateTime,
  createSampleData,
  ensureDefaultCategories
} from "@/lib/helpers/chat-helpers";
import { AILearningService } from "@/lib/services/ai-learning-service";

// Xử lý chuyển tiền giữa các ví
export const handleTransferMoney = async (
  transferData: TransferRequest,
  botPronoun: string = "tôi" // Mặc định cách xưng hô nếu không được truyền vào
): Promise<string> => {
  try {
    // Tạo hai giao dịch: chi từ ví nguồn và thu vào ví đích
    const currentDate = getFullDateTime();
    const sourceDescription =
      transferData.description ||
      `Chuyển tiền sang ${transferData.destWalletName}`;
    const destDescription =
      transferData.description ||
      `Nhận tiền từ ${transferData.sourceWalletName}`;

    // Kiểm tra ví nguồn tồn tại
    const sourceWallet = await WalletModel.getById(
      transferData.sourceWalletId!
    );
    if (!sourceWallet) {
      return `Không tìm thấy ví nguồn "${transferData.sourceWalletName}".`;
    }

    // Kiểm tra ví đích tồn tại
    const destWallet = await WalletModel.getById(transferData.destWalletId!);
    if (!destWallet) {
      return `Không tìm thấy ví đích "${transferData.destWalletName}".`;
    }

    // Kiểm tra số dư
    if (sourceWallet.balance < transferData.amount) {
      return `Ví "${
        sourceWallet.name
      }" không đủ số dư để chuyển ${formatCurrency(transferData.amount)}.`;
    }

    // Tính toán số dư mới
    const newSourceBalance = sourceWallet.balance - transferData.amount;
    const newDestBalance = destWallet.balance + transferData.amount;

    // 1. Tạo giao dịch chi từ ví nguồn
    await TransactionModel.create({
      wallet_id: transferData.sourceWalletId!,
      amount: -Math.abs(transferData.amount), // Số âm để thể hiện chi tiền
      type: "expense",
      description: sourceDescription,
      date: currentDate,
      category_id: null, // Không cần danh mục cho giao dịch chuyển tiền
    });

    // 2. Cập nhật số dư ví nguồn trực tiếp
    await WalletModel.update(transferData.sourceWalletId!, {
      balance: newSourceBalance,
    });

    // 3. Tạo giao dịch thu vào ví đích
    await TransactionModel.create({
      wallet_id: transferData.destWalletId!,
      amount: Math.abs(transferData.amount), // Số dương để thể hiện thu tiền
      type: "income",
      description: destDescription,
      date: currentDate,
      category_id: null, // Không cần danh mục cho giao dịch chuyển tiền
    });

    // 4. Cập nhật số dư ví đích trực tiếp
    await WalletModel.update(transferData.destWalletId!, {
      balance: newDestBalance,
    });

    return `${botPronoun} đã chuyển ${formatCurrency(
      transferData.amount
    )} từ ví "${sourceWallet.name}" sang ví "${destWallet.name}" thành công!`;
  } catch (error) {
    console.error("Lỗi khi chuyển tiền:", error);
    return `Không thể thực hiện chuyển tiền. Vui lòng thử lại sau.`;
  }
};

// Xử lý tạo ví mới
export const handleCreateWallet = async (
  walletData: WalletData,
  botPronoun: string = "tôi" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    // Tạo ví mới
    const newWallet = await WalletModel.create({
      name: walletData.name,
      balance: walletData.balance,
      type: walletData.type || "bank",
      icon: walletData.icon || "wallet-outline",
      color: walletData.color || "#3498db",
      is_default: walletData.is_default,
    });

    return `${botPronoun} đã tạo ví "${newWallet.name}" với số dư ${formatCurrency(
      newWallet.balance
    )}.`;
  } catch (error) {
    console.error("Lỗi khi tạo ví:", error);
    return `Không thể tạo ví. Vui lòng thử lại sau.`;
  }
};

// Xử lý cập nhật ví
export const handleUpdateWallet = async (
  walletId: string,
  walletData: Partial<WalletData>,
  botPronoun: string = "tôi" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    // Cập nhật ví
    const updatedWallet = await WalletModel.update(walletId, walletData);

    return `${botPronoun} đã cập nhật ví "${
      updatedWallet.name
    }" với số dư mới là ${formatCurrency(updatedWallet.balance)}.`;
  } catch (error) {
    console.error("Lỗi khi cập nhật ví:", error);
    return `Không thể cập nhật ví. Vui lòng thử lại sau.`;
  }
};

// Xử lý xem danh sách ví
export const handleViewWallets = async (
  userPronoun: string = "bạn" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    const wallets = await WalletModel.getAll();
    if (wallets.length === 0) {
      return `${userPronoun} chưa có ví nào. Hãy tạo ví đầu tiên!`;
    } else {
      let totalBalance = 0;
      let response = `Các ví của ${userPronoun}:\n\n`;

      wallets.forEach((wallet, index) => {
        totalBalance += wallet.balance;
        response += `${index + 1}. ${wallet.name}: ${formatCurrency(
          wallet.balance
        )}${wallet.is_default ? " (Mặc định)" : ""}\n`;
      });

      response += `\nTổng số dư: ${formatCurrency(totalBalance)}`;
      return response;
    }
  } catch (error) {
    console.error("Lỗi khi lấy danh sách ví:", error);
    return `Không thể lấy danh sách ví. Vui lòng thử lại sau.`;
  }
};

// Hàm hỗ trợ lấy icon phù hợp cho danh mục
const getCategoryIcon = (categoryName: string): string => {
  const lowerName = categoryName.toLowerCase();
  
  if (lowerName.includes("y tế")) return "medical-outline";
  if (lowerName.includes("sinh hoạt")) return "home-outline";
  if (lowerName.includes("mua sắm")) return "bag-outline";
  if (lowerName.includes("di chuyển")) return "car-outline";
  if (lowerName.includes("ăn uống")) return "restaurant-outline";
  if (lowerName.includes("giáo dục")) return "school-outline";
  if (lowerName.includes("giải trí")) return "game-controller-outline";
  
  return "cart-outline"; // Mặc định
};

// Hàm hỗ trợ lấy màu phù hợp cho danh mục
const getCategoryColor = (categoryName: string): string => {
  const lowerName = categoryName.toLowerCase();
  
  if (lowerName.includes("y tế")) return "#9b59b6";
  if (lowerName.includes("sinh hoạt")) return "#f39c12";
  if (lowerName.includes("mua sắm")) return "#16a085";
  if (lowerName.includes("di chuyển")) return "#3498db";
  if (lowerName.includes("ăn uống")) return "#e74c3c";
  if (lowerName.includes("giáo dục")) return "#27ae60";
  if (lowerName.includes("giải trí")) return "#8e44ad";
  
  return "#607d8b"; // Mặc định màu xám
};

// Đảm bảo các danh mục cần thiết tồn tại
const ensureAllCategoriesExist = async (): Promise<any[]> => {
  try {
    // Lấy các danh mục hiện có
    const expenseCategories = await CategoryModel.getByType("expense");
    
    // Định nghĩa các danh mục cần thiết
    const requiredCategories = [
      { name: "Y tế", icon: "medical-outline", color: "#9b59b6" },
      { name: "Sinh hoạt", icon: "home-outline", color: "#f39c12" },
      { name: "Mua sắm", icon: "bag-outline", color: "#16a085" },
      { name: "Di chuyển", icon: "car-outline", color: "#3498db" },
      { name: "Ăn uống", icon: "restaurant-outline", color: "#e74c3c" },
      { name: "Giáo dục", icon: "school-outline", color: "#27ae60" },
      { name: "Giải trí", icon: "game-controller-outline", color: "#8e44ad" },
      { name: "Chi tiêu khác", icon: "cart-outline", color: "#607d8b" }
    ];
    
    // Tạo các danh mục nếu chưa tồn tại
    for (const category of requiredCategories) {
      const exists = expenseCategories.some(
        cat => cat.name.toLowerCase() === category.name.toLowerCase()
      );
      
      if (!exists) {
        await CategoryModel.create({
          name: category.name,
          type: "expense",
          icon: category.icon,
          color: category.color
        });
        console.log(`Created missing category: ${category.name}`);
      }
    }
    
    // Lấy lại danh sách đã cập nhật
    return await CategoryModel.getByType("expense");
  } catch (error) {
    console.error("Error ensuring categories exist:", error);
    return [];
  }
};

// Tạo danh mục và lấy ID (hoặc tìm danh mục nếu đã tồn tại)
const getCategoryId = async (categoryName: string): Promise<string | null> => {
  try {
    const categories = await CategoryModel.getByType("expense");
    
    // Tìm danh mục phù hợp
    const category = categories.find(
      cat => cat.name.toLowerCase() === categoryName.toLowerCase()
    );
    
    if (category) {
      return category.id;
    }
    
    // Nếu không tìm thấy, tạo mới
    const newCategory = await CategoryModel.create({
      name: categoryName,
      type: "expense",
      icon: getCategoryIcon(categoryName),
      color: getCategoryColor(categoryName)
    });
    
    return newCategory.id;
  } catch (error) {
    console.error(`Error getting category ID for ${categoryName}:`, error);
    return null;
  }
};

// Xử lý tạo giao dịch mới - CẬP NHẬT ĐỂ SỬ DỤNG AI LEARNING
export const handleCreateTransaction = async (
  transactionData: TransactionData
): Promise<TransactionResult> => {
  try {
    // Đảm bảo biến botPronoun luôn có giá trị
    const botPronoun = transactionData.botPronoun || "tôi";
    
    // Đảm bảo các danh mục cần thiết tồn tại
    await ensureAllCategoriesExist();
    
    // PHẦN PHÂN LOẠI DANH MỤC CHÍNH XÁC - SỬ DỤNG AI LEARNING SERVICE
    if (transactionData.description && !transactionData.category_id) {
      try {
        // Sử dụng AI Learning Service để phân loại
        const categoryId = await AILearningService.findCategoryByDescription(
          transactionData.description, 
          transactionData.type
        );
        
        if (categoryId) {
          transactionData.category_id = categoryId;
          console.log(`AI Learning categorized: "${transactionData.description}" -> Category ID: ${categoryId}`);
        }
      } catch (e) {
        console.error("Lỗi khi phân loại danh mục bằng AI Learning:", e);
      }
    }
    
    // Lưu loại giao dịch trước khi sửa đổi giá trị amount
    const transactionType = transactionData.type;

    if (transactionData.type === "expense") {
      transactionData.amount = -Math.abs(transactionData.amount);
    } else {
      transactionData.amount = Math.abs(transactionData.amount);
    }

    // Sử dụng datetime đầy đủ cho giao dịch
    transactionData.date = getFullDateTime();

    console.log(
      "Transaction data before processing:",
      JSON.stringify(transactionData)
    );

    // Kiểm tra có tên ví trong yêu cầu không
    if (transactionData.wallet_keyword) {
      console.log(
        "Trying to find wallet by keyword:",
        transactionData.wallet_keyword
      );
      // Nếu có wallet_keyword, tìm ví phù hợp
      const walletId = await findWalletByName(transactionData.wallet_keyword);
      if (walletId) {
        transactionData.wallet_id = walletId;
        console.log("Found wallet by keyword:", walletId);
      }
    }

    // Nếu đã có wallet_id trực tiếp, sử dụng nó
    if (transactionData.wallet_id) {
      console.log("Using specific wallet ID:", transactionData.wallet_id);
    } else {
      // Xử lý default wallet nếu không có ví cụ thể
      // Check if any wallets exist
      const allWallets = await WalletModel.getAll();
      console.log("All wallets:", JSON.stringify(allWallets));

      if (allWallets.length === 0) {
        // Try to create sample data first
        console.log("No wallets found. Trying to create sample data...");
        const created = await createSampleData();

        if (created) {
          // Get the newly created default wallet
          const defaultWallet = await WalletModel.getDefault();
          if (defaultWallet) {
            transactionData.wallet_id = defaultWallet.id;
            console.log(
              "Using newly created default wallet:",
              defaultWallet.name
            );
          } else {
            return {
              message: `Đã tạo ví mẫu nhưng không thể lấy thông tin ví. Vui lòng thử lại.`,
              transactionId: null,
            };
          }
        } else {
          return {
            message: `Chưa có ví nào. Hãy tạo ví trước khi thêm giao dịch.\n\nVí dụ: 'Tạo ví MB Bank với số tiền 1tr'`,
            transactionId: null,
          };
        }
      } else {
        // If wallets exist, try to get default wallet first
        const defaultWallet = await WalletModel.getDefault();
        if (defaultWallet) {
          transactionData.wallet_id = defaultWallet.id;
          console.log("Using default wallet:", defaultWallet.name);
        } else {
          // If no default wallet, use the first wallet
          transactionData.wallet_id = allWallets[0].id;
          console.log(`Using first available wallet: ${allWallets[0].name}`);
        }
      }
    }

    if (!transactionData.wallet_id) {
      return {
        message: `Không thể xác định ví. Vui lòng thử lại.`,
        transactionId: null,
      };
    }

    // Lấy thông tin ví để hiển thị trong phản hồi
    let walletName = "";
    try {
      const wallet = await WalletModel.getById(transactionData.wallet_id);
      if (wallet) {
        walletName = wallet.name;
      }
    } catch (error) {
      console.error("Lỗi khi lấy thông tin ví:", error);
    }

    // Create default category if missing
    if (!transactionData.category_id) {
      try {
        // Đảm bảo có danh mục mặc định
        if (transactionData.type === "expense") {
          transactionData.category_id = await getCategoryId("Chi tiêu khác");
        } else {
          // Tạo hoặc lấy ID danh mục "Thu nhập khác"
          const incomeCategories = await CategoryModel.getByType("income");
          let incomeCategory = incomeCategories.find(cat => cat.name.includes("khác"));
          
          if (!incomeCategory) {
            incomeCategory = await CategoryModel.create({
              name: "Thu nhập khác",
              type: "income",
              icon: "cash-outline",
              color: "#2ecc71"
            });
          }
          
          transactionData.category_id = incomeCategory.id;
        }
      } catch (categoryError) {
        console.error("Error creating default category:", categoryError);
        return {
          message: `Không thể tạo danh mục mặc định. Vui lòng thử lại sau.`,
          transactionId: null,
        };
      }
    }

    console.log("Final transaction data:", JSON.stringify(transactionData));

    // Lấy thông tin ví hiện tại để cập nhật số dư
    const wallet = await WalletModel.getById(transactionData.wallet_id);
    if (!wallet) {
      throw new Error("Không tìm thấy thông tin ví");
    }

    // Tính toán số dư mới dựa trên loại giao dịch
    let newBalance = wallet.balance;
    if (transactionType === "expense") {
      newBalance = wallet.balance - Math.abs(transactionData.amount);
    } else {
      newBalance = wallet.balance + Math.abs(transactionData.amount);
    }

    // Create the transaction in the database
    const newTransaction = await TransactionModel.create({
      wallet_id: transactionData.wallet_id,
      category_id: transactionData.category_id,
      amount: transactionData.amount,
      type: transactionData.type,
      description: transactionData.description,
      date: transactionData.date,
    });
    
    console.log("Transaction created:", JSON.stringify(newTransaction));

    // Cập nhật số dư ví
    await WalletModel.update(transactionData.wallet_id, {
      balance: newBalance,
    });

    // Tạo phản hồi với định dạng đánh dấu
    const formattedAmount = formatCurrency(Math.abs(newTransaction.amount));

    if (transactionType === "income") {
      // Thu nhập - sử dụng cú pháp định dạng {{color:red|text}} và {{bold|text}}
      return {
        message: `{{bold|${botPronoun}}} đã {{color:#4CAF50|cộng}} ví {{bold|${walletName}}} {{bold|${formattedAmount}}} với nội dung: {{bold|${newTransaction.description}}}`,
        transactionId: newTransaction.id,
      };
    } else {
      // Chi tiêu
      return {
        message: `{{bold|${botPronoun}}} đã {{color:#F44336|trừ}} ví {{bold|${walletName}}} {{bold|${formattedAmount}}} với nội dung: {{bold|${newTransaction.description}}}`,
        transactionId: newTransaction.id,
      };
    }
  } catch (error) {
    console.error("Lỗi khi thêm giao dịch:", error);
    return {
      message: `Không thể thêm giao dịch. Vui lòng thử lại sau.`,
      transactionId: null,
    };
  }
};

// Xử lý xóa giao dịch
export const handleDeleteTransaction = async (
  transactionId: string
): Promise<boolean> => {
  try {
    // Lấy thông tin giao dịch để cập nhật lại số dư ví
    const transaction = await TransactionModel.getById(transactionId);
    if (!transaction) {
      console.error("Không tìm thấy giao dịch cần xóa.");
      return false;
    }

    // Lấy thông tin ví để cập nhật số dư
    const wallet = await WalletModel.getById(transaction.wallet_id);
    if (!wallet) {
      console.error("Không tìm thấy thông tin ví.");
      return false;
    }

    // Tính toán số dư mới
    // Nếu là chi tiêu (số âm), cần cộng lại vào số dư ví
    // Nếu là thu nhập (số dương), cần trừ đi khỏi số dư ví
    const newBalance = wallet.balance - transaction.amount;

    // Xóa giao dịch
    await TransactionModel.delete(transactionId);

    // Cập nhật số dư ví
    await WalletModel.update(transaction.wallet_id, {
      balance: newBalance,
    });

    return true;
  } catch (error) {
    console.error("Lỗi khi xóa giao dịch:", error);
    return false;
  }
};

// Xử lý xem danh sách giao dịch gần đây
export const handleViewTransactions = async (
  limit: number = 5,
  userPronoun: string = "bạn" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    const transactions = await TransactionModel.getRecent(limit);

    if (transactions.length === 0) {
      return `${userPronoun} chưa có giao dịch nào.`;
    } else {
      let response = `${transactions.length} giao dịch gần nhất của ${userPronoun}:\n\n`;

      transactions.forEach((transaction, index) => {
        const amount = formatCurrency(Math.abs(transaction.amount));
        const date = getRelativeDate(transaction.date);
        const type = transaction.amount > 0 ? "+" : "-";
        const category = transaction.category?.name || "Không có danh mục";

        response += `${index + 1}. ${
          transaction.description || category
        }: ${type}${amount} - ${date}\n`;
      });

      return response;
    }
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return `Có lỗi khi tải giao dịch. Vui lòng thử lại sau.`;
  }
};

// Xử lý xem báo cáo thống kê tài chính hàng tháng
export const handleViewMonthlyStats = async (
  month: number,
  year: number,
  userPronoun: string = "bạn" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    const stats = await TransactionModel.getMonthlyStats(month, year);

    // Format the month name
    const monthName = new Date(year, month - 1, 1).toLocaleString("vi", {
      month: "long",
    });

    let response = `📊 Báo cáo thu chi ${monthName} ${year} của ${userPronoun}:\n\n`;
    response += `📈 Tổng thu: ${formatCurrency(stats.income)}\n`;
    response += `📉 Tổng chi: ${formatCurrency(stats.expense)}\n`;
    response += `🔎 Chênh lệch: ${formatCurrency(
      stats.income - stats.expense
    )}`;

    // Add overall status
    if (stats.income > stats.expense) {
      response += `\n\n✅ Tài chính tháng này dương (tiết kiệm được ${formatCurrency(
        stats.income - stats.expense
      )})`;
    } else if (stats.income < stats.expense) {
      response += `\n\n⚠️ Tài chính tháng này âm (chi tiêu vượt ${formatCurrency(
        stats.expense - stats.income
      )})`;
    } else {
      response += "\n\n🟰 Tài chính tháng này cân bằng (thu chi bằng nhau)";
    }

    return response;
  } catch (error) {
    console.error("Error getting monthly stats:", error);
    return `Có lỗi khi lấy báo cáo thu chi. Vui lòng thử lại sau.`;
  }
};

// Xử lý xem danh mục
export const handleViewCategories = async (
  type: "income" | "expense" | null,
  userPronoun: string = "bạn" // Mặc định cách xưng hô
): Promise<string> => {
  try {
    let categories;
    let response;

    if (type) {
      categories = await CategoryModel.getByType(type);
      response = `Danh mục ${
        type === "income" ? "thu nhập" : "chi tiêu"
      } của ${userPronoun}:\n\n`;
    } else {
      categories = await CategoryModel.getAll();
      response = `Tất cả danh mục của ${userPronoun}:\n\n`;
    }

    if (categories.length === 0) {
      return `Không tìm thấy danh mục nào.`;
    } else {
      categories.forEach((category, index) => {
        response += `${index + 1}. ${category.name} (${
          category.type === "income" ? "Thu nhập" : "Chi tiêu"
        })\n`;
      });

      return response;
    }
  } catch (error) {
    console.error("Lỗi khi lấy danh mục:", error);
    return `Không thể lấy danh mục. Thử lại sau.`;
  }
};

// Biến để lưu trữ cách xưng hô mặc định của bot
let botPronoun: string = "tôi";

// Cài đặt cách xưng hô của bot
export const setBotPronoun = (pronoun: string) => {
  botPronoun = pronoun;
};

// Lấy cách xưng hô của bot
export const getBotPronoun = (): string => {
  return botPronoun || "tôi";
};