// File: lib/services/ai-learning-service.ts
// File này liên quan đến: app/(more)/ai-learning.tsx, lib/helpers/chat-helpers.ts, lib/services/chatbot-actions.ts
// File này xử lý logic học máy để AI phân loại giao dịch đúng danh mục theo dữ liệu đã học

import AsyncStorage from '@react-native-async-storage/async-storage';
import { CategoryModel } from "@/lib/models/category";

// Key lưu trữ dữ liệu học máy
const AI_LEARNING_DATA_KEY = '@ai_learning_data';

// Interface cho dữ liệu học máy
export interface LearningData {
  description: string;
  correctCategoryId: string;
  correctCategoryName: string;
  timestamp: string;
}

// Interface cho kết quả tìm kiếm category
export interface CategoryMatch {
  categoryId: string;
  confidence: number; // <PERSON><PERSON> tin cậy từ 0-1
  source: 'learned' | 'keyword'; // Nguồn phân loại
}

export class AILearningService {
  
  // Lấy tất cả dữ liệu học máy đã lưu
  static async getLearningData(): Promise<LearningData[]> {
    try {
      const stored = await AsyncStorage.getItem(AI_LEARNING_DATA_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
      return [];
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu học máy:", error);
      return [];
    }
  }

  // Lưu dữ liệu học máy
  static async saveLearningData(data: LearningData[]): Promise<boolean> {
    try {
      await AsyncStorage.setItem(AI_LEARNING_DATA_KEY, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error("Lỗi khi lưu dữ liệu học máy:", error);
      return false;
    }
  }

  // Thêm dữ liệu học mới
  static async addLearningData(
    description: string, 
    correctCategoryId: string
  ): Promise<boolean> {
    try {
      // Lấy thông tin category
      const category = await CategoryModel.getById(correctCategoryId);
      if (!category) {
        console.error("Category không tồn tại:", correctCategoryId);
        return false;
      }

      const newData: LearningData = {
        description: description.trim().toLowerCase(),
        correctCategoryId,
        correctCategoryName: category.name,
        timestamp: new Date().toISOString()
      };

      const existingData = await this.getLearningData();
      
      // Kiểm tra xem đã có dữ liệu học cho description này chưa
      const existingIndex = existingData.findIndex(
        item => item.description === newData.description
      );

      if (existingIndex >= 0) {
        // Cập nhật dữ liệu hiện có
        existingData[existingIndex] = newData;
      } else {
        // Thêm dữ liệu mới
        existingData.push(newData);
      }

      return await this.saveLearningData(existingData);
    } catch (error) {
      console.error("Lỗi khi thêm dữ liệu học:", error);
      return false;
    }
  }

  // Xóa dữ liệu học theo index
  static async removeLearningData(index: number): Promise<boolean> {
    try {
      const existingData = await this.getLearningData();
      if (index >= 0 && index < existingData.length) {
        existingData.splice(index, 1);
        return await this.saveLearningData(existingData);
      }
      return false;
    } catch (error) {
      console.error("Lỗi khi xóa dữ liệu học:", error);
      return false;
    }
  }

  // Xóa tất cả dữ liệu học
  static async clearAllLearningData(): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(AI_LEARNING_DATA_KEY);
      return true;
    } catch (error) {
      console.error("Lỗi khi xóa tất cả dữ liệu học:", error);
      return false;
    }
  }

  // Tính toán độ tương tự giữa hai chuỗi (Levenshtein distance)
  private static calculateSimilarity(str1: string, str2: string): number {
    const a = str1.toLowerCase();
    const b = str2.toLowerCase();
    
    if (a === b) return 1.0;
    
    // Kiểm tra substring match
    if (a.includes(b) || b.includes(a)) {
      const shorterLength = Math.min(a.length, b.length);
      const longerLength = Math.max(a.length, b.length);
      return shorterLength / longerLength * 0.9; // Giảm một chút cho substring match
    }

    // Tính Levenshtein distance
    const matrix = [];
    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }
    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        if (b.charAt(i - 1) === a.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    const distance = matrix[b.length][a.length];
    const maxLength = Math.max(a.length, b.length);
    return (maxLength - distance) / maxLength;
  }

  // Tìm category dựa trên dữ liệu đã học
  static async findCategoryByLearning(description: string): Promise<CategoryMatch | null> {
    try {
      const learningData = await this.getLearningData();
      if (learningData.length === 0) return null;

      const normalizedDescription = description.trim().toLowerCase();
      let bestMatch: { data: LearningData; similarity: number } | null = null;

      // Tìm match tốt nhất
      for (const data of learningData) {
        const similarity = this.calculateSimilarity(normalizedDescription, data.description);
        
        if (similarity > 0.7 && (!bestMatch || similarity > bestMatch.similarity)) {
          bestMatch = { data, similarity };
        }
      }

      if (bestMatch) {
        console.log(`AI Learning match found: "${description}" -> "${bestMatch.data.correctCategoryName}" (confidence: ${bestMatch.similarity.toFixed(2)})`);
        
        return {
          categoryId: bestMatch.data.correctCategoryId,
          confidence: bestMatch.similarity,
          source: 'learned'
        };
      }

      return null;
    } catch (error) {
      console.error("Lỗi khi tìm category từ dữ liệu học:", error);
      return null;
    }
  }

  // Tìm category kết hợp giữa học máy và keyword matching (để thay thế hàm cũ)
  static async findCategoryByDescription(
    description: string,
    type: "income" | "expense"
  ): Promise<string | null> {
    try {
      // BƯỚC 1: Ưu tiên sử dụng dữ liệu đã học
      const learnedMatch = await this.findCategoryByLearning(description);
      if (learnedMatch && learnedMatch.confidence >= 0.8) {
        // Kiểm tra category vẫn tồn tại
        const category = await CategoryModel.getById(learnedMatch.categoryId);
        if (category && category.type === type) {
          console.log(`Using learned data: "${description}" -> ${category.name}`);
          return learnedMatch.categoryId;
        }
      }

      // BƯỚC 2: Fallback về keyword matching (logic cũ)
      const categories = await CategoryModel.getByType(type);
      if (!categories || categories.length === 0) return null;

      const lowerDescription = description.toLowerCase();
      
      // Các từ khóa force-matching
      const forcedKeywords = [
        { 
          keywords: ["khám", "bệnh", "thuốc", "bác sĩ", "nha khoa", "răng", "y tế", "sức khỏe"],
          category: "y tế"
        },
        { 
          keywords: ["điện", "nước", "gas", "hóa đơn", "tiền điện", "tiền nước", "internet", "wifi"],
          category: "sinh hoạt"
        },
        { 
          keywords: ["mua", "quần", "áo", "giày", "dép", "thời trang", "shopping"],
          category: "mua sắm"
        },
        { 
          keywords: ["taxi", "grab", "xe", "bus", "xăng", "đi lại", "di chuyển", "gửi xe", "đỗ xe"],
          category: "di chuyển"
        },
        { 
          keywords: ["trường", "học", "sách", "học phí", "giáo dục", "học hành", "khóa học"],
          category: "giáo dục"
        },
        { 
          keywords: ["phim", "xem phim", "rạp", "giải trí", "du lịch", "đi chơi", "bar", "club"],
          category: "giải trí"
        },
        { 
          keywords: ["nhà", "thuê", "tiền nhà", "chung cư", "phòng trọ", "điện nước", "sửa nhà"],
          category: "nhà cửa"
        },
        { 
          keywords: ["thể thao", "gym", "tập", "bơi", "yoga", "thể dục", "sân bóng"],
          category: "thể thao"
        }
      ];
      
      // Kiểm tra từng nhóm từ khóa
      for (const group of forcedKeywords) {
        for (const keyword of group.keywords) {
          if (lowerDescription.includes(keyword)) {
            for (const category of categories) {
              if (category.name.toLowerCase().includes(group.category)) {
                console.log(`Keyword match: "${keyword}" in "${lowerDescription}" -> Category: ${category.name}`);
                return category.id;
              }
            }
          }
        }
      }
      
      // Xử lý đặc biệt cho "Ăn uống"
      const foodKeywords = ["ăn", "uống", "cơm", "bữa", "trưa", "tối", "sáng", "nhà hàng", 
                          "cà phê", "cafe", "trà sữa", "buffet", "ẩm thực", "quán ăn"];
      
      if (foodKeywords.some(key => lowerDescription.includes(key))) {
        for (const category of categories) {
          if (category.name.toLowerCase().includes("ăn uống")) {
            console.log(`Food match: "${lowerDescription}" -> Category: ${category.name}`);
            return category.id;
          }
        }
      }

      // Tìm danh mục "Khác"
      for (const category of categories) {
        if (category.name.toLowerCase().includes("khác")) {
          console.log(`No match found, using default category: ${category.name}`);
          return category.id;
        }
      }

      // Tránh dùng danh mục "Ăn uống" nếu không match
      const nonFoodCategory = categories.find(cat => !cat.name.toLowerCase().includes("ăn uống"));
      if (nonFoodCategory) {
        console.log(`Avoiding "Ăn uống", using alternative category: ${nonFoodCategory.name}`);
        return nonFoodCategory.id;
      }
      
      // Sử dụng danh mục đầu tiên
      console.log(`Last resort: Using first category: ${categories[0].name}`);
      return categories[0].id;
    } catch (error) {
      console.error("Lỗi khi tìm danh mục phù hợp:", error);
      return null;
    }
  }

  // Lấy thống kê về hiệu quả học máy
  static async getLearningStats(): Promise<{
    totalLearned: number;
    lastLearned: string | null;
    categories: { [categoryName: string]: number };
  }> {
    try {
      const learningData = await this.getLearningData();
      
      const stats = {
        totalLearned: learningData.length,
        lastLearned: null as string | null,
        categories: {} as { [categoryName: string]: number }
      };

      if (learningData.length > 0) {
        // Tìm thời gian học gần nhất
        const sortedByTime = learningData.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        stats.lastLearned = sortedByTime[0].timestamp;

        // Đếm số lượng theo từng category
        for (const data of learningData) {
          stats.categories[data.correctCategoryName] = 
            (stats.categories[data.correctCategoryName] || 0) + 1;
        }
      }

      return stats;
    } catch (error) {
      console.error("Lỗi khi lấy thống kê học máy:", error);
      return {
        totalLearned: 0,
        lastLearned: null,
        categories: {}
      };
    }
  }

  // Export dữ liệu học để backup
  static async exportLearningData(): Promise<string | null> {
    try {
      const data = await this.getLearningData();
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error("Lỗi khi export dữ liệu học:", error);
      return null;
    }
  }

  // Import dữ liệu học từ backup
  static async importLearningData(jsonData: string): Promise<boolean> {
    try {
      const data = JSON.parse(jsonData);
      
      // Validate data structure
      if (!Array.isArray(data)) {
        throw new Error("Dữ liệu không hợp lệ: phải là array");
      }

      for (const item of data) {
        if (!item.description || !item.correctCategoryId || !item.correctCategoryName || !item.timestamp) {
          throw new Error("Dữ liệu không hợp lệ: thiếu trường bắt buộc");
        }
      }

      return await this.saveLearningData(data);
    } catch (error) {
      console.error("Lỗi khi import dữ liệu học:", error);
      return false;
    }
  }
}