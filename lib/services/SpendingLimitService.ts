// File: lib/services/SpendingLimitService.ts
// File này quản lý giới hạn chi tiêu, lưu trữ local và thông báo
// File này liên quan đến: lib/services/NotificationService.ts, lib/models/category.ts, lib/models/transaction.ts

import AsyncStorage from '@react-native-async-storage/async-storage';
import { NotificationService } from './NotificationService';
import { TransactionModel } from '../models/transaction';

// Key lưu trữ trong AsyncStorage
const SPENDING_LIMITS_KEY = '@moneyup_spending_limits';
const LIMIT_SETTINGS_KEY = '@moneyup_limit_settings';

// Định nghĩa kiểu dữ liệu
export type LimitPeriod = 'weekly' | 'monthly' | 'yearly';

export interface SpendingLimit {
  categoryId: string;
  categoryName: string;
  categoryColor: string;
  categoryIcon: string;
  limit: number;
  period: LimitPeriod;
  notifications: {
    warning: boolean; // 80%
    danger: boolean;  // 100%
  };
  createdAt: string;
  updatedAt: string;
}

export interface LimitSettings {
  defaultPeriod: LimitPeriod;
  defaultWarningEnabled: boolean;
  defaultDangerEnabled: boolean;
  checkFrequency: 'realtime' | 'daily'; // Khi nào kiểm tra
  notificationTime: string; // Format: "09:00" cho daily summary
}

export interface LimitUsage {
  categoryId: string;
  currentSpent: number;
  limitAmount: number;
  percentage: number;
  remainingAmount: number;
  period: LimitPeriod;
  periodStart: Date;
  periodEnd: Date;
  status: 'safe' | 'warning' | 'danger' | 'exceeded';
}

export interface LimitAlert {
  categoryId: string;
  categoryName: string;
  type: 'warning' | 'danger' | 'exceeded';
  currentSpent: number;
  limitAmount: number;
  percentage: number;
  message: string;
}

export class SpendingLimitService {
  /**
   * Khởi tạo service - tạo settings mặc định nếu chưa có
   */
  static async initialize(): Promise<void> {
    try {
      const existingSettings = await this.getSettings();
      if (!existingSettings) {
        const defaultSettings: LimitSettings = {
          defaultPeriod: 'monthly',
          defaultWarningEnabled: true,
          defaultDangerEnabled: true,
          checkFrequency: 'realtime',
          notificationTime: '09:00',
        };
        await this.saveSettings(defaultSettings);
      }
    } catch (error) {
      console.error('Lỗi khởi tạo SpendingLimitService:', error);
    }
  }

  // ============ QUẢN LÝ LIMITS ============

  /**
   * Lấy tất cả giới hạn chi tiêu
   */
  static async getAllLimits(): Promise<SpendingLimit[]> {
    try {
      const data = await AsyncStorage.getItem(SPENDING_LIMITS_KEY);
      if (!data) return [];
      
      const limitsMap = JSON.parse(data) as Record<string, SpendingLimit>;
      return Object.values(limitsMap);
    } catch (error) {
      console.error('Lỗi lấy danh sách limits:', error);
      return [];
    }
  }

  /**
   * Lấy giới hạn theo category ID
   */
  static async getLimitByCategory(categoryId: string): Promise<SpendingLimit | null> {
    try {
      const data = await AsyncStorage.getItem(SPENDING_LIMITS_KEY);
      if (!data) return null;
      
      const limitsMap = JSON.parse(data) as Record<string, SpendingLimit>;
      return limitsMap[categoryId] || null;
    } catch (error) {
      console.error('Lỗi lấy limit theo category:', error);
      return null;
    }
  }

  /**
   * Lưu/Cập nhật giới hạn chi tiêu
   */
  static async saveLimit(limit: Omit<SpendingLimit, 'createdAt' | 'updatedAt'>): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(SPENDING_LIMITS_KEY);
      const limitsMap = data ? JSON.parse(data) : {};
      
      const now = new Date().toISOString();
      const existingLimit = limitsMap[limit.categoryId];
      
      limitsMap[limit.categoryId] = {
        ...limit,
        createdAt: existingLimit?.createdAt || now,
        updatedAt: now,
      };
      
      await AsyncStorage.setItem(SPENDING_LIMITS_KEY, JSON.stringify(limitsMap));
      console.log(`Đã lưu limit cho category ${limit.categoryId}: ${limit.limit}`);
    } catch (error) {
      console.error('Lỗi lưu limit:', error);
      throw error;
    }
  }

  /**
   * Xóa giới hạn chi tiêu
   */
  static async deleteLimit(categoryId: string): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(SPENDING_LIMITS_KEY);
      if (!data) return;
      
      const limitsMap = JSON.parse(data) as Record<string, SpendingLimit>;
      delete limitsMap[categoryId];
      
      await AsyncStorage.setItem(SPENDING_LIMITS_KEY, JSON.stringify(limitsMap));
      
      // Hủy các notification liên quan
      await this.cancelLimitNotifications(categoryId);
      
      console.log(`Đã xóa limit cho category ${categoryId}`);
    } catch (error) {
      console.error('Lỗi xóa limit:', error);
      throw error;
    }
  }

  // ============ TÍNH TOÁN SỬ DỤNG ============

  /**
   * Tính toán usage cho một category trong khoảng thời gian
   */
  static async calculateUsage(categoryId: string): Promise<LimitUsage | null> {
    try {
      const limit = await this.getLimitByCategory(categoryId);
      if (!limit) return null;

      const { periodStart, periodEnd } = this.getPeriodRange(limit.period);
      
      // Lấy tất cả giao dịch của category trong khoảng thời gian
      const allTransactions = await TransactionModel.getAll();
      const categoryTransactions = allTransactions.filter(transaction => {
        const transactionDate = new Date(transaction.date);
        return transaction.category_id === categoryId &&
               transactionDate >= periodStart &&
               transactionDate <= periodEnd &&
               transaction.amount < 0; // Chỉ tính chi tiêu (số âm)
      });

      // Tính tổng chi tiêu (lấy giá trị tuyệt đối)
      const currentSpent = categoryTransactions.reduce((sum, transaction) => {
        return sum + Math.abs(transaction.amount);
      }, 0);

      const percentage = limit.limit > 0 ? (currentSpent / limit.limit) * 100 : 0;
      const remainingAmount = Math.max(0, limit.limit - currentSpent);

      let status: LimitUsage['status'] = 'safe';
      if (percentage >= 100) status = 'exceeded';
      else if (percentage >= 100) status = 'danger';
      else if (percentage >= 80) status = 'warning';

      return {
        categoryId,
        currentSpent,
        limitAmount: limit.limit,
        percentage,
        remainingAmount,
        period: limit.period,
        periodStart,
        periodEnd,
        status,
      };
    } catch (error) {
      console.error('Lỗi tính toán usage:', error);
      return null;
    }
  }

  /**
   * Tính toán usage cho tất cả categories có limit
   */
  static async calculateAllUsages(): Promise<LimitUsage[]> {
    try {
      const limits = await this.getAllLimits();
      const usages: LimitUsage[] = [];

      for (const limit of limits) {
        const usage = await this.calculateUsage(limit.categoryId);
        if (usage) {
          usages.push(usage);
        }
      }

      return usages.sort((a, b) => b.percentage - a.percentage); // Sắp xếp theo % giảm dần
    } catch (error) {
      console.error('Lỗi tính toán tất cả usages:', error);
      return [];
    }
  }

  // ============ KIỂM TRA VÀ CẢNH BÁO ============

  /**
   * Kiểm tra limit khi có giao dịch mới
   */
  static async checkLimitAfterTransaction(categoryId: string, transactionAmount: number): Promise<LimitAlert | null> {
    try {
      // Chỉ kiểm tra với giao dịch chi tiêu
      if (transactionAmount >= 0) return null;

      const usage = await this.calculateUsage(categoryId);
      if (!usage) return null;

      const limit = await this.getLimitByCategory(categoryId);
      if (!limit) return null;

      const newPercentage = usage.percentage;
      let alertType: LimitAlert['type'] | null = null;
      let message = '';

      if (newPercentage >= 100 && limit.notifications.danger) {
        alertType = newPercentage > 100 ? 'exceeded' : 'danger';
        message = newPercentage > 100 
          ? `Đã vượt ${newPercentage.toFixed(1)}% giới hạn tháng này!`
          : `Đã đạt 100% giới hạn tháng này!`;
      } else if (newPercentage >= 80 && limit.notifications.warning) {
        alertType = 'warning';
        message = `Đã chi ${newPercentage.toFixed(1)}% giới hạn tháng này.`;
      }

      if (alertType) {
        const alert: LimitAlert = {
          categoryId: usage.categoryId,
          categoryName: limit.categoryName,
          type: alertType,
          currentSpent: usage.currentSpent,
          limitAmount: usage.limitAmount,
          percentage: newPercentage,
          message,
        };

        // Gửi notification nếu được bật
        await this.sendLimitNotification(alert);
        
        return alert;
      }

      return null;
    } catch (error) {
      console.error('Lỗi kiểm tra limit:', error);
      return null;
    }
  }

  /**
   * Kiểm tra tất cả limits và trả về danh sách cảnh báo
   */
  static async checkAllLimits(): Promise<LimitAlert[]> {
    try {
      const usages = await this.calculateAllUsages();
      const alerts: LimitAlert[] = [];

      for (const usage of usages) {
        const limit = await this.getLimitByCategory(usage.categoryId);
        if (!limit) continue;

        let alertType: LimitAlert['type'] | null = null;
        let message = '';

        if (usage.percentage >= 100) {
          if (limit.notifications.danger) {
            alertType = usage.percentage > 100 ? 'exceeded' : 'danger';
            message = usage.percentage > 100 
              ? `Vượt ${usage.percentage.toFixed(1)}% giới hạn!`
              : `Đạt 100% giới hạn!`;
          }
        } else if (usage.percentage >= 80) {
          if (limit.notifications.warning) {
            alertType = 'warning';
            message = `Đã chi ${usage.percentage.toFixed(1)}% giới hạn.`;
          }
        }

        if (alertType) {
          alerts.push({
            categoryId: usage.categoryId,
            categoryName: limit.categoryName,
            type: alertType,
            currentSpent: usage.currentSpent,
            limitAmount: usage.limitAmount,
            percentage: usage.percentage,
            message,
          });
        }
      }

      return alerts;
    } catch (error) {
      console.error('Lỗi kiểm tra tất cả limits:', error);
      return [];
    }
  }

  // ============ NOTIFICATION ============

  /**
   * Gửi notification cho limit alert
   */
  private static async sendLimitNotification(alert: LimitAlert): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (!settings || settings.checkFrequency !== 'realtime') return;

      let title = '';
      let emoji = '';

      switch (alert.type) {
        case 'warning':
          title = '⚠️ Cảnh báo chi tiêu';
          break;
        case 'danger':
          title = '🚨 Đạt giới hạn chi tiêu';
          break;
        case 'exceeded':
          title = '🔴 Vượt giới hạn chi tiêu';
          break;
      }

      await NotificationService.sendImmediateNotification(
        title,
        `${alert.categoryName}: ${alert.message}`,
        {
          type: 'spending-limit',
          categoryId: alert.categoryId,
          alertType: alert.type,
          percentage: alert.percentage,
        }
      );
    } catch (error) {
      console.error('Lỗi gửi limit notification:', error);
    }
  }

  /**
   * Hủy notifications cho một category
   */
  private static async cancelLimitNotifications(categoryId: string): Promise<void> {
    try {
      // Logic hủy notification nếu cần
      console.log(`Hủy notifications cho category ${categoryId}`);
    } catch (error) {
      console.error('Lỗi hủy limit notifications:', error);
    }
  }

  /**
   * Gửi báo cáo tổng hợp hàng ngày
   */
  static async sendDailySummary(): Promise<void> {
    try {
      const alerts = await this.checkAllLimits();
      if (alerts.length === 0) return;

      const warningCount = alerts.filter(a => a.type === 'warning').length;
      const dangerCount = alerts.filter(a => a.type === 'danger' || a.type === 'exceeded').length;

      let message = '';
      if (dangerCount > 0) {
        message = `${dangerCount} danh mục vượt/đạt giới hạn`;
      } else {
        message = `${warningCount} danh mục gần đạt giới hạn`;
      }

      await NotificationService.sendImmediateNotification(
        '📊 Báo cáo chi tiêu hôm nay',
        message,
        {
          type: 'daily-limit-summary',
          alertCount: alerts.length,
          alerts: alerts.slice(0, 3), // Chỉ gửi 3 alert đầu
        }
      );
    } catch (error) {
      console.error('Lỗi gửi daily summary:', error);
    }
  }

  // ============ SETTINGS ============

  /**
   * Lấy cài đặt
   */
  static async getSettings(): Promise<LimitSettings | null> {
    try {
      const data = await AsyncStorage.getItem(LIMIT_SETTINGS_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Lỗi lấy settings:', error);
      return null;
    }
  }

  /**
   * Lưu cài đặt
   */
  static async saveSettings(settings: LimitSettings): Promise<void> {
    try {
      await AsyncStorage.setItem(LIMIT_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Lỗi lưu settings:', error);
      throw error;
    }
  }

  // ============ UTILITIES ============

  /**
   * Tính khoảng thời gian cho period
   */
  private static getPeriodRange(period: LimitPeriod): { periodStart: Date; periodEnd: Date } {
    const now = new Date();
    let periodStart: Date;
    let periodEnd: Date;

    switch (period) {
      case 'weekly':
        // Từ thứ 2 tuần này đến Chủ nhật
        const dayOfWeek = now.getDay();
        const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
        periodStart = new Date(now);
        periodStart.setDate(now.getDate() + mondayOffset);
        periodStart.setHours(0, 0, 0, 0);
        
        periodEnd = new Date(periodStart);
        periodEnd.setDate(periodStart.getDate() + 6);
        periodEnd.setHours(23, 59, 59, 999);
        break;

      case 'yearly':
        periodStart = new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0);
        periodEnd = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
        break;

      case 'monthly':
      default:
        periodStart = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
        periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        break;
    }

    return { periodStart, periodEnd };
  }

  /**
   * Format period thành text hiển thị
   */
  static formatPeriod(period: LimitPeriod): string {
    switch (period) {
      case 'weekly':
        return 'Tuần';
      case 'yearly':
        return 'Năm';
      case 'monthly':
      default:
        return 'Tháng';
    }
  }

  /**
   * Format tiền tệ
   */
  static formatCurrency(amount: number): string {
    return amount.toLocaleString('vi-VN') + 'đ';
  }

  /**
   * Xóa tất cả dữ liệu (dùng cho reset hoặc logout)
   */
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(SPENDING_LIMITS_KEY);
      await AsyncStorage.removeItem(LIMIT_SETTINGS_KEY);
      console.log('Đã xóa tất cả dữ liệu spending limits');
    } catch (error) {
      console.error('Lỗi xóa dữ liệu:', error);
      throw error;
    }
  }
}