// File: lib/services/referral-code-service.ts
// File này liên quan đến: app/(tabs)/settings.tsx, components/ReferralCodeInput.tsx, lib/services/affiliate-service.ts

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ReferralCodeInfo {
  code: string;
  ownerName: string;
  ownerId: string;
  isValid: boolean;
}

export interface UserReferralInfo {
  hasUsedCode: boolean;
  usedCode?: string;
  referrerName?: string;
  referrerId?: string;
  usedAt?: string;
}

class ReferralCodeService {
  private baseUrl = 'https://api.aimoney.app';
  
  // Validate mã giới thiệu format (6 chữ số)
  validateCodeFormat(code: string): boolean {
    return /^[0-9]{6}$/.test(code);
  }

  // Kiểm tra mã giới thiệu có tồn tại và lấy thông tin chủ sở hữu
  async checkReferralCode(code: string): Promise<ReferralCodeInfo> {
    try {
      const response = await fetch(`${this.baseUrl}/referral/check-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({ code }),
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          code,
          ownerName: data.ownerName,
          ownerId: data.ownerId,
          isValid: true,
        };
      } else {
        return {
          code,
          ownerName: '',
          ownerId: '',
          isValid: false,
        };
      }
    } catch (error) {
      console.error('Error checking referral code:', error);
      // Fallback: mock data để test
      return await this.getMockCodeInfo(code);
    }
  }

  // Mock data để test offline
  private async getMockCodeInfo(code: string): Promise<ReferralCodeInfo> {
    const mockCodes: { [key: string]: { name: string; id: string } } = {
      '001234': { name: 'Hùng', id: 'user_hung' },
      '567890': { name: 'Minh', id: 'user_minh' },
      '123456': { name: 'Linh', id: 'user_linh' },
      '999999': { name: 'Admin', id: 'user_admin' },
    };

    const codeInfo = mockCodes[code];
    
    return {
      code,
      ownerName: codeInfo?.name || '',
      ownerId: codeInfo?.id || '',
      isValid: !!codeInfo,
    };
  }

  // Kiểm tra user đã sử dụng mã giới thiệu chưa
  async getUserReferralInfo(userId: string): Promise<UserReferralInfo> {
    try {
      // Kiểm tra local storage trước
      const localInfo = await AsyncStorage.getItem(`referral_info_${userId}`);
      if (localInfo) {
        return JSON.parse(localInfo);
      }

      // Gọi API để lấy thông tin
      const response = await fetch(`${this.baseUrl}/referral/user-info/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        const userInfo: UserReferralInfo = {
          hasUsedCode: data.hasUsedCode,
          usedCode: data.usedCode,
          referrerName: data.referrerName,
          referrerId: data.referrerId,
          usedAt: data.usedAt,
        };

        // Lưu vào local storage
        await AsyncStorage.setItem(`referral_info_${userId}`, JSON.stringify(userInfo));
        return userInfo;
      } else {
        return { hasUsedCode: false };
      }
    } catch (error) {
      console.error('Error getting user referral info:', error);
      
      // Kiểm tra local storage nếu API lỗi
      const localInfo = await AsyncStorage.getItem(`referral_info_${userId}`);
      if (localInfo) {
        return JSON.parse(localInfo);
      }
      
      return { hasUsedCode: false };
    }
  }

  // Sử dụng mã giới thiệu
  async useReferralCode(userId: string, code: string, referrerId: string): Promise<boolean> {
    try {
      // Kiểm tra user đã sử dụng mã chưa
      const userInfo = await this.getUserReferralInfo(userId);
      if (userInfo.hasUsedCode) {
        throw new Error('Bạn đã sử dụng mã giới thiệu rồi');
      }

      // Kiểm tra không được dùng mã của chính mình
      const userCode = await this.getUserCode(userId);
      if (code === userCode) {
        throw new Error('Không thể sử dụng mã giới thiệu của chính mình');
      }

      const response = await fetch(`${this.baseUrl}/referral/use-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          userId,
          code,
          referrerId,
          usedAt: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        // Cập nhật local storage
        const codeInfo = await this.checkReferralCode(code);
        const newUserInfo: UserReferralInfo = {
          hasUsedCode: true,
          usedCode: code,
          referrerName: codeInfo.ownerName,
          referrerId: referrerId,
          usedAt: new Date().toISOString(),
        };

        await AsyncStorage.setItem(`referral_info_${userId}`, JSON.stringify(newUserInfo));
        return true;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Không thể sử dụng mã giới thiệu');
      }
    } catch (error) {
      console.error('Error using referral code:', error);
      
      // Fallback: lưu offline để sync sau
      await this.saveOfflineReferralUsage(userId, code, referrerId);
      return true;
    }
  }

  // Lưu việc sử dụng mã offline để sync sau
  private async saveOfflineReferralUsage(userId: string, code: string, referrerId: string): Promise<void> {
    try {
      const codeInfo = await this.checkReferralCode(code);
      const newUserInfo: UserReferralInfo = {
        hasUsedCode: true,
        usedCode: code,
        referrerName: codeInfo.ownerName,
        referrerId: referrerId,
        usedAt: new Date().toISOString(),
      };

      await AsyncStorage.setItem(`referral_info_${userId}`, JSON.stringify(newUserInfo));

      // Lưu để sync sau
      const offlineUsages = await AsyncStorage.getItem('offline_referral_usages') || '[]';
      const usages = JSON.parse(offlineUsages);
      
      usages.push({
        userId,
        code,
        referrerId,
        usedAt: new Date().toISOString(),
        synced: false,
      });
      
      await AsyncStorage.setItem('offline_referral_usages', JSON.stringify(usages));
    } catch (error) {
      console.error('Error saving offline referral usage:', error);
    }
  }

  // Lấy mã giới thiệu của user hiện tại
  async getUserCode(userId: string): Promise<string> {
    try {
      // Kiểm tra local storage
      const localCode = await AsyncStorage.getItem(`user_referral_code_${userId}`);
      if (localCode) {
        return localCode;
      }

      // Gọi API
      const response = await fetch(`${this.baseUrl}/referral/user-code/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok && data.code) {
        await AsyncStorage.setItem(`user_referral_code_${userId}`, data.code);
        return data.code;
      } else {
        // Tạo mã mới nếu chưa có
        return await this.generateUserCode(userId);
      }
    } catch (error) {
      console.error('Error getting user code:', error);
      
      // Fallback: kiểm tra local hoặc tạo mới
      const localCode = await AsyncStorage.getItem(`user_referral_code_${userId}`);
      if (localCode) {
        return localCode;
      }
      
      return await this.generateUserCode(userId);
    }
  }

  // Tạo mã giới thiệu cho user
  async generateUserCode(userId: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/referral/generate-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({ userId }),
      });

      const data = await response.json();
      
      if (response.ok && data.code) {
        await AsyncStorage.setItem(`user_referral_code_${userId}`, data.code);
        return data.code;
      } else {
        throw new Error('Cannot generate code from server');
      }
    } catch (error) {
      console.error('Error generating user code:', error);
      
      // Fallback: tạo mã offline
      const offlineCode = this.generateOfflineCode();
      await AsyncStorage.setItem(`user_referral_code_${userId}`, offlineCode);
      return offlineCode;
    }
  }

  // Tạo mã offline (6 chữ số ngẫu nhiên)
  private generateOfflineCode(): string {
    const timestamp = Date.now().toString();
    const randomPart = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    return randomPart;
  }

  // Sync dữ liệu offline
  async syncOfflineData(): Promise<void> {
    await this.syncOfflineReferralUsages();
  }

  // Sync việc sử dụng mã offline
  private async syncOfflineReferralUsages(): Promise<void> {
    try {
      const offlineUsages = await AsyncStorage.getItem('offline_referral_usages');
      if (!offlineUsages) return;
      
      const usages = JSON.parse(offlineUsages);
      const unsyncedUsages = usages.filter((usage: any) => !usage.synced);
      
      for (const usage of unsyncedUsages) {
        try {
          const response = await fetch(`${this.baseUrl}/referral/use-code`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${await this.getAuthToken()}`,
            },
            body: JSON.stringify(usage),
          });

          if (response.ok) {
            usage.synced = true;
          }
        } catch (error) {
          console.error('Error syncing referral usage:', error);
        }
      }
      
      await AsyncStorage.setItem('offline_referral_usages', JSON.stringify(usages));
    } catch (error) {
      console.error('Error syncing offline referral usages:', error);
    }
  }

  // Lấy danh sách người được giới thiệu
  async getReferralList(userId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/referral/list/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data.referrals || [];
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error getting referral list:', error);
      return [];
    }
  }

  // Lấy auth token
  private async getAuthToken(): Promise<string> {
    try {
      return await AsyncStorage.getItem('auth_token') || '';
    } catch (error) {
      console.error('Error getting auth token:', error);
      return '';
    }
  }

  // Format mã giới thiệu hiển thị
  formatCode(code: string): string {
    return code.replace(/(\d{3})(\d{3})/, '$1-$2');
  }

  // Validate mã có phải của user không
  async isUserOwnCode(userId: string, code: string): Promise<boolean> {
    const userCode = await this.getUserCode(userId);
    return userCode === code;
  }

  // Reset referral info (chỉ dùng cho testing)
  async resetUserReferralInfo(userId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`referral_info_${userId}`);
    } catch (error) {
      console.error('Error resetting referral info:', error);
    }
  }
}

export const referralCodeService = new ReferralCodeService();