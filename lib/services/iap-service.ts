// File: lib/services/iap-service.ts

import * as RNIap from 'react-native-iap';

export const subscriptionSkus = ['premium_monthly', 'premium_yearly'];

export const initIAP = async () => {
  try {
    await RNIap.initConnection();
    // await RNIap.flushFailedPurchasesCachedAsPendingAndroid(); // Nếu làm cho Android thì mở dòng này
  } catch (err) {
    console.log('IAP init error', err);
  }
};

export const getSubscriptions = async () => {
  try {
    const subs = await RNIap.getSubscriptions({ skus: subscriptionSkus });
    return subs;
  } catch (err) {
    console.log('getSubscriptions error', err);
    return [];
  }
};

export const requestSubscription = async (sku: string) => {
  try {
    await RNIap.requestSubscription({ sku });
  } catch (err) {
    console.log('requestSubscription error', err);
  }
};
