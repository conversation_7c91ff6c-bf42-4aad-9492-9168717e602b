// File: lib/services/iap-service.ts

import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import * as RNIap from "react-native-iap";

// Product IDs cho iOS và Android
export const subscriptionSkus = Platform.select({
  ios: ["premium_monthly", "premium_yearly"],
  android: ["premium_monthly", "premium_yearly"],
  default: ["premium_monthly", "premium_yearly"],
});

// Types
export interface SubscriptionProduct {
  productId: string;
  price: string;
  currency: string;
  localizedPrice: string;
  title: string;
  description: string;
  introductoryPrice?: string;
  subscriptionPeriodNumberIOS?: string;
  subscriptionPeriodUnitIOS?: string;
  introductoryPriceCyclesIOS?: string;
  introductoryPricePeriodNumberIOS?: string;
  introductoryPricePeriodUnitIOS?: string;
}

export interface PurchaseResult {
  productId: string;
  transactionId: string;
  transactionDate: number;
  transactionReceipt: string;
  purchaseToken?: string;
  dataAndroid?: string;
  signatureAndroid?: string;
  isAcknowledgedAndroid?: boolean;
  purchaseStateAndroid?: number;
  developerPayloadAndroid?: string;
  obfuscatedAccountIdAndroid?: string;
  obfuscatedProfileIdAndroid?: string;
}

let isInitialized = false;
let purchaseUpdateSubscription: any = null;
let purchaseErrorSubscription: any = null;

export const initIAP = async (): Promise<boolean> => {
  try {
    console.log("🚀 Initializing IAP...");

    if (isInitialized) {
      console.log("✅ IAP already initialized");
      return true;
    }

    // Check if running on simulator/emulator
    if (__DEV__) {
      console.log("🧪 Development mode detected");

      // Check platform specific limitations
      if (Platform.OS === "ios") {
        console.log("📱 iOS detected - IAP requires real device");
      } else if (Platform.OS === "android") {
        console.log(
          "🤖 Android detected - IAP may work on emulator with Google Play"
        );
      }
    }

    // Initialize connection
    const result = await RNIap.initConnection();
    console.log("📱 IAP Connection result:", result);

    // Clear cache for Android
    if (Platform.OS === "android") {
      try {
        await RNIap.flushFailedPurchasesCachedAsPendingAndroid();
        console.log("🧹 Android cache cleared");
      } catch (error) {
        console.log("⚠️ Failed to clear Android cache:", error);
      }
    }

    // Setup purchase listeners
    setupPurchaseListeners();

    isInitialized = true;
    console.log("✅ IAP initialized successfully");
    return true;
  } catch (err: any) {
    console.error("❌ IAP init error:", err);

    // Handle specific error cases
    if (err.code === "E_IAP_NOT_AVAILABLE") {
      console.log("⚠️ IAP not available - this is normal on:");
      console.log("  • iOS Simulator");
      console.log("  • Android emulator without Google Play");
      console.log("  • Devices without App Store/Google Play");
      console.log("🧪 Will use mock data in development mode");
    } else if (err.code === "E_UNKNOWN") {
      console.log("⚠️ Unknown IAP error - may be temporary");
    }

    isInitialized = false;

    // In development, we'll still return true to allow testing with mock data
    if (__DEV__) {
      console.log("🧪 Development mode: allowing mock IAP functionality");
      return true;
    }

    return false;
  }
};

const setupPurchaseListeners = () => {
  // Remove existing listeners
  if (purchaseUpdateSubscription) {
    purchaseUpdateSubscription.remove();
  }
  if (purchaseErrorSubscription) {
    purchaseErrorSubscription.remove();
  }

  // Purchase update listener
  purchaseUpdateSubscription = RNIap.purchaseUpdatedListener(
    async (purchase: RNIap.InAppPurchase | RNIap.SubscriptionPurchase) => {
      console.log("🎉 Purchase successful:", purchase);

      try {
        // Acknowledge purchase for Android
        if (Platform.OS === "android") {
          await RNIap.acknowledgePurchaseAndroid(purchase.purchaseToken!);
          console.log("✅ Purchase acknowledged on Android");
        }

        // Finish transaction for iOS
        if (Platform.OS === "ios") {
          await RNIap.finishTransaction(purchase);
          console.log("✅ Transaction finished on iOS");
        }

        // Save purchase info locally
        await savePurchaseInfo(purchase);
      } catch (ackErr) {
        console.error("❌ Error acknowledging purchase:", ackErr);
      }
    }
  );

  // Purchase error listener
  purchaseErrorSubscription = RNIap.purchaseErrorListener(
    (error: RNIap.PurchaseError) => {
      console.error("❌ Purchase error:", error);

      // Handle specific error codes
      if (error.code === "E_USER_CANCELLED") {
        console.log("🚫 User cancelled purchase");
      } else if (error.code === "E_ITEM_UNAVAILABLE") {
        console.log("📦 Item unavailable");
      } else if (error.code === "E_NETWORK_ERROR") {
        console.log("🌐 Network error during purchase");
      }
    }
  );
};

export const getSubscriptions = async (): Promise<SubscriptionProduct[]> => {
  try {
    console.log("📦 Getting subscriptions...");
    console.log("🔍 SKUs to fetch:", subscriptionSkus);

    if (!isInitialized) {
      console.log("⚠️ IAP not initialized, initializing now...");
      const initResult = await initIAP();
      if (!initResult && !__DEV__) {
        throw new Error("Failed to initialize IAP");
      }
    }

    // Try to get real subscriptions if IAP is available
    try {
      const subs = await RNIap.getSubscriptions({ skus: subscriptionSkus });
      console.log("✅ Subscriptions fetched:", subs);

      if (subs.length === 0) {
        console.log("⚠️ No subscriptions found. Check:");
        console.log(
          "1. Product IDs are correct in App Store Connect/Google Play Console"
        );
        console.log("2. Products are approved and available");
        console.log("3. Bundle ID matches the one in store");
        console.log("4. You are testing on a real device (not simulator)");

        // Return mock data if no real products found in development
        if (__DEV__) {
          console.log(
            "🧪 No real products found, returning mock data for development"
          );
          return getMockSubscriptions();
        }
      }

      return subs as SubscriptionProduct[];
    } catch (iapError: any) {
      console.error("❌ IAP getSubscriptions error:", iapError);

      // Handle specific IAP errors
      if (iapError.code === "E_IAP_NOT_AVAILABLE") {
        console.log("⚠️ IAP not available, using mock data");
      } else if (iapError.message?.includes("null")) {
        console.log("⚠️ IAP service not properly initialized, using mock data");
      }

      // Return mock data for development/testing
      if (__DEV__) {
        console.log("🧪 Returning mock data for development");
        return getMockSubscriptions();
      }

      throw iapError;
    }
  } catch (err) {
    console.error("❌ getSubscriptions error:", err);

    // Return mock data for development/testing
    if (__DEV__) {
      console.log("🧪 Returning mock data for development");
      return getMockSubscriptions();
    }

    return [];
  }
};

export const requestSubscription = async (
  sku: string
): Promise<PurchaseResult | null> => {
  try {
    console.log("💳 Requesting subscription:", sku);

    if (!isInitialized) {
      console.log("⚠️ IAP not initialized, initializing now...");
      const initResult = await initIAP();
      if (!initResult && !__DEV__) {
        throw new Error("Failed to initialize IAP");
      }
    }

    // Try real purchase first
    try {
      const purchase = await RNIap.requestSubscription({ sku });
      console.log("✅ Subscription requested successfully:", purchase);
      return purchase as PurchaseResult;
    } catch (iapError: any) {
      console.error("❌ IAP requestSubscription error:", iapError);

      // Handle specific IAP errors
      if (iapError.code === "E_IAP_NOT_AVAILABLE") {
        if (__DEV__) {
          console.log(
            "🧪 IAP not available, simulating purchase for development"
          );
          // Return mock purchase result
          return {
            productId: sku,
            transactionId: `mock_${sku}_${Date.now()}`,
            transactionDate: Date.now(),
            transactionReceipt: `mock_receipt_${Date.now()}`,
          } as PurchaseResult;
        } else {
          throw {
            code: "E_IAP_NOT_AVAILABLE",
            message: "In-app purchases not available",
          };
        }
      }

      // Re-throw with more specific error info
      if (iapError.code === "E_USER_CANCELLED") {
        throw {
          code: "E_USER_CANCELLED",
          message: "User cancelled the purchase",
        };
      } else if (iapError.code === "E_ITEM_UNAVAILABLE") {
        throw {
          code: "E_ITEM_UNAVAILABLE",
          message: "Product is not available",
        };
      } else if (iapError.code === "E_NETWORK_ERROR") {
        throw { code: "E_NETWORK_ERROR", message: "Network error occurred" };
      }

      throw iapError;
    }
  } catch (err: any) {
    console.error("❌ requestSubscription error:", err);
    throw err;
  }
};

// Get current purchases/subscriptions
export const getCurrentPurchases = async (): Promise<RNIap.Purchase[]> => {
  try {
    console.log("🔍 Getting current purchases...");

    if (!isInitialized) {
      const initResult = await initIAP();
      if (!initResult) {
        console.log("⚠️ IAP not initialized, returning empty purchases");
        return [];
      }
    }

    // Check if IAP is actually available before calling getAvailablePurchases
    try {
      const purchases = await RNIap.getAvailablePurchases();
      console.log("📋 Current purchases:", purchases);
      return purchases;
    } catch (iapError: any) {
      if (iapError.code === "E_IAP_NOT_AVAILABLE") {
        console.log("⚠️ IAP not available for getting purchases");
        return [];
      }
      throw iapError;
    }
  } catch (err: any) {
    console.error("❌ getCurrentPurchases error:", err);

    // Handle specific error cases
    if (
      err.message?.includes("getAvailableItems") ||
      err.message?.includes("null")
    ) {
      console.log("⚠️ IAP service not properly initialized");
    }

    return [];
  }
};

// Restore purchases
export const restorePurchases = async (): Promise<RNIap.Purchase[]> => {
  try {
    console.log("🔄 Restoring purchases...");

    if (!isInitialized) {
      await initIAP();
    }

    const purchases = await RNIap.getAvailablePurchases();
    console.log("✅ Purchases restored:", purchases);

    // Save restored purchases
    for (const purchase of purchases) {
      await savePurchaseInfo(purchase);
    }

    return purchases;
  } catch (err) {
    console.error("❌ restorePurchases error:", err);
    return [];
  }
};

// Check if user has active subscription
export const hasActiveSubscription = async (): Promise<boolean> => {
  try {
    const purchases = await getCurrentPurchases();

    // Check if any purchase is a subscription and still valid
    const activeSubscription = purchases.find((purchase) =>
      subscriptionSkus.includes(purchase.productId)
    );

    if (activeSubscription) {
      console.log(
        "✅ Active subscription found:",
        activeSubscription.productId
      );
      return true;
    }

    console.log("❌ No active subscription found");
    return false;
  } catch (err) {
    console.error("❌ hasActiveSubscription error:", err);
    return false;
  }
};

// Save purchase info locally
const savePurchaseInfo = async (purchase: any) => {
  try {
    const purchaseData = {
      productId: purchase.productId,
      transactionId: purchase.transactionId,
      transactionDate: purchase.transactionDate,
      purchaseTime: Date.now(),
    };

    await AsyncStorage.setItem(
      `purchase_${purchase.productId}`,
      JSON.stringify(purchaseData)
    );

    // Also save to a general purchases list
    const existingPurchases =
      (await AsyncStorage.getItem("user_purchases")) || "[]";
    const purchases = JSON.parse(existingPurchases);

    // Add if not already exists
    const existingIndex = purchases.findIndex(
      (p: any) => p.transactionId === purchase.transactionId
    );
    if (existingIndex === -1) {
      purchases.push(purchaseData);
      await AsyncStorage.setItem("user_purchases", JSON.stringify(purchases));
    }

    console.log("💾 Purchase info saved locally");
  } catch (err) {
    console.error("❌ Error saving purchase info:", err);
  }
};

// Mock data for development
const getMockSubscriptions = (): SubscriptionProduct[] => {
  return [
    {
      productId: "premium_monthly",
      price: "49000",
      currency: "VND",
      localizedPrice: "49.000đ",
      title: "AI Money Premium Monthly",
      description: "Monthly subscription to AI Money Premium features",
    },
    {
      productId: "premium_yearly",
      price: "299000",
      currency: "VND",
      localizedPrice: "299.000đ",
      title: "AI Money Premium Yearly",
      description: "Yearly subscription to AI Money Premium features",
    },
  ];
};

// Cleanup function
export const endIAP = async () => {
  try {
    if (purchaseUpdateSubscription) {
      purchaseUpdateSubscription.remove();
      purchaseUpdateSubscription = null;
    }

    if (purchaseErrorSubscription) {
      purchaseErrorSubscription.remove();
      purchaseErrorSubscription = null;
    }

    await RNIap.endConnection();
    isInitialized = false;
    console.log("🔚 IAP connection ended");
  } catch (err) {
    console.error("❌ Error ending IAP:", err);
  }
};
