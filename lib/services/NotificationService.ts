// File: lib/services/NotificationService.ts
// File này xử lý tất cả thông báo cho app
// File này liên quan đến: lib/models/debt.ts, expo-notifications

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Cấu hình cách hiển thị notification khi app đang mở
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface DebtNotificationData {
  debtId: string;
  debtName: string;
  amount: number;
  type: 'payable' | 'receivable';
  dueDate: string;
}

export class NotificationService {
  /**
   * Khởi tạo và xin quyền thông báo
   */
  static async initialize(): Promise<boolean> {
    try {
      // Kiểm tra device có hỗ trợ push notification không
      if (!Device.isDevice) {
        console.log('Notifications chỉ hoạt động trên thiết bị thật');
        return false;
      }

      // Xin quyền thông báo
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Không được cấp quyền thông báo');
        return false;
      }

      // Cấu hình notification channel cho Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('debt-reminders', {
          name: 'Nhắc nhở nợ',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF6B35',
          sound: 'default',
        });
      }

      console.log('Notification service đã được khởi tạo');
      return true;
    } catch (error) {
      console.error('Lỗi khởi tạo notification service:', error);
      return false;
    }
  }

  /**
   * Lên lịch thông báo nhắc nhở nợ
   */
  static async scheduleDebtReminder(
    notificationId: string,
    reminderDate: Date,
    data: DebtNotificationData
  ): Promise<string | null> {
    try {
      // Kiểm tra ngày nhắc nhở phải trong tương lai
      if (reminderDate <= new Date()) {
        console.log('Ngày nhắc nhở phải trong tương lai');
        return null;
      }

      const schedulingOptions: Notifications.NotificationRequestInput = {
        identifier: notificationId,
        content: {
          title: '💰 Nhắc nhở thanh toán nợ',
          body: `${data.debtName} - ${this.formatCurrency(data.amount)} sắp đến hạn`,
          data: {
            type: 'debt-reminder',
            debtId: data.debtId,
            ...data
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
          categoryIdentifier: 'debt-reminder',
        },
        trigger: {
          date: reminderDate,
        },
      };

      // Cấu hình notification channel cho Android
      if (Platform.OS === 'android') {
        schedulingOptions.content.channelId = 'debt-reminders';
      }

      const notificationRequest = await Notifications.scheduleNotificationAsync(
        schedulingOptions
      );

      console.log(`Đã lên lịch thông báo: ${notificationRequest} cho ${reminderDate}`);
      return notificationRequest;
    } catch (error) {
      console.error('Lỗi lên lịch thông báo:', error);
      return null;
    }
  }

  /**
   * Lên lịch thông báo quá hạn
   */
  static async scheduleOverdueNotification(
    notificationId: string,
    dueDate: Date,
    data: DebtNotificationData
  ): Promise<string | null> {
    try {
      // Thông báo vào 9h sáng ngày hôm sau khi quá hạn
      const overdueDate = new Date(dueDate);
      overdueDate.setDate(overdueDate.getDate() + 1);
      overdueDate.setHours(9, 0, 0, 0);

      if (overdueDate <= new Date()) {
        return null;
      }

      const schedulingOptions: Notifications.NotificationRequestInput = {
        identifier: `${notificationId}_overdue`,
        content: {
          title: '⚠️ Khoản nợ đã quá hạn',
          body: `${data.debtName} - ${this.formatCurrency(data.amount)} đã quá hạn thanh toán`,
          data: {
            type: 'debt-overdue',
            debtId: data.debtId,
            ...data
          },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
          categoryIdentifier: 'debt-overdue',
        },
        trigger: {
          date: overdueDate,
        },
      };

      if (Platform.OS === 'android') {
        schedulingOptions.content.channelId = 'debt-reminders';
      }

      const notificationRequest = await Notifications.scheduleNotificationAsync(
        schedulingOptions
      );

      console.log(`Đã lên lịch thông báo quá hạn: ${notificationRequest}`);
      return notificationRequest;
    } catch (error) {
      console.error('Lỗi lên lịch thông báo quá hạn:', error);
      return null;
    }
  }

  /**
   * Hủy thông báo đã lên lịch
   */
  static async cancelDebtNotifications(debtId: string): Promise<void> {
    try {
      // Hủy thông báo nhắc nhở
      await Notifications.cancelScheduledNotificationAsync(`debt_reminder_${debtId}`);
      // Hủy thông báo quá hạn
      await Notifications.cancelScheduledNotificationAsync(`debt_reminder_${debtId}_overdue`);
      
      console.log(`Đã hủy thông báo cho nợ: ${debtId}`);
    } catch (error) {
      console.error('Lỗi hủy thông báo:', error);
    }
  }

  /**
   * Cập nhật thông báo khi thay đổi thông tin nợ
   */
  static async updateDebtNotifications(
    debtId: string,
    reminderDate?: Date,
    dueDate?: Date,
    data?: DebtNotificationData
  ): Promise<void> {
    try {
      // Hủy thông báo cũ
      await this.cancelDebtNotifications(debtId);

      if (!data) return;

      // Tạo thông báo mới nếu có ngày nhắc nhở
      if (reminderDate) {
        await this.scheduleDebtReminder(
          `debt_reminder_${debtId}`,
          reminderDate,
          data
        );
      }

      // Tạo thông báo quá hạn nếu có ngày đáo hạn
      if (dueDate) {
        await this.scheduleOverdueNotification(
          `debt_reminder_${debtId}`,
          dueDate,
          data
        );
      }
    } catch (error) {
      console.error('Lỗi cập nhật thông báo:', error);
    }
  }

  /**
   * Gửi thông báo ngay lập tức
   */
  static async sendImmediateNotification(
    title: string,
    body: string,
    data?: any
  ): Promise<string> {
    try {
      const notificationRequest = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
          sound: 'default',
        },
        trigger: null, // Gửi ngay
      });

      return notificationRequest;
    } catch (error) {
      console.error('Lỗi gửi thông báo ngay:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách thông báo đã lên lịch
   */
  static async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Lỗi lấy danh sách thông báo:', error);
      return [];
    }
  }

  /**
   * Lấy danh sách thông báo liên quan đến nợ
   */
  static async getDebtNotifications(debtId?: string): Promise<Notifications.NotificationRequest[]> {
    try {
      const allNotifications = await this.getScheduledNotifications();
      
      return allNotifications.filter(notification => {
        const data = notification.content.data as any;
        const isDebtNotification = data?.type?.includes('debt');
        
        if (debtId) {
          return isDebtNotification && data?.debtId === debtId;
        }
        
        return isDebtNotification;
      });
    } catch (error) {
      console.error('Lỗi lấy thông báo nợ:', error);
      return [];
    }
  }

  /**
   * Thiết lập listener để xử lý khi user tap vào notification
   */
  static setupNotificationListeners(
    onNotificationReceived?: (notification: Notifications.Notification) => void,
    onNotificationTapped?: (response: Notifications.NotificationResponse) => void
  ): void {
    // Listener khi nhận thông báo (app đang mở)
    if (onNotificationReceived) {
      Notifications.addNotificationReceivedListener(onNotificationReceived);
    }

    // Listener khi user tap vào thông báo
    if (onNotificationTapped) {
      Notifications.addNotificationResponseReceivedListener(onNotificationTapped);
    }
  }

  /**
   * Xóa tất cả thông báo đã hiển thị
   */
  static async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.dismissAllNotificationsAsync();
    } catch (error) {
      console.error('Lỗi xóa thông báo:', error);
    }
  }

  /**
   * Kiểm tra trạng thái quyền thông báo
   */
  static async getNotificationPermissionStatus(): Promise<string> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status;
    } catch (error) {
      console.error('Lỗi kiểm tra quyền thông báo:', error);
      return 'undetermined';
    }
  }

  /**
   * Format tiền tệ cho thông báo
   */
  private static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  /**
   * Thông báo tổng hợp hàng ngày (có thể dùng background task)
   */
  static async sendDailySummaryNotification(): Promise<void> {
    try {
      // Logic lấy thống kê nợ sắp đến hạn trong ngày
      // Có thể integrate với DebtModel.getUpcoming(1)
      
      await this.sendImmediateNotification(
        '📊 Tổng quan nợ hôm nay',
        'Bạn có 2 khoản nợ sắp đến hạn. Nhấn để xem chi tiết.',
        { type: 'daily-summary' }
      );
    } catch (error) {
      console.error('Lỗi gửi thông báo tổng hợp:', error);
    }
  }
}