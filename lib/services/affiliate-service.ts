// File: lib/services/affiliate-service.ts
// File này liên quan đến: app/(more)/affiliate-dashboard.tsx, app/(more)/affiliate-earnings.tsx, lib/models/affiliate.ts, lib/services/referral-code-service.ts

import AsyncStorage from '@react-native-async-storage/async-storage';
import { referralCodeService } from './referral-code-service';

export interface AffiliateStats {
  totalEarnings: number;
  pendingEarnings: number;
  totalReferrals: number;
  activeReferrals: number;
  clickCount: number;
  conversionRate: number;
  affiliateCode: string;
  tier: string;
  commissionRate: number;
}

export interface EarningsData {
  totalEarnings: number;
  availableBalance: number;
  pendingBalance: number;
  totalWithdrawn: number;
  minimumWithdraw: number;
}

export interface Transaction {
  id: string;
  type: "commission" | "withdrawal" | "bonus";
  amount: number;
  status: "completed" | "pending" | "failed";
  date: string;
  description: string;
  referralCode?: string;
  referredUserName?: string;
}

export interface WithdrawalRequest {
  amount: number;
  method: string;
  methodDetails: any;
  userId: string;
}

export interface ReferralUser {
  id: string;
  name: string;
  email: string;
  joinedAt: string;
  isPremium: boolean;
  totalSpent: number;
  commissionEarned: number;
  status: "active" | "inactive";
}

class AffiliateService {
  private baseUrl = 'https://api.aimoney.app';

  // Khởi tạo affiliate cho user
  async initializeAffiliate(userId: string): Promise<AffiliateStats> {
    try {
      // Lấy mã giới thiệu từ referral service
      const affiliateCode = await referralCodeService.getUserCode(userId);
      
      const response = await fetch(`${this.baseUrl}/affiliate/initialize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({ userId, affiliateCode }),
      });

      const data = await response.json();
      
      if (response.ok) {
        return {
          ...data.stats,
          affiliateCode,
        };
      } else {
        throw new Error(data.message || 'Failed to initialize affiliate');
      }
    } catch (error) {
      console.error('Error initializing affiliate:', error);
      // Fallback: tạo affiliate offline
      return await this.createOfflineAffiliate(userId);
    }
  }

  // Tạo affiliate offline (fallback)
  private async createOfflineAffiliate(userId: string): Promise<AffiliateStats> {
    const affiliateCode = await referralCodeService.getUserCode(userId);
    
    return {
      totalEarnings: 0,
      pendingEarnings: 0,
      totalReferrals: 0,
      activeReferrals: 0,
      clickCount: 0,
      conversionRate: 0,
      affiliateCode,
      tier: "Basic",
      commissionRate: 0.5,
    };
  }

  // Lấy thống kê affiliate
  async getAffiliateStats(userId: string): Promise<AffiliateStats> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/stats/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data;
      } else {
        throw new Error(data.message || 'Failed to get affiliate stats');
      }
    } catch (error) {
      console.error('Error getting affiliate stats:', error);
      // Fallback: return mock data
      const affiliateCode = await referralCodeService.getUserCode(userId);
      const totalReferrals = 25;
      
      return {
        totalEarnings: 1250000,
        pendingEarnings: 450000,
        totalReferrals,
        activeReferrals: 18,
        clickCount: 156,
        conversionRate: 16.02,
        affiliateCode,
        tier: this.getTierName(totalReferrals),
        commissionRate: this.getCommissionRate(totalReferrals),
      };
    }
  }

  // Lấy dữ liệu thu nhập
  async getEarningsData(userId: string): Promise<EarningsData> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/earnings/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data;
      } else {
        throw new Error(data.message || 'Failed to get earnings data');
      }
    } catch (error) {
      console.error('Error getting earnings data:', error);
      // Fallback: return mock data
      return {
        totalEarnings: 1250000,
        availableBalance: 800000,
        pendingBalance: 450000,
        totalWithdrawn: 650000,
        minimumWithdraw: 50000,
      };
    }
  }

  // Lấy lịch sử giao dịch
  async getTransactions(userId: string, page: number = 1, limit: number = 20): Promise<Transaction[]> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/transactions/${userId}?page=${page}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data.transactions;
      } else {
        throw new Error(data.message || 'Failed to get transactions');
      }
    } catch (error) {
      console.error('Error getting transactions:', error);
      // Fallback: return mock data
      return [
        {
          id: "1",
          type: "commission",
          amount: 125000,
          status: "completed",
          date: "2024-01-15",
          description: "Hoa hồng từ Premium Monthly",
          referralCode: "001234",
          referredUserName: "Nguyễn Văn A",
        },
        {
          id: "2",
          type: "withdrawal",
          amount: -300000,
          status: "completed",
          date: "2024-01-10",
          description: "Rút tiền qua MoMo",
        },
        {
          id: "3",
          type: "commission",
          amount: 250000,
          status: "pending",
          date: "2024-01-08",
          description: "Hoa hồng từ Premium Yearly",
          referralCode: "567890",
          referredUserName: "Trần Thị B",
        },
        {
          id: "4",
          type: "bonus",
          amount: 50000,
          status: "completed",
          date: "2024-01-05",
          description: "Bonus milestone 10 referrals",
        },
      ];
    }
  }

  // Lấy danh sách người được giới thiệu
  async getReferralList(userId: string): Promise<ReferralUser[]> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/referrals/${userId}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data.referrals;
      } else {
        throw new Error(data.message || 'Failed to get referral list');
      }
    } catch (error) {
      console.error('Error getting referral list:', error);
      // Fallback: return mock data
      return [
        {
          id: "ref_001",
          name: "Nguyễn Văn A",
          email: "<EMAIL>",
          joinedAt: "2024-01-15",
          isPremium: true,
          totalSpent: 250000,
          commissionEarned: 125000,
          status: "active",
        },
        {
          id: "ref_002",
          name: "Trần Thị B",
          email: "<EMAIL>",
          joinedAt: "2024-01-08",
          isPremium: true,
          totalSpent: 500000,
          commissionEarned: 250000,
          status: "active",
        },
        {
          id: "ref_003",
          name: "Lê Văn C",
          email: "<EMAIL>",
          joinedAt: "2024-01-12",
          isPremium: false,
          totalSpent: 0,
          commissionEarned: 0,
          status: "inactive",
        },
      ];
    }
  }

  // Tracking khi user mua Premium thông qua mã giới thiệu
  async trackPremiumPurchase(purchaseData: {
    userId: string;
    affiliateCode: string;
    packageType: string;
    amount: number;
    purchaseId: string;
  }): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/track-purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          ...purchaseData,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to track premium purchase');
      }

      // Cập nhật commission cho affiliate
      await this.updateCommission(purchaseData);
    } catch (error) {
      console.error('Error tracking premium purchase:', error);
      // Store offline for later sync
      await this.storeOfflinePurchase(purchaseData);
    }
  }

  // Cập nhật commission cho affiliate
  private async updateCommission(purchaseData: any): Promise<void> {
    try {
      // Lấy thông tin affiliate để tính commission rate
      const affiliateInfo = await referralCodeService.checkReferralCode(purchaseData.affiliateCode);
      
      if (!affiliateInfo.isValid) return;

      // Lấy commission rate dựa trên tier của affiliate
      const affiliateStats = await this.getAffiliateStats(affiliateInfo.ownerId);
      const commissionRate = affiliateStats.commissionRate;
      const commissionAmount = purchaseData.amount * commissionRate;

      const response = await fetch(`${this.baseUrl}/affiliate/commission`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          affiliateUserId: affiliateInfo.ownerId,
          affiliateCode: purchaseData.affiliateCode,
          amount: commissionAmount,
          purchaseId: purchaseData.purchaseId,
          purchaseAmount: purchaseData.amount,
          referredUserId: purchaseData.userId,
          type: 'commission',
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update commission');
      }

      // Kiểm tra milestone bonus
      await this.checkMilestoneBonus(affiliateInfo.ownerId);
    } catch (error) {
      console.error('Error updating commission:', error);
    }
  }

  // Kiểm tra và cấp milestone bonus
  private async checkMilestoneBonus(affiliateUserId: string): Promise<void> {
    try {
      const stats = await this.getAffiliateStats(affiliateUserId);
      const milestones = [10, 25, 50, 100, 200, 500];
      
      for (const milestone of milestones) {
        if (stats.totalReferrals === milestone) {
          const bonusAmount = milestone * 1000; // 1000 VND per referral as bonus
          
          await fetch(`${this.baseUrl}/affiliate/milestone-bonus`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${await this.getAuthToken()}`,
            },
            body: JSON.stringify({
              affiliateUserId,
              milestone,
              bonusAmount,
              timestamp: new Date().toISOString(),
            }),
          });
          break;
        }
      }
    } catch (error) {
      console.error('Error checking milestone bonus:', error);
    }
  }

  // Yêu cầu rút tiền
  async requestWithdrawal(withdrawalRequest: WithdrawalRequest): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/withdraw`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
        body: JSON.stringify({
          ...withdrawalRequest,
          timestamp: new Date().toISOString(),
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        return true;
      } else {
        throw new Error(data.message || 'Failed to request withdrawal');
      }
    } catch (error) {
      console.error('Error requesting withdrawal:', error);
      throw error;
    }
  }

  // Lưu giao dịch mua Premium offline để sync sau
  private async storeOfflinePurchase(purchaseData: any): Promise<void> {
    try {
      const offlinePurchases = await AsyncStorage.getItem('offline_affiliate_purchases') || '[]';
      const purchases = JSON.parse(offlinePurchases);
      
      purchases.push({
        ...purchaseData,
        timestamp: new Date().toISOString(),
        synced: false,
      });
      
      await AsyncStorage.setItem('offline_affiliate_purchases', JSON.stringify(purchases));
    } catch (error) {
      console.error('Error storing offline purchase:', error);
    }
  }

  // Sync dữ liệu offline lên server
  async syncOfflineData(): Promise<void> {
    await Promise.all([
      this.syncOfflinePurchases(),
      referralCodeService.syncOfflineData(), // Sync referral code data
    ]);
  }

  // Sync offline purchases
  private async syncOfflinePurchases(): Promise<void> {
    try {
      const offlinePurchases = await AsyncStorage.getItem('offline_affiliate_purchases');
      if (!offlinePurchases) return;
      
      const purchases = JSON.parse(offlinePurchases);
      const unsyncedPurchases = purchases.filter((purchase: any) => !purchase.synced);
      
      for (const purchase of unsyncedPurchases) {
        try {
          await this.trackPremiumPurchase(purchase);
          purchase.synced = true;
        } catch (error) {
          console.error('Error syncing purchase:', error);
        }
      }
      
      await AsyncStorage.setItem('offline_affiliate_purchases', JSON.stringify(purchases));
    } catch (error) {
      console.error('Error syncing offline purchases:', error);
    }
  }

  // Lấy auth token
  private async getAuthToken(): Promise<string> {
    try {
      return await AsyncStorage.getItem('auth_token') || '';
    } catch (error) {
      console.error('Error getting auth token:', error);
      return '';
    }
  }

  // Format currency
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  }

  // Format mã giới thiệu hiển thị
  formatCode(code: string): string {
    return referralCodeService.formatCode(code);
  }

  // Calculate commission
  calculateCommission(purchaseAmount: number, totalReferrals: number = 0): number {
    const rate = this.getCommissionRate(totalReferrals);
    return purchaseAmount * rate;
  }

  // Get commission rate based on tier
  getCommissionRate(totalReferrals: number): number {
    if (totalReferrals >= 500) return 0.70; // 70% for Diamond tier
    if (totalReferrals >= 200) return 0.65; // 65% for Platinum tier
    if (totalReferrals >= 100) return 0.60; // 60% for Gold tier
    if (totalReferrals >= 50) return 0.55;  // 55% for Silver tier  
    if (totalReferrals >= 20) return 0.52;  // 52% for Bronze tier
    return 0.50; // 50% for Basic tier
  }

  // Get tier name
  getTierName(totalReferrals: number): string {
    if (totalReferrals >= 500) return "Diamond";
    if (totalReferrals >= 200) return "Platinum";
    if (totalReferrals >= 100) return "Gold";
    if (totalReferrals >= 50) return "Silver";
    if (totalReferrals >= 20) return "Bronze";
    return "Basic";
  }

  // Get tier color
  getTierColor(totalReferrals: number): string {
    if (totalReferrals >= 500) return "#B9F2FF"; // Diamond
    if (totalReferrals >= 200) return "#E5E7EB"; // Platinum
    if (totalReferrals >= 100) return "#FFD700"; // Gold
    if (totalReferrals >= 50) return "#C0C0C0";  // Silver
    if (totalReferrals >= 20) return "#CD7F32";  // Bronze
    return "#6B7280"; // Basic
  }

  // Get next tier requirements
  getNextTierRequirements(totalReferrals: number): { nextTier: string; required: number; remaining: number } | null {
    const tiers = [
      { name: "Bronze", required: 20 },
      { name: "Silver", required: 50 },
      { name: "Gold", required: 100 },
      { name: "Platinum", required: 200 },
      { name: "Diamond", required: 500 },
    ];

    for (const tier of tiers) {
      if (totalReferrals < tier.required) {
        return {
          nextTier: tier.name,
          required: tier.required,
          remaining: tier.required - totalReferrals,
        };
      }
    }

    return null; // Đã đạt tier cao nhất
  }

  // Tính toán tổng earnings dự kiến nếu tất cả referrals mua Premium
  calculatePotentialEarnings(referralCount: number, averageSpending: number = 250000): number {
    const commissionRate = this.getCommissionRate(referralCount);
    return referralCount * averageSpending * commissionRate;
  }

  // Validate affiliate code format (sử dụng từ referral service)
  isValidAffiliateCode(code: string): boolean {
    return referralCodeService.validateCodeFormat(code);
  }

  // Kiểm tra user có phải là affiliate hay không
  async isUserAffiliate(userId: string): Promise<boolean> {
    try {
      const stats = await this.getAffiliateStats(userId);
      return stats.totalReferrals > 0 || stats.totalEarnings > 0;
    } catch (error) {
      return false;
    }
  }

  // Get affiliate performance metrics
  async getPerformanceMetrics(userId: string, period: 'week' | 'month' | 'year' = 'month'): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/affiliate/metrics/${userId}?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`,
        },
      });

      const data = await response.json();
      
      if (response.ok) {
        return data;
      } else {
        throw new Error(data.message || 'Failed to get performance metrics');
      }
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      // Return mock data
      return {
        period,
        newReferrals: 5,
        newEarnings: 625000,
        conversionRate: 16.02,
        topReferralSource: "Facebook",
        compareLastPeriod: {
          newReferrals: 3,
          newEarnings: 375000,
          conversionRate: 12.5,
        },
      };
    }
  }
}

export const affiliateService = new AffiliateService();