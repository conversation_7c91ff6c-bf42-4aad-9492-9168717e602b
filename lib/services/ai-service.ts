// File: lib/services/ai-service.ts
// Những file liên quan đến file này:
// 1. app/(tabs)/chatbot.tsx
// 2. lib/services/chatbot-actions.ts
// 3. lib/helpers/chat-helpers.ts
// 4. lib/types/chatbot-types.ts
// 5. .env (chứa OPENAI_API_KEY)

import * as SecureStore from "expo-secure-store";
import OpenAI from "openai";
import {
  parseTransferRequest,
  parseWalletFromMessage,
} from "../helpers/chat-helpers";
import { AIAnalysisResult } from "../types/chatbot-types";

// Khởi tạo OpenAI client
let openai: OpenAI;

// Hàm khởi tạo OpenAI client với API key bảo mật
export const initializeOpenAI = async (): Promise<boolean> => {
  try {
    // Kiểm tra xem đã lưu API key trong SecureStore chưa
    let apiKey = await SecureStore.getItemAsync("openai_api_key");

    if (!apiKey) {
      // Nếu chưa có trong SecureStore, lấy từ biến môi trường (lần đầu tiên)
      apiKey =
        "********************************************************************************************************************************************************************";

      if (apiKey) {
        // Lưu API key vào SecureStore để sử dụng sau này
        await SecureStore.setItemAsync("openai_api_key", apiKey);
      }
    }

    // Khởi tạo OpenAI client với API key lấy được
    if (apiKey) {
      openai = new OpenAI({
        apiKey: apiKey,
      });
      return true;
    } else {
      console.error("Không tìm thấy API key OpenAI");
      return false;
    }
  } catch (error) {
    console.error("Lỗi khi khởi tạo OpenAI client:", error);
    return false;
  }
};

// Kiểm tra API key có hợp lệ không
export const isApiKeyConfigured = (): boolean => {
  return (
    openai &&
    openai.apiKey &&
    openai.apiKey !== "REPLACE_WITH_YOUR_OPENAI_API_KEY"
  );
};

// Hàm để reset và cập nhật API key (có thể sử dụng sau này trong phần cài đặt)
export const updateOpenAIApiKey = async (
  newApiKey: string
): Promise<boolean> => {
  try {
    await SecureStore.setItemAsync("openai_api_key", newApiKey);
    openai = new OpenAI({
      apiKey: newApiKey,
    });
    return true;
  } catch (error) {
    console.error("Lỗi khi cập nhật API key OpenAI:", error);
    return false;
  }
};

// Function to process user message with OpenAI
export async function processMessageWithAI(
  userMessage: string,
  userPronoun: string,
  botPronoun: string
): Promise<AIAnalysisResult> {
  try {
    // Kiểm tra yêu cầu chuyển tiền trước khi gọi OpenAI
    const transferRequest = await parseTransferRequest(userMessage);
    if (transferRequest && transferRequest.isTransfer) {
      return {
        action: "transfer",
        data: transferRequest,
        confidence: transferRequest.isComplete ? 1.0 : 0.7,
      };
    }

    // Kiểm tra API key trước khi gọi
    if (!isApiKeyConfigured()) {
      // Thử khởi tạo lại OpenAI client nếu chưa được khởi tạo
      const initialized = await initializeOpenAI();
      if (!initialized) {
        console.error("OpenAI API key chưa được cấu hình");
        return {
          action: "api_key_missing",
          data: {},
          confidence: 1.0,
        };
      }
    }

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini", // Nâng cấp từ gpt-3.5-turbo lên gpt-4o-mini
      messages: [
        {
          role: "system",
          content: `Bạn là trợ lý tài chính thông minh trong ứng dụng AI Money.
          Nhiệm vụ của bạn là phân tích tin nhắn người dùng và trích xuất thông tin giao dịch hoặc tác vụ mà họ muốn thực hiện.

          Cách xưng hô: Bạn gọi người dùng là "${userPronoun}" và tự xưng là "${botPronoun}".

          Các loại tác vụ có thể bao gồm:
          1. Tạo giao dịch (thu nhập hoặc chi tiêu)
          2. Tạo ví mới
          3. Cập nhật/sửa ví hiện có
          4. Xem danh sách ví
          5. Xem danh sách giao dịch
          6. Xem báo cáo thu chi
          7. Xem danh mục chi tiêu

          QUAN TRỌNG: Luôn trả về kết quả dưới dạng JSON với định dạng:
          {
            "action": "create_transaction | create_wallet | update_wallet | view_wallets | view_transactions | view_statistics | view_categories | unknown",
            "data": {
              // Dữ liệu tương ứng với hành động
            },
            "confidence": 0.0 to 1.0
          }

          Nếu nội dung là xác nhận như "đồng ý", "xác nhận", "có" thì trả về:
          {
            "action": "confirm",
            "data": {},
            "confidence": 0.9
          }

          Nếu không hiểu được yêu cầu, hãy trả về:
          {
            "action": "unknown",
            "data": {},
            "confidence": 0.0
          }

          Đối với tạo giao dịch, hãy phân tích và gợi ý danh mục phù hợp dựa trên mô tả. Ví dụ:
          - "ăn trưa" -> danh mục "Ăn uống"
          - "đi taxi" -> danh mục "Di chuyển"
          - "mua quần áo" -> danh mục "Mua sắm"
          - "tiền điện" -> danh mục "Sinh hoạt"
          - "lương tháng" -> danh mục "Lương"

          Ví dụ:
          1. Nếu người dùng nói "ăn trưa 150k", trả về:
          {
            "action": "create_transaction",
            "data": {
              "description": "Ăn trưa",
              "amount": 150000,
              "type": "expense",
              "category_keyword": "ăn uống",
              "date": "2023-08-15"
            },
            "confidence": 0.95
          }

          2. Nếu người dùng nói "tạo ví MB Bank số dư 500k", trả về:
          {
            "action": "create_wallet",
            "data": {
              "name": "MB Bank",
              "balance": 500000,
              "type": "bank"
            },
            "confidence": 0.90
          }

          3. Nếu người dùng nói "sửa ví abbank 100tr" hoặc "cập nhật ví abbank 100tr", trả về:
          {
            "action": "update_wallet",
            "data": {
              "name": "abbank",
              "balance": *********,
              "type": "bank"
            },
            "confidence": 0.90
          }

          4. Nếu người dùng nói "ăn sáng 100k vib", trả về:
          {
            "action": "create_transaction",
            "data": {
              "description": "Ăn sáng",
              "amount": 100000,
              "type": "expense",
              "category_keyword": "ăn uống",
              "date": "2023-08-15",
              "wallet_keyword": "vib"
            },
            "confidence": 0.95
          }

          5. Nếu tin nhắn có đề cập đến ví cụ thể, hãy thêm trường "wallet_keyword" vào data.`,
        },
        { role: "user", content: userMessage },
      ],
      temperature: 0.7, // Giữ nguyên temperature
      max_tokens: 500, // Giữ nguyên max tokens
    });

    const aiResponse = response.choices[0].message.content;
    console.log("AI Response:", aiResponse);

    try {
      // Parse the JSON response, kiểm tra null
      if (!aiResponse) {
        return {
          action: "unknown",
          data: {},
          confidence: 0,
        };
      }

      // Xử lý trường hợp phản hồi không phải JSON
      if (
        aiResponse.trim().startsWith("{") &&
        aiResponse.trim().endsWith("}")
      ) {
        try {
          const parsedResponse = JSON.parse(aiResponse);

          // Nếu là tạo giao dịch, kiểm tra xem tin nhắn có chứa tên ví không
          if (
            parsedResponse.action === "create_transaction" &&
            !parsedResponse.data.wallet_keyword
          ) {
            // Thử phân tích tên ví từ tin nhắn
            const walletId = await parseWalletFromMessage(userMessage);
            if (walletId) {
              // Nếu tìm thấy ví, thêm vào dữ liệu
              const wallet = await getWalletById(walletId);
              if (wallet) {
                parsedResponse.data.wallet_keyword = wallet.name.toLowerCase();
                parsedResponse.data.wallet_id = walletId;
              }
            }
          }

          return parsedResponse;
        } catch (parseError) {
          console.error("Error parsing JSON response:", parseError);
          // Cố gắng tìm đoạn JSON trong phản hồi
          const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            try {
              const jsonPart = jsonMatch[0];
              return JSON.parse(jsonPart);
            } catch (innerError) {
              console.error("Failed to extract JSON:", innerError);
            }
          }
        }
      }

      // Nếu không phải JSON hoặc không thể phân tích, xử lý message
      if (
        userMessage.toLowerCase().includes("đồng ý") ||
        userMessage.toLowerCase().includes("xác nhận") ||
        userMessage.toLowerCase().includes("có")
      ) {
        return {
          action: "confirm",
          data: {},
          confidence: 0.9,
        };
      }

      // Trả về unknown nếu không phân tích được
      return {
        action: "unknown",
        data: {},
        confidence: 0,
      };
    } catch (parseError) {
      console.error("Error parsing AI response:", parseError);
      return {
        action: "unknown",
        data: {},
        confidence: 0,
      };
    }
  } catch (error) {
    console.error("Error calling OpenAI API:", error);
    return {
      action: "error",
      data: { message: "Lỗi kết nối với OpenAI" },
      confidence: 0,
    };
  }
}

// Hàm helper nội bộ để lấy thông tin ví theo ID
async function getWalletById(id: string) {
  try {
    // Import WalletModel để tránh circular dependency
    const { WalletModel } = require("@/lib/models/wallet");
    return await WalletModel.getById(id);
  } catch (error) {
    console.error("Error getting wallet by ID:", error);
    return null;
  }
}
