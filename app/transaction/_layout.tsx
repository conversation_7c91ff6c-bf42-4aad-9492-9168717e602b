import { useColorScheme } from "@/hooks/useColorScheme";
import { Stack } from "expo-router";
import { Platform } from "react-native";

export default function TransactionLayout() {
  const { colorScheme } = useColorScheme();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        header: () => null,
        contentStyle: {
          backgroundColor: colorScheme === "dark" ? "#121212" : "#FFFFFF",
        },
        animation: Platform.OS === "android" ? "none" : "default",
        presentation: "card",
      }}
    >
      <Stack.Screen
        name="[id]"
        options={{
          headerShown: false,
          header: () => null,
        }}
      />
    </Stack>
  );
}
