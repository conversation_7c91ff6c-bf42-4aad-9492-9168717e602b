// File: list.tsx
// Đường dẫn: <PERSON><PERSON> thể nằm trong thư mục app/transaction hoặc app/(tabs)/transaction tùy theo cấu trúc của app
// File này liên quan đến: context/ThemeContext.tsx, components/Themed.tsx, lib/models/transaction.ts

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext"; // Import useTheme từ ThemeContext
import { TransactionModel, type Transaction } from "@/lib/models/transaction";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { formatDate, setDateFormatterLocale } from "@/utils/dateFormatter";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function TransactionsList() {
  const { t, locale } = useLocalization();
  // Set locale for date formatter
  setDateFormatterLocale(locale);
  const translateCategoryName = useCategoryTranslation();

  // Sử dụng useTheme hook để lấy theme từ ThemeContext
  const { isDark, themeColors, getCardStyle, getShadowStyle, getIconContainerStyle } = useTheme();

  // Filter array with translation keys
  const FILTERS = [
    { id: "all", name: t("transactions.filterAll") },
    { id: "income", name: t("stats.income") },
    { id: "expense", name: t("stats.expense") },
  ];

  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load transactions when screen gains focus
  useFocusEffect(
    useCallback(() => {
      fetchTransactions();
    }, [])
  );

  // Fetch transactions from Supabase
  const fetchTransactions = async () => {
    try {
      setIsLoading(true);
      const data = await TransactionModel.getAll();
      setTransactions(data);
    } catch (error) {
      console.error("Error fetching transactions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter transactions by type and search query
  const filteredTransactions = transactions.filter((transaction) => {
    // Filter by type (income/expense)
    if (activeFilter === "income" && transaction.amount <= 0) return false;
    if (activeFilter === "expense" && transaction.amount > 0) return false;

    // Filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      return (
        transaction.description.toLowerCase().includes(query) ||
        transaction.category?.name.toLowerCase().includes(query) ||
        transaction.wallet?.name.toLowerCase().includes(query) ||
        false
      );
    }

    return true;
  });

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
      edges={["top"]}
    >
      {/* Custom Header */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("transactions.transactionList")}
        </Text>
        <View style={{ width: 30 }} />
      </View>

      {/* Search */}
      <View
        style={[styles.searchContainer, { backgroundColor: themeColors.background }]}
      >
        <View
          style={[
            styles.searchBar,
            {
              backgroundColor: themeColors.iconBackground,
              borderWidth: 1,
              borderColor: themeColors.border,
              ...getShadowStyle('low'),
            },
          ]}
        >
          <Ionicons
            name="search"
            size={20}
            color={themeColors.primary}
          />
          <TextInput
            style={[styles.searchInput, { color: themeColors.text }]}
            placeholder={t("transactions.searchTransactions")}
            placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Ionicons
                name="close-circle"
                size={20}
                color={themeColors.primary}
              />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* Filters */}
      <View
        style={[styles.filtersContainer, { backgroundColor: themeColors.background }]}
      >
        {FILTERS.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterButton,
              {
                backgroundColor: themeColors.iconBackground,
                borderWidth: 1,
                borderColor:
                  activeFilter === filter.id ? themeColors.primary : themeColors.border,
                ...getShadowStyle('low'),
              },
              activeFilter === filter.id && [
                styles.activeFilterButton,
                {
                  backgroundColor: isDark
                    ? `${themeColors.primary}30`
                    : `${themeColors.primary}20`,
                  borderColor: themeColors.primary,
                },
              ],
            ]}
            onPress={() => setActiveFilter(filter.id)}
          >
            <Text
              style={[
                styles.filterText,
                { color: themeColors.primary },
                activeFilter === filter.id && [
                  styles.activeFilterText,
                  { color: themeColors.primary },
                ],
              ]}
            >
              {filter.name}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Transactions Card Container */}
      <View
        style={[
          styles.transactionsCardContainer,
          getCardStyle('high'),
        ]}
      >
        {/* Loading State */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={themeColors.primary} />
            <Text style={{ marginTop: 12, color: themeColors.text }}>
              {t("common.loading")}
            </Text>
          </View>
        ) : (
          /* Transaction List */
          <FlatList
            data={filteredTransactions}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => {
              const isIncome = item.amount > 0;
              // Dịch tên danh mục
              const categoryName = item.category?.name || item?.description;
              const translatedCategoryName =
                translateCategoryName(categoryName);

              return (
                <TouchableOpacity
                  style={[
                    styles.transactionItem,
                    {
                      backgroundColor: "transparent",
                      borderBottomColor: themeColors.border,
                    },
                  ]}
                  onPress={() => router.push(`/transaction/${item.id}`)}
                >
                  <View
                    style={[
                      styles.transactionLeft,
                      { backgroundColor: "transparent" },
                    ]}
                  >
                    <View
                      style={[
                        styles.categoryIcon,
                        {
                          backgroundColor:
                            item.category?.color ||
                            (isIncome ? "#4CAF50" : "#F44336"),
                          shadowColor:
                            item.category?.color ||
                            (isIncome ? "#4CAF50" : "#F44336"),
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: 0.3,
                          shadowRadius: 3,
                          elevation: 3,
                        },
                      ]}
                    >
                      <Ionicons
                        name={
                          (item.category?.icon ||
                            (isIncome ? "cash" : "cart")) as any
                        }
                        size={18}
                        color="white"
                      />
                    </View>
                    <View style={{ backgroundColor: "transparent", flex: 1 }}>
                      <Text
                        style={[styles.transactionTitle, { color: themeColors.text }]}
                      >
                        {translatedCategoryName}
                      </Text>
                      <Text
                        style={[
                          styles.transactionDetails,
                          { color: themeColors.primary },
                        ]}
                      >
                        {formatDate(new Date(item.date))} •{" "}
                        {item.wallet?.name || t("transactions.undefinedWallet")}
                      </Text>
                    </View>
                  </View>
                  <Text
                    style={[
                      styles.transactionAmount,
                      {
                        color: isIncome
                          ? isDark
                            ? "#81C784"
                            : "#4CAF50"
                          : themeColors.danger,
                      },
                    ]}
                  >
                    {isIncome ? "+" : ""}
                    {Math.abs(item.amount).toLocaleString()}đ
                  </Text>
                </TouchableOpacity>
              );
            }}
            contentContainerStyle={styles.transactionsList}
            ListEmptyComponent={
              <View
                style={[
                  styles.emptyContainer,
                  { backgroundColor: "transparent" },
                ]}
              >
                <Ionicons
                  name="search"
                  size={60}
                  color={themeColors.primary}
                />
                <Text
                  style={[
                    styles.emptyText,
                    { color: themeColors.primary },
                  ]}
                >
                  {t("transactions.noTransactions")}
                </Text>
              </View>
            }
          />
        )}
      </View>

      {/* Add Button */}
      <TouchableOpacity
        style={[
          styles.addButton,
          {
            backgroundColor: themeColors.primary,
            ...getShadowStyle('high'),
          },
        ]}
        onPress={() => router.push("/transaction/new")}
      >
        <Ionicons name="add" size={28} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    borderRadius: 12,
    height: 48,
  },
  searchInput: {
    flex: 1,
    height: 48,
    marginLeft: 8,
    fontSize: 16,
  },
  filtersContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 12,
  },
  activeFilterButton: {
    borderWidth: 1,
  },
  filterText: {
    fontSize: 14,
    fontWeight: "500",
  },
  activeFilterText: {
    fontWeight: "600",
  },
  transactionsCardContainer: {
    flex: 1,
    margin: 16,
    marginTop: 8,
    borderRadius: 16,
    overflow: "hidden",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 30,
  },
  transactionsList: {
    paddingHorizontal: 5,
  },
  transactionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 14,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  transactionDetails: {
    fontSize: 14,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 80,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
  },
  addButton: {
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
  },
});