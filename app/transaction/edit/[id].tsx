// File: app/transaction/edit/[id].tsx
// File này liên quan đến: context/ThemeContext.tsx, lib/models/transaction.ts, lib/models/wallet.ts, lib/models/category.ts, constants/WalletData.ts

import { Text, View } from "@/components/Themed";
import { BankLogos, isImageIcon } from "@/constants/WalletData";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { CategoryModel, type Category } from "@/lib/models/category";
import {
  TransactionModel,
  type Transaction,
  type TransactionUpdateInput,
} from "@/lib/models/transaction";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { setDateFormatterLocale } from "@/utils/dateFormatter";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function EditTransactionScreen() {
  const { id } = useLocalSearchParams();
  const transactionId = typeof id === "string" ? id : "";
  const { t, locale } = useLocalization();
  const translateCategoryName = useCategoryTranslation();

  // Set locale for date formatter
  setDateFormatterLocale(locale);

  // Sử dụng useTheme hook để lấy theme từ ThemeContext
  const { isDark, themeColors, getCardStyle, getShadowStyle } = useTheme();
  const { currency } = useCurrency();

  // Kích thước màn hình
  const { width, height } = Dimensions.get("window");

  // Animation cho modal
  const modalAnimation = useRef(new Animated.Value(0)).current;

  // Animated value cho toast
  const toastAnimation = useRef(new Animated.Value(0)).current;
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // Initial state for form
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Form state
  const [description, setDescription] = useState("");
  const [amount, setAmount] = useState("");
  const [transactionType, setTransactionType] = useState<"expense" | "income">(
    "expense"
  );
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [mode, setMode] = useState<"date" | "time">("date");

  // State cho popup chọn danh mục và ví
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showWalletModal, setShowWalletModal] = useState(false);

  // Get filtered categories based on transaction type
  const categories = useMemo(() => {
    return allCategories.filter((cat) => cat.type === transactionType);
  }, [allCategories, transactionType]);

  // Fetch transaction data and related data (wallets, categories)
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Load transaction
        if (!transactionId) {
          throw new Error("Transaction ID is required");
        }
        const transactionData = await TransactionModel.getById(transactionId);
        if (!transactionData) {
          throw new Error("Transaction not found");
        }
        setTransaction(transactionData);

        // Set form values
        setDescription(transactionData.description || "");
        setAmount(Math.abs(transactionData.amount).toString());
        setTransactionType(transactionData.amount > 0 ? "income" : "expense");
        setSelectedWallet(transactionData.wallet_id);
        setSelectedCategory(transactionData.category_id);
        setDate(new Date(transactionData.date));

        // Load wallets
        const walletsData = await WalletModel.getAll();
        setWallets(walletsData);

        // Load categories
        const categoriesData = await CategoryModel.getAll();
        setAllCategories(categoriesData);
      } catch (error) {
        console.error("Error loading edit data:", error);
        Alert.alert(t("common.error"), t("transactions.loadError"));
        router.back();
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [transactionId]);

  // Animation cho modal khi mở
  const animateModal = (show: boolean) => {
    Animated.timing(modalAnimation, {
      toValue: show ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Hàm hiển thị thông báo toast
  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);

    // Animation hiển thị toast
    Animated.sequence([
      // Hiện lên
      Animated.timing(toastAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Giữ 3 giây
      Animated.delay(3000),
      // Ẩn đi
      Animated.timing(toastAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowToast(false);
    });
  };

  // Format the amount with thousand separators
  const formattedAmount = amount ? parseInt(amount, 10).toLocaleString() : "";

  // Handle amount input
  const handleAmountChange = (text: string) => {
    // Only allow numeric input
    const numericValue = text.replace(/[^0-9]/g, "");
    setAmount(numericValue);
  };

  // Hàm chuyển đổi loại giao dịch khi bấm vào dấu +/-
  const toggleTransactionTypeSign = () => {
    setTransactionType(transactionType === "expense" ? "income" : "expense");
  };

  // Show date/time picker
  const showDateTimePicker = (currentMode: "date" | "time") => {
    setMode(currentMode);
    if (Platform.OS === "android") {
      setShowDatePicker(true);
    } else {
      if (currentMode === "date") {
        setShowDatePicker(true);
      } else {
        setShowTimePicker(true);
      }
    }
  };

  // Handle date/time change
  const handleDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || date;

    if (Platform.OS === "android") {
      setShowDatePicker(false);
      if (mode === "date" && event.type !== "dismissed") {
        setDate(currentDate);
        // On Android, after setting date, automatically show time picker
        setTimeout(() => {
          showDateTimePicker("time");
        }, 500);
      } else if (mode === "time" && event.type !== "dismissed") {
        setDate(currentDate);
      }
    } else {
      // iOS behavior
      if (mode === "date") {
        setShowDatePicker(false);
      } else {
        setShowTimePicker(false);
      }
      setDate(currentDate);
    }
  };

  // Format date for display
  const formatDateDisplay = (date: Date) => {
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${t("dateTime.today")}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${t("dateTime.yesterday")}`;
    } else {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${date.getDate()}/${date.getMonth() + 1}`;
    }
  };

  // Hàm mở/đóng modal chọn category
  const toggleCategoryModal = (show: boolean) => {
    if (show) {
      setShowCategoryModal(true);
      animateModal(true);
    } else {
      animateModal(false);
      setTimeout(() => setShowCategoryModal(false), 300);
    }
  };

  // Hàm mở/đóng modal chọn wallet
  const toggleWalletModal = (show: boolean) => {
    if (show) {
      setShowWalletModal(true);
      animateModal(true);
    } else {
      animateModal(false);
      setTimeout(() => setShowWalletModal(false), 300);
    }
  };

  // Handle deleting transaction
  const handleDelete = () => {
    Alert.alert(
      t("common.delete"),
      t("transactions.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              setIsLoading(true);
              await TransactionModel.delete(transactionId);
              showToastMessage(t("transactions.transactionDeleted"));

              // Delay để hiển thị thông báo trước khi chuyển hướng
              setTimeout(() => {
                router.replace("/(tabs)");
              }, 500);
            } catch (error) {
              console.error("Lỗi khi xóa giao dịch:", error);
              Alert.alert(t("common.error"), t("transactions.deleteError"));
              setIsLoading(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleSave = async () => {
    try {
      // Validate inputs
      if (!amount.trim() || isNaN(Number(amount))) {
        Alert.alert(
          t("common.error"),
          t("addTransaction.error.amountRequired")
        );
        return;
      }

      if (selectedCategory === null) {
        Alert.alert(
          t("common.error"),
          t("addTransaction.error.categoryRequired")
        );
        return;
      }

      if (selectedWallet === null) {
        Alert.alert(
          t("common.error"),
          t("addTransaction.error.walletRequired")
        );
        return;
      }

      setIsSaving(true);

      // Lấy thông tin giao dịch gốc để so sánh
      const originalTransaction = await TransactionModel.getById(transactionId);
      if (!originalTransaction) {
        throw new Error("Không tìm thấy thông tin giao dịch");
      }

      // Lấy thông tin ví hiện tại
      const wallet = await WalletModel.getById(selectedWallet);
      if (!wallet) {
        throw new Error("Không tìm thấy thông tin ví");
      }

      // Tính toán số tiền mới
      const newAmount =
        transactionType === "expense"
          ? -Math.abs(Number(amount))
          : Math.abs(Number(amount));

      // Tạo dữ liệu cập nhật giao dịch
      const transactionData: TransactionUpdateInput = {
        description: description.trim(),
        amount: newAmount,
        type: transactionType,
        wallet_id: selectedWallet,
        category_id: selectedCategory,
        date: date.toISOString(),
      };

      // Cập nhật giao dịch
      await TransactionModel.update(transactionId, transactionData);

      // Tính toán chênh lệch số dư ví dựa trên sự thay đổi của giao dịch
      let walletBalanceChange = 0;

      // Nếu ví không thay đổi, cập nhật số dư dựa trên chênh lệch số tiền
      if (originalTransaction.wallet_id === selectedWallet) {
        // Tính toán chênh lệch giữa số tiền mới và số tiền cũ
        const amountDifference = newAmount - originalTransaction.amount;

        // Cập nhật số dư ví dựa trên chênh lệch
        walletBalanceChange = amountDifference;
      } else {
        // Nếu ví thay đổi, cần xử lý cả ví cũ và ví mới
        // 1. Hoàn trả số tiền cho ví cũ
        const oldWallet = await WalletModel.getById(
          originalTransaction.wallet_id
        );
        if (oldWallet) {
          let oldWalletNewBalance = oldWallet.balance;
          if (originalTransaction.type === "income") {
            // Nếu là thu nhập, trừ đi số tiền khi xóa khỏi ví cũ
            oldWalletNewBalance =
              oldWallet.balance - Math.abs(originalTransaction.amount);
          } else {
            // Nếu là chi tiêu, cộng lại số tiền khi xóa khỏi ví cũ
            oldWalletNewBalance =
              oldWallet.balance + Math.abs(originalTransaction.amount);
          }
          await WalletModel.update(originalTransaction.wallet_id, {
            balance: oldWalletNewBalance,
          });
        }

        // 2. Thêm số tiền mới vào ví mới
        if (transactionType === "income") {
          walletBalanceChange = Math.abs(newAmount);
        } else {
          walletBalanceChange = -Math.abs(newAmount);
        }
      }

      // Cập nhật số dư ví hiện tại
      const newBalance = wallet.balance + walletBalanceChange;
      await WalletModel.update(selectedWallet, {
        balance: newBalance,
      });

      // Hiển thị thông báo thành công
      showToastMessage(t("transactions.transactionUpdated"));

      // Chờ hiển thị thông báo xong rồi quay lại
      setTimeout(() => {
        router.replace("/(tabs)");
      }, 500);
    } catch (error) {
      console.error("Error updating transaction:", error);
      Alert.alert(t("common.error"), t("transactions.updateError"));
      setIsSaving(false);
    }
  };

  // Lấy thông tin của category đã chọn
  const selectedCategoryInfo = useMemo(() => {
    return categories.find((cat) => cat.id === selectedCategory);
  }, [categories, selectedCategory]);

  // Lấy thông tin của wallet đã chọn
  const selectedWalletInfo = useMemo(() => {
    return wallets.find((wallet) => wallet.id === selectedWallet);
  }, [wallets, selectedWallet]);

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View
          style={[styles.header, { backgroundColor: themeColors.background }]}
        >
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("transactions.editTransaction")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Màu sắc từ themeColors
  const backgroundColor = themeColors.background;
  const primaryColor = themeColors.primary;
  const cardBgColor = themeColors.cardBackground;
  const textColor = themeColors.text;
  const borderColor = themeColors.border;
  const secondaryTextColor = themeColors.secondaryText;
  const inputBgColor = themeColors.iconBackground;
  const modalBgColor = isDark ? "rgba(0, 0, 0, 0.8)" : "rgba(0, 0, 0, 0.5)";

  // Màu cho dấu -/+
  const expenseColor = themeColors.danger; // Màu đỏ cho chi tiêu (-)
  const incomeColor = "#4CAF50"; // Màu xanh lá cho thu nhập (+)

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor }]}
    >
      {/* Toast Notification ở giữa màn hình */}
      {showToast && (
        <Animated.View
          style={[
            styles.toastContainer,
            {
              transform: [
                {
                  translateY: toastAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
              opacity: toastAnimation,
            },
          ]}
        >
          <View
            style={[
              styles.toast,
              {
                backgroundColor: primaryColor,
                ...getShadowStyle("medium"),
              },
            ]}
          >
            <Ionicons name="checkmark-circle-outline" size={24} color="white" />
            <Text style={styles.toastMessage}>{toastMessage}</Text>
          </View>
        </Animated.View>
      )}

      <View style={[styles.header, { backgroundColor }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={textColor} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: textColor }]}>
          {t("transactions.editTransaction")}
        </Text>
        <TouchableOpacity
          style={[
            styles.deleteHeaderButton,
            {
              backgroundColor:
                amount && selectedCategory !== null && selectedWallet !== null
                  ? primaryColor
                  : isDark
                  ? "#375980"
                  : "#BBDEFB",
              ...getShadowStyle("low"),
            },
          ]}
          onPress={handleSave}
        >
          <Text
            style={{
              color:
                amount && selectedCategory !== null && selectedWallet !== null
                  ? "white"
                  : isDark
                  ? secondaryTextColor
                  : "#90CAF9",
              fontWeight: "600",
            }}
          >
            {t("common.save")}
          </Text>
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          style={{ backgroundColor }}
          showsVerticalScrollIndicator={false}
        >
          {/* Chọn loại giao dịch */}
          <View style={[styles.typeSelector, { backgroundColor }]}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    transactionType === "expense" ? primaryColor : inputBgColor,
                  borderColor: borderColor,
                  borderWidth: 1,
                  ...getShadowStyle(
                    transactionType === "expense" ? "medium" : "low"
                  ),
                },
              ]}
              onPress={() => setTransactionType("expense")}
            >
              <Ionicons
                name="arrow-down-circle"
                size={24}
                color={
                  transactionType === "expense" ? "white" : themeColors.danger
                }
              />
              <Text
                style={[
                  styles.typeText,
                  {
                    color: transactionType === "expense" ? "white" : textColor,
                  },
                ]}
              >
                {t("addTransaction.expenseTab")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    transactionType === "income" ? primaryColor : inputBgColor,
                  borderColor: borderColor,
                  borderWidth: 1,
                  ...getShadowStyle(
                    transactionType === "income" ? "medium" : "low"
                  ),
                },
              ]}
              onPress={() => setTransactionType("income")}
            >
              <Ionicons
                name="arrow-up-circle"
                size={24}
                color={transactionType === "income" ? "white" : "#4CAF50"}
              />
              <Text
                style={[
                  styles.typeText,
                  {
                    color: transactionType === "income" ? "white" : textColor,
                  },
                ]}
              >
                {t("addTransaction.incomeTab")}
              </Text>
            </TouchableOpacity>
          </View>

          <View
            style={[
              styles.card,
              getCardStyle("high"),
              {
                marginHorizontal: 16,
              },
            ]}
          >
            {/* Nhập số tiền với dấu +/- */}
            <View style={[styles.section, { backgroundColor: cardBgColor }]}>
              <Text style={[styles.label, { color: textColor }]}>
                {t("addTransaction.amount")}
              </Text>
              <View
                style={[
                  styles.amountContainer,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: borderColor,
                    borderWidth: 1,
                    ...getShadowStyle("low"),
                  },
                ]}
              >
                {/* Nút dấu +/- */}
                <TouchableOpacity
                  style={styles.signButton}
                  onPress={toggleTransactionTypeSign}
                >
                  <Text
                    style={[
                      styles.sign,
                      {
                        color:
                          transactionType === "expense"
                            ? expenseColor
                            : incomeColor,
                        fontSize: 34,
                      },
                    ]}
                  >
                    {transactionType === "expense" ? "-" : "+"}
                  </Text>
                </TouchableOpacity>

                <TextInput
                  style={[styles.amountInput, { color: textColor }]}
                  keyboardType="numeric"
                  placeholder="0"
                  placeholderTextColor={secondaryTextColor}
                  value={formattedAmount}
                  onChangeText={handleAmountChange}
                />
                <Text style={[styles.currency, { color: secondaryTextColor }]}>
                  {currency}
                </Text>
              </View>
            </View>

            {/* Hàng ngang chứa ví và danh mục */}
            <View
              style={[styles.rowContainer, { backgroundColor: cardBgColor }]}
            >
              {/* Phần ví bên trái */}
              <View
                style={[styles.columnSection, { backgroundColor: cardBgColor }]}
              >
                <Text style={[styles.label, { color: textColor }]}>
                  {t("addTransaction.wallet")}
                </Text>

                {/* Nút chọn ví */}
                <TouchableOpacity
                  style={[
                    styles.selectorButton,
                    {
                      backgroundColor: inputBgColor,
                      borderColor: selectedWallet
                        ? selectedWalletInfo?.color || borderColor
                        : borderColor,
                      borderWidth: 1,
                      ...getShadowStyle("low"),
                      height: 60,
                    },
                  ]}
                  onPress={() => toggleWalletModal(true)}
                >
                  {selectedWallet ? (
                    <View
                      style={[
                        styles.selectedItemContainer,
                        { backgroundColor: inputBgColor },
                      ]}
                    >
                      <View
                        style={[
                          styles.smallWalletIcon,
                          {
                            backgroundColor:
                              selectedWalletInfo?.color || primaryColor,
                            ...getShadowStyle("medium"),
                          },
                        ]}
                      >
                        {isImageIcon(selectedWalletInfo?.icon || "") ? (
                          <Image
                            source={BankLogos[selectedWalletInfo?.icon || ""]}
                            style={{ width: 20, height: 20 }}
                            contentFit="contain"
                          />
                        ) : (
                          <Ionicons
                            name={(selectedWalletInfo?.icon as any) || "wallet"}
                            size={18}
                            color="white"
                          />
                        )}
                      </View>
                      <View style={{ backgroundColor: "transparent" }}>
                        <Text
                          style={[
                            styles.smallWalletName,
                            { color: textColor, flex: 1 },
                          ]}
                          numberOfLines={1}
                        >
                          {selectedWalletInfo?.name || ""}
                        </Text>
                        <Text
                          style={[
                            styles.smallWalletBalance,
                            { color: secondaryTextColor },
                          ]}
                        >
                          {selectedWalletInfo?.balance.toLocaleString() || "0"}đ
                        </Text>
                      </View>
                    </View>
                  ) : (
                    <View
                      style={[
                        styles.selectedItemContainer,
                        {
                          backgroundColor: inputBgColor,
                        },
                      ]}
                    >
                      <Ionicons
                        name="wallet-outline"
                        size={22}
                        color={secondaryTextColor}
                      />
                      <Text
                        numberOfLines={2}
                        style={[
                          styles.smallPlaceholderText,
                          { color: secondaryTextColor },
                        ]}
                      >
                        {t("addTransaction.selectFromWallet") || "Chọn ví"}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>

              {/* Phần danh mục bên phải */}
              <View
                style={[styles.columnSection, { backgroundColor: cardBgColor }]}
              >
                <Text style={[styles.label, { color: textColor }]}>
                  {t("addTransaction.category")}
                </Text>

                {/* Nút chọn danh mục */}
                <TouchableOpacity
                  style={[
                    styles.selectorButton,
                    {
                      backgroundColor: inputBgColor,
                      borderColor: selectedCategory
                        ? selectedCategoryInfo?.color || borderColor
                        : borderColor,
                      borderWidth: 1,
                      ...getShadowStyle("low"),
                      height: 60,
                    },
                  ]}
                  onPress={() => toggleCategoryModal(true)}
                >
                  {selectedCategory ? (
                    <View
                      style={[
                        styles.selectedItemContainer,
                        { backgroundColor: inputBgColor },
                      ]}
                    >
                      <View
                        style={[
                          styles.smallCategoryIcon,
                          {
                            backgroundColor:
                              selectedCategoryInfo?.color || primaryColor,
                            ...getShadowStyle("medium"),
                          },
                        ]}
                      >
                        <Ionicons
                          name={
                            (selectedCategoryInfo?.icon as any) || "help-circle"
                          }
                          size={20}
                          color="white"
                        />
                      </View>
                      <Text
                        style={[
                          styles.smallSelectedItemText,
                          { color: textColor, flex: 1 },
                        ]}
                        numberOfLines={1}
                      >
                        {translateCategoryName(
                          selectedCategoryInfo?.name || ""
                        )}
                      </Text>
                    </View>
                  ) : (
                    <View
                      style={[
                        styles.selectedItemContainer,
                        { backgroundColor: inputBgColor },
                      ]}
                    >
                      <Ionicons
                        name="list"
                        size={22}
                        color={secondaryTextColor}
                      />
                      <Text
                        style={[
                          styles.smallPlaceholderText,
                          { color: secondaryTextColor },
                        ]}
                      >
                        {t("addTransaction.selectCategory") || "Chọn danh mục"}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Mô tả */}
            <View style={[styles.section, { backgroundColor: cardBgColor }]}>
              <Text style={[styles.label, { color: textColor }]}>
                {t("addTransaction.note")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  styles.textAreaContainer,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: borderColor,
                    borderWidth: 1,
                    ...getShadowStyle("low"),
                    height: 80,
                  },
                ]}
              >
                <TextInput
                  style={[
                    styles.input,
                    styles.textArea,
                    {
                      color: textColor,
                      height: 80,
                    },
                  ]}
                  multiline
                  numberOfLines={3}
                  placeholder={
                    t("addTransaction.note") + " (" + t("common.optional") + ")"
                  }
                  placeholderTextColor={secondaryTextColor}
                  value={description}
                  onChangeText={setDescription}
                  textAlignVertical="top"
                />
              </View>
            </View>

            {/* Ngày giao dịch */}
            <View
              style={[styles.smallSection, { backgroundColor: cardBgColor }]}
            >
              <Text style={[styles.smallLabel, { color: textColor }]}>
                {t("addTransaction.transactionDate")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.smallDatePickerButton,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: borderColor,
                    borderWidth: 1,
                    ...getShadowStyle("low"),
                  },
                ]}
                onPress={() => showDateTimePicker("date")}
              >
                <Ionicons
                  name="calendar-outline"
                  size={18}
                  color={secondaryTextColor}
                />
                <Text style={[styles.smallDateText, { color: textColor }]}>
                  {formatDateDisplay(date)}
                </Text>
              </TouchableOpacity>

              {/* Date Picker */}
              {showDatePicker && (
                <DateTimePicker
                  testID="dateTimePicker"
                  value={date}
                  mode={mode}
                  is24Hour={true}
                  display={Platform.OS === "ios" ? "spinner" : "default"}
                  onChange={handleDateChange}
                  themeVariant={isDark ? "dark" : "light"}
                />
              )}

              {/* Time Picker (iOS only) */}
              {Platform.OS === "ios" && showTimePicker && (
                <DateTimePicker
                  testID="timeTimePicker"
                  value={date}
                  mode="time"
                  is24Hour={true}
                  display="spinner"
                  onChange={handleDateChange}
                  themeVariant={isDark ? "dark" : "light"}
                />
              )}
            </View>
          </View>

          {/* Nút lưu */}
          <TouchableOpacity
            style={[
              styles.saveButton,
              {
                backgroundColor:
                  amount && selectedCategory !== null && selectedWallet !== null
                    ? primaryColor
                    : isDark
                    ? "#375980"
                    : "#BBDEFB",
                marginTop: 20,
                ...getShadowStyle("medium"),
              },
            ]}
            onPress={handleSave}
            disabled={
              !amount ||
              selectedCategory === null ||
              selectedWallet === null ||
              isSaving
            }
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text
                style={[
                  styles.saveButtonText,
                  {
                    color:
                      amount &&
                      selectedCategory !== null &&
                      selectedWallet !== null
                        ? "white"
                        : secondaryTextColor,
                  },
                ]}
              >
                {t("transactions.updateTransaction")}
              </Text>
            )}
          </TouchableOpacity>

          {/* Nút xóa */}
          <TouchableOpacity
            style={[
              styles.deleteButton,
              {
                backgroundColor: isDark ? "#3B1F1F" : "#FFEBEE",
                marginTop: 16,
                ...getShadowStyle("medium"),
              },
            ]}
            onPress={handleDelete}
          >
            <Ionicons
              name="trash-outline"
              size={22}
              color={themeColors.danger}
              style={{ marginRight: 8 }}
            />
            <Text
              style={{
                color: themeColors.danger,
                fontSize: 16,
                fontWeight: "600",
              }}
            >
              {t("transactions.deleteTransaction")}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Modal chọn danh mục */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => toggleCategoryModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => toggleCategoryModal(false)}>
          <View
            style={[styles.modalOverlay, { backgroundColor: modalBgColor }]}
          >
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  backgroundColor: cardBgColor,
                  transform: [
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [500, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, { color: textColor }]}>
                      {t("addTransaction.selectCategory") || "Chọn danh mục"}
                    </Text>
                    <TouchableOpacity
                      onPress={() => toggleCategoryModal(false)}
                    >
                      <Ionicons name="close" size={24} color={textColor} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.modalContent}>
                    {categories.length === 0 ? (
                      <Text
                        style={{
                          color: textColor,
                          textAlign: "center",
                          width: "100%",
                          padding: 20,
                        }}
                      >
                        {t("addTransaction.noCategoriesMessage")}
                      </Text>
                    ) : (
                      <ScrollView style={{ maxHeight: height * 0.5 }}>
                        <View style={styles.categoriesGrid}>
                          {categories.map((category) => (
                            <TouchableOpacity
                              key={category.id}
                              style={[
                                styles.categoryItem,
                                {
                                  backgroundColor: inputBgColor,
                                  borderColor:
                                    selectedCategory === category.id
                                      ? category.color
                                      : borderColor,
                                  borderWidth: 1,
                                  ...getShadowStyle(
                                    selectedCategory === category.id
                                      ? "medium"
                                      : "low"
                                  ),
                                },
                              ]}
                              onPress={() => {
                                setSelectedCategory(category.id);
                                toggleCategoryModal(false);
                              }}
                            >
                              <View
                                style={[
                                  styles.categoryIcon,
                                  {
                                    backgroundColor: category.color,
                                    ...getShadowStyle("medium"),
                                  },
                                ]}
                              >
                                <Ionicons
                                  name={category.icon as any}
                                  size={24}
                                  color="white"
                                />
                              </View>
                              <Text
                                style={[
                                  styles.categoryName,
                                  { color: textColor },
                                ]}
                              >
                                {translateCategoryName(category.name)}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    )}
                  </View>

                  <View style={styles.modalFooter}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: primaryColor },
                      ]}
                      onPress={() => toggleCategoryModal(false)}
                    >
                      <Text style={styles.closeButtonText}>
                        {t("addTransaction.closeButton") || "Đóng"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Modal chọn ví */}
      <Modal
        visible={showWalletModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => toggleWalletModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => toggleWalletModal(false)}>
          <View
            style={[styles.modalOverlay, { backgroundColor: modalBgColor }]}
          >
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  backgroundColor: cardBgColor,
                  transform: [
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [500, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, { color: textColor }]}>
                      {t("addTransaction.selectFromWallet") || "Chọn ví"}
                    </Text>
                    <TouchableOpacity onPress={() => toggleWalletModal(false)}>
                      <Ionicons name="close" size={24} color={textColor} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.modalContent}>
                    {wallets.length === 0 ? (
                      <Text
                        style={{
                          color: textColor,
                          textAlign: "center",
                          width: "100%",
                          padding: 20,
                        }}
                      >
                        {t("addTransaction.noWalletsMessage")}
                      </Text>
                    ) : (
                      <ScrollView style={{ maxHeight: height * 0.6 }}>
                        <View style={styles.walletsGrid}>
                          {wallets.map((wallet) => (
                            <TouchableOpacity
                              key={wallet.id}
                              style={[
                                styles.walletItem,
                                {
                                  backgroundColor: inputBgColor,
                                  borderColor:
                                    selectedWallet === wallet.id
                                      ? wallet.color
                                      : borderColor,
                                  borderWidth: 1,
                                  ...getShadowStyle(
                                    selectedWallet === wallet.id
                                      ? "medium"
                                      : "low"
                                  ),
                                },
                              ]}
                              onPress={() => {
                                setSelectedWallet(wallet.id);
                                toggleWalletModal(false);
                              }}
                            >
                              <View
                                style={[
                                  styles.walletIconGrid,
                                  {
                                    backgroundColor: wallet.color,
                                    ...getShadowStyle("medium"),
                                  },
                                ]}
                              >
                                {isImageIcon(wallet.icon) ? (
                                  <Image
                                    source={BankLogos[wallet.icon]}
                                    style={{ width: 30, height: 30 }}
                                    contentFit="contain"
                                  />
                                ) : (
                                  <Ionicons
                                    name={wallet.icon as any}
                                    size={24}
                                    color="white"
                                  />
                                )}
                              </View>
                              <Text
                                style={[
                                  styles.walletNameGrid,
                                  { color: textColor },
                                ]}
                              >
                                {wallet.name}
                              </Text>
                              <Text
                                style={[
                                  styles.walletBalanceGrid,
                                  { color: secondaryTextColor },
                                ]}
                              >
                                {wallet.balance.toLocaleString()}đ
                              </Text>
                              {selectedWallet === wallet.id && (
                                <View style={styles.checkmarkContainer}>
                                  <Ionicons
                                    name="checkmark-circle"
                                    size={24}
                                    color={wallet.color}
                                  />
                                </View>
                              )}
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    )}
                  </View>

                  <View style={styles.modalFooter}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: primaryColor },
                      ]}
                      onPress={() => toggleWalletModal(false)}
                    >
                      <Text style={styles.closeButtonText}>
                        {t("addTransaction.closeButton") || "Đóng"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  deleteHeaderButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 10,
  },
  // Toast Notification
  toastContainer: {
    position: "absolute",
    top: "50%",
    left: 0,
    right: 0,
    zIndex: 9999,
    alignItems: "center",
    paddingHorizontal: 16,
    transform: [{ translateY: -50 }],
  },
  toast: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 250,
    maxWidth: "80%",
  },
  toastMessage: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 12,
    flex: 1,
  },
  card: {
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 4,
    overflow: "hidden",
  },
  typeSelector: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    gap: 10,
  },
  typeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    paddingVertical: 12,
    borderRadius: 16,
  },
  typeText: {
    marginLeft: 6,
    fontWeight: "600",
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  // Styles for smaller sections
  smallSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  smallLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
    paddingLeft: 4,
  },
  smallDatePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    padding: 12,
    height: 50,
  },
  smallDateText: {
    marginLeft: 8,
    fontSize: 14,
  },
  // Row container styles
  rowContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: "space-between",
  },
  columnSection: {
    width: "48%",
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
    paddingLeft: 4,
  },
  // Amount input styles
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    height: 60,
    borderRadius: 14,
  },
  signButton: {
    paddingRight: 8,
    paddingLeft: 12,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    display: "flex",
  },
  sign: {
    fontSize: 24,
    fontWeight: "bold",
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: "bold",
  },
  currency: {
    fontSize: 16,
    fontWeight: "600",
  },
  // Text area styles
  inputContainer: {
    borderRadius: 14,
    height: 60,
  },
  input: {
    flex: 1,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  textAreaContainer: {
    height: 80,
  },
  textArea: {
    paddingTop: 16,
    height: 80,
    textAlignVertical: "top",
  },
  // Selector button styles
  selectorButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 14,
    height: 70,
  },
  selectedItemContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  // Small wallet and category icon styles
  smallWalletIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  smallWalletName: {
    fontSize: 14,
    fontWeight: "600",
  },
  smallWalletBalance: {
    fontSize: 12,
    marginTop: 2,
  },
  smallPlaceholderText: {
    fontSize: 14,
    marginLeft: 10,
  },
  smallSelectedItemText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 10,
  },
  smallCategoryIcon: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalContainer: {
    width: "100%",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: "hidden",
    paddingBottom: Platform.OS === "ios" ? 20 : 0,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(150, 150, 150, 0.2)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
  },
  modalContent: {
    padding: 16,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(150, 150, 150, 0.2)",
    alignItems: "center",
  },
  closeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    minWidth: 120,
  },
  closeButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  // Category grid styles
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 8,
    paddingVertical: 8,
    justifyContent: "space-between",
  },
  categoryItem: {
    width: "30%",
    padding: 10,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 14,
    margin: 4,
  },
  categoryIcon: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 6,
  },
  categoryName: {
    fontSize: 11,
    textAlign: "center",
    fontWeight: "500",
  },
  // Wallet grid styles
  walletsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 8,
    paddingVertical: 8,
    justifyContent: "space-between",
  },
  walletItem: {
    width: "46%",
    padding: 12,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 14,
    margin: 6,
    position: "relative",
    minHeight: 130,
  },
  walletIconGrid: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  walletNameGrid: {
    fontSize: 14,
    textAlign: "center",
    fontWeight: "600",
    marginBottom: 5,
  },
  walletBalanceGrid: {
    fontSize: 13,
    textAlign: "center",
    fontWeight: "500",
  },
  checkmarkContainer: {
    position: "absolute",
    top: 5,
    right: 5,
  },
  // Button styles
  saveButton: {
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: "700",
  },
  deleteButton: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 30,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
  },
});
