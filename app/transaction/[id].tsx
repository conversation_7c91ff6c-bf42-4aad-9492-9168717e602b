// File: [id].tsx
// Đường dẫn: <PERSON><PERSON> thể nằm trong thư mục app/transaction hoặc app/(tabs)/transaction tùy theo cấu trúc của app
// File này liên quan đến: context/ThemeContext.tsx, lib/models/transaction.ts, lib/models/wallet.ts

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext"; // Import useTheme từ ThemeContext
import { TransactionModel, type Transaction } from "@/lib/models/transaction";
import { WalletModel } from "@/lib/models/wallet";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { formatDate, setDateFormatterLocale } from "@/utils/dateFormatter";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect, useLocalSearchParams } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function TransactionDetailScreen() {
  const { id } = useLocalSearchParams();
  const transactionId = typeof id === "string" ? id : "";
  const { t, locale } = useLocalization();
  const translateCategoryName = useCategoryTranslation();

  // Set locale for date formatter
  setDateFormatterLocale(locale);

  // Sử dụng useTheme hook để lấy theme từ ThemeContext
  const { isDark, themeColors, getCardStyle, getShadowStyle, getIconContainerStyle } = useTheme();

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchTransaction = async () => {
    try {
      setIsLoading(true);
      if (!transactionId) return;

      const data = await TransactionModel.getById(transactionId);
      setTransaction(data);
    } catch (error) {
      console.error("Error fetching transaction:", error);
      Alert.alert(t("common.error"), t("addTransaction.error.saveFailed"));
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchTransaction();
  }, [transactionId]);

  useFocusEffect(
    useCallback(() => {
      fetchTransaction();
    }, [])
  );

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("transactions.transactionDetails")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!transaction) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("transactions.transactionDetails")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View
          style={[styles.emptyContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={themeColors.primary}
          />
          <Text
            style={{
              color: themeColors.primary,
              fontSize: 16,
              marginTop: 12,
            }}
          >
            {t("transactions.transactionNotFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Format the date from transaction.date
  const isIncome = transaction.amount > 0;
  const formattedDate = formatDate(new Date(transaction.date));
  // Dịch tên danh mục
  const categoryName = transaction.category?.name || transaction?.description;
  const translatedCategoryName = translateCategoryName(categoryName);

  // Handle edit transaction
  const handleEdit = () => {
    router.push(`/transaction/edit/${transaction.id}`);
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("transactions.transactionDetails")}
        </Text>
        <TouchableOpacity
          onPress={handleEdit}
          style={[
            styles.editButton,
            {
              backgroundColor: themeColors.iconBackground,
              borderColor: themeColors.border,
              ...getShadowStyle('low'),
            },
          ]}
        >
          <Ionicons name="create-outline" size={20} color={themeColors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Main Card */}
        <View
          style={[
            styles.mainCard,
            getCardStyle('high'),
          ]}
        >
          {/* Transaction Icon */}
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor:
                  transaction.category?.color ||
                  (isIncome ? "#4CAF50" : "#F44336"),
                shadowColor:
                  transaction.category?.color ||
                  (isIncome ? "#4CAF50" : "#F44336"),
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 4,
                elevation: 5,
              },
            ]}
          >
            <Ionicons
              name={
                (transaction.category?.icon ||
                  (isIncome ? "cash" : "cart")) as any
              }
              size={32}
              color="white"
            />
          </View>

          {/* Title and Category */}
          <Text style={[styles.title, { color: themeColors.text }]}>
            {translatedCategoryName}
          </Text>
          {/* Amount */}
          <Text
            style={[
              styles.amount,
              {
                color: isIncome
                  ? isDark
                    ? "#81C784"
                    : "#4CAF50"
                  : themeColors.danger,
              },
            ]}
          >
            {isIncome ? "+" : ""}
            {Math.abs(transaction.amount).toLocaleString()}đ
          </Text>

          {/* Date */}
          <Text
            style={[styles.date, { color: themeColors.primary }]}
          >
            {formattedDate}
          </Text>
        </View>

        {/* Detail Section */}
        <View
          style={[
            styles.detailsCard,
            getCardStyle('medium'),
          ]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            {t("transactions.details")}
          </Text>

          <View style={[styles.detailRow, { backgroundColor: themeColors.cardBackground }]}>
            <View
              style={[
                styles.detailIconContainer,
                getIconContainerStyle(),
              ]}
            >
              <Ionicons name="wallet-outline" size={20} color={themeColors.primary} />
            </View>
            <View
              style={[
                styles.detailTextContainer,
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <Text
                style={[
                  styles.detailLabel,
                  { color: themeColors.primary },
                ]}
              >
                {t("addTransaction.wallet")}
              </Text>
              <Text style={[styles.detailValue, { color: themeColors.text }]}>
                {transaction.wallet?.name || t("transactions.undefinedWallet")}
              </Text>
            </View>
          </View>

          <View style={[styles.detailRow, { backgroundColor: themeColors.cardBackground }]}>
            <View
              style={[
                styles.detailIconContainer,
                getIconContainerStyle(),
              ]}
            >
              <Ionicons
                name="calendar-outline"
                size={20}
                color={themeColors.primary}
              />
            </View>
            <View
              style={[
                styles.detailTextContainer,
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <Text
                style={[
                  styles.detailLabel,
                  { color: themeColors.primary },
                ]}
              >
                {t("transactions.time")}
              </Text>
              <Text style={[styles.detailValue, { color: themeColors.text }]}>
                {formattedDate}
              </Text>
            </View>
          </View>

          <View style={[styles.detailRow, { backgroundColor: themeColors.cardBackground }]}>
            <View
              style={[
                styles.detailIconContainer,
                getIconContainerStyle(),
              ]}
            >
              <Ionicons
                name="pricetag-outline"
                size={20}
                color={themeColors.primary}
              />
            </View>
            <View
              style={[
                styles.detailTextContainer,
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <Text
                style={[
                  styles.detailLabel,
                  { color: themeColors.primary },
                ]}
              >
                {t("transactions.type")}
              </Text>
              <Text style={[styles.detailValue, { color: themeColors.text }]}>
                {transaction.type === "income"
                  ? t("stats.income")
                  : t("stats.expense")}
              </Text>
            </View>
          </View>

          <View style={[styles.detailRow, { backgroundColor: themeColors.cardBackground }]}>
            <View
              style={[
                styles.detailIconContainer,
                getIconContainerStyle(),
              ]}
            >
              <Ionicons
                name="document-text-outline"
                size={20}
                color={themeColors.primary}
              />
            </View>
            <View
              style={[
                styles.detailTextContainer,
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <Text
                style={[
                  styles.detailLabel,
                  { color: themeColors.primary },
                ]}
              >
                {t("addTransaction.note")}
              </Text>
              <Text style={[styles.detailValue, { color: themeColors.text }]}>
                {transaction.description || t("transactions.noNotes")}
              </Text>
            </View>
          </View>
        </View>

        {/* Actions */}
        <View
          style={[styles.actionsContainer, { backgroundColor: "transparent" }]}
        >
          <TouchableOpacity
            style={[
              styles.actionButton,
              {
                backgroundColor: themeColors.dangerBackground,
                borderWidth: 1,
                borderColor: themeColors.dangerBorder,
                ...getShadowStyle('medium'),
              },
            ]}
            onPress={() => {
              Alert.alert(
                t("transactions.deleteTransaction"),
                t("transactions.deleteConfirm"),
                [
                  { text: t("common.cancel"), style: "cancel" },
                  {
                    text: t("common.delete"),
                    style: "destructive",
                    onPress: async () => {
                      try {
                        setIsLoading(true);

                        // Lấy thông tin ví hiện tại để cập nhật số dư
                        const wallet = await WalletModel.getById(
                          transaction.wallet_id
                        );
                        if (!wallet) {
                          throw new Error("Không tìm thấy thông tin ví");
                        }

                        // Tính toán số dư mới dựa trên loại giao dịch
                        let newBalance = wallet.balance;
                        if (transaction.type === "income") {
                          // Nếu là thu nhập, trừ đi số tiền khi xóa
                          newBalance =
                            wallet.balance - Math.abs(transaction.amount);
                        } else {
                          // Nếu là chi tiêu, cộng lại số tiền khi xóa
                          newBalance =
                            wallet.balance + Math.abs(transaction.amount);
                        }

                        // Xóa giao dịch
                        await TransactionModel.delete(transaction.id);

                        // Cập nhật số dư ví
                        await WalletModel.update(transaction.wallet_id, {
                          balance: newBalance,
                        });

                        Alert.alert(
                          t("common.success"),
                          t("transactions.transactionDeleted")
                        );
                        router.back();
                      } catch (error) {
                        console.error("Error deleting transaction:", error);
                        Alert.alert(
                          t("common.error"),
                          t("transactions.deleteError")
                        );
                        setIsLoading(false);
                      }
                    },
                  },
                ]
              );
            }}
          >
            <Ionicons
              name="trash-outline"
              size={20}
              color={themeColors.danger}
            />
            <Text
              style={{
                color: themeColors.danger,
                marginLeft: 8,
                fontWeight: "600",
              }}
            >
              {t("transactions.deleteTransaction")}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  mainCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    marginBottom: 20,
  },
  iconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  amount: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 12,
    paddingTop: 12,
  },
  date: {
    fontSize: 14,
  },
  detailsCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  detailIconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  detailTextContainer: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: "500",
  },
  actionsContainer: {
    marginTop: 10,
    marginBottom: 30,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});