// File: app/moving/utils/walletDetection.ts
// <PERSON>àm xử lý phân tích hình ảnh và nhận dạng ví

import * as FileSystem from 'expo-file-system';
import Constants from 'expo-constants';

// Đọc API key từ biến môi trường
const getApiKey = () => {
  // Ưu tiên đọc từ process.env (khi build)
  if (process.env.OPENAI_API_KEY) {
    return process.env.OPENAI_API_KEY;
  }
  
  // Đọc từ Constants.manifest.extra (khi development)
  if (Constants.expoConfig?.extra?.openaiApiKey) {
    return Constants.expoConfig.extra.openaiApiKey;
  }
  
  // Fallback - Nên throw error thay vì để key trống
  console.error("OPENAI_API_KEY không được tìm thấy trong biến môi trường");
  return "";
};

// Hàm tạo ID đơn giản để tránh lỗi crypto.getRandomValues()
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Danh sách các từ khóa để phát hiện loại ví
const BANK_KEYWORDS = [
  // Ngân hàng Việt Nam
  'vietcombank', 'vcb', 'vietinbank', 'bidv', 'agribank', 'techcombank', 'tech', 
  'mb bank', 'mbbank', 'mb', 'sacombank', 'acb', 'vpbank', 'vp bank', 'tpbank', 
  'ocb', 'hsbc', 'hdbank', 'vib', 'scb', 'seabank', 'shinhanbank', 'abbank', 
  'eximbank', 'cbbank', 'bacabank', 'liobank', 'lpbank', 'lpb', 'ncb', 
  'oceanbank', 'pgbank', 'pvcombank', 'vrb', 'wooribank', 'vietabank', 
  'hlbank', 'baovietbank', 'vietcapitalbank', 'kienlongbank', 'namabank',
  'abbank', 'agribank', 'bacabank', 'cbbank', 'hlbank', 'liobank', 'lpbbank',
  'ncb', 'oceanbank', 'pgbank', 'pvcombank', 'sacombank', 'scb', 'seabank',
  'shinhanbank', 'tpbank', 'vib', 'vietcombank', 'vietinbank', 'vpbank', 'vrb',
  'wooribank', 'nganluong'
];

const EWALLET_KEYWORDS = [
  // Ví điện tử & Tiền điện tử
  'momo', 'zalopay', 'zalo pay', 'vnpay', 'viettelpay', 'viettel pay', 'airpay', 
  'shopee', 'shopeepay', 'shopee pay', 'appota', 'payoo', 'binance', 'onus', 
  'cake', 'grabpay', 'grab pay', 'vtcpay', 'vnptmoney', 'viettelmoney', 
  'vimo', 'paypal', 'nextpay', 'truemoney', 'xm', 'baokim',
  'tiki', 'oneu', 'payme', 'payoo', 'vnpay', 'vnptmoney', 'viettelpay', 
  'viettelmoney', 'vimo', 'zalopay', 'gold'
];

const CASH_KEYWORDS = [
  'tiền mặt', 'tiền', 'cash', 'ví', 'wallet', 'tienmat', 'default'
];

// Ánh xạ tên ví với loại ví phổ biến
const detectWalletType = (name: string): 'bank' | 'ewallet' | 'cash' => {
  const lowerName = name.toLowerCase();
  
  if (BANK_KEYWORDS.some(keyword => lowerName.includes(keyword))) {
    return 'bank';
  }
  
  if (EWALLET_KEYWORDS.some(keyword => lowerName.includes(keyword))) {
    return 'ewallet';
  }
  
  if (CASH_KEYWORDS.some(keyword => lowerName.includes(keyword))) {
    return 'cash';
  }
  
  // Mặc định là ngân hàng
  return 'bank';
};

// Ánh xạ icon dựa vào tên ví
const getIconForWallet = (name: string): string => {
  const lowerName = name.toLowerCase();
  
  // Kiểm tra nếu tên chứa tên của các ngân hàng/ví có logo
  // Trả về tên file logo tương ứng
  
  // Danh sách logo các ngân hàng
  if (lowerName.includes('abbank')) return 'abbank.png';
  if (lowerName.includes('acb')) return 'acb.png';
  if (lowerName.includes('agribank')) return 'agribank.png';
  if (lowerName.includes('appota')) return 'appota.png';
  if (lowerName.includes('bacabank')) return 'bacabank.png';
  if (lowerName.includes('baokim')) return 'baokim.png';
  if (lowerName.includes('bidv')) return 'bidv.png';
  if (lowerName.includes('binance')) return 'binance.png';
  if (lowerName.includes('cake')) return 'cake.png';
  if (lowerName.includes('cbbank')) return 'cbbank.png';
  if (lowerName.includes('grabpay')) return 'grabpay.png';
  if (lowerName.includes('hlbank')) return 'hlbank.png';
  if (lowerName.includes('hsbc')) return 'hsbc.png';
  if (lowerName.includes('liobank')) return 'liobank.png';
  if (lowerName.includes('lpbank') || lowerName.includes('lpbbank')) return 'lpbbank.png';
  if (lowerName.includes('mbbank') || lowerName.includes('mb ')) return 'mbbank.png';
  if (lowerName.includes('momo')) return 'momo.png';
  if (lowerName.includes('ncb')) return 'ncb.png';
  if (lowerName.includes('nextpay')) return 'nextpay.png';
  if (lowerName.includes('ocb')) return 'ocb.png';
  if (lowerName.includes('oceanbank')) return 'oceanbank.png';
  if (lowerName.includes('oneu')) return 'oneu.png';
  if (lowerName.includes('onus')) return 'onus.png';
  if (lowerName.includes('payme')) return 'payme.png';
  if (lowerName.includes('payoo')) return 'payoo.png';
  if (lowerName.includes('paypal')) return 'paypal.png';
  if (lowerName.includes('pgbank')) return 'pgbank.png';
  if (lowerName.includes('pvcombank')) return 'pvcombank.png';
  if (lowerName.includes('sacombank')) return 'sacombank.png';
  if (lowerName.includes('scb')) return 'scb.png';
  if (lowerName.includes('seabank')) return 'seabank.png';
  if (lowerName.includes('shinhanbank')) return 'shinhanbank.png';
  if (lowerName.includes('shopeepay')) return 'shopeepay.png';
  if (lowerName.includes('techcombank') || lowerName.includes('tech')) return 'techcombank.png';
  if (lowerName.includes('tienmat') || lowerName.includes('tiền mặt')) return 'tienmat.png';
  if (lowerName.includes('tiki')) return 'tiki.png';
  if (lowerName.includes('timo')) return 'timo.png';
  if (lowerName.includes('tpbank')) return 'tpbank.png';
  if (lowerName.includes('truemoney')) return 'truemoney.png';
  if (lowerName.includes('vib')) return 'vib.png';
  if (lowerName.includes('vietcombank') || lowerName.includes('vcb')) return 'vietcombank.png';
  if (lowerName.includes('vietinbank')) return 'vietinbank.png';
  if (lowerName.includes('viettelmoney')) return 'viettelmoney.png';
  if (lowerName.includes('viettelpay')) return 'viettelpay.png';
  if (lowerName.includes('vimo')) return 'vimo.png';
  if (lowerName.includes('vnpay')) return 'vnpay.png';
  if (lowerName.includes('vnptmoney')) return 'vnptmoney.png';
  if (lowerName.includes('vpbank')) return 'vpbank.png';
  if (lowerName.includes('vrb')) return 'vrb.png';
  if (lowerName.includes('vtcpay')) return 'vtcpay.png';
  if (lowerName.includes('wooribank')) return 'wooribank.png';
  if (lowerName.includes('xm')) return 'xm.png';
  if (lowerName.includes('zalopay')) return 'zalopay.png';
  if (lowerName.includes('gold')) return 'gold.png';
  
  // Nếu không tìm thấy, trả về logo mặc định dựa trên loại ví
  const walletType = detectWalletType(name);
  return walletType === 'bank' ? 'default.png' : 
         walletType === 'ewallet' ? 'default.png' : 'tienmat.png';
};

// Tạo màu cho loại ví
const getColorForType = (type: 'bank' | 'ewallet' | 'cash'): string => {
  switch (type) {
    case 'bank':
      return '#2196F3'; // Xanh dương
    case 'ewallet':
      return '#FF9800'; // Cam
    case 'cash':
      return '#4CAF50'; // Xanh lá
    default:
      return '#2196F3';
  }
};

// Tạo icon cho loại ví
const getIconForType = (type: 'bank' | 'ewallet' | 'cash'): string => {
  switch (type) {
    case 'bank':
      return 'card-outline'; // Icon thẻ
    case 'ewallet':
      return 'phone-portrait-outline'; // Icon điện thoại
    case 'cash':
      return 'cash-outline'; // Icon tiền
    default:
      return 'wallet-outline';
  }
};

/**
 * Hàm chính: Phân tích ảnh và trích xuất thông tin ví sử dụng OpenAI API
 * 
 * @param imageUri URI của hình ảnh cần phân tích
 * @returns Danh sách các ví được phát hiện
 */
export const detectWalletsFromImage = async (imageUri: string): Promise<any[]> => {
  try {
    // Lấy API key từ môi trường
    const OPENAI_API_KEY = getApiKey();
    
    if (!OPENAI_API_KEY) {
      console.error("API key không hợp lệ hoặc không tìm thấy");
      return [];
    }
    
    // Chuyển đổi ảnh thành base64
    const base64Image = await FileSystem.readAsStringAsync(imageUri, { encoding: FileSystem.EncodingType.Base64 });
    
    // Kiểm tra kích thước của base64Image để đảm bảo rằng nó không quá lớn
    console.log("Image size in bytes (base64):", base64Image.length);
    
    // Khởi tạo biến để lưu trữ mô hình sẽ sử dụng
    let modelToUse = "gpt-4o-mini";
    let detectedWallets = [];
    
    // Hàm gọi OpenAI API với mô hình được chỉ định
    const callOpenAIAPI = async (model: string) => {
      console.log(`Calling OpenAI API with model: ${model}`);
      
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: "Đây là ảnh chụp màn hình của một ứng dụng quản lý tài chính. Vui lòng phân tích và trích xuất thông tin về các ví/tài khoản hiển thị trong ảnh. Trích xuất tên ví và số dư từng ví đúng định dạng.\n\nTrả về kết quả dưới dạng JSON theo định dạng sau: { \"wallets\": [ { \"name\": \"Tên ví\", \"balance\": số dư (số nguyên, không có dấu chấm phẩy) } ] }.\n\nChỉ trả về JSON, không giải thích gì thêm."
                },
                {
                  type: "image_url",
                  image_url: {
                    url: `data:image/jpeg;base64,${base64Image}`
                  }
                }
              ]
            }
          ],
          max_tokens: 1000
        })
      });
      
      return await response.json();
    };
    
    // Hàm xử lý phản hồi từ API để trả về danh sách ví
    const processAPIResponse = (data: any) => {
      if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
        throw new Error("Invalid response from OpenAI API");
      }
      
      // Phân tích phản hồi JSON
      const content = data.choices[0].message.content;
      let jsonMatch = content.match(/\{[\s\S]*\}/);
      
      if (!jsonMatch) {
        throw new Error("Could not extract JSON from API response");
      }
      
      const jsonData = JSON.parse(jsonMatch[0]);
      
      if (!jsonData.wallets || !Array.isArray(jsonData.wallets)) {
        throw new Error("Invalid JSON format from API response");
      }
      
      // Chuyển đổi dữ liệu OpenAI thành định dạng ví của ứng dụng
      return jsonData.wallets.map(wallet => {
        const walletName = wallet.name.trim();
        const walletType = detectWalletType(walletName);
        
        // Chuyển đổi số dư thành số nguyên, loại bỏ ký tự không phải số
        let balance = 0;
        try {
          if (typeof wallet.balance === 'number') {
            balance = wallet.balance;
          } else {
            balance = parseInt(wallet.balance.toString().replace(/\D/g, "") || "0");
          }
        } catch (e) {
          console.error("Error parsing balance:", e);
        }
        
        // Xác định icon dựa vào tên ví
        const iconName = getIconForWallet(walletName);
        // Nếu icon là file .png thì giữ nguyên, nếu không thì sử dụng icon based on type
        const finalIcon = iconName.endsWith('.png') ? iconName : getIconForType(walletType);
        
        return {
          id: generateId(), // Sử dụng hàm tạo ID đơn giản
          name: walletName,
          balance: balance,
          type: walletType,
          color: getColorForType(walletType),
          icon: finalIcon
        };
      });
    };
    
    // Gọi API lần đầu với mô hình mini
    try {
      const data = await callOpenAIAPI(modelToUse);
      console.log("OpenAI API response (mini):", JSON.stringify(data, null, 2));
      
      detectedWallets = processAPIResponse(data);
      
      // Nếu không tìm thấy ví, thử lại với mô hình lớn hơn
      if (detectedWallets.length === 0) {
        console.log("No wallets detected with mini model, retrying with gpt-4o");
        modelToUse = "gpt-4o";
        const retryData = await callOpenAIAPI(modelToUse);
        console.log("OpenAI API retry response (standard):", JSON.stringify(retryData, null, 2));
        
        detectedWallets = processAPIResponse(retryData);
      }
    } catch (error) {
      console.error("Error with gpt-4o-mini, retrying with gpt-4o:", error);
      
      // Nếu xảy ra lỗi với mô hình mini, thử lại với mô hình lớn hơn
      try {
        modelToUse = "gpt-4o";
        const retryData = await callOpenAIAPI(modelToUse);
        detectedWallets = processAPIResponse(retryData);
      } catch (retryError) {
        console.error("Error with gpt-4o retry:", retryError);
        return []; // Trả về mảng rỗng nếu cả hai mô hình đều thất bại
      }
    }
    
    // Lọc ví không hợp lệ và sắp xếp theo số dư giảm dần
    return detectedWallets
      .filter(wallet => wallet.name && !isNaN(wallet.balance))
      .sort((a, b) => b.balance - a.balance);
    
  } catch (error) {
    console.error('Error detecting wallets from image:', error);
    
    // Trả về một mảng rỗng nếu có lỗi
    return [];
  }
};