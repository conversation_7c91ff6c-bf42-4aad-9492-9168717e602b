import AsyncStorage from "@react-native-async-storage/async-storage";

const MOVING_USAGE_KEY = "@moneyup_moving_usage";
const MAX_USES_PER_DAY = 2;

interface MovingUsage {
  date: string;  // YYYY-MM-DD format
  count: number;
}

/**
 * Get today's date in YYYY-MM-DD format
 */
const getTodayString = (): string => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

/**
 * Get current moving usage for today
 */
export const getMovingUsage = async (): Promise<MovingUsage> => {
  try {
    const stored = await AsyncStorage.getItem(MOVING_USAGE_KEY);
    if (!stored) {
      return { date: getTodayString(), count: 0 };
    }

    const usage: MovingUsage = JSON.parse(stored);
    
    // If the stored date is not today, reset the count
    if (usage.date !== getTodayString()) {
      return { date: getTodayString(), count: 0 };
    }

    return usage;
  } catch (error) {
    console.error("Error getting moving usage:", error);
    return { date: getTodayString(), count: 0 };
  }
};

/**
 * Increment moving usage for today
 * Returns true if successful, false if daily limit reached
 */
export const incrementMovingUsage = async (): Promise<boolean> => {
  try {
    const usage = await getMovingUsage();
    
    // If already at limit, return false
    if (usage.count >= MAX_USES_PER_DAY) {
      return false;
    }

    // Increment count and save
    usage.count += 1;
    await AsyncStorage.setItem(MOVING_USAGE_KEY, JSON.stringify(usage));
    return true;
  } catch (error) {
    console.error("Error incrementing moving usage:", error);
    return false;
  }
};

/**
 * Check if user can use moving feature today
 */
export const canUseMoving = async (): Promise<boolean> => {
  const usage = await getMovingUsage();
  return usage.count < MAX_USES_PER_DAY;
};

/**
 * Get remaining uses for today
 */
export const getRemainingUses = async (): Promise<number> => {
  const usage = await getMovingUsage();
  return Math.max(0, MAX_USES_PER_DAY - usage.count);
}; 