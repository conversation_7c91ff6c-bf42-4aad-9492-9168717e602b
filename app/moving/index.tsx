// File: app/moving/index.tsx
// M<PERSON>n hình <PERSON>n App - giúp người dùng nhập ví từ ứng dụng tài chính khác
// Đã cập nhật: Hỗ trợ chọn nhiều hình <PERSON> (tối đa 3)

import { Ionicons } from "@expo/vector-icons";
import * as FileSystem from "expo-file-system";
import * as ImageManipulator from "expo-image-manipulator";
import * as ImagePicker from "expo-image-picker";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
    ActivityIndicator,
    Alert,
    AlertButton,
    FlatList,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Import local components
import { Colors } from "@/constants/Colors";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { WalletModel } from "@/lib/models/wallet";

// Import component riêng của màn hình này
import WalletPreview from "./components/WalletPreview";
import { canUseMoving, getRemainingUses, incrementMovingUsage } from "./utils/movingUsage";
import { detectWalletsFromImage } from "./utils/walletDetection";

/**
 * Hàm hỗ trợ thay thế biến trong template string
 * @param template Template string với định dạng {variable}
 * @param params Object chứa các biến cần thay thế
 * @returns String đã được thay thế biến
 */
const formatTemplate = (template: string, params: Record<string, any>): string => {
  if (!template || !params) return template;
  
  let result = template;
  Object.keys(params).forEach(key => {
    result = result.replace(new RegExp(`{${key}}`, 'g'), params[key]);
  });
  
  return result;
};

// Giới hạn số lượng hình ảnh tối đa
const MAX_IMAGES = 3;

export default function MovingScreen() {
  const { isDark } = useTheme();
  const theme = Colors[isDark ? "dark" : "light"];
  const { t } = useLocalization();

  // Đọc tham số query
  const { fromSetup } = useLocalSearchParams();
  const isFromSetup = fromSetup === "true";

  // Màu nền xanh nhạt và màu chủ đạo - đồng nhất với các màn hình khác
  const backgroundColor = isDark ? "#1E3A5F" : "#E3F2FD";
  const primaryColor = isDark ? "#64B5F6" : "#2196F3";
  const cardBgColor = isDark ? "#2C4A77" : "white";
  const borderColor = isDark ? "#3B5E94" : "#D1E4FD";
  const inputBgColor = isDark ? "#213A61" : "#F5F9FF";
  const textColor = isDark ? "#FFFFFF" : "#333333";
  const secondaryTextColor = isDark ? "#7CB9F8" : "#0D47A1";

  // State
  const [images, setImages] = useState<string[]>([]); // Thay đổi thành mảng ảnh
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0); // Ảnh đang xem
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedImageCount, setProcessedImageCount] = useState(0); // Số ảnh đã xử lý
  const [detectedWallets, setDetectedWallets] = useState<any[]>([]);
  const [isCreatingWallets, setIsCreatingWallets] = useState(false);
  const [selectedWallets, setSelectedWallets] = useState<{
    [key: string]: boolean;
  }>({});
  const [remainingUses, setRemainingUses] = useState<number>(2);

  // Check usage limit when component mounts
  useEffect(() => {
    const checkUsage = async () => {
      const canUse = await canUseMoving();
      const remaining = await getRemainingUses();
      setRemainingUses(remaining);
      
      if (!canUse) {
        const backButton: AlertButton = { 
          text: "OK", 
          onPress: () => router.back() 
        };
        Alert.alert(
          t("moving.limitReached") || "Đã đạt giới hạn",
          t("moving.limitReachedText") || 
          "Bạn đã sử dụng hết số lần chuyển ví cho phép trong ngày. Vui lòng thử lại vào ngày mai.",
          [backButton]
        );
      }
    };
    
    checkUsage();
  }, []);

  // Xử lý chọn nhiều hình từ thư viện
  const pickImages = async () => {
    try {
      // Check if user can use moving feature
      const canUse = await canUseMoving();
      if (!canUse) {
        const okButton: AlertButton = { text: "OK" };
        Alert.alert(
          t("moving.limitReached") || "Đã đạt giới hạn",
          t("moving.limitReachedText") || 
          "Bạn đã sử dụng hết số lần chuyển ví cho phép trong ngày. Vui lòng thử lại vào ngày mai.",
          [okButton]
        );
        return;
      }

      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          t("moving.permissionDenied") || "Quyền truy cập bị từ chối",
          t("moving.galleryPermissionText") ||
            "Vui lòng cấp quyền truy cập thư viện ảnh để sử dụng tính năng này.",
          [{ text: "OK" }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        allowsMultipleSelection: true, // Cho phép chọn nhiều ảnh
        selectionLimit: MAX_IMAGES, // Giới hạn 3 ảnh
        orderedSelection: true, // Duy trì thứ tự khi chọn
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Increment usage counter when user actually selects images
        const success = await incrementMovingUsage();
        if (!success) {
          const okButton: AlertButton = { text: "OK" };
          Alert.alert(
            t("moving.limitReached") || "Đã đạt giới hạn",
            t("moving.limitReachedText") || 
            "Bạn đã sử dụng hết số lần chuyển ví cho phép trong ngày. Vui lòng thử lại vào ngày mai.",
            [okButton]
          );
          return;
        }

        // Update remaining uses display
        const remaining = await getRemainingUses();
        setRemainingUses(remaining);

        // Xử lý khi chọn quá nhiều ảnh
        if (result.assets.length > MAX_IMAGES) {
          Alert.alert(
            t("moving.tooManyImages") || "Quá nhiều ảnh",
            formatTemplate(
              t("moving.tooManyImagesText") || 
              "Bạn chỉ có thể chọn tối đa {max} ảnh. Chỉ {max} ảnh đầu tiên sẽ được xử lý.",
              { max: MAX_IMAGES }
            )
          );
          // Chỉ lấy MAX_IMAGES ảnh đầu
          const selectedImageUris = result.assets
            .slice(0, MAX_IMAGES)
            .map((asset) => asset.uri);
          setImages(selectedImageUris);
          setSelectedImageIndex(0); // Reset về ảnh đầu tiên
          processImages(selectedImageUris);
        } else {
          const selectedImageUris = result.assets.map((asset) => asset.uri);
          setImages(selectedImageUris);
          setSelectedImageIndex(0); // Reset về ảnh đầu tiên
          processImages(selectedImageUris);
        }
      }
    } catch (error) {
      console.error("Lỗi khi chọn ảnh:", error);
      Alert.alert(
        t("moving.errorTitle") || "Lỗi",
        t("moving.imageProcessingErrorText") ||
          "Không thể chọn ảnh. Vui lòng thử lại."
      );
    }
  };

  // Xử lý chụp ảnh mới
  const takePhoto = async () => {
    try {
      // Check if user can use moving feature
      const canUse = await canUseMoving();
      if (!canUse) {
        const okButton: AlertButton = { text: "OK" };
        Alert.alert(
          t("moving.limitReached") || "Đã đạt giới hạn",
          t("moving.limitReachedText") || 
          "Bạn đã sử dụng hết số lần chuyển ví cho phép trong ngày. Vui lòng thử lại vào ngày mai.",
          [okButton]
        );
        return;
      }

      const permissionResult =
        await ImagePicker.requestCameraPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          t("moving.permissionDenied") || "Quyền truy cập bị từ chối",
          t("moving.cameraPermissionText") ||
            "Vui lòng cấp quyền truy cập camera để sử dụng tính năng này.",
          [{ text: "OK" }]
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: false,
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Increment usage counter when user actually takes a photo
        const success = await incrementMovingUsage();
        if (!success) {
          const okButton: AlertButton = { text: "OK" };
          Alert.alert(
            t("moving.limitReached") || "Đã đạt giới hạn",
            t("moving.limitReachedText") || 
            "Bạn đã sử dụng hết số lần chuyển ví cho phép trong ngày. Vui lòng thử lại vào ngày mai.",
            [okButton]
          );
          return;
        }

        // Update remaining uses display
        const remaining = await getRemainingUses();
        setRemainingUses(remaining);

        const capturedImageUri = result.assets[0].uri;
        setImages([capturedImageUri]); // Thêm ảnh vừa chụp vào mảng
        setSelectedImageIndex(0);
        processImages([capturedImageUri]);
      }
    } catch (error) {
      console.error("Lỗi khi chụp ảnh:", error);
      Alert.alert(
        t("moving.errorTitle") || "Lỗi",
        t("moving.imageProcessingErrorText") ||
          "Không thể chụp ảnh. Vui lòng thử lại."
      );
    }
  };

  // Xử lý danh sách ảnh đã chọn
  const processImages = async (imageUris: string[]) => {
    if (imageUris.length === 0) return;

    setIsProcessing(true);
    setDetectedWallets([]);
    setProcessedImageCount(0);

    try {
      let allDetectedWallets: any[] = [];

      // Xử lý từng ảnh một
      for (let i = 0; i < imageUris.length; i++) {
        const imageUri = imageUris[i];
        setProcessedImageCount(i + 1);

        try {
          // Resize ảnh để giảm kích thước trước khi xử lý
          let processedImageUri = imageUri;

          try {
            // Lấy thông tin kích thước ảnh
            const imageInfo = await FileSystem.getInfoAsync(imageUri);
            if (imageInfo.exists && imageInfo.size > 1000000) {
              // Nếu ảnh lớn hơn 1MB
              // Nén ảnh để giảm kích thước
              const manipResult = await ImageManipulator.manipulateAsync(
                imageUri,
                [{ resize: { width: 1200 } }], // Chỉ đặt width, giữ nguyên tỷ lệ
                { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
              );

              processedImageUri = manipResult.uri;
            }
          } catch (error) {
            console.error("Lỗi khi resize ảnh:", error);
            // Tiếp tục với ảnh gốc nếu có lỗi
          }

          // Gọi API để phát hiện ví từ ảnh
          const detectedWalletsFromImage = await detectWalletsFromImage(
            processedImageUri
          );

          // Kiểm tra xem ví nào đã tồn tại để tránh trùng lặp
          // Một ví được xem là trùng nếu có cùng tên
          const newWallets = detectedWalletsFromImage.filter((newWallet) => {
            return !allDetectedWallets.some(
              (existingWallet) =>
                existingWallet.name.toLowerCase() ===
                newWallet.name.toLowerCase()
            );
          });

          // Thêm các ví mới vào danh sách đã phát hiện
          allDetectedWallets = [...allDetectedWallets, ...newWallets];
        } catch (error) {
          console.error(
            `Lỗi khi xử lý ảnh ${i + 1}/${imageUris.length}:`,
            error
          );
          // Tiếp tục với ảnh tiếp theo
        }
      }

      // Cập nhật state với tất cả ví đã phát hiện
      if (allDetectedWallets.length === 0) {
        Alert.alert(
          t("moving.noWalletsFound") || "Không tìm thấy ví",
          t("moving.noWalletsFoundText") ||
            "Không thể phát hiện ví trong các hình ảnh đã chọn. Vui lòng thử lại với hình ảnh khác hoặc đảm bảo ảnh hiển thị rõ tên và số dư ví.",
          [{ text: "OK" }]
        );
      } else {
        setDetectedWallets(allDetectedWallets);

        // Đánh dấu tất cả các ví được chọn mặc định
        const initialSelection = {};
        allDetectedWallets.forEach((wallet) => {
          initialSelection[wallet.id] = true;
        });
        setSelectedWallets(initialSelection);
      }
    } catch (error) {
      console.error("Lỗi khi xử lý các hình ảnh:", error);
      Alert.alert(
        t("moving.imageProcessingError") || "Lỗi khi phân tích",
        t("moving.imageProcessingErrorText") ||
          "Không thể phân tích ảnh. Vui lòng thử lại với ảnh khác.",
        [{ text: "OK" }]
      );
    } finally {
      setIsProcessing(false);
      setProcessedImageCount(0);
    }
  };

  // Chuyển đến ảnh tiếp theo
  const goToNextImage = () => {
    if (selectedImageIndex < images.length - 1) {
      setSelectedImageIndex(selectedImageIndex + 1);
    }
  };

  // Quay lại ảnh trước
  const goToPreviousImage = () => {
    if (selectedImageIndex > 0) {
      setSelectedImageIndex(selectedImageIndex - 1);
    }
  };

  // Xử lý khi người dùng thay đổi lựa chọn ví
  const handleWalletSelection = (walletId: string, isSelected: boolean) => {
    setSelectedWallets((prev) => ({
      ...prev,
      [walletId]: isSelected,
    }));
  };

  // Xử lý cập nhật thông tin ví
  const handleWalletUpdate = (walletId: string, updatedData: any) => {
    setDetectedWallets((prev) =>
      prev.map((wallet) =>
        wallet.id === walletId ? { ...wallet, ...updatedData } : wallet
      )
    );
  };

  // Xử lý tạo ví
  const handleCreateWallets = async () => {
    // Kiểm tra nếu không có ví nào được chọn
    const hasSelectedWallets = Object.values(selectedWallets).some(
      (selected) => selected
    );
    if (!hasSelectedWallets) {
      Alert.alert(
        t("moving.errorTitle") || "Thông báo",
        t("moving.selectWalletPrompt") || "Vui lòng chọn ít nhất một ví để tạo."
      );
      return;
    }

    setIsCreatingWallets(true);

    try {
      // Lọc ra các ví đã được chọn
      const walletsToCreate = detectedWallets.filter(
        (wallet) => selectedWallets[wallet.id]
      );
      let successCount = 0;

      // Tạo từng ví một
      for (const wallet of walletsToCreate) {
        try {
          // Chuẩn bị dữ liệu cho ví mới
          const walletData = {
            name: wallet.name,
            balance: parseFloat(
              wallet.balance
                .toString()
                .replace(/\./g, "")
                .replace(/,/g, "")
                .replace(/₫/g, "")
            ),
            type: wallet.type || "bank",
            color: wallet.color || "#2196F3",
            icon: wallet.icon || "card-outline",
          };

          // Kiểm tra dữ liệu hợp lệ
          if (!walletData.name || isNaN(walletData.balance)) {
            continue;
          }

          // Tạo ví mới trong database
          await WalletModel.create(walletData);
          successCount++;
        } catch (error) {
          console.error(`Lỗi khi tạo ví ${wallet.name}:`, error);
        }
      }

      // Hiển thị thông báo thành công
      if (successCount > 0) {
        Alert.alert(
          t("moving.successTitle") || "Thành công",
          formatTemplate(
            t("moving.successMessage") || "Đã tạo {count} ví mới từ ứng dụng khác.",
            { count: successCount }
          ),
          [
            {
              text: "OK",
              onPress: () => {
                // Kiểm tra để quyết định điều hướng
                if (isFromSetup) {
                  router.push("/(setup)/create-categories");
                } else {
                  // Quay về trang danh sách ví
                  router.push("/wallet");
                }
              },
            },
          ]
        );
      } else {
        Alert.alert(
          t("moving.errorTitle") || "Lỗi",
          t("moving.errorMessage") || "Không thể tạo ví. Vui lòng thử lại."
        );
      }
    } catch (error) {
      console.error("Lỗi khi tạo ví:", error);
      Alert.alert(
        t("moving.errorTitle") || "Lỗi",
        t("moving.errorMessage") || "Không thể tạo ví. Vui lòng thử lại."
      );
    } finally {
      setIsCreatingWallets(false);
    }
  };

  // Xóa một ảnh khỏi danh sách
  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);

    if (newImages.length === 0) {
      // Nếu không còn ảnh nào, reset mọi thứ
      setImages([]);
      setDetectedWallets([]);
      setSelectedImageIndex(0);
    } else {
      setImages(newImages);
      // Điều chỉnh selectedImageIndex nếu cần
      if (selectedImageIndex >= newImages.length) {
        setSelectedImageIndex(newImages.length - 1);
      }
      // Xử lý lại tất cả các ảnh còn lại
      processImages(newImages);
    }
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        {/* Header */}
        <View style={[styles.header, { backgroundColor }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={textColor} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: textColor }]}>
            {t("moving.title") || "Chuyển App"}
          </Text>
          <Text style={[styles.usageLimit, { color: secondaryTextColor }]}>
            {formatTemplate(
              t("moving.remainingUses") || "Còn {count} lần sử dụng trong ngày",
              { count: remainingUses }
            )}
          </Text>
          <View style={{ width: 24 }}></View>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Phần giới thiệu */}
          {images.length === 0 &&
            !isProcessing &&
            detectedWallets.length === 0 && (
              <View
                style={[
                  styles.introContainer,
                  { backgroundColor: cardBgColor, borderColor },
                ]}
              >
                <View
                  style={[
                    styles.iconContainer,
                    { backgroundColor: primaryColor },
                  ]}
                >
                  <Ionicons name="swap-horizontal" size={40} color="white" />
                </View>
                <Text style={[styles.introTitle, { color: textColor }]}>
                  {t("moving.intro") || "Chuyển dữ liệu từ ứng dụng khác"}
                </Text>
                <Text style={[styles.introText, { color: secondaryTextColor }]}>
                  {t("moving.introText") ||
                    "Chụp ảnh màn hình ứng dụng tài chính của bạn, AI sẽ tự động nhận diện và tạo các ví tương ứng trong AI Money."}
                </Text>
                <Text
                  style={[
                    styles.multipleText,
                    { color: primaryColor, marginBottom: 16 },
                  ]}
                >
                  {formatTemplate(
                    t("moving.multipleText") || "Bạn có thể chọn tối đa {max} ảnh để phân tích cùng lúc.",
                    { max: MAX_IMAGES }
                  )}
                </Text>

                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[
                      styles.button,
                      { backgroundColor: inputBgColor, borderColor },
                    ]}
                    onPress={pickImages}
                  >
                    <Ionicons
                      name="images-outline"
                      size={22}
                      color={primaryColor}
                      style={{ marginRight: 8 }}
                    />
                    <Text style={[styles.buttonText, { color: textColor }]}>
                      {t("moving.selectImage") || "Chọn ảnh"}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.button, { backgroundColor: primaryColor }]}
                    onPress={takePhoto}
                  >
                    <Ionicons
                      name="camera-outline"
                      size={22}
                      color="white"
                      style={{ marginRight: 8 }}
                    />
                    <Text style={[styles.buttonText, { color: "white" }]}>
                      {t("moving.takePhoto") || "Chụp ảnh"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

          {/* Hiển thị ảnh đã chọn */}
          {images.length > 0 && (
            <View
              style={[
                styles.imageContainer,
                { backgroundColor: cardBgColor, borderColor },
              ]}
            >
              {/* Hiển thị ảnh đang được chọn */}
              <View style={styles.imagePreviewContainer}>
                <Image
                  source={{ uri: images[selectedImageIndex] }}
                  style={styles.selectedImage}
                />

                {/* Điều hướng chuyển ảnh nếu có nhiều ảnh */}
                {images.length > 1 && (
                  <View style={styles.imageNavigation}>
                    <TouchableOpacity
                      style={[
                        styles.navButton,
                        { opacity: selectedImageIndex > 0 ? 1 : 0.5 },
                      ]}
                      onPress={goToPreviousImage}
                      disabled={selectedImageIndex === 0}
                    >
                      <Ionicons
                        name="chevron-back"
                        size={24}
                        color={primaryColor}
                      />
                    </TouchableOpacity>

                    <Text style={{ color: textColor }}>
                      {selectedImageIndex + 1}/{images.length}
                    </Text>

                    <TouchableOpacity
                      style={[
                        styles.navButton,
                        {
                          opacity:
                            selectedImageIndex < images.length - 1 ? 1 : 0.5,
                        },
                      ]}
                      onPress={goToNextImage}
                      disabled={selectedImageIndex === images.length - 1}
                    >
                      <Ionicons
                        name="chevron-forward"
                        size={24}
                        color={primaryColor}
                      />
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {/* Hiển thị thumbnail danh sách ảnh đã chọn */}
              {images.length > 1 && (
                <View style={styles.thumbnailContainer}>
                  <FlatList
                    data={images}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    keyExtractor={(item, index) => index.toString()}
                    renderItem={({ item, index }) => (
                      <TouchableOpacity
                        style={[
                          styles.thumbnail,
                          {
                            borderColor:
                              index === selectedImageIndex
                                ? primaryColor
                                : borderColor,
                            borderWidth: index === selectedImageIndex ? 2 : 1,
                          },
                        ]}
                        onPress={() => setSelectedImageIndex(index)}
                      >
                        <Image
                          source={{ uri: item }}
                          style={styles.thumbnailImage}
                        />
                        <TouchableOpacity
                          style={styles.removeThumbnailButton}
                          onPress={() => removeImage(index)}
                        >
                          <Ionicons
                            name="close-circle"
                            size={20}
                            color="#F44336"
                          />
                        </TouchableOpacity>
                      </TouchableOpacity>
                    )}
                  />

                  {/* Nút thêm ảnh nếu chưa đạt giới hạn */}
                  {images.length < MAX_IMAGES && !isProcessing && (
                    <TouchableOpacity
                      style={[
                        styles.addImageButton,
                        { backgroundColor: inputBgColor, borderColor },
                      ]}
                      onPress={pickImages}
                    >
                      <Ionicons name="add" size={24} color={primaryColor} />
                    </TouchableOpacity>
                  )}
                </View>
              )}

              {!isProcessing &&
                detectedWallets.length > 0 &&
                images.length < MAX_IMAGES && (
                  <TouchableOpacity
                    style={[
                      styles.changeImageButton,
                      { backgroundColor: inputBgColor, borderColor },
                    ]}
                    onPress={pickImages}
                  >
                    <Ionicons
                      name="add-outline"
                      size={16}
                      color={primaryColor}
                      style={{ marginRight: 4 }}
                    />
                    <Text style={{ color: primaryColor, fontSize: 14 }}>
                      {formatTemplate(
                        t("moving.addMoreImages") || "Thêm ảnh ({remaining} còn lại)",
                        { remaining: MAX_IMAGES - images.length }
                      )}
                    </Text>
                  </TouchableOpacity>
                )}
            </View>
          )}

          {/* Hiển thị trạng thái xử lý */}
          {isProcessing && (
            <View
              style={[
                styles.processingContainer,
                { backgroundColor: cardBgColor, borderColor },
              ]}
            >
              <ActivityIndicator size="large" color={primaryColor} />
              <Text style={[styles.processingText, { color: textColor }]}>
                {images.length > 1
                  ? formatTemplate(
                      t("moving.processingMultiple") || "Đang phân tích ảnh... ({current}/{total})",
                      { current: processedImageCount, total: images.length }
                    )
                  : t("moving.processingImage") || "Đang phân tích ảnh..."}
              </Text>
            </View>
          )}

          {/* Hiển thị kết quả nhận diện */}
          {!isProcessing && detectedWallets.length > 0 && (
            <View
              style={[
                styles.resultsContainer,
                { backgroundColor: cardBgColor, borderColor },
              ]}
            >
              <View style={styles.resultsHeader}>
                <Text style={[styles.resultsTitle, { color: textColor }]}>
                  {/* Sửa lỗi tại đây - Sửa từ "Vị" thành "Ví" */}
                  Ví được phát hiện ({detectedWallets.length})
                </Text>

                <TouchableOpacity
                  onPress={() => {
                    // Chọn/bỏ chọn tất cả
                    const allSelected = Object.values(selectedWallets).every(
                      (selected) => selected
                    );
                    const newSelectedState = {};
                    detectedWallets.forEach((wallet) => {
                      newSelectedState[wallet.id] = !allSelected;
                    });
                    setSelectedWallets(newSelectedState);
                  }}
                >
                  <Text style={{ color: primaryColor }}>
                    {Object.values(selectedWallets).every(
                      (selected) => selected
                    )
                      ? t("moving.deselectAll") || "Bỏ chọn tất cả"
                      : t("moving.selectAll") || "Chọn tất cả"}
                  </Text>
                </TouchableOpacity>
              </View>

              {detectedWallets.map((wallet, index) => (
                <WalletPreview
                  key={wallet.id}
                  wallet={wallet}
                  isSelected={selectedWallets[wallet.id] || false}
                  onSelectionChange={(selected) =>
                    handleWalletSelection(wallet.id, selected)
                  }
                  onWalletUpdate={(updatedData) =>
                    handleWalletUpdate(wallet.id, updatedData)
                  }
                  isDark={isDark}
                  isLast={index === detectedWallets.length - 1}
                />
              ))}

              <TouchableOpacity
                style={[
                  styles.createButton,
                  {
                    backgroundColor: isCreatingWallets
                      ? isDark
                        ? "#375980"
                        : "#BBDEFB"
                      : primaryColor,
                    opacity: Object.values(selectedWallets).some(
                      (selected) => selected
                    )
                      ? 1
                      : 0.5,
                  },
                ]}
                onPress={handleCreateWallets}
                disabled={
                  isCreatingWallets ||
                  !Object.values(selectedWallets).some((selected) => selected)
                }
              >
                <Text style={styles.createButtonText}>
                  {isCreatingWallets
                    ? t("moving.creatingWallets") || "Đang tạo ví..."
                    : t("moving.createSelectedWallets") || "Tạo ví đã chọn"}
                </Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Hướng dẫn sử dụng */}
          <View
            style={[
              styles.tipsContainer,
              { backgroundColor: cardBgColor, borderColor },
            ]}
          >
            <Text style={[styles.tipsTitle, { color: textColor }]}>
              {t("moving.tipsTitle") || "Mẹo sử dụng"}
            </Text>
            <View style={styles.tipItem}>
              <Ionicons
                name="checkmark-circle"
                size={18}
                color={primaryColor}
                style={styles.tipIcon}
              />
              <Text style={[styles.tipText, { color: secondaryTextColor }]}>
                {t("moving.tip1") ||
                  "Chụp màn hình danh sách ví/tài khoản từ ứng dụng cũ"}
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons
                name="checkmark-circle"
                size={18}
                color={primaryColor}
                style={styles.tipIcon}
              />
              <Text style={[styles.tipText, { color: secondaryTextColor }]}>
                {t("moving.tip2") ||
                  "Chọn nhiều ảnh để nhập đồng thời nhiều ví từ nhiều màn hình khác nhau"}
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons
                name="checkmark-circle"
                size={18}
                color={primaryColor}
                style={styles.tipIcon}
              />
              <Text style={[styles.tipText, { color: secondaryTextColor }]}>
                {t("moving.tip3") ||
                  "Đảm bảo tên và số dư của ví được hiển thị rõ ràng trong ảnh"}
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Ionicons
                name="checkmark-circle"
                size={18}
                color={primaryColor}
                style={styles.tipIcon}
              />
              <Text style={[styles.tipText, { color: secondaryTextColor }]}>
                {t("moving.tip4") ||
                  "Kiểm tra và chỉnh sửa thông tin trước khi tạo ví"}
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  usageLimit: {
    fontSize: 14,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  introContainer: {
    borderRadius: 16,
    padding: 20,
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  introTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    textAlign: "center",
  },
  introText: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
  },
  multipleText: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    borderRadius: 12,
    flex: 1,
    marginHorizontal: 5,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  buttonText: {
    fontWeight: "600",
  },
  imageContainer: {
    borderRadius: 16,
    padding: 12,
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imagePreviewContainer: {
    width: "100%",
    alignItems: "center",
  },
  selectedImage: {
    width: "100%",
    height: 300,
    borderRadius: 12,
    resizeMode: "contain",
  },
  imageNavigation: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  navButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
  },
  thumbnailContainer: {
    flexDirection: "row",
    marginTop: 12,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    width: "100%",
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    position: "relative",
  },
  thumbnailImage: {
    width: "100%",
    height: "100%",
    borderRadius: 7,
  },
  removeThumbnailButton: {
    position: "absolute",
    top: -8,
    right: -8,
    backgroundColor: "white",
    borderRadius: 12,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
  addImageButton: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
    borderWidth: 1,
    borderStyle: "dashed",
  },
  changeImageButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 12,
  },
  processingContainer: {
    borderRadius: 16,
    padding: 20,
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  processingText: {
    marginTop: 12,
    fontSize: 16,
  },
  resultsContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  createButton: {
    height: 50,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  createButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  tipsContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  tipItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  tipIcon: {
    marginTop: 2,
    marginRight: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
});