// File: app/moving/_layout.tsx
// Cấu hình layout cho route trong thư mục moving

import { useColorScheme } from "@/hooks/useColorScheme";
import { Stack } from "expo-router";
import { Platform } from "react-native";

export default function MovingLayout() {
  const { colorScheme } = useColorScheme();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        header: () => null,
        contentStyle: {
          backgroundColor: colorScheme === "dark" ? "#1E3A5F" : "#E3F2FD",
        },
        animation: Platform.OS === "android" ? "slide_from_right" : "default",
        presentation: "card",
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
          header: () => null,
        }}
      />
    </Stack>
  );
}