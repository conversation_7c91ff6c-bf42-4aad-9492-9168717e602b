// File: app/moving/components/WalletPreview.tsx
// Component hiển thị và chỉnh sửa từng ví được nhận diện
// Đ<PERSON> cập nhật: Cho phép bấm vào cả item để chọn/bỏ chọn ví

import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

// Import hook useLocalization
import { useLocalization } from "@/context/LocalizationContext";

// Import BankLogos
const BankLogos = {
  // Ngân hàng và ví điện tử (56 hình ảnh từ danh sách)
  "abbank.png": require("@/assets/images/bank-logos/abbank.png"),
  "acb.png": require("@/assets/images/bank-logos/acb.png"),
  "agribank.png": require("@/assets/images/bank-logos/agribank.png"),
  "appota.png": require("@/assets/images/bank-logos/appota.png"),
  "bacabank.png": require("@/assets/images/bank-logos/bacabank.png"),
  "baokim.png": require("@/assets/images/bank-logos/baokim.png"),
  "bidv.png": require("@/assets/images/bank-logos/bidv.png"),
  "binance.png": require("@/assets/images/bank-logos/binance.png"),
  "cake.png": require("@/assets/images/bank-logos/cake.png"),
  "cbbank.png": require("@/assets/images/bank-logos/cbbank.png"),
  "default.png": require("@/assets/images/bank-logos/default.png"),
  "gold.png": require("@/assets/images/bank-logos/gold.png"),
  "grabpay.png": require("@/assets/images/bank-logos/grabpay.png"),
  "hlbank.png": require("@/assets/images/bank-logos/hlbank.png"),
  "hsbc.png": require("@/assets/images/bank-logos/hsbc.png"),
  "liobank.png": require("@/assets/images/bank-logos/liobank.png"),
  "lpbbank.png": require("@/assets/images/bank-logos/lpbbank.png"),
  "mbbank.png": require("@/assets/images/bank-logos/mbbank.png"),
  "momo.png": require("@/assets/images/bank-logos/momo.png"),
  "ncb.png": require("@/assets/images/bank-logos/ncb.png"),
  "nextpay.png": require("@/assets/images/bank-logos/nextpay.png"),
  "nganluong.png": require("@/assets/images/bank-logos/nganluong.png"),
  "ocb.png": require("@/assets/images/bank-logos/ocb.png"),
  "oceanbank.png": require("@/assets/images/bank-logos/oceanbank.png"),
  "oneu.png": require("@/assets/images/bank-logos/oneu.png"),
  "onus.png": require("@/assets/images/bank-logos/onus.png"),
  "payme.png": require("@/assets/images/bank-logos/payme.png"),
  "payoo.png": require("@/assets/images/bank-logos/payoo.png"),
  "paypal.png": require("@/assets/images/bank-logos/paypal.png"),
  "pgbank.png": require("@/assets/images/bank-logos/pgbank.png"),
  "pvcombank.png": require("@/assets/images/bank-logos/pvcombank.png"),
  "sacombank.png": require("@/assets/images/bank-logos/sacombank.png"),
  "scb.png": require("@/assets/images/bank-logos/scb.png"),
  "seabank.png": require("@/assets/images/bank-logos/seabank.png"),
  "shinhanbank.png": require("@/assets/images/bank-logos/shinhanbank.png"),
  "shopeepay.png": require("@/assets/images/bank-logos/shopeepay.png"),
  "techcombank.png": require("@/assets/images/bank-logos/techcombank.png"),
  "tienmat.png": require("@/assets/images/bank-logos/tienmat.png"),
  "tiki.png": require("@/assets/images/bank-logos/tiki.png"),
  "timo.png": require("@/assets/images/bank-logos/timo.png"),
  "tpbank.png": require("@/assets/images/bank-logos/tpbank.png"),
  "truemoney.png": require("@/assets/images/bank-logos/truemoney.png"),
  "vib.png": require("@/assets/images/bank-logos/vib.png"),
  "vietcombank.png": require("@/assets/images/bank-logos/vietcombank.png"),
  "vietinbank.png": require("@/assets/images/bank-logos/vietinbank.png"),
  "viettelmoney.png": require("@/assets/images/bank-logos/viettelmoney.png"),
  "viettelpay.png": require("@/assets/images/bank-logos/viettelpay.png"),
  "vimo.png": require("@/assets/images/bank-logos/vimo.png"),
  "vnpay.png": require("@/assets/images/bank-logos/vnpay.png"),
  "vnptmoney.png": require("@/assets/images/bank-logos/vnptmoney.png"),
  "vpbank.png": require("@/assets/images/bank-logos/vpbank.png"),
  "vrb.png": require("@/assets/images/bank-logos/vrb.png"),
  "vtcpay.png": require("@/assets/images/bank-logos/vtcpay.png"),
  "wooribank.png": require("@/assets/images/bank-logos/wooribank.png"),
  "xm.png": require("@/assets/images/bank-logos/xm.png"),
  "zalopay.png": require("@/assets/images/bank-logos/zalopay.png"),
};

// Format tiền tệ
const formatMoney = (amount) => {
  if (!amount) return "";

  // Loại bỏ tất cả các dấu chấm hiện có
  const cleanAmount = amount.toString().replace(/\./g, "");

  // Thêm dấu chấm ngăn cách hàng nghìn
  return cleanAmount.replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

// Chuyển đổi từ số có dấu chấm ngăn cách về số nguyên
const parseMoney = (formattedAmount) => {
  if (!formattedAmount) return "0";

  // Loại bỏ tất cả các dấu chấm
  return formattedAmount.toString().replace(/\./g, "");
};

// Ánh xạ tên ví với loại ví phổ biến
const mapWalletNameToType = (name: string): "bank" | "ewallet" | "cash" => {
  const lowerName = name.toLowerCase();

  // Danh sách các ngân hàng phổ biến
  const banks = [
    "vietcombank",
    "vcb",
    "vietinbank",
    "bidv",
    "agribank",
    "techcombank",
    "tech",
    "mb bank",
    "mbbank",
    "mb",
    "sacombank",
    "acb",
    "vpbank",
    "vp bank",
    "tpbank",
    "ocb",
    "hsbc",
    "hdbank",
    "vib",
    "scb",
    "seabank",
    "shinhanbank",
    "abbank",
    "eximbank",
    "cbbank",
    "bacabank",
    "liobank",
    "lpbank",
    "lpb",
    "ncb",
    "oceanbank",
    "pgbank",
    "pvcombank",
    "vrb",
    "wooribank",
    "vietabank",
  ];

  // Danh sách ví điện tử phổ biến
  const ewallets = [
    "momo",
    "zalopay",
    "vnpay",
    "viettelpay",
    "airpay",
    "shopeepay",
    "appota",
    "payoo",
    "binance",
    "onus",
    "cake",
    "grabpay",
    "vtcpay",
    "vnptmoney",
    "viettelmoney",
    "vimo",
    "paypal",
    "nextpay",
    "truemoney",
    "xm",
    "baokim",
    "tiki",
    "oneu",
    "payme",
  ];

  // Kiểm tra xem tên có chứa từ khóa của ngân hàng không
  if (banks.some((bank) => lowerName.includes(bank))) {
    return "bank";
  }

  // Kiểm tra xem tên có chứa từ khóa của ví điện tử không
  if (ewallets.some((ewallet) => lowerName.includes(ewallet))) {
    return "ewallet";
  }

  // Mặc định là tiền mặt nếu không thuộc hai loại trên
  // hoặc nếu tên có chứa cụm "tiền mặt", "tiền", "cash"
  if (
    lowerName.includes("tiền mặt") ||
    lowerName.includes("tiền") ||
    lowerName.includes("cash")
  ) {
    return "cash";
  }

  // Mặc định là ngân hàng
  return "bank";
};

// Ánh xạ màu sắc cho loại ví
const getColorForType = (type: "bank" | "ewallet" | "cash"): string => {
  switch (type) {
    case "bank":
      return "#2196F3"; // Xanh dương
    case "ewallet":
      return "#FF9800"; // Cam
    case "cash":
      return "#4CAF50"; // Xanh lá
    default:
      return "#2196F3";
  }
};

// Ánh xạ icon cho loại ví
const getIconForType = (type: "bank" | "ewallet" | "cash"): string => {
  switch (type) {
    case "bank":
      return "card-outline"; // Icon thẻ
    case "ewallet":
      return "phone-portrait-outline"; // Icon điện thoại
    case "cash":
      return "cash-outline"; // Icon tiền
    default:
      return "wallet-outline";
  }
};

interface WalletPreviewProps {
  wallet: any;
  isSelected: boolean;
  onSelectionChange: (selected: boolean) => void;
  onWalletUpdate: (updatedData: any) => void;
  isDark: boolean;
  isLast: boolean;
}

const WalletPreview: React.FC<WalletPreviewProps> = ({
  wallet,
  isSelected,
  onSelectionChange,
  onWalletUpdate,
  isDark,
  isLast,
}) => {
  const { t } = useLocalization();

  // Màu nền và màu chủ đạo
  const backgroundColor = isDark ? "#1E3A5F" : "#E3F2FD";
  const cardBgColor = isDark ? "#2C4A77" : "white";
  const borderColor = isDark ? "#3B5E94" : "#D1E4FD";
  const inputBgColor = isDark ? "#213A61" : "#F5F9FF";
  const textColor = isDark ? "#FFFFFF" : "#333333";
  const primaryColor = isDark ? "#64B5F6" : "#2196F3";

  // State để lưu trữ thông tin ví có thể chỉnh sửa
  const [name, setName] = useState(wallet.name || "");
  const [balance, setBalance] = useState(formatMoney(wallet.balance || 0));
  const [isEditing, setIsEditing] = useState(false);
  const [walletType, setWalletType] = useState<"bank" | "ewallet" | "cash">(
    wallet.type || mapWalletNameToType(wallet.name || "")
  );

  // Cập nhật walletType dựa trên tên khi component được render lần đầu
  useEffect(() => {
    if (!wallet.type) {
      const detectedType = mapWalletNameToType(wallet.name || "");
      setWalletType(detectedType);
      onWalletUpdate({ type: detectedType });
    }
  }, []);

  // Cập nhật dữ liệu ví khi người dùng hoàn thành chỉnh sửa
  const handleSaveChanges = () => {
    // Cập nhật dữ liệu ví
    const updatedData = {
      name,
      balance: parseMoney(balance),
      type: walletType,
      color: getColorForType(walletType),
      icon:
        wallet.icon && wallet.icon.endsWith(".png")
          ? wallet.icon
          : getIconForType(walletType),
    };

    onWalletUpdate(updatedData);
    setIsEditing(false);
  };

  // Xử lý khi người dùng nhấp vào item để chọn/bỏ chọn
  const toggleSelection = () => {
    // Chỉ chuyển đổi trạng thái chọn khi không ở chế độ chỉnh sửa
    if (!isEditing) {
      onSelectionChange(!isSelected);
    }
  };

  // Kiểm tra xem wallet.icon có phải là file .png không
  const isImageIcon = wallet.icon && wallet.icon.endsWith(".png");

  // Render biểu tượng ví
  const renderWalletIcon = () => {
    if (isImageIcon && BankLogos[wallet.icon]) {
      return (
        <Image
          source={BankLogos[wallet.icon]}
          style={{ width: 24, height: 24 }}
          contentFit="contain"
        />
      );
    } else {
      return (
        <Ionicons
          name={getIconForType(walletType) as any}
          size={20}
          color="white"
        />
      );
    }
  };

  // Lấy tên loại ví dựa trên type
  const getWalletTypeName = (type: "bank" | "ewallet" | "cash") => {
    switch (type) {
      case "bank":
        return t("moving.walletTypes.bank") || t("wallets.bankType") || "Ngân hàng";
      case "ewallet":
        return t("moving.walletTypes.ewallet") || t("wallets.ewalletType") || "Ví điện tử";
      case "cash":
        return t("moving.walletTypes.cash") || t("wallets.cashType") || "Tiền mặt";
      default:
        return "";
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: cardBgColor,
          borderColor: isSelected ? getColorForType(walletType) : borderColor,
          borderWidth: isSelected ? 2 : 1,
          marginBottom: isLast ? 0 : 12,
        },
      ]}
      onPress={toggleSelection}
      activeOpacity={0.8}
      disabled={isEditing} // Vô hiệu hóa khi đang chỉnh sửa
    >
      <View style={styles.header}>
        <View
          style={[
            styles.checkbox,
            {
              borderColor: isSelected
                ? getColorForType(walletType)
                : borderColor,
              backgroundColor: isSelected
                ? getColorForType(walletType)
                : "transparent",
            },
          ]}
        >
          {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
        </View>

        <TouchableOpacity
          style={[
            styles.editButton,
            {
              backgroundColor: isEditing
                ? getColorForType(walletType)
                : inputBgColor,
            },
          ]}
          onPress={(e) => {
            // Ngăn sự kiện lan tỏa để không kích hoạt toggleSelection
            e.stopPropagation();
            if (isEditing) {
              handleSaveChanges();
            } else {
              setIsEditing(true);
            }
          }}
        >
          <Ionicons
            name={isEditing ? "checkmark" : "create-outline"}
            size={18}
            color={isEditing ? "white" : getColorForType(walletType)}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View
          style={[
            styles.iconContainer,
            { backgroundColor: getColorForType(walletType) },
          ]}
        >
          {renderWalletIcon()}
        </View>

        <View style={styles.walletInfo}>
          {isEditing ? (
            <>
              <TextInput
                style={[
                  styles.nameInput,
                  { color: textColor, backgroundColor: inputBgColor },
                ]}
                value={name}
                onChangeText={setName}
                placeholder={t("moving.walletName") || "Tên ví"}
                placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
              />

              <View style={styles.balanceInputContainer}>
                <TextInput
                  style={[
                    styles.balanceInput,
                    { color: textColor, backgroundColor: inputBgColor },
                  ]}
                  value={balance}
                  onChangeText={(text) => {
                    // Loại bỏ tất cả ký tự không phải số
                    const numericValue = text.replace(/[^\d]/g, "");
                    setBalance(formatMoney(numericValue));
                  }}
                  keyboardType="numeric"
                  placeholder={t("moving.balance") || "Số dư"}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                />
                <Text
                  style={{
                    color: isDark ? "#6D8AC3" : "#90CAF9",
                    marginLeft: 4,
                  }}
                >
                  đ
                </Text>
              </View>

              <View style={styles.typeSelector}>
                {["bank", "ewallet", "cash"].map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.typeOption,
                      {
                        backgroundColor:
                          walletType === type
                            ? getColorForType(type as any)
                            : inputBgColor,
                        borderColor:
                          walletType === type
                            ? getColorForType(type as any)
                            : borderColor,
                      },
                    ]}
                    onPress={(e) => {
                      e.stopPropagation();
                      setWalletType(type as "bank" | "ewallet" | "cash");
                    }}
                  >
                    <Ionicons
                      name={
                        getIconForType(
                          type as "bank" | "ewallet" | "cash"
                        ) as any
                      }
                      size={14}
                      color={
                        walletType === type
                          ? "white"
                          : getColorForType(type as any)
                      }
                      style={{ marginRight: 4 }}
                    />
                    <Text
                      style={{
                        color: walletType === type ? "white" : textColor,
                        fontSize: 12,
                      }}
                    >
                      {getWalletTypeName(type as "bank" | "ewallet" | "cash")}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          ) : (
            <>
              <Text style={[styles.walletName, { color: textColor }]}>
                {wallet.name}
              </Text>

              <Text
                style={[
                  styles.walletBalance,
                  { color: getColorForType(walletType) },
                ]}
              >
                {formatMoney(wallet.balance)} đ
              </Text>

              <View style={styles.walletType}>
                <Ionicons
                  name={getIconForType(walletType) as any}
                  size={14}
                  color={getColorForType(walletType)}
                  style={{ marginRight: 4 }}
                />
                <Text
                  style={{
                    color: isDark ? "#6D8AC3" : "#90CAF9",
                    fontSize: 12,
                  }}
                >
                  {getWalletTypeName(walletType)}
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: "center",
    alignItems: "center",
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  walletInfo: {
    flex: 1,
  },
  walletName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  walletBalance: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 4,
  },
  walletType: {
    flexDirection: "row",
    alignItems: "center",
  },
  nameInput: {
    borderRadius: 8,
    padding: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "transparent",
  },
  balanceInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  balanceInput: {
    flex: 1,
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: "transparent",
  },
  typeSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  typeOption: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    flex: 1,
    marginHorizontal: 2,
    justifyContent: "center",
  },
});

export default WalletPreview;