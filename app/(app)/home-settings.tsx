// File: app/(app)/home-settings.tsx
// <PERSON><PERSON><PERSON> hình "Tùy chọn trang chủ" cho phép người dùng tùy chỉnh số lượng ví và giao dịch hiển thị ở trang chủ
// Thêm tính năng cài đặt hiển thị giới hạn chi tiêu trên trang chủ
// File này liên quan đến: context/ThemeContext.tsx, context/LocalizationContext.tsx, app/(tabs)/index.tsx

import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Slider from "@react-native-community/slider";
import { router, useFocusEffect } from "expo-router";
import React, { useCallback, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";

// Khóa lưu trữ cho AsyncStorage
const HOME_MAX_WALLETS_KEY = "home_max_wallets";
const HOME_MAX_TRANSACTIONS_KEY = "home_max_transactions";
const SHOW_SPENDING_LIMITS_KEY = 'show_spending_limits_on_home';
const HOME_SPENDING_LIMITS_COUNT_KEY = 'home_spending_limits_count';

// Giá trị mặc định
const DEFAULT_MAX_WALLETS = 0; // 0 nghĩa là hiển thị tất cả
const DEFAULT_MAX_TRANSACTIONS = 10;
const DEFAULT_SHOW_SPENDING_LIMITS = false;
const DEFAULT_SPENDING_LIMITS_COUNT = 5;

export default function HomeSettingsScreen() {
  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // States cho sliders
  const [maxWallets, setMaxWallets] = useState(DEFAULT_MAX_WALLETS);
  const [maxTransactions, setMaxTransactions] = useState(DEFAULT_MAX_TRANSACTIONS);

  // States cho spending limits
  const [showSpendingLimits, setShowSpendingLimits] = useState(DEFAULT_SHOW_SPENDING_LIMITS);
  const [spendingLimitsCount, setSpendingLimitsCount] = useState(DEFAULT_SPENDING_LIMITS_COUNT);

  // Các tùy chọn số lượng spending limits
  const limitCountOptions = [
    { value: 3, label: '3' },
    { value: 5, label: '5' },
    { value: 8, label: '8' },
    { value: 10, label: '10' },
    { value: 0, label: 'Tất cả' }, // 0 = hiển thị tất cả
  ];

  // Tải cài đặt khi màn hình được focus
  useFocusEffect(
    useCallback(() => {
      loadSettings();
    }, [])
  );

  // Hàm tải cài đặt từ AsyncStorage
  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      // Lấy dữ liệu từ AsyncStorage
      const savedMaxWallets = await AsyncStorage.getItem(HOME_MAX_WALLETS_KEY);
      const savedMaxTransactions = await AsyncStorage.getItem(HOME_MAX_TRANSACTIONS_KEY);
      const savedShowSpendingLimits = await AsyncStorage.getItem(SHOW_SPENDING_LIMITS_KEY);
      const savedSpendingLimitsCount = await AsyncStorage.getItem(HOME_SPENDING_LIMITS_COUNT_KEY);
      
      // Cập nhật state với dữ liệu đã lưu hoặc giá trị mặc định
      setMaxWallets(savedMaxWallets ? parseInt(savedMaxWallets) : DEFAULT_MAX_WALLETS);
      setMaxTransactions(savedMaxTransactions ? parseInt(savedMaxTransactions) : DEFAULT_MAX_TRANSACTIONS);
      setShowSpendingLimits(savedShowSpendingLimits ? JSON.parse(savedShowSpendingLimits) : DEFAULT_SHOW_SPENDING_LIMITS);
      setSpendingLimitsCount(savedSpendingLimitsCount ? parseInt(savedSpendingLimitsCount) : DEFAULT_SPENDING_LIMITS_COUNT);
    } catch (error) {
      console.error("Error loading home settings:", error);
      Alert.alert(
        t("common.error") || "Lỗi",
        t("settings.loadSettingsError") || "Không thể tải cài đặt. Vui lòng thử lại sau."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm lưu cài đặt vào AsyncStorage
  const saveSettings = async () => {
    try {
      setIsSaving(true);
      
      // Lưu cài đặt vào AsyncStorage
      await AsyncStorage.setItem(HOME_MAX_WALLETS_KEY, maxWallets.toString());
      await AsyncStorage.setItem(HOME_MAX_TRANSACTIONS_KEY, maxTransactions.toString());
      await AsyncStorage.setItem(SHOW_SPENDING_LIMITS_KEY, JSON.stringify(showSpendingLimits));
      await AsyncStorage.setItem(HOME_SPENDING_LIMITS_COUNT_KEY, spendingLimitsCount.toString());
      
      Alert.alert(
        t("common.success") || "Thành công",
        t("settings.saveSettingsSuccess") || "Cài đặt đã được lưu thành công",
        [
          {
            text: "OK",
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error("Error saving home settings:", error);
      Alert.alert(
        t("common.error") || "Lỗi",
        t("settings.saveSettingsError") || "Không thể lưu cài đặt. Vui lòng thử lại sau."
      );
    } finally {
      setIsSaving(false);
    }
  };

  // Khôi phục về cài đặt mặc định
  const resetToDefault = () => {
    Alert.alert(
      t("settings.resetDefaults") || "Khôi phục mặc định",
      t("settings.resetDefaultsConfirm") || "Bạn có chắc chắn muốn khôi phục về cài đặt mặc định không?",
      [
        {
          text: t("common.cancel") || "Hủy",
          style: "cancel"
        },
        {
          text: t("common.reset") || "Khôi phục",
          style: "destructive",
          onPress: async () => {
            try {
              setIsSaving(true);
              // Đặt lại giá trị mặc định
              setMaxWallets(DEFAULT_MAX_WALLETS);
              setMaxTransactions(DEFAULT_MAX_TRANSACTIONS);
              setShowSpendingLimits(DEFAULT_SHOW_SPENDING_LIMITS);
              setSpendingLimitsCount(DEFAULT_SPENDING_LIMITS_COUNT);
              
              // Lưu cài đặt mặc định vào AsyncStorage
              await AsyncStorage.setItem(HOME_MAX_WALLETS_KEY, DEFAULT_MAX_WALLETS.toString());
              await AsyncStorage.setItem(HOME_MAX_TRANSACTIONS_KEY, DEFAULT_MAX_TRANSACTIONS.toString());
              await AsyncStorage.setItem(SHOW_SPENDING_LIMITS_KEY, JSON.stringify(DEFAULT_SHOW_SPENDING_LIMITS));
              await AsyncStorage.setItem(HOME_SPENDING_LIMITS_COUNT_KEY, DEFAULT_SPENDING_LIMITS_COUNT.toString());
              
              Alert.alert(
                t("common.success") || "Thành công",
                t("settings.resetSuccess") || "Cài đặt đã được khôi phục về mặc định"
              );
            } catch (error) {
              console.error("Error resetting settings:", error);
              Alert.alert(
                t("common.error") || "Lỗi",
                t("settings.resetError") || "Không thể khôi phục cài đặt. Vui lòng thử lại sau."
              );
            } finally {
              setIsSaving(false);
            }
          }
        }
      ]
    );
  };

  // Lấy text hiển thị cho giá trị slider
  const getWalletsDisplayText = (value) => {
    if (value === 0) {
      return t("settings.allWallets") || "Tất cả ví";
    }
    return `${value} ${t("settings.wallets") || "ví"}`;
  };

  const getWalletLabel = (value: number) => {
    return `${value} ${t("settings.walletLabel") || "ví"}`;
  };

  // Xử lý khi thay đổi giá trị slider
  const handleWalletsSliderChange = (value) => {
    setMaxWallets(Math.round(value));
  };

  const handleTransactionsSliderChange = (value) => {
    setMaxTransactions(Math.round(value));
  };

  // Xử lý toggle spending limits
  const handleSpendingLimitsToggle = (value: boolean) => {
    setShowSpendingLimits(value);
  };

  // Xử lý thay đổi số lượng spending limits
  const handleSpendingLimitsCountChange = (count: number) => {
    setSpendingLimitsCount(count);
  };

  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("settings.homeOptions") || "Tùy chọn trang chủ"}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View
          style={[
            styles.loadingContainer,
            { backgroundColor: "transparent" },
          ]}
        >
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            {t("common.loading") || "Đang tải..."}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("settings.homeOptions") || "Tùy chọn trang chủ"}
        </Text>
        <TouchableOpacity
          style={[
            styles.saveButton, 
            {
              backgroundColor: themeColors.primary,
              shadowColor: isDark ? "#0F1C30" : "#1565C0",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 3,
              elevation: 2,
            }
          ]}
          onPress={saveSettings}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.saveText}>
              {t("common.save") || "Lưu"}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ backgroundColor: 'transparent' }}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Card cho cài đặt ví */}
        <View style={[styles.cardContainer, getCardStyle('medium')]}>
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Ionicons name="wallet-outline" size={22} color={themeColors.primary} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                {t("settings.walletDisplay") || "Hiển thị ví"}
              </Text>
            </View>
            
            <Text style={[styles.sectionDescription, { color: themeColors.secondaryText }]}>
              {t("settings.walletDisplayDescription") || 
                "Tùy chỉnh số lượng ví hiển thị ở trang chủ. Chọn 0 để hiển thị tất cả ví."}
            </Text>
            
            <View style={styles.sliderContainer}>
              <View style={styles.sliderValueContainer}>
                <Text style={[styles.sliderValue, { color: themeColors.primary }]}>
                  {getWalletsDisplayText(maxWallets)}
                </Text>
              </View>
              
              <Slider
                style={styles.slider}
                minimumValue={0}
                maximumValue={10}
                step={1}
                value={maxWallets}
                onValueChange={handleWalletsSliderChange}
                minimumTrackTintColor={themeColors.primary}
                maximumTrackTintColor={isDark ? "#3B5E94" : "#BBDEFB"}
                thumbTintColor={themeColors.primary}
              />
              
              <View style={styles.sliderLabels}>
                <Text style={[styles.sliderLabel, { color: themeColors.secondaryText }]}>
                  {t("settings.all") || "Tất cả"}
                </Text>
                <Text style={[styles.sliderLabel, { color: themeColors.secondaryText }]}>
                  10
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Card cho cài đặt giao dịch */}
        <View style={[styles.cardContainer, getCardStyle('medium')]}>
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Ionicons name="time-outline" size={22} color={themeColors.primary} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                {t("settings.transactionDisplay") || "Hiển thị giao dịch"}
              </Text>
            </View>
            
            <Text style={[styles.sectionDescription, { color: themeColors.secondaryText }]}>
              {t("settings.transactionDisplayDescription") || 
                "Tùy chỉnh số lượng giao dịch gần đây hiển thị ở trang chủ."}
            </Text>
            
            <View style={styles.sliderContainer}>
              <View style={styles.sliderValueContainer}>
                <Text style={[styles.sliderValue, { color: themeColors.primary }]}>
                  {maxTransactions} {t("settings.transactionLabel") || "giao dịch"}
                </Text>
              </View>
              
              <Slider
                style={styles.slider}
                minimumValue={3}
                maximumValue={20}
                step={1}
                value={maxTransactions}
                onValueChange={handleTransactionsSliderChange}
                minimumTrackTintColor={themeColors.primary}
                maximumTrackTintColor={isDark ? "#3B5E94" : "#BBDEFB"}
                thumbTintColor={themeColors.primary}
              />
              
              <View style={styles.sliderLabels}>
                <Text style={[styles.sliderLabel, { color: themeColors.secondaryText }]}>
                  3
                </Text>
                <Text style={[styles.sliderLabel, { color: themeColors.secondaryText }]}>
                  20
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Card cho cài đặt giới hạn chi tiêu */}
        <View style={[styles.cardContainer, getCardStyle('medium')]}>
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <Ionicons name="speedometer-outline" size={22} color={themeColors.primary} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Giới hạn chi tiêu
              </Text>
              <View style={{ flex: 1 }} />
              <Switch
                value={showSpendingLimits}
                onValueChange={handleSpendingLimitsToggle}
                trackColor={{ false: themeColors.border, true: themeColors.primary + '50' }}
                thumbColor={showSpendingLimits ? themeColors.primary : themeColors.secondaryText}
              />
            </View>
            
            <Text style={[styles.sectionDescription, { color: themeColors.secondaryText }]}>
              Hiển thị danh mục có giới hạn chi tiêu ngay trên trang chủ để theo dõi dễ dàng
            </Text>

            {/* Tùy chọn số lượng danh mục khi bật */}
            {showSpendingLimits && (
              <View style={[styles.spendingLimitsOptions, { backgroundColor: 'transparent' }]}>
                <Text style={[styles.optionsLabel, { color: themeColors.text }]}>
                  Số lượng danh mục hiển thị:
                </Text>
                
                <View style={[styles.countButtonsContainer, { backgroundColor: 'transparent' }]}>
                  {limitCountOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.countButton,
                        {
                          backgroundColor: spendingLimitsCount === option.value 
                            ? themeColors.primary 
                            : themeColors.cardBackground,
                          borderColor: spendingLimitsCount === option.value 
                            ? themeColors.primary 
                            : themeColors.border,
                          borderWidth: 1,
                        }
                      ]}
                      onPress={() => handleSpendingLimitsCountChange(option.value)}
                    >
                      <Text
                        style={[
                          styles.countButtonText,
                          {
                            color: spendingLimitsCount === option.value 
                              ? 'white' 
                              : themeColors.text,
                          }
                        ]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Thông tin thêm */}
                <View style={[styles.infoBox, { backgroundColor: themeColors.cardBackground, borderColor: themeColors.border }]}>
                  <Ionicons
                    name="information-circle-outline"
                    size={16}
                    color={themeColors.primary}
                    style={{ marginRight: 6 }}
                  />
                  <Text style={[styles.infoText, { color: themeColors.secondaryText }]}>
                    Ưu tiên hiển thị danh mục có % cao nhất hoặc gần đạt ngưỡng cảnh báo
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
        
        {/* Nút khôi phục mặc định */}
        <TouchableOpacity
          style={[
            styles.resetButton,
            getCardStyle('light'),
            {
              backgroundColor: isDark ? "#42212A" : "#FFEBEE",
              borderColor: isDark ? "#4D2A33" : "#FFCDD2",
              shadowColor: isDark ? "#1A0A0A" : "#D32F2F",
            }
          ]}
          onPress={resetToDefault}
        >
          <Ionicons name="refresh-outline" size={20} color={isDark ? "#FF8A80" : "#F44336"} />
          <Text 
            style={[
              styles.resetButtonText,
              { color: isDark ? "#FF8A80" : "#F44336" }
            ]}
          >
            {t("settings.resetDefaults") || "Khôi phục mặc định"}
          </Text>
        </TouchableOpacity>

        {/* Giải thích */}
        <View style={styles.explainContainer}>
          <Text style={[styles.explainText, { color: themeColors.secondaryText }]}>
            {t("settings.homeOptionsExplain") ||
              "Những cài đặt này sẽ được áp dụng cho trang chủ. Bạn vẫn có thể xem đầy đủ các ví và giao dịch trong mục Ví và Giao dịch."}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  saveText: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  cardContainer: {
    margin: 16,
    marginBottom: 8,
    overflow: "hidden",
    borderRadius: 12,
  },
  sectionContainer: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "600",
    marginLeft: 10,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 20,
  },
  sliderContainer: {
    marginTop: 10,
  },
  sliderValueContainer: {
    alignItems: "center",
    marginBottom: 12,
  },
  sliderValue: {
    fontSize: 18,
    fontWeight: "bold",
  },
  slider: {
    width: "100%",
    height: 40,
  },
  sliderLabels: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 4,
    marginTop: -10,
  },
  sliderLabel: {
    fontSize: 13,
  },
  // Spending Limits Options Styles
  spendingLimitsOptions: {
    marginTop: 12,
  },
  optionsLabel: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 12,
  },
  countButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  countButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 60,
    alignItems: 'center',
  },
  countButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 10,
    borderRadius: 6,
    borderWidth: 1,
    marginTop: 8,
  },
  infoText: {
    fontSize: 12,
    lineHeight: 16,
    flex: 1,
  },
  resetButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 16,
    marginTop: 8,
    paddingVertical: 16,
    borderRadius: 12,
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  explainContainer: {
    marginHorizontal: 24,
    marginTop: 20,
    marginBottom: 10,
    alignItems: "center",
  },
  explainText: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
});