// File: app/(app)/share-app.js
// File này hiển thị màn hình chia sẻ ứng dụng với thiết kế trực quan
// File này liên quan đến: app/(tabs)/settings.js, utils/updateService.js

import { useTheme } from '@/context/ThemeContext';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as Clipboard from 'expo-clipboard';
import * as Haptics from 'expo-haptics';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import * as Linking from 'expo-linking';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const APP_STORE_ID = '6745965337';
const APP_STORE_URL = `https://apps.apple.com/app/id${APP_STORE_ID}`;
const PLAY_STORE_URL = `https://play.google.com/store/apps/details?id=com.manhvu.AIMoney`;
const FACEBOOK_SHARE_URL = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(APP_STORE_URL)}`;
const ZALO_SHARE_MESSAGE = `Khám phá AIMoney - Ứng dụng quản lý tài chính cá nhân! Tải về tại: ${APP_STORE_URL}`;

export default function ShareAppScreen() {
  const {
    isDark,
    themeColors,
    getCardStyle,
    getIconContainerStyle,
    getShadowStyle
  } = useTheme();
  
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // Hàm quay lại màn hình trước
  const handleGoBack = () => {
    router.back();
  };

  // Hàm mở App Store/Play Store
  const openStoreLink = async () => {
    try {
      setLoading(true);
      const url = Platform.OS === 'ios' ? APP_STORE_URL : PLAY_STORE_URL;
      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        Alert.alert(
          'Không thể mở',
          'Không thể mở liên kết đến cửa hàng ứng dụng.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Lỗi khi mở link cửa hàng:', error);
      Alert.alert(
        'Lỗi',
        'Đã xảy ra lỗi khi cố gắng mở liên kết.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Hàm chia sẻ qua Facebook
  const shareViaFacebook = async () => {
    try {
      setLoading(true);
      
      const canOpen = await Linking.canOpenURL(FACEBOOK_SHARE_URL);
      if (canOpen) {
        await Linking.openURL(FACEBOOK_SHARE_URL);
      } else {
        // Thử mở app Facebook nếu có
        const fbAppUrl = `fb://share?u=${encodeURIComponent(APP_STORE_URL)}`;
        const canOpenFbApp = await Linking.canOpenURL(fbAppUrl);
        
        if (canOpenFbApp) {
          await Linking.openURL(fbAppUrl);
        } else {
          // Nếu không mở được, copy link vào clipboard
          copyToClipboard(APP_STORE_URL);
          Alert.alert(
            'Không tìm thấy Facebook',
            'Không thể mở Facebook để chia sẻ. Đã sao chép link vào bộ nhớ tạm.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Lỗi khi chia sẻ qua Facebook:', error);
      copyToClipboard(APP_STORE_URL);
    } finally {
      setLoading(false);
    }
  };
  
  // Hàm chia sẻ qua Zalo
  const shareViaZalo = async () => {
    try {
      setLoading(true);
      // Thử mở Zalo với scheme
      const zaloUrl = `zalo://forward?text=${encodeURIComponent(ZALO_SHARE_MESSAGE)}`;
      const canOpenZalo = await Linking.canOpenURL(zaloUrl);
      
      if (canOpenZalo) {
        await Linking.openURL(zaloUrl);
      } else {
        // Nếu không mở được Zalo, copy link để người dùng tự dán
        copyToClipboard(APP_STORE_URL);
        Alert.alert(
          'Không tìm thấy Zalo',
          'Không thể mở Zalo để chia sẻ. Đã sao chép link vào bộ nhớ tạm.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Lỗi khi chia sẻ qua Zalo:', error);
      copyToClipboard(APP_STORE_URL);
    } finally {
      setLoading(false);
    }
  };
  
  // Hàm copy link vào clipboard
  const copyToClipboard = async (text) => {
    try {
      await Clipboard.setStringAsync(text);
      // Kích hoạt phản hồi xúc giác khi copy thành công
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
      
      Alert.alert(
        'Đã sao chép!',
        'Link ứng dụng đã được sao chép vào bộ nhớ tạm. Bạn có thể dán vào bất kỳ đâu để chia sẻ.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Lỗi khi sao chép:', error);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen 
        options={{
          headerShown: false,
        }}
      />
      
      {/* Custom Header */}
      <View style={styles.customHeader}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={handleGoBack}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerText, { color: themeColors.text }]}>
          Giới thiệu bạn bè
        </Text>
        <View style={styles.placeholderView} />
      </View>

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Banner ứng dụng */}
        <LinearGradient
          colors={isDark ? 
            ['#1E3A8A', '#2563EB', '#3B82F6'] : 
            ['#1E40AF', '#3B82F6', '#60A5FA']}
          style={styles.headerBanner}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}
        >
          <View style={styles.headerContent}>
            <Image
              source={require('@/assets/images/icon.png')}
              style={styles.appIcon}
              contentFit="cover"
            />
            
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>AIMoney</Text>
              <Text style={styles.headerSubtitle}>Quản lý tài chính thông minh</Text>
              
              <TouchableOpacity 
                style={styles.storeButton}
                onPress={openStoreLink}
                activeOpacity={0.8}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <>
                    <Ionicons 
                      name={Platform.OS === 'ios' ? 'logo-apple' : 'logo-google-playstore'} 
                      size={18} 
                      color="#FFFFFF" 
                    />
                    <Text style={styles.storeButtonText}>
                      Xem trên {Platform.OS === 'ios' ? 'App Store' : 'Play Store'}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
        
        {/* Card chia sẻ */}
        <View style={[
          styles.shareCard,
          getCardStyle('high'),
          { backgroundColor: themeColors.cardBackground },
          { marginTop: -50 }
        ]}>
          <View style={styles.shareCardHeader}>
            <Text style={[styles.shareCardTitle, { color: themeColors.text }]}>
              Giới thiệu AIMoney
            </Text>
            <Text style={[styles.shareCardSubtitle, { color: themeColors.secondaryText }]}>
              Chia sẻ ứng dụng với bạn bè của bạn
            </Text>
          </View>
          
          {/* Link copy section */}
          <View style={[
            styles.linkContainer,
            { 
              backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.1)',
              borderColor: isDark ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.3)' 
            }
          ]}>
            <Text 
              style={[
                styles.linkText, 
                { color: isDark ? '#90CAF9' : '#1E40AF' }
              ]}
              numberOfLines={1}
              ellipsizeMode="middle"
            >
              {APP_STORE_URL}
            </Text>
            <TouchableOpacity
              style={[
                styles.copyButton,
                { backgroundColor: isDark ? '#3B82F6' : '#2563EB' }
              ]}
              onPress={() => copyToClipboard(APP_STORE_URL)}
              activeOpacity={0.8}
            >
              {copied ? (
                <Ionicons name="checkmark" size={20} color="#FFFFFF" />
              ) : (
                <Ionicons name="copy-outline" size={20} color="#FFFFFF" />
              )}
              <Text style={styles.copyButtonText}>
                {copied ? 'Đã sao chép' : 'Sao chép'}
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Chia sẻ options */}
          <Text style={[styles.sectionTitle, { color: themeColors.text, marginTop: 24 }]}>
            Chọn cách chia sẻ
          </Text>
          
          <View style={styles.shareOptions}>
            {/* Facebook Option */}
            <TouchableOpacity
              style={[
                styles.shareOptionButton,
                { backgroundColor: isDark ? 'rgba(24, 119, 242, 0.2)' : 'rgba(24, 119, 242, 0.1)' }
              ]}
              onPress={shareViaFacebook}
              activeOpacity={0.8}
            >
              <View style={[styles.optionIconContainer, { backgroundColor: '#1877F2' }]}>
                <Ionicons name="logo-facebook" size={22} color="#FFFFFF" />
              </View>
              <Text style={[styles.optionText, { color: themeColors.text }]}>Facebook</Text>
            </TouchableOpacity>
            
            {/* Zalo Option */}
            <TouchableOpacity
              style={[
                styles.shareOptionButton,
                { backgroundColor: isDark ? 'rgba(0, 174, 239, 0.2)' : 'rgba(0, 174, 239, 0.1)' }
              ]}
              onPress={shareViaZalo}
              activeOpacity={0.8}
            >
              <View style={[styles.optionIconContainer, { backgroundColor: '#0068FF' }]}>
                <Text style={styles.zaloIcon}>Z</Text>
              </View>
              <Text style={[styles.optionText, { color: themeColors.text }]}>Zalo</Text>
            </TouchableOpacity>
            
            {/* Copy Option */}
            <TouchableOpacity
              style={[
                styles.shareOptionButton,
                { backgroundColor: isDark ? 'rgba(37, 99, 235, 0.2)' : 'rgba(37, 99, 235, 0.1)' }
              ]}
              onPress={() => copyToClipboard(APP_STORE_URL)}
              activeOpacity={0.8}
            >
              <View style={[styles.optionIconContainer, { backgroundColor: '#3B82F6' }]}>
                <Ionicons name="copy" size={22} color="#FFFFFF" />
              </View>
              <Text style={[styles.optionText, { color: themeColors.text }]}>Sao chép</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Lợi ích section */}
        <View style={[
          styles.benefitsCard,
          getCardStyle('medium'),
          { backgroundColor: themeColors.cardBackground }
        ]}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Tại sao nên sử dụng AIMoney?
          </Text>
          
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconWrap}>
                <MaterialCommunityIcons name="robot" size={24} color="#8B5CF6" />
              </View>
              <View style={styles.benefitContent}>
                <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
                  Trợ lý AI thông minh
                </Text>
                <Text style={[styles.benefitDesc, { color: themeColors.secondaryText }]}>
                  Được hỗ trợ bởi AI để phân tích chi tiêu và đưa ra gợi ý tài chính phù hợp
                </Text>
              </View>
            </View>

            <View style={styles.benefitItem}>
              <View style={styles.benefitIconWrap}>
                <MaterialCommunityIcons name="finance" size={24} color="#3B82F6" />
              </View>
              <View style={styles.benefitContent}>
                <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
                  Quản lý tài chính dễ dàng
                </Text>
                <Text style={[styles.benefitDesc, { color: themeColors.secondaryText }]}>
                  Quản lý thu chi, theo dõi ngân sách và lập kế hoạch tiết kiệm
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconWrap}>
                <MaterialCommunityIcons name="chart-line" size={24} color="#10B981" />
              </View>
              <View style={styles.benefitContent}>
                <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
                  Báo cáo chi tiết
                </Text>
                <Text style={[styles.benefitDesc, { color: themeColors.secondaryText }]}>
                  Biểu đồ trực quan giúp phân tích chi tiêu và phát hiện xu hướng
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconWrap}>
                <MaterialCommunityIcons name="shield-lock" size={24} color="#F59E0B" />
              </View>
              <View style={styles.benefitContent}>
                <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
                  Bảo mật tuyệt đối
                </Text>
                <Text style={[styles.benefitDesc, { color: themeColors.secondaryText }]}>
                  Dữ liệu được mã hóa và không bao giờ chia sẻ với bên thứ ba
                </Text>
              </View>
            </View>
            
            <View style={styles.benefitItem}>
              <View style={styles.benefitIconWrap}>
                <MaterialCommunityIcons name="palette-swatch" size={24} color="#EC4899" />
              </View>
              <View style={styles.benefitContent}>
                <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
                  Giao diện hiện đại
                </Text>
                <Text style={[styles.benefitDesc, { color: themeColors.secondaryText }]}>
                  Thiết kế đẹp mắt, hỗ trợ chế độ tối và dễ sử dụng
                </Text>
              </View>
            </View>
          </View>
        </View>
        
        {/* Action Button */}
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: isDark ? '#3B82F6' : '#2563EB' }
          ]}
          onPress={() => copyToClipboard(APP_STORE_URL)}
          activeOpacity={0.7}
        >
          <Text style={styles.actionButtonText}>
            Sao chép link ứng dụng
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 32,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  customHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 44,
    marginTop: 20,
  },
  backButton: {
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 18,
  },
  headerText: {
    fontSize: 17,
    fontWeight: '600',
  },
  placeholderView: {
    width: 36,
  },
  headerBanner: {
    paddingTop: 20,
    paddingBottom: 80,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTextContainer: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 12,
  },
  storeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  storeButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 6,
  },
  shareCard: {
    marginHorizontal: 16,
    borderRadius: 20,
    padding: 24,
    marginBottom: 16,
  },
  shareCardHeader: {
    marginBottom: 20,
  },
  shareCardTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  shareCardSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    padding: 4,
    height: 50,
  },
  linkText: {
    flex: 1,
    paddingLeft: 12,
    fontSize: 14,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 8,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 6,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  shareOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  shareOptionButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  zaloIcon: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  optionText: {
    fontWeight: '600',
  },
  benefitsCard: {
    marginHorizontal: 16,
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
  },
  benefitsList: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'flex-start',
  },
  benefitIconWrap: {
    width: 46,
    height: 46,
    borderRadius: 23,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  benefitDesc: {
    fontSize: 14,
    lineHeight: 20,
  },
  actionButton: {
    marginHorizontal: 16,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0, // Xóa margin ở dưới nút
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});