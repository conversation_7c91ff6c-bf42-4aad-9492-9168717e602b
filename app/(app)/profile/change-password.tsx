// File: app/profile/change-password.tsx
// File này liên quan đến: context/ThemeContext.tsx, context/LocalizationContext.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { supabase } from "@/lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function ChangePasswordScreen() {
  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validatePassword = (password: string) => {
    return {
      length: password.length >= 6,
      number: /[0-9]/.test(password),
    };
  };

  const handleSave = async () => {
    // Validate inputs
    if (!currentPassword) {
      Alert.alert(t("common.error"), t("profile.currentPasswordRequired"));
      return;
    }

    if (!newPassword) {
      Alert.alert(t("common.error"), t("profile.newPasswordRequired"));
      return;
    }

    if (newPassword.length < 6) {
      Alert.alert(t("common.error"), t("profile.passwordLengthError"));
      return;
    }

    if (newPassword !== confirmNewPassword) {
      Alert.alert(t("common.error"), t("profile.passwordMismatch"));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Lấy thông tin phiên hiện tại
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData.session || !sessionData.session.user) {
        throw new Error("Bạn chưa đăng nhập");
      }

      // Đổi mật khẩu bằng cách cung cấp mật khẩu hiện tại
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      // Thành công
      Alert.alert(t("common.success"), t("profile.passwordUpdateSuccess"), [
        { text: t("common.confirm"), onPress: () => router.back() },
      ]);
    } catch (error: any) {
      console.error("Lỗi khi đổi mật khẩu:", error);

      // Hiển thị thông báo lỗi phù hợp
      if (error.message.includes("auth")) {
        setError("Không thể xác thực. Vui lòng đăng nhập lại và thử lại.");
      } else {
        setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Kiểm tra điều kiện mật khẩu
  const validation = validatePassword(newPassword);
  const isAllValid = validation.length && validation.number;
  const isFormValid =
    currentPassword && isAllValid && newPassword === confirmNewPassword;

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("profile.changePassword")}
        </Text>
        <TouchableOpacity
          style={[
            styles.saveButton,
            {
              backgroundColor: isFormValid
                ? themeColors.primary
                : isDark
                ? "#375980"
                : "#BBDEFB",
              shadowColor: isDark ? "#0F1C30" : "#1565C0",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 3,
              elevation: 2,
            },
          ]}
          onPress={handleSave}
          disabled={loading || !isFormValid}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text
              style={[
                styles.saveText,
                {
                  color: isFormValid ? "white" : themeColors.secondaryText,
                },
              ]}
            >
              {t("common.save")}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ backgroundColor: 'transparent' }}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Main Card */}
        <View
          style={[
            styles.cardContainer,
            getCardStyle('medium')
          ]}
        >
          {error && (
            <View
              style={[
                styles.errorContainer,
                {
                  backgroundColor: isDark
                    ? "rgba(255,59,48,0.1)"
                    : "rgba(255,59,48,0.05)",
                  borderColor: isDark ? "#4D2A33" : "#FFCDD2",
                },
              ]}
            >
              <Ionicons
                name="alert-circle-outline"
                size={22}
                color={isDark ? "#FF8A80" : "#FF3B30"}
              />
              <Text
                style={[
                  styles.errorText,
                  { color: isDark ? "#FF8A80" : "#FF3B30" },
                ]}
              >
                {error}
              </Text>
            </View>
          )}

          <View style={styles.formSection}>
            {/* Current Password Field */}
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.currentPassword")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("profile.currentPassword")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  secureTextEntry={!showCurrentPassword}
                  value={currentPassword}
                  onChangeText={setCurrentPassword}
                  editable={!loading}
                />
                <TouchableOpacity
                  style={styles.visibilityToggle}
                  onPress={() => setShowCurrentPassword(!showCurrentPassword)}
                  disabled={loading}
                >
                  <Ionicons
                    name={showCurrentPassword ? "eye-off" : "eye"}
                    size={20}
                    color={themeColors.secondaryText}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* New Password Field */}
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.newPassword")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("profile.newPassword")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  secureTextEntry={!showNewPassword}
                  value={newPassword}
                  onChangeText={setNewPassword}
                  editable={!loading}
                />
                <TouchableOpacity
                  style={styles.visibilityToggle}
                  onPress={() => setShowNewPassword(!showNewPassword)}
                  disabled={loading}
                >
                  <Ionicons
                    name={showNewPassword ? "eye-off" : "eye"}
                    size={20}
                    color={themeColors.secondaryText}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Confirm New Password Field */}
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.confirmNewPassword")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("profile.confirmNewPassword")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  secureTextEntry={!showConfirmPassword}
                  value={confirmNewPassword}
                  onChangeText={setConfirmNewPassword}
                  editable={!loading}
                />
                <TouchableOpacity
                  style={styles.visibilityToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={loading}
                >
                  <Ionicons
                    name={showConfirmPassword ? "eye-off" : "eye"}
                    size={20}
                    color={themeColors.secondaryText}
                  />
                </TouchableOpacity>
              </View>
              {newPassword &&
                confirmNewPassword &&
                newPassword !== confirmNewPassword && (
                  <Text
                    style={[
                      styles.mismatchText,
                      { color: isDark ? "#FF8A80" : "#FF3B30" },
                    ]}
                  >
                    {t("profile.passwordMismatch")}
                  </Text>
                )}
            </View>

            {/* Password Requirements */}
            <View
              style={[
                styles.requirementsContainer,
                {
                  backgroundColor: "transparent",
                  borderWidth: 0,
                  borderColor: "transparent",
                  shadowColor: "transparent",
                  shadowOffset: { width: 0, height: 0 },
                  shadowOpacity: 0,
                  shadowRadius: 0,
                  elevation: 0,
                },
              ]}
            >
              <Text
                style={[
                  styles.requirementsTitle,
                  { color: themeColors.secondaryText, marginLeft: 4 },
                ]}
              >
                {t("profile.passwordRequirements")}
              </Text>
              <View style={styles.requirementItem}>
                <Ionicons
                  name={
                    validation.length ? "checkmark-circle" : "ellipse-outline"
                  }
                  size={16}
                  color={
                    validation.length ? "#4CAF50" : themeColors.secondaryText
                  }
                  style={styles.requirementIcon}
                />
                <Text
                  style={[
                    styles.requirementText,
                    {
                      color: validation.length
                        ? isDark
                          ? "#81C784"
                          : "#4CAF50"
                        : themeColors.text,
                    },
                  ]}
                >
                  {t("profile.passwordLength")}
                </Text>
              </View>
              <View style={styles.requirementItem}>
                <Ionicons
                  name={
                    validation.number ? "checkmark-circle" : "ellipse-outline"
                  }
                  size={16}
                  color={
                    validation.number ? "#4CAF50" : themeColors.secondaryText
                  }
                  style={styles.requirementIcon}
                />
                <Text
                  style={[
                    styles.requirementText,
                    {
                      color: validation.number
                        ? isDark
                          ? "#81C784"
                          : "#4CAF50"
                        : themeColors.text,
                    },
                  ]}
                >
                  {t("profile.passwordNumber")}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[
            styles.saveButtonLarge,
            getCardStyle('medium'),
            {
              backgroundColor: isFormValid
                ? themeColors.primary
                : isDark
                ? "#375980"
                : "#BBDEFB",
              height: 56,
            },
          ]}
          onPress={handleSave}
          disabled={loading || !isFormValid}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text
              style={[
                styles.saveButtonText,
                {
                  color: isFormValid ? "#fff" : themeColors.secondaryText,
                },
              ]}
            >
              {t("profile.saveChanges")}
            </Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  saveText: {
    fontSize: 16,
    fontWeight: "600",
  },
  cardContainer: {
    margin: 16,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 14,
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  errorText: {
    marginLeft: 12,
    flex: 1,
    fontSize: 14,
  },
  formSection: {
    backgroundColor: "transparent",
  },
  formGroup: {
    marginBottom: 20,
    position: "relative",
    backgroundColor: "transparent",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
    paddingLeft: 4,
  },
  inputContainer: {
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    height: 54,
  },
  input: {
    height: 54,
    paddingHorizontal: 16,
    fontSize: 16,
    flex: 1,
  },
  visibilityToggle: {
    padding: 12,
  },
  mismatchText: {
    fontSize: 14,
    marginTop: 6,
    marginLeft: 4,
  },
  requirementsContainer: {
    padding: 0,
    borderRadius: 0,
    marginTop: 12,
  },
  requirementsTitle: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 12,
  },
  requirementItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  requirementIcon: {
    marginRight: 10,
  },
  requirementText: {
    fontSize: 14,
  },
  saveButtonLarge: {
    marginHorizontal: 16,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 12,
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: "600",
  },
});