// File: app/profile/index.tsx
// File này liên quan đến: context/ThemeContext.tsx, context/AuthContext.tsx, context/LocalizationContext.tsx

import { Text, View } from "@/components/Themed";
import { useAuth } from "@/context/AuthContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { UserModel, UserProfile } from "@/lib/models/user";
import { supabase } from "@/lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { manipulateAsync, SaveFormat } from "expo-image-manipulator";
import * as ImagePicker from "expo-image-picker";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function ProfileScreen() {
  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();
  const { setAvatar } = useAuth();

  const [loading, setLoading] = useState(true);
  const [savingProfile, setSavingProfile] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [fullName, setFullName] = useState("");
  const [phone, setPhone] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [avatarError, setAvatarError] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  useEffect(() => {
    if (profile?.avatar_url) {
      setAvatarError(false);
    }
  }, [profile?.avatar_url]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const userProfile = await UserModel.getCurrentProfile();

      // Xử lý URL ảnh nếu cần
      if (userProfile && userProfile.avatar_url) {
        // Đảm bảo URL ảnh đúng định dạng
        // Thêm timestamp để đảm bảo không bị cache
        userProfile.avatar_url = `${userProfile.avatar_url}?t=${Date.now()}`;
        console.log("Avatar URL:", userProfile.avatar_url);
      }

      setProfile(userProfile);

      if (userProfile) {
        setFullName(userProfile.full_name || "");
        setPhone(userProfile.phone || "");
      } else {
        // If for some reason we still don't have a profile, show friendly message
        setError("Could not load profile. Please try again later.");
      }
    } catch (error) {
      console.error("Error loading profile:", error);
      setError("Could not load profile information. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!profile) return;

    try {
      setSavingProfile(true);
      setError(null);

      const updatedProfile = await UserModel.updateProfile({
        full_name: fullName,
        phone: phone,
      });

      if (updatedProfile) {
        setProfile(updatedProfile);
        Alert.alert(t("common.success"), t("profile.updateSuccess"));
      } else {
        setError("Could not update profile information. Please try again.");
      }
    } catch (error) {
      console.error("Error saving profile:", error);
      setError("Could not update profile information. Please try again.");
    } finally {
      setSavingProfile(false);
    }
  };

  const handlePickImage = async () => {
    // Request permission
    const permissionResult =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert(t("common.error"), t("profile.photoPermissionRequired"));
      return;
    }

    // Launch the image picker
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
      allowsMultipleSelection: false,
      exif: false,
      base64: false,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      try {
        // Reset avatar error flag when uploading new avatar
        setAvatarError(false);

        // Resize and compress the image using ImageManipulator
        // Resize to 150x150 to have even smaller file size
        const manipResult = await manipulateAsync(
          result.assets[0].uri,
          [{ resize: { width: 500, height: 500 } }],
          {
            compress: 0.2, // Giảm chất lượng xuống 20% để có kích thước nhỏ hơn
            format: SaveFormat.JPEG,
            base64: true,
          }
        );

        console.log(
          "Resized image dimensions:",
          manipResult.width,
          "x",
          manipResult.height
        );

        // Upload the resized image - using URI first, with base64 as fallback
        await uploadImage(manipResult.uri, manipResult.base64);
      } catch (error) {
        console.error("Error manipulating image:", error);
        Alert.alert(t("common.error"), t("profile.uploadError"));
      }
    }
  };

  const uploadImage = async (uri: string, base64Data?: string) => {
    try {
      setUploadingAvatar(true);
      setError(null);

      let publicUrl;
      try {
        // Thử tải lên bằng URI trước
        publicUrl = await UserModel.uploadAvatar(uri);
      } catch (uriError) {
        // Nếu tải lên URI thất bại và có base64, thử dùng base64
        if (base64Data) {
          console.log("URI upload failed, trying with base64");
          publicUrl = await UserModel.uploadAvatarBase64(base64Data);
        } else {
          throw uriError;
        }
      }

      if (publicUrl) {
        // Thêm timestamp để tránh caching
        const cachedUrl = `${publicUrl}?t=${Date.now()}`;
        console.log("New avatar URL:", cachedUrl);
        setAvatar(cachedUrl);

        // Update local state with new avatar URL
        setProfile((prev) =>
          prev ? { ...prev, avatar_url: cachedUrl } : null
        );
        Alert.alert(t("common.success"), t("profile.avatarUpdateSuccess"));
      } else {
        setError("Could not upload avatar image. Please try again.");
        Alert.alert(t("common.error"), t("profile.avatarUpdateError"));
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      setError("Could not upload avatar image. Please try again.");
      Alert.alert(t("common.error"), t("profile.avatarUpdateError"));
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleDeleteAccount = async () => {
    Alert.alert(t("profile.deleteAccount"), t("profile.deleteAccountConfirm"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("common.delete"),
        style: "destructive",
        onPress: async () => {
          try {
            setLoading(true);
            // Call the account deletion method
            const success = await UserModel.deleteAccount();

            if (success) {
              // Sign out the user after successful deletion
              await supabase.auth.signOut();

              Alert.alert(
                t("common.success"),
                t("profile.accountDeletedSuccess"),
                [
                  {
                    text: "OK",
                    onPress: () => router.replace("/(auth)/login"),
                  },
                ]
              );
            } else {
              throw new Error("Failed to delete account");
            }
          } catch (error) {
            console.error("Error deleting account:", error);
            Alert.alert(
              t("common.error"),
              t("profile.deleteAccountError") ||
                "Could not delete your account. Please try again later."
            );
            setLoading(false);
          }
        },
      },
    ]);
  };

  if (error) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("profile.title")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View
          style={[styles.errorContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={isDark ? "#FF8A80" : "#FF3B30"}
          />
          <Text style={[styles.errorText, { color: themeColors.text }]}>{error}</Text>
          <TouchableOpacity
            style={[
              styles.retryButton,
              {
                backgroundColor: themeColors.primary,
                shadowColor: isDark ? "#0F1C30" : "#1565C0",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 3,
              },
            ]}
            onPress={loadProfile}
          >
            <Text style={styles.retryButtonText}>{t("common.retry")}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (loading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("profile.title")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View
          style={[styles.loadingContainer, { backgroundColor: "transparent" }]}
        >
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("profile.title")}
        </Text>
        <TouchableOpacity
          style={[
            styles.saveButton,
            {
              backgroundColor: themeColors.primary,
              shadowColor: isDark ? "#0F1C30" : "#1565C0",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.2,
              shadowRadius: 3,
              elevation: 2,
            },
          ]}
          onPress={handleSaveProfile}
          disabled={savingProfile}
        >
          {savingProfile ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.saveText}>{t("common.save")}</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ backgroundColor: 'transparent' }}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Card Container */}
        <View
          style={[
            styles.cardContainer,
            getCardStyle('medium')
          ]}
        >
          {/* Avatar Section */}
          <View
            style={[styles.avatarSection, { backgroundColor: "transparent" }]}
          >
            <View
              style={[
                styles.avatarContainer,
                getCardStyle('light'),
                {
                  backgroundColor: themeColors.inputBackground,
                },
              ]}
            >
              {uploadingAvatar ? (
                <ActivityIndicator size="large" color={themeColors.primary} />
              ) : profile?.avatar_url && !avatarError ? (
                <Image
                  source={{ uri: profile.avatar_url }}
                  style={styles.avatarImage}
                  resizeMode="cover"
                />
              ) : (
                <Ionicons name="person" size={60} color={themeColors.primary} />
              )}
            </View>
            <TouchableOpacity
              style={[
                styles.changeAvatarButton,
                getCardStyle('light'),
                {
                  backgroundColor: themeColors.inputBackground,
                },
              ]}
              onPress={handlePickImage}
              disabled={uploadingAvatar}
            >
              <Text style={{ color: themeColors.primary, fontWeight: "600" }}>
                {t("profile.changePhoto")}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Form Section */}
          <View
            style={[styles.formSection, { backgroundColor: "transparent" }]}
          >
            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.name")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("auth.enterName")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  value={fullName}
                  onChangeText={setFullName}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.email")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("auth.enterEmail")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  value={profile?.email || ""}
                  keyboardType="email-address"
                  editable={false}
                />
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: themeColors.text }]}>
                {t("profile.phone")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  getCardStyle('light'),
                  {
                    backgroundColor: themeColors.inputBackground,
                  },
                ]}
              >
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder={t("profile.phone")}
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            {/* Links to other settings */}
            <TouchableOpacity
              style={[
                styles.settingLink,
                getCardStyle('light'),
                {
                  backgroundColor: themeColors.inputBackground,
                },
              ]}
              onPress={() =>
                router.push({ pathname: "/(app)/profile/change-password" })
              }
            >
              <View
                style={[
                  styles.settingLinkLeft,
                  { backgroundColor: "transparent" },
                ]}
              >
                <View
                  style={[
                    styles.settingLinkIconContainer,
                    {
                      backgroundColor: isDark ? "#3B5E94" : "#BBDEFB",
                    },
                  ]}
                >
                  <Ionicons
                    name="lock-closed-outline"
                    size={22}
                    color={themeColors.primary}
                  />
                </View>
                <Text style={[styles.settingLinkText, { color: themeColors.text }]}>
                  {t("profile.changePassword")}
                </Text>
              </View>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={themeColors.secondaryText}
              />
            </TouchableOpacity>

            {/* Delete Account Button */}
            <TouchableOpacity
              style={[
                styles.deleteAccountButton,
                getCardStyle('light'),
                {
                  backgroundColor: isDark ? "#42212A" : "#FFEBEE",
                  borderColor: isDark ? "#4D2A33" : "#FFCDD2",
                  shadowColor: isDark ? "#1A0A0A" : "#D32F2F",
                },
              ]}
              onPress={handleDeleteAccount}
            >
              <Ionicons
                name="trash-outline"
                size={20}
                color={isDark ? "#FF8A80" : "#F44336"}
              />
              <Text
                style={[
                  styles.deleteAccountText,
                  { color: isDark ? "#FF8A80" : "#F44336" },
                ]}
              >
                {t("profile.deleteAccount")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  saveText: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  cardContainer: {
    margin: 16,
    borderRadius: 16,
    overflow: "hidden",
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  avatarSection: {
    alignItems: "center",
    marginTop: 10,
    marginBottom: 30,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    overflow: "hidden",
  },
  avatarImage: {
    width: "100%",
    height: "100%",
  },
  changeAvatarButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  formSection: {
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 20,
    backgroundColor: "transparent",
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
    paddingLeft: 4,
  },
  inputContainer: {
    borderRadius: 12,
    overflow: "hidden",
  },
  input: {
    height: 54,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  settingLink: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  settingLinkLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingLinkIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  settingLinkText: {
    fontSize: 16,
    fontWeight: "500",
  },
  deleteAccountButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 30,
    paddingVertical: 16,
    borderRadius: 12,
  },
  deleteAccountText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
  },
  retryButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
});