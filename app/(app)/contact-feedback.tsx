// File: app/(app)/contact-feedback.tsx
// File này chứa màn hình <PERSON> hệ - Góp ý cho ứng dụng
// File này liên quan đến: app/(tabs)/settings.tsx, context/ThemeContext.tsx

import { Text, View } from "@/components/Themed";
import { useTheme } from "@/context/ThemeContext";
import { useLocalization } from "@/context/LocalizationContext";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import {
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Linking,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function ContactFeedbackScreen() {
  const { t } = useLocalization();
  const {
    isDark,
    themeColors,
    getCardStyle,
    getIconContainerStyle,
  } = useTheme();

  // Thông tin liên hệ
  const CONTACT_INFO = [
    {
      id: "email",
      title: "Email",
      value: "<EMAIL>",
      icon: "mail-outline" as const,
      action: () => handleEmailPress("<EMAIL>"),
    },
    {
      id: "zalo",
      title: "Zalo",
      value: "0936817477",
      icon: "chatbubble-outline" as const,
      action: () => handleZaloPress("0936817477"),
    },
    {
      id: "facebook",
      title: "Facebook",
      value: "Vũ Duy Mạnh",
      icon: "logo-facebook" as const,
      action: () => handleFacebookPress("https://www.facebook.com/vuduymanh.ken/"),
    },
  ];

  // Xử lý khi nhấn vào email
  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  // Xử lý khi nhấn vào Zalo
  const handleZaloPress = (phoneNumber: string) => {
    const zaloUrl = Platform.select({
      ios: `zalo://qr/p/${phoneNumber}`,
      android: `https://zalo.me/${phoneNumber}`,
    });
    
    Linking.canOpenURL(zaloUrl!)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(zaloUrl!);
        } else {
          // Nếu không có ứng dụng Zalo, mở trang web Zalo
          return Linking.openURL(`https://zalo.me/${phoneNumber}`);
        }
      })
      .catch((err) => console.error('An error occurred', err));
  };

  // Xử lý khi nhấn vào Facebook
  const handleFacebookPress = (url: string) => {
    Linking.openURL(url);
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
      edges={["top", "left", "right"]}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="arrow-back-outline"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Liên hệ
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={{ flex: 1, backgroundColor: themeColors.background }}
        contentContainerStyle={{ paddingBottom: 40 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Phần Liên hệ */}
        <View
          style={[
            styles.sectionContainer,
            { backgroundColor: "transparent" },
          ]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            Thông tin liên hệ
          </Text>

          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {CONTACT_INFO.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.contactItem,
                  {
                    backgroundColor: "transparent",
                    borderBottomColor: themeColors.border,
                    borderBottomWidth:
                      index === CONTACT_INFO.length - 1 ? 0 : 1,
                  },
                ]}
                onPress={item.action}
              >
                <View
                  style={[
                    styles.contactLeft,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <View style={[styles.iconContainer, getIconContainerStyle()]}>
                    <Ionicons
                      name={item.icon}
                      size={22}
                      color={themeColors.primary}
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent" }}>
                    <Text
                      style={[styles.contactTitle, { color: themeColors.text }]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.contactValue,
                        { color: themeColors.secondaryText },
                      ]}
                    >
                      {item.value}
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="open-outline"
                  size={20}
                  color={isDark ? "#7CB9F8" : "#90CAF9"}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Phần thông tin bổ sung */}
        <View
          style={[
            styles.infoContainer,
            { backgroundColor: "transparent" },
          ]}
        >
          <Text
            style={[
              styles.infoText,
              { color: themeColors.secondaryText },
            ]}
          >
            Cảm ơn bạn đã sử dụng ứng dụng AI Money. Mọi ý kiến đóng góp của bạn sẽ giúp chúng tôi cải thiện ứng dụng tốt hơn.
          </Text>
        </View>


      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: 8,
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  placeholder: {
    width: 32,
  },
  sectionContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 10,
    paddingLeft: 4,
  },
  sectionCard: {
    overflow: "hidden",
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  contactLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  contactValue: {
    fontSize: 14,
  },
  infoContainer: {
    alignItems: "center",
    marginHorizontal: 24,
    marginTop: 10,
    marginBottom: 24,
  },
  infoText: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },
  appInfoContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 40,
  },
  appInfoText: {
    fontSize: 14,
    marginBottom: 4,
  },
});