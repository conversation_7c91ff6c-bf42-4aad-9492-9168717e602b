// File: app/currency/index.tsx
// File này liên quan đến: context/ThemeContext.tsx, context/CurrencyContext.tsx, context/LocalizationContext.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { Alert, FlatList, StyleSheet, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useCurrency } from "../../context/CurrencyContext";

// Danh sách đơn vị tiền tệ phổ biến
const CURRENCIES = [
  { code: "VND", name: "Việt Nam Đồng", symbol: "₫" },
  { code: "USD", name: "US Dollar", symbol: "$" },
  { code: "EUR", name: "Euro", symbol: "€" },
  { code: "JPY", name: "Japanese Yen", symbol: "¥" },
  { code: "GBP", name: "British Pound", symbol: "£" },
  { code: "AUD", name: "Australian Dollar", symbol: "A$" },
  { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
  { code: "SGD", name: "Singapore Dollar", symbol: "S$" },
  { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
  { code: "KRW", name: "Korean Won", symbol: "₩" },
  { code: "THB", name: "Thai Baht", symbol: "฿" },
];

export default function CurrencyScreen() {
  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle, getIconContainerStyle } = useTheme();
  const { t } = useLocalization();
  
  // Sử dụng currency context
  const { currency, setCurrency } = useCurrency();

  // Xử lý khi chọn đơn vị tiền tệ
  const handleSelectCurrency = (currencyCode: string) => {
    // Tìm symbol tương ứng với currency code
    const selectedCurrency = CURRENCIES.find((c) => c.code === currencyCode);

    if (!selectedCurrency) return;

    Alert.alert(
      t("currency.confirmChange"),
      t("currency.changeConfirmMessage", { code: currencyCode }),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.confirm"),
          onPress: () => {
            // Cập nhật đơn vị tiền tệ trong context
            setCurrency(currencyCode, selectedCurrency.symbol);

            // Hiển thị thông báo thành công
            Alert.alert(
              t("common.success"),
              t("currency.updateSuccess", { code: currencyCode })
            );
          },
        },
      ]
    );
  };

  // Hiển thị ví dụ định dạng
  const getCurrencyExample = (code: string) => {
    const amount = 1000000;

    switch (code) {
      case "VND":
        return new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
          maximumFractionDigits: 0,
        }).format(amount);
      case "USD":
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount / 25000); // Quy đổi gần đúng
      case "EUR":
        return new Intl.NumberFormat("de-DE", {
          style: "currency",
          currency: "EUR",
        }).format(amount / 27000); // Quy đổi gần đúng
      default:
        const symbol = CURRENCIES.find((c) => c.code === code)?.symbol || "";
        return `${symbol}${(amount / 25000).toLocaleString()}`;
    }
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("currency.title")}
        </Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Card Container */}
      <View 
        style={[
          styles.cardContainer, 
          getCardStyle('medium')
        ]}
      >
        <View
          style={[
            styles.infoContainer,
            getCardStyle('light'),
            {
              backgroundColor: themeColors.inputBackground,
            },
          ]}
        >
          <Ionicons
            name="information-circle-outline"
            size={22}
            color={themeColors.primary}
            style={styles.infoIcon}
          />
          <Text style={[styles.infoText, { color: themeColors.secondaryText }]}>
            {t("currency.infoMessage")}
          </Text>
        </View>

        <FlatList
          data={CURRENCIES}
          keyExtractor={(item) => item.code}
          style={styles.list}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.currencyItem,
                getCardStyle('light'),
                {
                  backgroundColor: themeColors.inputBackground,
                  shadowOpacity: currency === item.code ? 0.2 : 0.1,
                  elevation: currency === item.code ? 3 : 1,
                },
              ]}
              onPress={() => handleSelectCurrency(item.code)}
            >
              <View
                style={[styles.currencyInfo, { backgroundColor: "transparent" }]}
              >
                <View
                  style={[
                    styles.currencySymbolContainer,
                    getIconContainerStyle(),
                    {
                      backgroundColor: themeColors.primary,
                      shadowColor: themeColors.primary,
                    },
                  ]}
                >
                  <Text style={styles.currencySymbol}>
                    {item.symbol}
                  </Text>
                </View>
                <View style={{ backgroundColor: "transparent" }}>
                  <Text style={[styles.currencyCode, { color: themeColors.text }]}>
                    {item.code}
                  </Text>
                  <Text
                    style={[
                      styles.currencyName,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {item.name}
                  </Text>
                  <Text
                    style={[
                      styles.currencyExample,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {getCurrencyExample(item.code)}
                  </Text>
                </View>
              </View>
              {currency === item.code && (
                <View 
                  style={[
                    styles.checkContainer,
                    getCardStyle('light'),
                    {
                      backgroundColor: isDark ? "#213A61" : "#E3F2FD",
                    }
                  ]}
                >
                  <Ionicons name="checkmark-circle" size={24} color={themeColors.primary} />
                </View>
              )}
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  cardContainer: {
    flex: 1,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    padding: 16,
  },
  infoContainer: {
    flexDirection: "row",
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 16,
  },
  currencyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
  },
  currencyInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  currencySymbolContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: "bold",
    color: "white",
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  currencyName: {
    fontSize: 14,
    marginBottom: 4,
  },
  currencyExample: {
    fontSize: 13,
  },
  checkContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  separator: {
    height: 12,
  },
});