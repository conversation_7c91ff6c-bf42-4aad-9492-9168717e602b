// File: app/_layout.tsx
// File này liên quan đến: context/AuthContext.tsx, context/LocalizationContext.tsx, context/ThemeContext.tsx, context/CurrencyContext.tsx, lib/services/NotificationService.ts

import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider as NavigationThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import * as Notifications from "expo-notifications";
import { SplashScreen, Stack, useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect, useRef, useState } from "react";
import { Alert } from "react-native";
import "react-native-reanimated";

import { AuthProvider, useAuth } from "@/context/AuthContext";
import { LocalizationProvider } from "@/context/LocalizationContext";
import { PremiumProvider } from "@/context/PremiumContext";
import { ThemeProvider } from "@/context/ThemeContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { NotificationService } from "@/lib/services/NotificationService";
import { initNetworkListener } from "@/lib/supabaseUtils";
import { checkForUpdates, openAppStore } from "@/utils/updateService";
import { CurrencyProvider } from "../context/CurrencyContext";

// Prevent auto splash screen hiding
SplashScreen.preventAutoHideAsync();

// Auth navigation container to handle redirects based on auth state
function AuthNavigationContainer({ children }: { children: React.ReactNode }) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    const checkInitialAuth = async () => {
      console.log("user", user);

      if (!user) {
        router.replace("/(auth)/login");
      } else {
        router.replace("/(tabs)");
      }
    };

    checkInitialAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isLoading]);

  return <>{children}</>;
}

export default function RootLayout() {
  const { colorScheme } = useColorScheme();
  const [loaded, error] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  const networkListenerRef = useRef<any>(null);
  const hasCheckedUpdate = useRef(false);
  const hasInitializedNotifications = useRef(false);

  useEffect(() => {
    // Set up network listener
    networkListenerRef.current = initNetworkListener();

    // Clean up on unmount
    return () => {
      if (networkListenerRef.current) {
        networkListenerRef.current();
      }
    };
  }, []);

  // Initialize notifications khi app start
  useEffect(() => {
    if (loaded && !hasInitializedNotifications.current) {
      hasInitializedNotifications.current = true;
      initializeNotifications();
    }
  }, [loaded]);

  // Thêm useEffect để kiểm tra cập nhật khi ứng dụng khởi động
  useEffect(() => {
    const checkAppUpdate = async () => {
      // Chỉ kiểm tra một lần sau khi ứng dụng tải xong
      if (loaded && !hasCheckedUpdate.current) {
        hasCheckedUpdate.current = true;

        try {
          // Kiểm tra với App Store
          const result = await checkForUpdates();

          if (result.hasUpdate) {
            // Hiển thị thông báo có cập nhật mới
            Alert.alert(
              "Đã có phiên bản mới",
              `Phiên bản ${result.latestVersion} đã có sẵn trên App Store.\n\n${result.releaseNotes}`,
              [
                { text: "Để sau", style: "cancel" },
                { text: "Cập nhật ngay", onPress: openAppStore },
              ]
            );
          }
        } catch (error) {
          console.error("Lỗi khi kiểm tra cập nhật:", error);
        }
      }
    };

    checkAppUpdate();
  }, [loaded]);

  const initializeNotifications = async () => {
    try {
      // Khởi tạo NotificationService
      const isInitialized = await NotificationService.initialize();

      if (isInitialized) {
        console.log("✅ Notification service đã khởi tạo thành công");

        // Setup listeners để handle notifications
        setupNotificationListeners();
      } else {
        console.log("⚠️ Không thể khởi tạo notification service");
      }
    } catch (error) {
      console.error("❌ Lỗi khởi tạo notifications:", error);
    }
  };

  const setupNotificationListeners = () => {
    // Listener khi nhận notification (app đang mở)
    NotificationService.setupNotificationListeners(
      // Khi nhận notification
      (notification) => {
        console.log("📩 Nhận notification:", notification);
        // Có thể show toast hoặc update UI
      },

      // Khi user tap vào notification
      (response) => {
        console.log("👆 User tap notification:", response);
        handleNotificationTap(response);
      }
    );
  };

  const handleNotificationTap = (
    response: Notifications.NotificationResponse
  ) => {
    const data = response.notification.request.content.data as any;

    try {
      // Delay một chút để đảm bảo app đã load xong
      setTimeout(() => {
        switch (data?.type) {
          case "debt-reminder":
          case "debt-overdue":
            // Navigate đến chi tiết nợ
            if (data?.debtId) {
              router.push(`/debt/${data.debtId}` as any);
            } else {
              router.push("/debt" as any);
            }
            break;

          case "upcoming-debts":
          case "daily-summary":
            // Navigate đến trang tổng quan nợ
            router.push("/debt" as any);
            break;

          default:
            // Navigate mặc định đến trang chính
            router.push("/(tabs)");
            break;
        }
      }, 1000);
    } catch (error) {
      console.error("Lỗi navigate từ notification:", error);
      // Fallback navigate
      setTimeout(() => {
        router.push("/(tabs)");
      }, 1000);
    }
  };

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  useEffect(() => {
    if (error) throw error;
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <ThemeProvider>
      <CurrencyProvider>
        <LocalizationProvider>
          <AuthProvider>
            <PremiumProvider>
              <AuthNavigationContainer>
                <NavigationThemeProvider
                  value={colorScheme === "dark" ? DarkTheme : DefaultTheme}
                >
                  <Stack
                    screenOptions={{
                      headerShown: false,
                      gestureEnabled: false,
                      animation: "fade",
                    }}
                  >
                    <Stack.Screen name="(auth)" />
                    <Stack.Screen name="(setup)" />
                    <Stack.Screen name="(tabs)" />
                    <Stack.Screen name="(app)" />
                    <Stack.Screen name="(more)" />
                    <Stack.Screen
                      name="wallet"
                      options={{ presentation: "card" }}
                    />
                    <Stack.Screen
                      name="stats"
                      options={{ presentation: "card" }}
                    />
                    <Stack.Screen
                      name="currency"
                      options={{ presentation: "card" }}
                    />
                    <Stack.Screen name="index" redirect />
                    <Stack.Screen name="+not-found" />
                  </Stack>
                  <StatusBar
                    style={colorScheme === "dark" ? "light" : "dark"}
                  />
                </NavigationThemeProvider>
              </AuthNavigationContainer>
            </PremiumProvider>
          </AuthProvider>
        </LocalizationProvider>
      </CurrencyProvider>
    </ThemeProvider>
  );
}

function ThemeContextConsumer({ children }) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const themePreference = await AsyncStorage.getItem(
          "@moneyup_theme_preference"
        );
        setIsDark(themePreference === "dark");
      } catch (error) {
        console.error("Error loading theme preference:", error);
      }
    };

    loadThemePreference();
  }, []);

  return children({ isDark });
}
