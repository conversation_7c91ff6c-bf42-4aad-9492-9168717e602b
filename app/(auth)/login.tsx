import { useAuth } from "@/context/AuthContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LinearGradient } from "expo-linear-gradient";
import { Link } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Formik } from "formik";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import * as Yup from "yup";
import { Text, View } from "../../components/Themed";

const { width, height } = Dimensions.get("window");

// Keys for AsyncStorage
const STORAGE_KEYS = {
  REMEMBERED_EMAIL: "@aimoney:remembered_email",
  REMEMBER_ME: "@aimoney:remember_me",
};

export default function LoginScreen() {
  const [secure, setSecure] = useState(true);
  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });
  const [valuesLoaded, setValuesLoaded] = useState(false);
  const { colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const { signIn } = useAuth();
  const { t } = useLocalization();

  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];
  const logoScaleAnim = useState(new Animated.Value(0.8))[0];

  // Load remembered email from AsyncStorage when component mounts
  useEffect(() => {
    const loadRememberedEmail = async () => {
      try {
        const rememberedEmail = await AsyncStorage.getItem(
          STORAGE_KEYS.REMEMBERED_EMAIL
        );
        const rememberMe = await AsyncStorage.getItem(STORAGE_KEYS.REMEMBER_ME);

        if (rememberedEmail && rememberMe === "true") {
          setInitialValues({
            email: rememberedEmail,
            password: "",
            rememberMe: true,
          });
        }
        setValuesLoaded(true);
      } catch (error) {
        console.log("Error loading remembered email:", error);
        setValuesLoaded(true);
      }
    };

    loadRememberedEmail();
  }, []);

  // Start animations when component mounts
  useEffect(() => {
    Animated.stagger(200, [
      Animated.timing(logoScaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, []);

  const toggleTheme = () => {
    setColorScheme(isDark ? "light" : "dark");
  };

  const handleLogin = async (
    values: { email: string; password: string; rememberMe: boolean },
    { setSubmitting }: any
  ) => {
    try {
      setLoading(true);

      // Save or remove email based on rememberMe state
      if (values.rememberMe) {
        await AsyncStorage.setItem(STORAGE_KEYS.REMEMBERED_EMAIL, values.email);
        await AsyncStorage.setItem(STORAGE_KEYS.REMEMBER_ME, "true");
      } else {
        await AsyncStorage.removeItem(STORAGE_KEYS.REMEMBERED_EMAIL);
        await AsyncStorage.removeItem(STORAGE_KEYS.REMEMBER_ME);
      }

      await signIn(values.email, values.password);
      // AuthNavigationContainer will automatically handle navigation
    } catch (error) {
      // Error is handled in signIn function
      console.log("Login error:", error);
    } finally {
      setLoading(false);
      setSubmitting(false);
    }
  };

  // Create validation schema using Yup
  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .required(t("auth.emailRequired"))
      .email(t("auth.invalidEmail")),
    password: Yup.string()
      .required(t("auth.passwordRequired"))
      .min(6, t("auth.passwordTooShort")),
    rememberMe: Yup.boolean(),
  });

  const bgColor = isDark ? "#121212" : "#f7f7f7";
  const cardBgColor = isDark
    ? "rgba(40, 40, 40, 0.9)"
    : "rgba(255, 255, 255, 0.9)";
  const textColor = isDark ? "#ffffff" : "#333333";
  const placeholderColor = isDark ? "#888" : "#aaa";
  const inputBgColor = isDark
    ? "rgba(60, 60, 60, 1)"
    : "rgba(240, 240, 240, 1)";
  const errorColor = "#FF375F";
  const disabledButtonColor = isDark
    ? "rgba(70, 70, 70, 0.5)"
    : "rgba(200, 200, 200, 0.8)";
  const buttonGradientColors = isDark
    ? ["#0057FF", "#00C6FF"]
    : ["#007AFF", "#03A9F4"];

  // Don't render the form until we've loaded the remembered email
  if (!valuesLoaded) {
    return (
      <View
        style={[
          styles.container,
          {
            backgroundColor: bgColor,
            justifyContent: "center",
            alignItems: "center",
          },
        ]}
      >
        <ActivityIndicator
          size="large"
          color={isDark ? "#00C6FF" : "#007AFF"}
        />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: bgColor }]}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <StatusBar style={isDark ? "light" : "dark"} />

      <LinearGradient
        colors={isDark ? ["#151515", "#0d0d0d"] : ["#FFFFFF", "#F0F7FF"]}
        style={styles.gradientBackground}
      />

      {isDark && (
        <Animated.View style={[styles.bubbleContainer, { opacity: fadeAnim }]}>
          <View
            style={[
              styles.bubble,
              styles.bubble1,
              { backgroundColor: "rgba(30, 144, 255, 0.05)" },
            ]}
          />
          <View
            style={[
              styles.bubble,
              styles.bubble2,
              { backgroundColor: "rgba(70, 130, 180, 0.07)" },
            ]}
          />
        </Animated.View>
      )}

      <TouchableOpacity style={styles.themeToggle} onPress={toggleTheme}>
        <View style={styles.themeToggleButton}>
          <Ionicons
            name={isDark ? "sunny-outline" : "moon-outline"}
            size={20}
            color={isDark ? "#FFD700" : "#6200EE"}
          />
        </View>
      </TouchableOpacity>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.headerContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: logoScaleAnim }, { translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.logoContainer}>
            <Ionicons
              name="wallet-outline"
              size={40}
              color={isDark ? "#00C6FF" : "#007AFF"}
            />
          </View>
          <Text style={[styles.title, { color: textColor }]}>AI Money</Text>
          <Text style={[styles.subtitle, { color: isDark ? "#BBB" : "#888" }]}>
            {t("appSubtitle")}
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.card,
            {
              backgroundColor: cardBgColor,
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleLogin}
            enableReinitialize
          >
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              isSubmitting,
              isValid,
              setFieldValue,
            }) => (
              <>
                <View
                  style={[
                    styles.formContainer,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <Text style={[styles.formTitle, { color: textColor }]}>
                    {t("auth.login")}
                  </Text>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.email")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1,
                        },
                        touched.email && errors.email ? styles.inputError : {},
                      ]}
                    >
                      <Ionicons
                        name="mail-outline"
                        size={18}
                        color={
                          touched.email && errors.email
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterEmail")}
                        placeholderTextColor={placeholderColor}
                        value={values.email}
                        onChangeText={handleChange("email")}
                        onBlur={handleBlur("email")}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        returnKeyType="next"
                        editable={!loading && !isSubmitting}
                      />
                      {touched.email &&
                        values.email.trim() !== "" &&
                        !errors.email && (
                          <Ionicons
                            name="checkmark-circle"
                            size={18}
                            color="#4CD964"
                            style={styles.validIcon}
                          />
                        )}
                    </View>
                    {touched.email && errors.email ? (
                      <Text style={styles.errorText}>{errors.email}</Text>
                    ) : null}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.password")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1,
                        },
                        touched.password && errors.password
                          ? styles.inputError
                          : {},
                      ]}
                    >
                      <Ionicons
                        name="lock-closed-outline"
                        size={18}
                        color={
                          touched.password && errors.password
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterPassword")}
                        placeholderTextColor={placeholderColor}
                        value={values.password}
                        onChangeText={handleChange("password")}
                        onBlur={handleBlur("password")}
                        secureTextEntry={secure}
                        returnKeyType="done"
                        editable={!loading && !isSubmitting}
                      />
                      <TouchableOpacity
                        onPress={() => setSecure(!secure)}
                        style={styles.secureButton}
                        activeOpacity={0.7}
                        disabled={loading || isSubmitting}
                      >
                        <Ionicons
                          name={secure ? "eye-off-outline" : "eye-outline"}
                          size={18}
                          color={
                            touched.password && errors.password
                              ? errorColor
                              : isDark
                              ? "#BBB"
                              : "#888"
                          }
                        />
                      </TouchableOpacity>
                    </View>
                    {touched.password && errors.password ? (
                      <Text style={styles.errorText}>{errors.password}</Text>
                    ) : null}
                  </View>

                  {/* Remember Account checkbox */}
                  <TouchableOpacity
                    style={styles.rememberContainer}
                    onPress={() => {
                      setFieldValue("rememberMe", !values.rememberMe);
                    }}
                    disabled={loading || isSubmitting}
                  >
                    <View
                      style={[
                        styles.checkbox,
                        { borderColor: isDark ? "#00C6FF" : "#007AFF" },
                        values.rememberMe && {
                          backgroundColor: isDark ? "#00C6FF" : "#007AFF",
                        },
                      ]}
                    >
                      {values.rememberMe && (
                        <Ionicons name="checkmark" size={14} color="#FFF" />
                      )}
                    </View>
                    <Text style={[styles.rememberText, { color: textColor }]}>
                      {t("auth.rememberMe")}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.buttonContainer,
                      (!isValid || !values.email || !values.password) && {
                        backgroundColor: disabledButtonColor,
                      },
                    ]}
                    onPress={() => handleSubmit()}
                    activeOpacity={0.8}
                  >
                    <LinearGradient
                      colors={buttonGradientColors as any}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.buttonGradient}
                    >
                      {loading || isSubmitting ? (
                        <ActivityIndicator color="white" size="small" />
                      ) : (
                        <Text style={styles.buttonText}>{t("auth.login")}</Text>
                      )}
                    </LinearGradient>
                  </TouchableOpacity>
                </View>

                <View
                  style={[styles.footer, { backgroundColor: "transparent" }]}
                >
                  <Text style={[styles.footerText, { color: textColor }]}>
                    {t("auth.noAccount")}
                  </Text>
                  <Link href="/(auth)/register" asChild>
                    <TouchableOpacity
                      disabled={loading || isSubmitting}
                      style={styles.linkButton}
                    >
                      <Text
                        style={[
                          styles.link,
                          { color: isDark ? "#00C6FF" : "#007AFF" },
                        ]}
                      >
                        {t("auth.signup")}
                      </Text>
                    </TouchableOpacity>
                  </Link>
                </View>
              </>
            )}
          </Formik>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 12,
  },
  gradientBackground: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  bubbleContainer: {
    position: "absolute",
    width: width,
    height: height,
    overflow: "hidden",
  },
  bubble: {
    position: "absolute",
    borderRadius: 300,
  },
  bubble1: {
    width: 250,
    height: 250,
    left: -50,
    top: height * 0.1,
  },
  bubble2: {
    width: 200,
    height: 200,
    right: -30,
    bottom: height * 0.2,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  themeToggle: {
    position: "absolute",
    top: 60,
    right: 20,
    zIndex: 10,
  },
  themeToggleButton: {
    padding: 8,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 25,
  },
  logoContainer: {
    width: 70,
    height: 70,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 6,
    paddingTop: 12,
  },
  subtitle: {
    fontSize: 14,
    textAlign: "center",
  },
  card: {
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 5,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.08)",
  },
  formContainer: {
    marginBottom: 15,
  },
  formTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
    textAlign: "center",
  },
  inputGroup: {
    marginBottom: 16,
    backgroundColor: "transparent",
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 6,
    marginLeft: 4,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 48,
    borderWidth: 1.5,
    borderColor: "rgba(0, 0, 0, 0.1)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  inputError: {
    borderWidth: 1.5,
    borderColor: "#FF375F",
    shadowColor: "#FF375F",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  errorText: {
    color: "#FF375F",
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  icon: {
    marginRight: 10,
  },
  validIcon: {
    marginLeft: 8,
  },
  input: {
    flex: 1,
    fontSize: 15,
    height: 48,
    backgroundColor: "transparent",
    paddingVertical: 8,
    paddingHorizontal: 2,
    opacity: 1,
    color: "inherit",
  },
  secureButton: {
    padding: 6,
  },
  buttonContainer: {
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonGradient: {
    paddingVertical: 13,
    alignItems: "center",
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: "white",
    fontSize: 15,
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  footerText: {
    fontSize: 14,
    marginRight: 5,
  },
  linkButton: {
    paddingVertical: 3,
    paddingHorizontal: 6,
  },
  link: {
    color: "#007AFF",
    fontWeight: "600",
    fontSize: 14,
  },
  rememberContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: "#888",
    borderRadius: 4,
    marginRight: 8,
  },
  rememberText: {
    color: "#888",
    fontSize: 14,
  },
});
