import { useAuth } from "@/context/AuthContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { Text, View } from "../../components/Themed";

const { width, height } = Dimensions.get("window");

export default function ForgotPasswordScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [email, setEmail] = useState((params.email as string) || "");
  const [loading, setLoading] = useState(false);
  const { colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const { forgotPassword } = useAuth();
  const { t } = useLocalization();

  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const toggleTheme = () => {
    setColorScheme(isDark ? "light" : "dark");
  };

  const handleResetPassword = async () => {
    if (!email) {
      Alert.alert(t("common.error"), t("auth.forgotPasswordEmailRequired"));
      return;
    }

    try {
      setLoading(true);
      await forgotPassword(email);
      Alert.alert(t("common.success"), t("auth.passwordResetEmailSent"), [
        {
          text: t("common.confirm"),
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Reset password error:", error);
    } finally {
      setLoading(false);
    }
  };

  const bgColor = isDark ? "#121212" : "#f7f7f7";
  const cardBgColor = isDark ? "#242424" : "#ffffff";
  const textColor = isDark ? "#ffffff" : "#333333";
  const placeholderColor = isDark ? "#888" : "#aaa";
  const accentColor = "#007AFF";
  const inputBgColor = isDark ? "#333333" : "#f0f0f0";

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: bgColor }]}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <StatusBar style={isDark ? "light" : "dark"} />

      <TouchableOpacity style={styles.themeToggle} onPress={toggleTheme}>
        <Ionicons
          name={isDark ? "sunny-outline" : "moon-outline"}
          size={24}
          color={isDark ? "#FFD700" : "#6200EE"}
        />
      </TouchableOpacity>

      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <Ionicons
          name="arrow-back"
          size={24}
          color={isDark ? "#FFFFFF" : "#333333"}
        />
      </TouchableOpacity>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.headerContainer,
            { opacity: fadeAnim, transform: [{ translateY: slideAnim }] },
          ]}
        >
          <Text style={[styles.title, { color: textColor, paddingTop: 4 }]}>
            AIMoney
          </Text>
          <Text style={[styles.subtitle, { color: isDark ? "#BBB" : "#888" }]}>
            {t("appSubtitle")}
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.card,
            {
              backgroundColor: cardBgColor,
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View
            style={[styles.formContainer, { backgroundColor: "transparent" }]}
          >
            <Text
              style={[styles.formTitle, { color: textColor, paddingTop: 3 }]}
            >
              {t("auth.resetPassword")}
            </Text>

            <Text
              style={[styles.description, { color: isDark ? "#BBB" : "#888" }]}
            >
              {t("auth.passwordResetInstructions")}
            </Text>

            <View style={styles.inputGroup}>
              <Text style={[styles.label, { color: textColor }]}>
                {t("auth.email")}
              </Text>
              <View
                style={[styles.inputWrapper, { backgroundColor: inputBgColor }]}
              >
                <Ionicons
                  name="mail-outline"
                  size={22}
                  color={isDark ? "#BBB" : "#888"}
                  style={styles.icon}
                />
                <TextInput
                  style={[styles.input, { color: textColor }]}
                  placeholder={t("auth.enterEmail")}
                  placeholderTextColor={placeholderColor}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  returnKeyType="done"
                  editable={!loading}
                />
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.button,
                !email || loading ? styles.buttonDisabled : null,
              ]}
              onPress={handleResetPassword}
              activeOpacity={0.8}
              disabled={!email || loading}
            >
              {loading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text style={styles.buttonText}>{t("auth.resetPassword")}</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={() => router.back()}
              disabled={loading}
            >
              <Text style={[styles.backToLoginText, { color: textColor }]}>
                {t("auth.backToLogin")}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
  },
  themeToggle: {
    position: "absolute",
    top: 50,
    right: 24,
    zIndex: 10,
    padding: 8,
    borderRadius: 20,
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 24,
    zIndex: 10,
    padding: 8,
    borderRadius: 20,
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 30,
  },
  title: {
    fontSize: 36,
    fontWeight: "bold",
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: "#888",
    textAlign: "center",
  },
  card: {
    borderRadius: 20,
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  formContainer: {
    marginBottom: 20,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: "700",
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 15,
    textAlign: "center",
    marginBottom: 24,
    lineHeight: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 15,
    fontWeight: "600",
    marginBottom: 8,
    marginLeft: 4,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 56,
    backgroundColor: "#f0f0f0",
  },
  icon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
  },
  button: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: "center",
    marginBottom: 16,
  },
  buttonDisabled: {
    backgroundColor: "#007AFF80",
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  backToLoginButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: "center",
    marginBottom: 16,
  },
  backToLoginText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});
