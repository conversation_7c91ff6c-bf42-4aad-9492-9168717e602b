// File này nằm ở (auth)/register.tsx
// Các file liên quan: AuthContext.tsx, Themed.tsx, LocalizationContext.tsx, lib/supabase.ts

import { useAuth } from "@/context/AuthContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { cleanupAuthListeners } from "@/lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Link, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { Formik } from "formik";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Animated,
  BackHandler,
  Dimensions,
  Platform,
  View as RNView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import * as Yup from "yup";
import { Text, View } from "../../components/Themed";

const { width, height } = Dimensions.get("window");

export default function RegisterScreen() {
  const [securePassword, setSecurePassword] = useState(true);
  const [secureConfirmPassword, setSecureConfirmPassword] = useState(true);
  const [loading, setLoading] = useState(false);
  const { colorScheme, setColorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const { signUp } = useAuth();
  const { t } = useLocalization();
  const [registerComplete, setRegisterComplete] = useState(false);

  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];
  const logoScaleAnim = useState(new Animated.Value(0.8))[0];

  // Start animations when component mounts
  useEffect(() => {
    Animated.stagger(200, [
      Animated.timing(logoScaleAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
    
    // Dọn dẹp bất kỳ auth listener nào khi component mount
    cleanupAuthListeners();
    
    // Xử lý nút back để tránh race condition
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => {
        if (loading) {
          // Ngăn không cho back khi đang xử lý đăng ký
          return true;
        }
        
        // Clean up auth listeners trước khi quay lại màn hình trước
        cleanupAuthListeners();
        router.back();
        return true;
      }
    );
    
    return () => {
      backHandler.remove();
      // Đảm bảo xóa listeners khi unmount
      cleanupAuthListeners();
    };
  }, [fadeAnim, logoScaleAnim, slideAnim, loading]);

  const toggleTheme = () => {
    setColorScheme(isDark ? "light" : "dark");
  };

  const handleRegister = async (
    values: {
      name: string;
      email: string;
      password: string;
      confirmPassword: string;
    },
    { setSubmitting }: any
  ) => {
    if (loading || registerComplete) return;
    
    try {
      setLoading(true);
      
      // Clean up any existing listeners first
      cleanupAuthListeners();
      
      // Sử dụng hàm signUp từ AuthContext
      await signUp(values.email, values.password, values.name);
      
      // Đánh dấu đã đăng ký thành công để tránh gọi nhiều lần
      setRegisterComplete(true);
      
    } catch (error) {
      console.log("Register error:", error);
    } finally {
      // Đảm bảo reset các state
      setSubmitting(false);
      
      // Delay resetting loading để tránh bị nhấn lại nút ngay lập tức
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  // Create validation schema using Yup
  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t("auth.nameRequired")),
    email: Yup.string()
      .required(t("auth.emailRequired"))
      .email(t("auth.invalidEmail")),
    password: Yup.string()
      .required(t("auth.passwordRequired"))
      .min(6, t("auth.passwordTooShort")),
    confirmPassword: Yup.string()
      .required(t("auth.confirmPasswordRequired"))
      .oneOf([Yup.ref("password")], t("auth.passwordMismatch")),
  });

  const bgColor = isDark ? "#121212" : "#f7f7f7";
  const cardBgColor = isDark
    ? "rgba(40, 40, 40, 0.9)"
    : "rgba(255, 255, 255, 0.9)";
  const textColor = isDark ? "#ffffff" : "#333333";
  const placeholderColor = isDark ? "#888" : "#aaa";
  const inputBgColor = isDark
    ? "rgba(60, 60, 60, 1)"
    : "rgba(240, 240, 240, 1)";
  const errorColor = "#FF375F";
  const disabledButtonColor = isDark
    ? "rgba(70, 70, 70, 0.5)"
    : "rgba(200, 200, 200, 0.8)";
  const buttonGradientColors = isDark
    ? ["#0057FF", "#00C6FF"]
    : ["#007AFF", "#03A9F4"];

  const handleGoBack = () => {
    // Clean up listeners trước khi điều hướng
    cleanupAuthListeners();
    router.back();
  };

  return (
    <RNView style={[styles.container, { backgroundColor: bgColor }]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      <LinearGradient
        colors={isDark ? ["#151515", "#0d0d0d"] : ["#FFFFFF", "#F0F7FF"]}
        style={styles.gradientBackground}
      />

      {isDark && (
        <Animated.View style={[styles.bubbleContainer, { opacity: fadeAnim }]}>
          <View
            style={[
              styles.bubble,
              styles.bubble1,
              { backgroundColor: "rgba(30, 144, 255, 0.05)" },
            ]}
          />
          <View
            style={[
              styles.bubble,
              styles.bubble2,
              { backgroundColor: "rgba(70, 130, 180, 0.07)" },
            ]}
          />
        </Animated.View>
      )}

      <TouchableOpacity style={styles.themeToggle} onPress={toggleTheme}>
        <View style={styles.themeToggleButton}>
          <Ionicons
            name={isDark ? "sunny-outline" : "moon-outline"}
            size={20}
            color={isDark ? "#FFD700" : "#6200EE"}
          />
        </View>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.backButton} 
        onPress={handleGoBack}
        disabled={loading || registerComplete}
      >
        <View style={styles.backButtonContainer}>
          <Ionicons
            name="arrow-back"
            size={20}
            color={isDark ? "#FFFFFF" : "#333333"}
          />
        </View>
      </TouchableOpacity>

      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={Platform.OS === "ios" ? 30 : 80}
        extraHeight={120}
      >
        <Animated.View
          style={[
            styles.headerContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: logoScaleAnim }, { translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.logoContainer}>
            <Ionicons
              name="wallet-outline"
              size={40}
              color={isDark ? "#00C6FF" : "#007AFF"}
            />
          </View>
          <Text style={[styles.title, { color: textColor }]}>AI Money</Text>
          <Text style={[styles.subtitle, { color: isDark ? "#BBB" : "#888" }]}>
            {t("appSubtitle")}
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.card,
            {
              backgroundColor: cardBgColor,
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <Formik
            initialValues={{
              name: "",
              email: "",
              password: "",
              confirmPassword: "",
            }}
            validationSchema={validationSchema}
            onSubmit={handleRegister}
          >
            {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
              isSubmitting,
              isValid,
            }) => (
              <>
                <View
                  style={[
                    styles.formContainer,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <Text style={[styles.formTitle, { color: textColor }]}>
                    {t("auth.createNewAccount")}
                  </Text>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.name")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1.5,
                        },
                        touched.name && errors.name ? styles.inputError : {},
                      ]}
                    >
                      <Ionicons
                        name="person-outline"
                        size={18}
                        color={
                          touched.name && errors.name
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterName")}
                        placeholderTextColor={placeholderColor}
                        value={values.name}
                        onChangeText={handleChange("name")}
                        onBlur={handleBlur("name")}
                        returnKeyType="next"
                        editable={!loading && !isSubmitting && !registerComplete}
                      />
                      {touched.name &&
                        values.name.trim() !== "" &&
                        !errors.name && (
                          <Ionicons
                            name="checkmark-circle"
                            size={18}
                            color="#4CD964"
                            style={styles.validIcon}
                          />
                        )}
                    </View>
                    {touched.name && errors.name ? (
                      <Text style={styles.errorText}>{errors.name}</Text>
                    ) : null}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.email")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1.5,
                        },
                        touched.email && errors.email ? styles.inputError : {},
                      ]}
                    >
                      <Ionicons
                        name="mail-outline"
                        size={18}
                        color={
                          touched.email && errors.email
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterEmail")}
                        placeholderTextColor={placeholderColor}
                        value={values.email}
                        onChangeText={handleChange("email")}
                        onBlur={handleBlur("email")}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        returnKeyType="next"
                        editable={!loading && !isSubmitting && !registerComplete}
                      />
                      {touched.email &&
                        values.email.trim() !== "" &&
                        !errors.email && (
                          <Ionicons
                            name="checkmark-circle"
                            size={18}
                            color="#4CD964"
                            style={styles.validIcon}
                          />
                        )}
                    </View>
                    {touched.email && errors.email ? (
                      <Text style={styles.errorText}>{errors.email}</Text>
                    ) : null}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.password")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1.5,
                        },
                        touched.password && errors.password
                          ? styles.inputError
                          : {},
                      ]}
                    >
                      <Ionicons
                        name="lock-closed-outline"
                        size={18}
                        color={
                          touched.password && errors.password
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterPassword")}
                        placeholderTextColor={placeholderColor}
                        value={values.password}
                        onChangeText={handleChange("password")}
                        onBlur={handleBlur("password")}
                        secureTextEntry={securePassword}
                        returnKeyType="next"
                        editable={!loading && !isSubmitting && !registerComplete}
                      />
                      <TouchableOpacity
                        onPress={() => setSecurePassword(!securePassword)}
                        style={styles.secureButton}
                        activeOpacity={0.7}
                        disabled={loading || isSubmitting || registerComplete}
                      >
                        <Ionicons
                          name={
                            securePassword ? "eye-off-outline" : "eye-outline"
                          }
                          size={18}
                          color={
                            touched.password && errors.password
                              ? errorColor
                              : isDark
                              ? "#BBB"
                              : "#888"
                          }
                        />
                      </TouchableOpacity>
                    </View>
                    {touched.password && errors.password ? (
                      <Text style={styles.errorText}>{errors.password}</Text>
                    ) : null}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={[styles.label, { color: textColor }]}>
                      {t("auth.confirmPassword")}
                    </Text>
                    <View
                      style={[
                        styles.inputWrapper,
                        {
                          backgroundColor: inputBgColor,
                          borderColor: isDark
                            ? "rgba(255, 255, 255, 0.15)"
                            : "rgba(0, 0, 0, 0.1)",
                          borderWidth: 1.5,
                        },
                        touched.confirmPassword && errors.confirmPassword
                          ? styles.inputError
                          : {},
                      ]}
                    >
                      <Ionicons
                        name="lock-closed-outline"
                        size={18}
                        color={
                          touched.confirmPassword && errors.confirmPassword
                            ? errorColor
                            : isDark
                            ? "#BBB"
                            : "#888"
                        }
                        style={styles.icon}
                      />
                      <TextInput
                        style={[
                          styles.input,
                          {
                            color: textColor,
                            backgroundColor: "transparent",
                          },
                        ]}
                        placeholder={t("auth.enterConfirmPassword")}
                        placeholderTextColor={placeholderColor}
                        value={values.confirmPassword}
                        onChangeText={handleChange("confirmPassword")}
                        onBlur={handleBlur("confirmPassword")}
                        secureTextEntry={secureConfirmPassword}
                        returnKeyType="done"
                        editable={!loading && !isSubmitting && !registerComplete}
                      />
                      <TouchableOpacity
                        onPress={() =>
                          setSecureConfirmPassword(!secureConfirmPassword)
                        }
                        style={styles.secureButton}
                        activeOpacity={0.7}
                        disabled={loading || isSubmitting || registerComplete}
                      >
                        <Ionicons
                          name={
                            secureConfirmPassword
                              ? "eye-off-outline"
                              : "eye-outline"
                          }
                          size={18}
                          color={
                            touched.confirmPassword && errors.confirmPassword
                              ? errorColor
                              : isDark
                              ? "#BBB"
                              : "#888"
                          }
                        />
                      </TouchableOpacity>
                    </View>
                    {touched.confirmPassword && errors.confirmPassword ? (
                      <Text style={styles.errorText}>
                        {errors.confirmPassword}
                      </Text>
                    ) : null}
                  </View>

                  <TouchableOpacity
                    style={[
                      styles.buttonContainer,
                      !isValid ||
                      loading ||
                      isSubmitting ||
                      registerComplete ||
                      !values.name ||
                      !values.email ||
                      !values.password ||
                      !values.confirmPassword
                        ? { backgroundColor: disabledButtonColor }
                        : {},
                    ]}
                    onPress={() => {
                      if (!loading && !isSubmitting && !registerComplete) {
                        // Clean up listeners trước khi submit
                        cleanupAuthListeners();
                        handleSubmit();
                      }
                    }}
                    activeOpacity={0.8}
                    disabled={
                      !isValid ||
                      loading ||
                      isSubmitting ||
                      registerComplete ||
                      !values.name ||
                      !values.email ||
                      !values.password ||
                      !values.confirmPassword
                    }
                  >
                    {isValid &&
                    !registerComplete &&
                    !loading &&
                    !isSubmitting &&
                    values.name &&
                    values.email &&
                    values.password &&
                    values.confirmPassword ? (
                      <LinearGradient
                        colors={buttonGradientColors as any}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.buttonGradient}
                      >
                        {loading || isSubmitting ? (
                          <ActivityIndicator color="white" size="small" />
                        ) : (
                          <Text style={styles.buttonText}>
                            {t("auth.register")}
                          </Text>
                        )}
                      </LinearGradient>
                    ) : (
                      <View
                        style={[
                          styles.buttonGradient,
                          {
                            backgroundColor: isDark
                              ? "rgba(60, 60, 60, 0.8)"
                              : "rgba(230, 230, 230, 0.8)",
                          },
                        ]}
                      >
                        {loading || isSubmitting ? (
                          <ActivityIndicator 
                            color={isDark ? "#BBB" : "#999"} 
                            size="small" 
                          />
                        ) : (
                          <Text
                            style={[
                              styles.buttonText,
                              { color: isDark ? "#999" : "#666" },
                            ]}
                          >
                            {t("auth.register")}
                          </Text>
                        )}
                      </View>
                    )}
                  </TouchableOpacity>
                </View>

                <View
                  style={[styles.footer, { backgroundColor: "transparent" }]}
                >
                  <Text style={[styles.footerText, { color: textColor }]}>
                    {t("auth.hasAccount")}
                  </Text>
                  <Link href="/(auth)/login" asChild>
                    <TouchableOpacity
                      disabled={loading || isSubmitting || registerComplete}
                      style={styles.linkButton}
                      onPress={() => {
                        // Dọn dẹp listeners trước khi chuyển trang
                        cleanupAuthListeners();
                      }}
                    >
                      <Text
                        style={[
                          styles.link,
                          { color: isDark ? "#00C6FF" : "#007AFF" },
                        ]}
                      >
                        {t("auth.login")}
                      </Text>
                    </TouchableOpacity>
                  </Link>
                </View>
              </>
            )}
          </Formik>
        </Animated.View>
      </KeyboardAwareScrollView>
    </RNView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 12,
  },
  gradientBackground: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  bubbleContainer: {
    position: "absolute",
    width: width,
    height: height,
    overflow: "hidden",
  },
  bubble: {
    position: "absolute",
    borderRadius: 300,
  },
  bubble1: {
    width: 250,
    height: 250,
    left: -50,
    top: height * 0.1,
  },
  bubble2: {
    width: 200,
    height: 200,
    right: -30,
    bottom: height * 0.2,
  },
  scrollContent: {
    backgroundColor: "transparent",
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 100, // Increased padding to ensure content is not hidden by keyboard
  },
  themeToggle: {
    position: "absolute",
    top: 60,
    right: 20,
    zIndex: 10,
  },
  themeToggleButton: {
    padding: 8,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  backButton: {
    position: "absolute",
    top: 60,
    left: 20,
    zIndex: 10,
  },
  backButtonContainer: {
    padding: 8,
    borderRadius: 16,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 25,
  },
  logoContainer: {
    width: 70,
    height: 70,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 6,
    paddingTop: 12,
  },
  subtitle: {
    fontSize: 14,
    textAlign: "center",
  },
  card: {
    borderRadius: 20,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 5,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.08)",
  },
  formContainer: {
    marginBottom: 15,
  },
  formTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 20,
    textAlign: "center",
  },
  inputGroup: {
    marginBottom: 16,
    backgroundColor: "transparent",
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 6,
    marginLeft: 4,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 48,
    borderWidth: 1.5,
    borderColor: "rgba(0, 0, 0, 0.1)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  inputError: {
    borderWidth: 1.5,
    borderColor: "#FF375F",
    shadowColor: "#FF375F",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  errorText: {
    color: "#FF375F",
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  icon: {
    marginRight: 10,
  },
  validIcon: {
    marginLeft: 8,
  },
  input: {
    flex: 1,
    fontSize: 15,
    height: 48,
    backgroundColor: "transparent",
    paddingVertical: 8,
    paddingHorizontal: 2,
    opacity: 1,
  },
  secureButton: {
    padding: 6,
  },
  buttonContainer: {
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 15,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonGradient: {
    paddingVertical: 13,
    alignItems: "center",
    borderRadius: 12,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: "white",
    fontSize: 15,
    fontWeight: "600",
  },
  policyContainer: {
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  policyText: {
    fontSize: 13,
    textAlign: "center",
    lineHeight: 18,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  footerText: {
    fontSize: 14,
    marginRight: 5,
  },
  linkButton: {
    paddingVertical: 3,
    paddingHorizontal: 6,
  },
  link: {
    fontWeight: "600",
    fontSize: 14,
  },
});