// File này nằm ở app/(auth)/_layout.tsx
// Các file liên quan: register.tsx, login.tsx, forgot-password.tsx

import { Stack } from "expo-router";
import { useEffect } from "react";
import { cleanupAuthListeners } from "@/lib/supabase";

export default function AuthLayout() {
  // Đảm bảo xóa listener khi không cần thiết
  useEffect(() => {
    // Khi component unmount, xóa tất cả các listeners để tránh bị memory leak
    return () => {
      console.log("[Auth Layout] Cleaning up auth listeners on unmount");
      cleanupAuthListeners();
    };
  }, []);

  return (
    <Stack
      initialRouteName="login"
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen name="login" />
      <Stack.Screen name="register" />
      <Stack.Screen name="forgot-password" />
    </Stack>
  );
}