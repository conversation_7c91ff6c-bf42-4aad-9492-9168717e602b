// File: app/category/edit/[id].tsx
// File này liên quan đến: context/ThemeContext.tsx
// File này đã được cập nhật để sử dụng đầy đủ hệ thống theme tập trung

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import {
  Category,
  CategoryModel,
  CategoryUpdateInput,
} from "@/lib/models/category";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Available icons
const CATEGORY_ICONS = [
  "restaurant-outline",
  "car-outline",
  "cart-outline",
  "film-outline",
  "cash-outline",
  "gift-outline",
  "home-outline",
  "medkit-outline",
  "airplane-outline",
  "book-outline",
  "basket-outline",
  "build-outline",
  "business-outline",
  "fitness-outline",
  "accessibility-outline",
];

// Available colors
const CATEGORY_COLORS = [
  "#4CAF50",
  "#2196F3",
  "#9C27B0",
  "#F44336",
  "#FF9800",
  "#795548",
  "#607D8B",
  "#E91E63",
  "#3F51B5",
];

export default function EditCategoryScreen() {
  const { id } = useLocalSearchParams();
  const categoryId = typeof id === "string" ? id : "";

  // Sử dụng đầy đủ các thuộc tính từ useTheme
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  const [name, setName] = useState("");
  const [selectedType, setSelectedType] = useState<"expense" | "income">(
    "expense"
  );
  const [selectedIcon, setSelectedIcon] = useState(CATEGORY_ICONS[0]);
  const [selectedColor, setSelectedColor] = useState(CATEGORY_COLORS[0]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [category, setCategory] = useState<Category | null>(null);

  // Load category data from API
  useEffect(() => {
    const loadCategory = async () => {
      if (!categoryId) {
        setIsLoading(false);
        setError("Invalid category ID");
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const categoryData = await CategoryModel.getById(categoryId);

        if (!categoryData) {
          setError("Category not found");
          return;
        }

        setCategory(categoryData);
        setName(categoryData.name);
        setSelectedType(categoryData.type);
        setSelectedIcon(categoryData.icon);
        setSelectedColor(categoryData.color);
      } catch (err) {
        console.error("Error loading category:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load category"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadCategory();
  }, [categoryId]);

  const handleSave = async () => {
    // Validate inputs
    if (!name.trim() || !category) {
      Alert.alert(t("common.error"), t("categories.nameRequired"));
      return;
    }

    try {
      setIsSaving(true);

      // Create update data object
      const updateData: CategoryUpdateInput = {
        name: name.trim(),
        type: selectedType,
        icon: selectedIcon,
        color: selectedColor,
      };

      await CategoryModel.update(categoryId, updateData);

      // Show success message and go back
      Alert.alert(t("common.success"), t("categories.categoryUpdated"), [
        { text: t("common.confirm"), onPress: () => router.back() },
      ]);
    } catch (error) {
      console.error("Error updating category:", error);
      Alert.alert(
        t("common.error"),
        error instanceof Error
          ? error.message
          : "An unexpected error occurred when updating the category."
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      t("common.delete"),
      t("categories.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              setIsSaving(true);
              await CategoryModel.delete(categoryId);
              Alert.alert(
                t("common.success"),
                t("categories.categoryDeleted"),
                [
                  {
                    text: t("common.confirm"),
                    onPress: () => router.back(),
                  },
                ]
              );
            } catch (error) {
              console.error("Error deleting category:", error);
              Alert.alert(
                t("common.error"),
                error instanceof Error
                  ? error.message
                  : "Failed to delete category"
              );
              setIsSaving(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Loading view
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.editCategory")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error view
  if (error || !category) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.editCategory")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View
          style={[styles.emptyContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={themeColors.primary}
          />
          <Text
            style={{
              color: themeColors.secondaryText,
              fontSize: 16,
              marginTop: 12,
            }}
          >
            {error || t("categories.categoryNotFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
            disabled={isSaving}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.editCategory")}
          </Text>
          <View style={[styles.headerButtonsContainer, { backgroundColor: 'transparent' }]}>
            <TouchableOpacity
              onPress={handleDelete}
              style={[
                styles.deleteHeaderButton,
                {
                  backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                  ...getShadowStyle('low'),
                  shadowColor: '#B71C1C',
                }
              ]}
              disabled={isSaving}
            >
              <Ionicons name="trash-outline" size={20} color={themeColors.danger} />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleSave}
              disabled={!name.trim() || isSaving}
              style={[
                styles.saveButton,
                {
                  backgroundColor: name && !isSaving 
                    ? themeColors.primary 
                    : (isDark ? "#375980" : "#BBDEFB"),
                  ...getShadowStyle('low')
                }
              ]}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: 16,
                  fontWeight: "500",
                }}
              >
                {isSaving ? t("common.loading") : t("common.save")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Preview */}
          <View
            style={[
              styles.previewCard,
              getCardStyle('high')
            ]}
          >
            <View
              style={[styles.categoryIcon, { backgroundColor: selectedColor }]}
            >
              <Ionicons name={selectedIcon as any} size={36} color="white" />
            </View>
            <Text style={[styles.previewTitle, { color: themeColors.text }]}>
              {name || t("categories.categoryName")}
            </Text>
            <Text style={{ 
              color: themeColors.secondaryText, 
              fontSize: 16,
              marginTop: 4
            }}>
              {selectedType === "expense"
                ? t("categories.expense")
                : t("categories.income")}
            </Text>
          </View>

          {/* Form Fields */}
          <View
            style={[
              styles.formSection,
              getCardStyle('medium')
            ]}
          >
            {/* Category Name Field */}
            <Text style={[styles.label, { color: themeColors.text }]}>
              {t("categories.categoryName")}
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: themeColors.iconBackground,
                  borderColor: themeColors.border,
                  ...getShadowStyle('low')
                },
              ]}
            >
              <TextInput
                style={[styles.input, { color: themeColors.text }]}
                placeholder={t("categories.enterCategoryName")}
                placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                value={name}
                onChangeText={setName}
                editable={!isSaving}
              />
            </View>

            {/* Category Type Selection */}
            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("categories.categoryType")}
            </Text>
            <View style={[styles.typeContainer, { backgroundColor: 'transparent' }]}>
              <TouchableOpacity
                style={[
                  styles.typeOption,
                  {
                    backgroundColor:
                      selectedType === "expense"
                        ? isDark ? `${selectedColor}30` : `${selectedColor}20`
                        : themeColors.iconBackground,
                    borderColor:
                      selectedType === "expense"
                        ? selectedColor
                        : themeColors.border,
                    ...getShadowStyle(selectedType === "expense" ? 'medium' : 'low')
                  },
                ]}
                onPress={() => setSelectedType("expense")}
                disabled={isSaving}
              >
                <Ionicons
                  name="trending-down-outline"
                  size={24}
                  color={
                    selectedType === "expense"
                      ? selectedColor
                      : themeColors.primary
                  }
                />
                <Text
                  style={[
                    styles.typeText,
                    {
                      color:
                        selectedType === "expense" ? selectedColor : themeColors.text,
                      fontWeight:
                        selectedType === "expense" ? "600" : "normal",
                    },
                  ]}
                >
                  {t("categories.expense")}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeOption,
                  {
                    backgroundColor:
                      selectedType === "income"
                        ? isDark ? `${selectedColor}30` : `${selectedColor}20`
                        : themeColors.iconBackground,
                    borderColor:
                      selectedType === "income"
                        ? selectedColor
                        : themeColors.border,
                    ...getShadowStyle(selectedType === "income" ? 'medium' : 'low')
                  },
                ]}
                onPress={() => setSelectedType("income")}
                disabled={isSaving}
              >
                <Ionicons
                  name="trending-up-outline"
                  size={24}
                  color={
                    selectedType === "income"
                      ? selectedColor
                      : themeColors.primary
                  }
                />
                <Text
                  style={[
                    styles.typeText,
                    {
                      color:
                        selectedType === "income" ? selectedColor : themeColors.text,
                      fontWeight:
                        selectedType === "income" ? "600" : "normal",
                    },
                  ]}
                >
                  {t("categories.income")}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Icon Selection */}
            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("categories.icon")}
            </Text>
            <View style={[styles.iconGrid, { backgroundColor: 'transparent' }]}>
              {CATEGORY_ICONS.map((icon) => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconOption,
                    {
                      backgroundColor: themeColors.iconBackground,
                      borderColor:
                        selectedIcon === icon ? selectedColor : themeColors.border,
                      ...getShadowStyle(selectedIcon === icon ? 'medium' : 'low')
                    },
                  ]}
                  onPress={() => setSelectedIcon(icon)}
                  disabled={isSaving}
                >
                  <Ionicons
                    name={icon as any}
                    size={24}
                    color={
                      selectedIcon === icon
                        ? selectedColor
                        : themeColors.primary
                    }
                  />
                </TouchableOpacity>
              ))}
            </View>

            {/* Color Selection */}
            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("categories.color")}
            </Text>
            <View style={[styles.colorContainer, { backgroundColor: 'transparent' }]}>
              {CATEGORY_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.selectedColorOption,
                    { 
                      shadowColor: color,
                      shadowOpacity: selectedColor === color ? 0.6 : 0.3,
                      shadowOffset: { width: 0, height: 2 },
                      shadowRadius: 3,
                      elevation: selectedColor === color ? 5 : 4,
                    }
                  ]}
                  onPress={() => setSelectedColor(color)}
                  disabled={isSaving}
                >
                  {selectedColor === color && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerButtonsContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "transparent",
  },
  deleteHeaderButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginRight: 8,
  },
  saveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  previewCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: "center",
  },
  categoryIcon: {
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  previewTitle: {
    fontSize: 22,
    fontWeight: "600",
    marginBottom: 4,
  },
  formSection: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  inputContainer: {
    borderRadius: 12,
    borderWidth: 1,
  },
  input: {
    height: 54,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  typeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  typeOption: {
    flex: 1,
    padding: 16,
    borderRadius: 14,
    alignItems: "center",
    borderWidth: 2,
    marginHorizontal: 6,
  },
  typeText: {
    marginTop: 10,
    fontSize: 14,
    textAlign: "center",
  },
  iconGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  iconOption: {
    width: 56,
    height: 56,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
  },
  colorContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  colorOption: {
    width: 46,
    height: 46,
    borderRadius: 23,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: "white",
    transform: [{ scale: 1.1 }], // Hiệu ứng phóng to khi được chọn
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
});