// File: app/category/add.tsx
// File này liên quan đến: context/ThemeContext.tsx, create-categories.tsx
// File này đã được cập nhật để hỗ trợ chọn danh mục có sẵn

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { CategoryModel, type CategoryCreateInput } from "@/lib/models/category";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Available icons
const CATEGORY_ICONS = [
  "restaurant-outline",
  "car-outline",
  "cart-outline",
  "film-outline",
  "cash-outline",
  "gift-outline",
  "home-outline",
  "medkit-outline",
  "airplane-outline",
  "book-outline",
  "basket-outline",
  "build-outline",
  "business-outline",
  "fitness-outline",
  "accessibility-outline",
];

// Available colors
const CATEGORY_COLORS = [
  "#4CAF50",
  "#2196F3",
  "#9C27B0",
  "#F44336",
  "#FF9800",
  "#795548",
  "#607D8B",
  "#E91E63",
  "#3F51B5",
];

// Danh sách danh mục mẫu
// Danh mục chi tiêu
const TEMPLATE_EXPENSE_CATEGORIES = [
  {
    id: 1,
    name: "Ăn uống",
    icon: "restaurant-outline",
    color: "#FF9800",
    type: "expense",
  },
  {
    id: 2,
    name: "Di chuyển",
    icon: "car-outline",
    color: "#9C27B0",
    type: "expense",
  },
  {
    id: 3,
    name: "Mua sắm",
    icon: "cart-outline",
    color: "#F44336",
    type: "expense",
  },
  {
    id: 4,
    name: "Hóa đơn",
    icon: "document-text-outline",
    color: "#2196F3",
    type: "expense",
  },
  {
    id: 5,
    name: "Nhà cửa",
    icon: "home-outline",
    color: "#FF5722",
    type: "expense",
  },
  {
    id: 6,
    name: "Giải trí",
    icon: "film-outline",
    color: "#673AB7",
    type: "expense",
  },
  {
    id: 7,
    name: "Sức khỏe",
    icon: "fitness-outline",
    color: "#4CAF50",
    type: "expense",
  },
  {
    id: 8,
    name: "Giáo dục",
    icon: "school-outline",
    color: "#009688",
    type: "expense",
  },
];

// Danh mục thu nhập
const TEMPLATE_INCOME_CATEGORIES = [
  {
    id: 9,
    name: "Lương",
    icon: "cash-outline",
    color: "#4CAF50",
    type: "income",
  },
  {
    id: 10,
    name: "Thưởng",
    icon: "gift-outline",
    color: "#2196F3",
    type: "income",
  },
  {
    id: 11,
    name: "Đầu tư",
    icon: "trending-up-outline",
    color: "#9C27B0",
    type: "income",
  },
  {
    id: 12,
    name: "Khác",
    icon: "wallet-outline",
    color: "#607D8B",
    type: "income",
  },
];

export default function AddCategoryScreen() {
  // Sử dụng hook useTheme với đầy đủ các thuộc tính
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  // State cho tab hiện tại ('custom' hoặc 'template')
  const [activeTab, setActiveTab] = useState<'custom' | 'template'>('template');
  const [currentTemplateTab, setCurrentTemplateTab] = useState<"expense" | "income">("expense");

  const [name, setName] = useState("");
  const [selectedType, setSelectedType] = useState<"expense" | "income">("expense");
  const [selectedIcon, setSelectedIcon] = useState(CATEGORY_ICONS[0]);
  const [selectedColor, setSelectedColor] = useState(CATEGORY_COLORS[0]);
  const [isLoading, setIsLoading] = useState(false);
  
  // State cho danh mục mẫu đã chọn
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const handleSave = async () => {
    // Validate inputs
    if (activeTab === 'custom' && !name.trim()) {
      Alert.alert(t("common.error"), t("categories.nameRequired"));
      return;
    }
    
    if (activeTab === 'template' && !selectedTemplate) {
      Alert.alert(t("common.error"), t("categories.templateRequired"));
      return;
    }

    try {
      setIsLoading(true);

      // Tạo đối tượng dữ liệu danh mục
      let categoryData: CategoryCreateInput;
      
      if (activeTab === 'custom') {
        // Sử dụng dữ liệu từ form tùy chỉnh
        categoryData = {
          name: name.trim(),
          type: selectedType,
          icon: selectedIcon,
          color: selectedColor,
        };
      } else {
        // Sử dụng dữ liệu từ mẫu đã chọn
        categoryData = {
          name: selectedTemplate.name,
          type: selectedTemplate.type,
          icon: selectedTemplate.icon,
          color: selectedTemplate.color,
        };
      }

      await CategoryModel.create(categoryData);

      // Hiển thị thông báo thành công và quay lại
      Alert.alert(t("common.success"), t("categories.categoryAdded"), [
        {
          text: t("common.confirm"),
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Error creating category:", error);
      Alert.alert(
        t("common.error"),
        error instanceof Error
          ? error.message
          : "An unexpected error occurred when creating the category."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Xử lý chọn danh mục mẫu
  const handleSelectTemplate = (template: any) => {
    setSelectedTemplate(template);
  };

  // Render phần danh mục mẫu
  const renderTemplateCategories = () => {
    const templates = currentTemplateTab === "expense" 
      ? TEMPLATE_EXPENSE_CATEGORIES 
      : TEMPLATE_INCOME_CATEGORIES;

    return (
      <View style={[styles.templateContainer, { backgroundColor: 'transparent' }]}>
        <View style={[styles.tabContainer, { backgroundColor: "transparent" }]}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              { backgroundColor: isDark ? "#333" : "#f1f1f1" },
              currentTemplateTab === "expense" && {
                backgroundColor: isDark ? "#0066CC" : "#007AFF",
              },
            ]}
            onPress={() => setCurrentTemplateTab("expense")}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.tabText,
                { color: isDark ? "#BBB" : "#666" },
                currentTemplateTab === "expense" && { color: "white" },
              ]}
            >
              {t("categories.expense")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              { backgroundColor: isDark ? "#333" : "#f1f1f1" },
              currentTemplateTab === "income" && {
                backgroundColor: isDark ? "#0066CC" : "#007AFF",
              },
            ]}
            onPress={() => setCurrentTemplateTab("income")}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.tabText,
                { color: isDark ? "#BBB" : "#666" },
                currentTemplateTab === "income" && { color: "white" },
              ]}
            >
              {t("categories.income")}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.categoriesGrid}>
          {templates.map((template) => (
            <TouchableOpacity
              key={template.id}
              style={[
                styles.categoryItem,
                getCardStyle('low'),
                selectedTemplate?.id === template.id && {
                  borderColor: template.color,
                  borderWidth: 2,
                },
              ]}
              onPress={() => handleSelectTemplate(template)}
              disabled={isLoading}
            >
              <View
                style={[
                  styles.categoryIcon,
                  { backgroundColor: template.color },
                ]}
              >
                <Ionicons name={template.icon as any} size={24} color="white" />
              </View>
              <Text
                style={[
                  styles.categoryName,
                  { color: themeColors.text },
                ]}
              >
                {template.name}
              </Text>
              {selectedTemplate?.id === template.id && (
                <View style={styles.checkmarkContainer}>
                  <Ionicons
                    name="checkmark-circle"
                    size={22}
                    color={template.color}
                  />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {selectedTemplate && (
          <View 
            style={[
              styles.previewCard,
              getCardStyle('medium'),
              { marginTop: 20 }
            ]}
          >
            <View
              style={[styles.categoryIconPreview, { backgroundColor: selectedTemplate.color }]}
            >
              <Ionicons name={selectedTemplate.icon as any} size={36} color="white" />
            </View>
            <Text style={[styles.previewTitle, { color: themeColors.text }]}>
              {selectedTemplate.name}
            </Text>
            <Text style={{ 
              color: themeColors.secondaryText, 
              fontSize: 16,
              marginTop: 4
            }}>
              {selectedTemplate.type === "expense"
                ? t("categories.expense")
                : t("categories.income")}
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
            disabled={isLoading}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.addCategory")}
          </Text>
          <TouchableOpacity
            onPress={handleSave}
            disabled={(activeTab === 'custom' && !name.trim()) || 
                    (activeTab === 'template' && !selectedTemplate) || 
                    isLoading}
            style={[
              styles.saveButton,
              {
                backgroundColor: ((activeTab === 'custom' && name) || 
                                (activeTab === 'template' && selectedTemplate)) && !isLoading 
                  ? themeColors.primary 
                  : (isDark ? "#375980" : "#BBDEFB"),
                ...getShadowStyle('low')
              }
            ]}
          >
            {isLoading ? (
              <ActivityIndicator
                size="small"
                color="white"
              />
            ) : (
              <Text
                style={{
                  color: "white",
                  fontSize: 16,
                  fontWeight: "500",
                }}
              >
                {t("common.save")}
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Tab Selector */}
        <View style={[styles.mainTabContainer, { backgroundColor: "transparent" }]}>
          <TouchableOpacity
            style={[
              styles.mainTabButton,
              activeTab === "template" && {
                backgroundColor: isDark ? themeColors.primary + "30" : themeColors.primary + "15",
                borderBottomColor: themeColors.primary,
                borderBottomWidth: 3,
              },
            ]}
            onPress={() => setActiveTab("template")}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.mainTabText,
                { color: isDark ? "#BBB" : "#666" },
                activeTab === "template" && { color: themeColors.primary, fontWeight: "600" },
              ]}
            >
              {t("categories.templates")}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.mainTabButton,
              activeTab === "custom" && {
                backgroundColor: isDark ? themeColors.primary + "30" : themeColors.primary + "15",
                borderBottomColor: themeColors.primary,
                borderBottomWidth: 3,
              },
            ]}
            onPress={() => setActiveTab("custom")}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.mainTabText,
                { color: isDark ? "#BBB" : "#666" },
                activeTab === "custom" && { color: themeColors.primary, fontWeight: "600" },
              ]}
            >
              {t("categories.custom")}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {activeTab === 'template' ? (
            // Render danh mục mẫu nếu đang ở tab template
            renderTemplateCategories()
          ) : (
            <>
              {/* Preview */}
              <View
                style={[
                  styles.previewCard,
                  getCardStyle('high')
                ]}
              >
                <View
                  style={[styles.categoryIcon, { backgroundColor: selectedColor }]}
                >
                  <Ionicons name={selectedIcon as any} size={36} color="white" />
                </View>
                <Text style={[styles.previewTitle, { color: themeColors.text }]}>
                  {name || t("categories.categoryName")}
                </Text>
                <Text style={{ 
                  color: themeColors.secondaryText, 
                  fontSize: 16,
                  marginTop: 4
                }}>
                  {selectedType === "expense"
                    ? t("categories.expense")
                    : t("categories.income")}
                </Text>
              </View>

              {/* Form Fields */}
              <View
                style={[
                  styles.formSection,
                  getCardStyle('medium')
                ]}
              >
                {/* Category Name Field */}
                <Text style={[styles.label, { color: themeColors.text }]}>
                  {t("categories.categoryName")}
                </Text>
                <View
                  style={[
                    styles.inputContainer,
                    {
                      backgroundColor: themeColors.iconBackground,
                      borderColor: themeColors.border,
                    },
                  ]}
                >
                  <TextInput
                    style={[styles.input, { color: themeColors.text }]}
                    placeholder={t("categories.enterCategoryName")}
                    placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                    value={name}
                    onChangeText={setName}
                    editable={!isLoading}
                  />
                </View>

                {/* Category Type Selection */}
                <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
                  {t("categories.categoryType")}
                </Text>
                <View style={[styles.typeContainer, { backgroundColor: 'transparent' }]}>
                  <TouchableOpacity
                    style={[
                      styles.typeOption,
                      {
                        backgroundColor:
                          selectedType === "expense"
                            ? isDark ? `${selectedColor}30` : `${selectedColor}20`
                            : themeColors.iconBackground,
                        borderColor:
                          selectedType === "expense"
                            ? selectedColor
                            : themeColors.border,
                        ...getShadowStyle(selectedType === "expense" ? 'medium' : 'low')
                      },
                    ]}
                    onPress={() => setSelectedType("expense")}
                    disabled={isLoading}
                  >
                    <Ionicons
                      name="trending-down-outline"
                      size={24}
                      color={
                        selectedType === "expense"
                          ? selectedColor
                          : themeColors.primary
                      }
                    />
                    <Text
                      style={[
                        styles.typeText,
                        {
                          color:
                            selectedType === "expense" ? selectedColor : themeColors.text,
                          fontWeight:
                            selectedType === "expense" ? "600" : "normal",
                        },
                      ]}
                    >
                      {t("categories.expense")}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.typeOption,
                      {
                        backgroundColor:
                          selectedType === "income"
                            ? isDark ? `${selectedColor}30` : `${selectedColor}20`
                            : themeColors.iconBackground,
                        borderColor:
                          selectedType === "income"
                            ? selectedColor
                            : themeColors.border,
                        ...getShadowStyle(selectedType === "income" ? 'medium' : 'low')
                      },
                    ]}
                    onPress={() => setSelectedType("income")}
                    disabled={isLoading}
                  >
                    <Ionicons
                      name="trending-up-outline"
                      size={24}
                      color={
                        selectedType === "income"
                          ? selectedColor
                          : themeColors.primary
                      }
                    />
                    <Text
                      style={[
                        styles.typeText,
                        {
                          color:
                            selectedType === "income" ? selectedColor : themeColors.text,
                          fontWeight:
                            selectedType === "income" ? "600" : "normal",
                        },
                      ]}
                    >
                      {t("categories.income")}
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Icon Selection */}
                <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
                  {t("categories.icon")}
                </Text>
                <View style={[styles.iconGrid, { backgroundColor: 'transparent' }]}>
                  {CATEGORY_ICONS.map((icon) => (
                    <TouchableOpacity
                      key={icon}
                      style={[
                        styles.iconOption,
                        {
                          backgroundColor: themeColors.iconBackground,
                          borderColor:
                            selectedIcon === icon ? selectedColor : themeColors.border,
                          ...getShadowStyle(selectedIcon === icon ? 'medium' : 'low')
                        },
                      ]}
                      onPress={() => setSelectedIcon(icon)}
                      disabled={isLoading}
                    >
                      <Ionicons
                        name={icon as any}
                        size={24}
                        color={
                          selectedIcon === icon
                            ? selectedColor
                            : themeColors.primary
                        }
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Color Selection */}
                <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
                  {t("categories.color")}
                </Text>
                <View style={[styles.colorContainer, { backgroundColor: 'transparent' }]}>
                  {CATEGORY_COLORS.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        selectedColor === color && styles.selectedColorOption,
                        { 
                          shadowColor: color,
                          shadowOpacity: selectedColor === color ? 0.6 : 0.3,
                          shadowOffset: { width: 0, height: 2 },
                          shadowRadius: 3,
                          elevation: selectedColor === color ? 5 : 4,
                        }
                      ]}
                      onPress={() => setSelectedColor(color)}
                      disabled={isLoading}
                    >
                      {selectedColor === color && (
                        <Ionicons name="checkmark" size={16} color="white" />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </>
          )}

          {/* Save Button */}
          <TouchableOpacity
            style={[
              styles.saveButtonLarge,
              {
                backgroundColor: ((activeTab === 'custom' && name) || 
                                (activeTab === 'template' && selectedTemplate)) && !isLoading 
                  ? themeColors.primary 
                  : (isDark ? "#375980" : "#BBDEFB"),
                ...getShadowStyle('medium')
              },
            ]}
            onPress={handleSave}
            disabled={(activeTab === 'custom' && !name) || 
                    (activeTab === 'template' && !selectedTemplate) || 
                    isLoading}
          >
            {isLoading ? (
              <ActivityIndicator
                size="small"
                color="white"
              />
            ) : (
              <Text
                style={[
                  styles.saveButtonText,
                  {
                    color: "white",
                  },
                ]}
              >
                {t("categories.addCategory")}
              </Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  previewCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    alignItems: "center",
  },
  categoryIcon: {
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  categoryIconPreview: {
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  previewTitle: {
    fontSize: 22,
    fontWeight: "600",
    marginBottom: 4,
  },
  formSection: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  inputContainer: {
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  input: {
    height: 54,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  typeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  typeOption: {
    flex: 1,
    padding: 16,
    borderRadius: 14,
    alignItems: "center",
    borderWidth: 2,
    marginHorizontal: 6,
  },
  typeText: {
    marginTop: 10,
    fontSize: 14,
    textAlign: "center",
  },
  iconGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  iconOption: {
    width: 56,
    height: 56,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
  },
  colorContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  colorOption: {
    width: 46,
    height: 46,
    borderRadius: 23,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: "white",
    transform: [{ scale: 1.1 }], // Hiệu ứng phóng to khi được chọn
  },
  saveButtonLarge: {
    height: 56,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 12,
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: "600",
  },
  // Tab styles
  mainTabContainer: {
    flexDirection: "row",
    marginTop: 5,
    marginBottom: 10,
    paddingHorizontal: 16,
  },
  mainTabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "transparent",
  },
  mainTabText: {
    fontSize: 16,
    fontWeight: "500",
  },
  // Styles for template tab
  templateContainer: {
    marginBottom: 20,
  },
  tabContainer: {
    flexDirection: "row",
    marginHorizontal: 0,
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
  },
  tabButton: {
    flex: 1,
    padding: 15,
    alignItems: "center",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "600",
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  categoryItem: {
    width: "30%",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    marginBottom: 15,
    position: "relative",
  },
  categoryName: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 8,
  },
  checkmarkContainer: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "white",
    borderRadius: 12,
  },
});