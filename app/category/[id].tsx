// File: app/category/[id].tsx
// File này liên quan đến: context/ThemeContext.tsx
// File này đã đư<PERSON><PERSON> cập nhật để sử dụng đầy đủ hệ thống theme tập trung

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Category, CategoryModel } from "@/lib/models/category";
import { TransactionModel } from "@/lib/models/transaction";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function CategoryDetailScreen() {
  const { id } = useLocalSearchParams();
  const categoryId = typeof id === "string" ? id : "";

  // Sử dụng đầy đủ các thuộc tính từ useTheme
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  const [category, setCategory] = useState<Category | null>(null);
  const [categoryTransactions, setCategoryTransactions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load category data from API
  useEffect(() => {
    const loadCategory = async () => {
      if (!categoryId) {
        setIsLoading(false);
        setError("Invalid category ID");
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const categoryData = await CategoryModel.getById(categoryId);

        if (!categoryData) {
          setError("Category not found");
          return;
        }

        setCategory(categoryData);

        // Load transactions for this category
        const allTransactions = await TransactionModel.getAll();
        const filteredTransactions = allTransactions
          .filter((transaction) => transaction.category_id === categoryId)
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
          .map((transaction) => ({
            id: transaction.id,
            title: transaction.description || categoryData.name,
            amount: transaction.amount,
            category: categoryData.name,
            date: new Date(transaction.date).toLocaleString("vi-VN", {
              hour: "2-digit",
              minute: "2-digit",
              day: "2-digit",
              month: "2-digit",
            }),
            wallet: transaction.wallet?.name || "Ví",
          }));

        setCategoryTransactions(filteredTransactions);
      } catch (err) {
        console.error("Error loading category:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load category"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadCategory();
  }, [categoryId]);

  const handleDeleteCategory = async () => {
    Alert.alert(
      t("common.delete"),
      t("categories.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await CategoryModel.delete(categoryId);
              Alert.alert(
                t("common.success"),
                t("categories.categoryDeleted"),
                [
                  {
                    text: t("common.confirm"),
                    onPress: () => router.back(),
                  },
                ]
              );
            } catch (error) {
              console.error("Error deleting category:", error);
              Alert.alert(
                t("common.error"),
                error instanceof Error
                  ? error.message
                  : "Failed to delete category"
              );
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Loading view
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.categoryDetails")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error view or category not found
  if (error || !category) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.categoryDetails")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View
          style={[styles.emptyContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={themeColors.primary}
          />
          <Text
            style={{
              color: themeColors.secondaryText,
              fontSize: 16,
              marginTop: 12,
            }}
          >
            {error || t("categories.categoryNotFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("categories.categoryDetails")}
        </Text>
        <View style={[styles.headerButtonsContainer, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            onPress={handleDeleteCategory}
            style={[
              styles.deleteHeaderButton,
              {
                backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                ...getShadowStyle('low'),
                shadowColor: '#B71C1C',
              }
            ]}
          >
            <Ionicons name="trash-outline" size={20} color={themeColors.danger} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => router.push(`/category/edit/${category.id}` as any)}
            style={[
              styles.editButton,
              {
                backgroundColor: themeColors.primary,
                ...getShadowStyle('low'),
              }
            ]}
          >
            <Ionicons name="create-outline" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Category Card */}
        <View 
          style={[
            styles.categoryCard, 
            getCardStyle('high')
          ]}
        >
          <View
            style={[styles.categoryHeader, { backgroundColor: "transparent" }]}
          >
            <View
              style={[
                styles.categoryIconContainer,
                { backgroundColor: category.color },
              ]}
            >
              <Ionicons name={category.icon as any} size={36} color="white" />
            </View>
            <View style={[{ backgroundColor: "transparent" }, styles.categoryInfo]}>
              <Text style={[styles.categoryName, { color: themeColors.text }]}>
                {category.name}
              </Text>
              <Text style={{ 
                color: themeColors.secondaryText, 
                fontSize: 16,
                marginTop: 4
              }}>
                {category.type === "expense"
                  ? t("categories.expense")
                  : t("categories.income")}
              </Text>
            </View>
          </View>
        </View>

        {/* Recent Transactions */}
        <View 
          style={[
            styles.sectionContainer, 
            getCardStyle('medium')
          ]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            {t("home.recentTransactions")}
          </Text>

          {categoryTransactions.length > 0 ? (
            categoryTransactions.map((transaction, index) => (
              <TouchableOpacity
                key={transaction.id}
                style={[
                  styles.transactionItem,
                  {
                    backgroundColor: themeColors.iconBackground,
                    borderBottomColor: themeColors.border,
                    borderBottomWidth: index === categoryTransactions.length - 1 ? 0 : 1,
                    ...getShadowStyle('low'),
                  },
                ]}
                onPress={() =>
                  router.push(`/transaction/${transaction.id}` as any)
                }
              >
                <View
                  style={[
                    styles.transactionLeft,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <View
                    style={[
                      styles.transactionIcon,
                      {
                        backgroundColor: category.color,
                        ...getShadowStyle('low'),
                        shadowColor: category.color,
                      },
                    ]}
                  >
                    <Ionicons
                      name={category.icon as any}
                      size={18}
                      color="white"
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent" }}>
                    <Text
                      style={[styles.transactionTitle, { color: themeColors.text }]}
                    >
                      {transaction.title}
                    </Text>
                    <Text
                      style={[
                        styles.transactionDetails,
                        { color: themeColors.secondaryText },
                      ]}
                    >
                      {transaction.date}
                    </Text>
                  </View>
                </View>
                <View style={{ backgroundColor: 'transparent', alignItems: 'flex-end' }}>
                  <Text
                    style={[
                      styles.transactionAmount,
                      {
                        color: transaction.amount < 0 ? themeColors.danger : "#4CAF50",
                        fontWeight: "600",
                      },
                    ]}
                  >
                    {transaction.amount.toLocaleString()}đ
                  </Text>
                  <Text
                    style={[
                      styles.transactionWallet,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {transaction.wallet}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View
              style={[
                styles.emptyListContainer,
                { 
                  backgroundColor: themeColors.iconBackground,
                  borderRadius: 12,
                  borderColor: themeColors.border,
                  borderWidth: 1,
                },
              ]}
            >
              <Ionicons
                name="receipt-outline"
                size={40}
                color={themeColors.primary}
              />
              <Text
                style={{
                  color: themeColors.secondaryText,
                  fontSize: 16,
                  marginTop: 12,
                  textAlign: "center",
                }}
              >
                Chưa có giao dịch nào trong danh mục này
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerButtonsContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "transparent",
  },
  deleteHeaderButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginRight: 8,
  },
  editButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  categoryCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
  },
  categoryHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryInfo: {
    flex: 1,
  },
  categoryIconContainer: {
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  categoryName: {
    fontSize: 24,
    fontWeight: "600",
    marginBottom: 4,
  },
  sectionContainer: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  transactionIcon: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  transactionDetails: {
    fontSize: 14,
  },
  transactionAmount: {
    fontSize: 17,
    fontWeight: "bold",
  },
  transactionWallet: {
    fontSize: 14,
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyListContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    marginTop: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});