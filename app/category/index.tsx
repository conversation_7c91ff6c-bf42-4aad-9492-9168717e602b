// File: app/category/index.tsx
// File này liên quan đến: context/ThemeContext.tsx, components/LimitProgressBar.tsx, lib/services/SpendingLimitService.ts
// File này đã được cập nhật để hiển thị progress bar limit cho từng category

import { Text } from "@/components/Themed";
import { LimitProgressBar } from "@/components/LimitProgressBar";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Category, CategoryModel } from "@/lib/models/category";
import { TransactionModel } from "@/lib/models/transaction";
import { SpendingLimitService, LimitUsage } from "@/lib/services/SpendingLimitService";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Biến toàn cục để lưu trữ tùy chọn người dùng
// Biến này tồn tại trong suốt vòng đời của ứng dụng
const UserPreferences = {
  categoryViewMode: "list" as "list" | "grid",
};

// Interface để lưu trữ thông tin sử dụng của danh mục với limit data
interface CategoryWithUsageAndLimit extends Category {
  usage: number; // Số tiền đã sử dụng trong tháng
  limitUsage?: LimitUsage; // Thông tin limit nếu có
}

export default function CategoryListScreen() {
  // Sử dụng hook useTheme với đầy đủ các thuộc tính
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();
  const translateCategoryName = useCategoryTranslation();
  const { symbol } = useCurrency();

  const [categoriesExpense, setCategoriesExpense] = useState<
    CategoryWithUsageAndLimit[]
  >([]);
  const [categoriesIncome, setCategoriesIncome] = useState<CategoryWithUsageAndLimit[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(
    null
  );
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    UserPreferences.categoryViewMode
  );
  // Thêm state để theo dõi trạng thái chỉnh sửa
  const [isEditMode, setIsEditMode] = useState(false);
  // Thêm state để theo dõi trạng thái ẩn/hiện số tiền
  const [isMoneyHidden, setIsMoneyHidden] = useState(false);

  // Tạo refs để cuộn đến các phần
  const scrollViewRef = React.useRef<ScrollView>(null);

  // Lưu trữ vị trí Y của các phần
  const [expenseSectionY, setExpenseSectionY] = useState(0);
  const [incomeSectionY, setIncomeSectionY] = useState(0);

  // Thêm state để lưu trữ tổng số tiền chi tiêu và thu nhập
  const [summary, setSummary] = useState({
    totalExpense: 0,
    totalIncome: 0,
    balance: 0,
    currentMonth: new Date().getMonth() + 1,
    currentYear: new Date().getFullYear(),
  });

  // Fetch tất cả danh mục từ API và tính toán số tiền đã sử dụng + limit info
  const fetchCategoriesWithUsage = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Lấy tháng và năm hiện tại
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentYear = now.getFullYear();

      // Lấy tất cả giao dịch
      const allTransactions = await TransactionModel.getAll();

      // Lọc giao dịch trong tháng hiện tại
      const startDate = new Date(currentYear, currentMonth - 1, 1);
      const endDate = new Date(currentYear, currentMonth, 0);

      const monthlyTransactions = allTransactions.filter((transaction) => {
        const transactionDate = new Date(transaction.date);
        return transactionDate >= startDate && transactionDate <= endDate;
      });

      // Tính tổng chi tiêu và thu nhập
      let totalExpense = 0;
      let totalIncome = 0;

      monthlyTransactions.forEach((transaction) => {
        if (transaction.amount < 0) {
          // Chi tiêu (số âm)
          totalExpense += Math.abs(transaction.amount);
        } else {
          // Thu nhập (số dương)
          totalIncome += transaction.amount;
        }
      });

      // Tính số dư (chênh lệch giữa thu nhập và chi tiêu)
      const balance = totalIncome - totalExpense;

      // Cập nhật state summary
      setSummary({
        totalExpense,
        totalIncome,
        balance,
        currentMonth,
        currentYear,
      });

      // Lấy danh mục chi tiêu
      const expenseCategories = await CategoryModel.getByType("expense");

      // Lấy tất cả limit usages
      const allLimitUsages = await SpendingLimitService.calculateAllUsages();

      // Tính toán số tiền đã sử dụng cho mỗi danh mục chi tiêu + thông tin limit
      const expenseCategoriesWithUsage = expenseCategories.map((category) => {
        // Lọc các giao dịch thuộc danh mục này
        const categoryTransactions = monthlyTransactions.filter(
          (transaction) => transaction.category_id === category.id
        );

        // Tính tổng số tiền (lấy giá trị tuyệt đối vì expense là số âm)
        const usageAmount = categoryTransactions.reduce((sum, transaction) => {
          return sum + Math.abs(transaction.amount);
        }, 0);

        // Tìm limit usage cho category này
        const limitUsage = allLimitUsages.find(usage => usage.categoryId === category.id);

        // Trả về danh mục với thông tin sử dụng và limit
        return {
          ...category,
          usage: usageAmount,
          limitUsage: limitUsage || undefined,
        };
      });

      // Lấy danh mục thu nhập
      const incomeCategories = await CategoryModel.getByType("income");

      // Tính toán số tiền đã sử dụng cho mỗi danh mục thu nhập
      const incomeCategoriesWithUsage = incomeCategories.map((category) => {
        // Lọc các giao dịch thuộc danh mục này
        const categoryTransactions = monthlyTransactions.filter(
          (transaction) => transaction.category_id === category.id
        );

        // Tính tổng số tiền
        const usageAmount = categoryTransactions.reduce((sum, transaction) => {
          return sum + Math.abs(transaction.amount);
        }, 0);

        // Thu nhập thường không có limit, nhưng giữ cấu trúc nhất quán
        return {
          ...category,
          usage: usageAmount,
          limitUsage: undefined,
        };
      });

      setCategoriesExpense(expenseCategoriesWithUsage);
      setCategoriesIncome(incomeCategoriesWithUsage);
    } catch (err) {
      console.error(`Error fetching categories:`, err);
      setError(
        err instanceof Error ? err.message : `Failed to load categories`
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Load categories on component mount
  useEffect(() => {
    fetchCategoriesWithUsage();
  }, []);

  // Reload categories when navigating back to the screen
  useFocusEffect(
    React.useCallback(() => {
      fetchCategoriesWithUsage();
      // Reset selected category when screen is focused
      setSelectedCategoryId(null);
      // Đảm bảo tắt chế độ chỉnh sửa khi quay lại màn hình
      setIsEditMode(false);
    }, [])
  );

  // Xử lý sự kiện chạm vào màn hình để ẩn nút khi nhấn ra ngoài danh mục
  useEffect(() => {
    const handleScreenTouch = () => {
      if (selectedCategoryId) {
        setSelectedCategoryId(null);
      }
    };

    return () => {
      // Clean up if needed
    };
  }, [selectedCategoryId]);

  const handleDeleteCategory = async (categoryId: string) => {
    Alert.alert(
      t("common.delete"),
      t("categories.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              await CategoryModel.delete(categoryId);
              // Refresh the categories
              fetchCategoriesWithUsage();
            } catch (error) {
              console.error("Error deleting category:", error);
              Alert.alert(
                t("common.error"),
                error instanceof Error
                  ? error.message
                  : "Failed to delete category"
              );
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Chuyển đổi chế độ xem và lưu vào biến toàn cục
  const toggleViewMode = () => {
    const newMode = viewMode === "list" ? "grid" : "list";
    setViewMode(newMode);
    UserPreferences.categoryViewMode = newMode;
  };

  // Hàm chuyển đổi chế độ chỉnh sửa
  const toggleEditMode = () => {
    setIsEditMode(!isEditMode);
    // Khi thoát khỏi chế độ chỉnh sửa, bỏ chọn danh mục
    if (isEditMode) {
      setSelectedCategoryId(null);
    }
  };

  // Hàm chuyển đổi ẩn/hiện số tiền
  const toggleMoneyVisibility = () => {
    setIsMoneyHidden(!isMoneyHidden);
  };

  // Hàm để cuộn đến phần chi tiêu
  const scrollToExpense = () => {
    if (scrollViewRef.current && expenseSectionY > 0) {
      scrollViewRef.current.scrollTo({ y: expenseSectionY, animated: true });
    }
  };

  // Hàm để cuộn đến phần thu nhập
  const scrollToIncome = () => {
    if (scrollViewRef.current && incomeSectionY > 0) {
      scrollViewRef.current.scrollTo({ y: incomeSectionY, animated: true });
    }
  };

  const handleLongPress = (categoryId: string) => {
    // Nếu không ở chế độ chỉnh sửa, mới xử lý nhấn giữ
    if (!isEditMode) {
      setSelectedCategoryId(categoryId);
    }
  };

  // Hàm định dạng hiển thị số tiền sử dụng
  const renderUsageAmount = (usage: number) => {
    // Nếu đang ẩn số tiền
    if (isMoneyHidden) {
      return "******";
    }

    // Nếu usage = 0, hiển thị 0 + currency symbol
    if (usage <= 0) {
      return "0" + symbol;
    }

    // Nếu có usage, định dạng số tiền với dấu phân cách hàng nghìn
    return usage.toLocaleString() + symbol;
  };

  // Render item cho danh sách danh mục theo thiết kế mới
  const renderCategoryItem = ({ item }: { item: CategoryWithUsageAndLimit }) => {
    if (viewMode === "list") {
      // Danh sách dạng list với thiết kế cập nhật
      return (
        <View style={[styles.categoryItemWrapper, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            style={[
              styles.categoryItemList,
              getCardStyle('medium'),
              {
                borderLeftWidth: 3,
                borderLeftColor: item.color,
              }
            ]}
            onPress={() => router.push(`/category/${item.id}`)}
            onLongPress={() => handleLongPress(item.id)}
          >
            <View
              style={[
                styles.iconContainer,
                {
                  backgroundColor: item.color,
                  ...getShadowStyle('medium'),
                  shadowColor: item.color,
                }
              ]}
            >
              <Ionicons name={item.icon as any} size={24} color="white" />
            </View>

            <View style={styles.categoryContent}>
              <Text style={[styles.categoryName, { color: themeColors.text }]}>
                {translateCategoryName(item.name)}
              </Text>

              <Text style={[styles.categoryUsage, { color: themeColors.secondaryText }]}>
                {renderUsageAmount(item.usage)}
              </Text>
            </View>

            {/* Hiển thị nút sửa và xóa khi ở chế độ chỉnh sửa hoặc khi danh mục được chọn */}
            {(isEditMode || selectedCategoryId === item.id) && (
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles.editButton,
                    {
                      backgroundColor: isDark ? themeColors.iconBackground : "#E3F2FD",
                    }
                  ]}
                  onPress={() => {
                    router.push(`/category/edit/${item.id}`);
                  }}
                >
                  <Ionicons name="create-outline" size={22} color={themeColors.primary} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.deleteButton,
                    {
                      backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                    }
                  ]}
                  onPress={() => {
                    handleDeleteCategory(item.id);
                  }}
                >
                  <Ionicons name="trash-outline" size={22} color={themeColors.danger} />
                </TouchableOpacity>
              </View>
            )}
          </TouchableOpacity>

          {/* Hiển thị progress bar cho limit nếu có */}
          {item.limitUsage && (
            <View style={[styles.limitProgressContainer, { backgroundColor: 'transparent' }]}>
              <LimitProgressBar
                usage={item.limitUsage}
                size="small"
                showDetails={false}
                onPress={() => router.push('/spending-limits')}
              />
            </View>
          )}
        </View>
      );
    } else {
      // Grid view với thiết kế cập nhật
      return (
        <View style={[styles.gridItemWrapper, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            style={[
              styles.categoryItemGrid, 
              getCardStyle('medium')
            ]}
            onPress={() => router.push(`/category/${item.id}`)}
            onLongPress={() => handleLongPress(item.id)}
            delayLongPress={300}
          >
            <View
              style={[
                styles.categoryIconContainerGrid,
                { 
                  backgroundColor: item.color,
                  shadowColor: item.color,
                  shadowOpacity: 0.6,
                },
              ]}
            >
              <Ionicons name={item.icon as any} size={28} color="white" />
            </View>

            <Text
              style={[
                styles.categoryNameGrid,
                { color: themeColors.text }
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {translateCategoryName(item.name)}
            </Text>

            <Text
              style={[
                styles.categoryUsageGrid,
                { color: themeColors.secondaryText }
              ]}
              numberOfLines={1}
            >
              {renderUsageAmount(item.usage)}
            </Text>

            {/* Hiển thị nút chỉnh sửa khi ở chế độ chỉnh sửa hoặc danh mục được chọn */}
            {(isEditMode || selectedCategoryId === item.id) && (
              <View
                style={[
                  styles.gridButtonsContainer,
                  {
                    backgroundColor: isDark
                      ? "rgba(33, 58, 97, 0.9)"
                      : "rgba(255, 255, 255, 0.9)",
                    borderColor: themeColors.border,
                    borderWidth: 1,
                  },
                ]}
              >
                <TouchableOpacity
                  style={[
                    styles.iconButtonGrid,
                    {
                      backgroundColor: isDark ? themeColors.iconBackground : "#E3F2FD",
                    }
                  ]}
                  onPress={() => {
                    router.push(`/category/edit/${item.id}`);
                  }}
                >
                  <Ionicons name="create-outline" size={18} color={themeColors.primary} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.iconButtonGrid,
                    {
                      backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                    }
                  ]}
                  onPress={() => {
                    handleDeleteCategory(item.id);
                  }}
                >
                  <Ionicons name="trash-outline" size={18} color={themeColors.danger} />
                </TouchableOpacity>
              </View>
            )}
          </TouchableOpacity>

          {/* Hiển thị progress bar cho limit nếu có - Grid version */}
          {item.limitUsage && (
            <View style={[styles.gridLimitProgressContainer, { backgroundColor: 'transparent' }]}>
              <LimitProgressBar
                usage={item.limitUsage}
                size="small"
                showDetails={false}
                onPress={() => router.push('/spending-limits')}
                style={{ marginTop: 4 }}
              />
            </View>
          )}
        </View>
      );
    }
  };

  // Loading view
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.manageCategories")}
          </Text>
          <View style={{ width: 72 }} />
        </View>

        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Error view
  if (error) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("categories.manageCategories")}
          </Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={60} color={isDark ? "#64B5F6" : "#90CAF9"} />
          <Text style={[styles.errorText, { color: themeColors.text }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: themeColors.primary }]}
            onPress={() => fetchCategoriesWithUsage()}
          >
            <Text style={styles.retryButtonText}>{t("common.retry")}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      {/* Header cố định (không nằm trong ScrollView) */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("categories.manageCategories")}
        </Text>

        <View style={styles.headerRightButtons}>
          <TouchableOpacity
            onPress={toggleViewMode}
            style={[
              styles.viewModeButton,
              getIconContainerStyle(),
            ]}
            accessibilityLabel={
              viewMode === "list"
                ? t("categories.gridView")
                : t("categories.listView")
            }
          >
            <Ionicons
              name={viewMode === "list" ? "grid-outline" : "list-outline"}
              size={24}
              color={themeColors.primary}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* ScrollView bao toàn bộ nội dung để có thể cuộn */}
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1, backgroundColor: "transparent" }}
        ref={scrollViewRef}
      >
        {/* Card số dư */}
        <View
          style={{
            paddingHorizontal: 16,
            marginBottom: 20,
            backgroundColor: "transparent",
          }}
        >
          {/* Hiển thị số dư với thiết kế nổi bật */}
          <View
            style={{
              backgroundColor: isDark ? themeColors.cardBackground : themeColors.primary,
              borderRadius: 16,
              padding: 20,
              marginBottom: 14,
              alignItems: "center",
              borderWidth: 1,
              borderColor: isDark ? themeColors.border : "#1976D2",
              ...getShadowStyle('high'),
            }}
          >
            {/* Chữ Số dư và icon */}
            <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 10 }}>
              <Text
                style={{
                  color: "white",
                  fontSize: 18,
                  fontWeight: "bold",
                  marginRight: 10,
                }}
              >
                {t("home.yourBalance")}
              </Text>
              <TouchableOpacity
                onPress={toggleMoneyVisibility}
              >
                <Ionicons
                  name={isMoneyHidden ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color="white"
                />
              </TouchableOpacity>
            </View>

            <Text
              style={{
                color: "white",
                fontSize: 28,
                fontWeight: "bold",
                lineHeight: 34,
              }}
            >
              {isMoneyHidden
                ? "******"
                : (summary.balance >= 0 ? "+" : "") +
                  summary.balance.toLocaleString() +
                  symbol}
            </Text>
          </View>

          {/* Thêm thông số tổng chi tiêu và tổng thu nhập */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              backgroundColor: "transparent",
            }}
          >
            {/* Tổng chi tiêu - đã làm nổi bật */}
            <View
              style={{
                flex: 1,
                backgroundColor: isDark ? themeColors.dangerBackground : "#FFE0E0",
                borderRadius: 12,
                padding: 16,
                marginRight: 6,
                alignItems: "center",
                borderWidth: 1,
                borderColor: isDark ? themeColors.dangerBorder : "#FFCDD2",
                ...getShadowStyle('medium'),
              }}
            >
              <TouchableOpacity
                onPress={scrollToExpense}
                style={{ width: "100%", alignItems: "center" }}
              >
                <Text
                  style={{
                    color: isDark ? themeColors.danger : "#D32F2F",
                    fontSize: 14,
                    fontWeight: "600",
                    marginBottom: 6,
                  }}
                >
                  {t("stats.totalExpense")}
                </Text>
                <Text
                  style={{ 
                    color: isDark ? themeColors.danger : "#D32F2F", 
                    fontSize: 18, 
                    fontWeight: "bold" 
                  }}
                >
                  {isMoneyHidden
                    ? "******"
                    : summary.totalExpense.toLocaleString() + symbol}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Tổng thu nhập */}
            <View
              style={{
                flex: 1,
                backgroundColor: isDark ? themeColors.iconBackground : "#E3F2FD",
                borderRadius: 12,
                padding: 16,
                marginLeft: 6,
                alignItems: "center",
                borderWidth: 1,
                borderColor: isDark ? themeColors.border : "#BBDEFB",
                ...getShadowStyle('medium'),
              }}
            >
              <TouchableOpacity
                onPress={scrollToIncome}
                style={{ width: "100%", alignItems: "center" }}
              >
                <Text
                  style={{
                    color: isDark ? themeColors.secondaryText : "#1565C0",
                    fontSize: 14,
                    fontWeight: "600",
                    marginBottom: 6,
                  }}
                >
                  {t("stats.totalIncome")}
                </Text>
                <Text
                  style={{ 
                    color: isDark ? themeColors.secondaryText : "#1565C0", 
                    fontSize: 18, 
                    fontWeight: "bold" 
                  }}
                >
                  {isMoneyHidden
                    ? "******"
                    : summary.totalIncome.toLocaleString() + symbol}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Nút thêm danh mục, sửa, và báo cáo */}
        <View
          style={{
            flexDirection: "row",
            paddingHorizontal: 16,
            marginBottom: 20,
            justifyContent: "space-between",
            backgroundColor: "transparent",
          }}
        >
          <TouchableOpacity
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              paddingVertical: 12,
              paddingHorizontal: 20,
              borderRadius: 12,
              flex: 1,
              marginRight: 10,
              ...getCardStyle('low'),
            }}
            onPress={() => router.push("/category/add")}
          >
            <Ionicons name="add-outline" size={20} color={themeColors.primary} />
            <Text
              style={{
                color: themeColors.text,
                marginLeft: 8,
                fontSize: 15,
                fontWeight: "500",
              }}
            >
              {t("common.add")}
            </Text>
          </TouchableOpacity>

          {/* Nút Sửa/XONG */}
          {isEditMode ? (
            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 12,
                paddingHorizontal: 20,
                borderRadius: 12,
                backgroundColor: themeColors.primary,
                flex: 1,
                marginLeft: 10,
                ...getShadowStyle('medium'),
              }}
              onPress={toggleEditMode}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: 15,
                  fontWeight: "600",
                }}
              >
                {t("common.done")}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 12,
                paddingHorizontal: 20,
                borderRadius: 12,
                flex: 1,
                marginLeft: 10,
                ...getCardStyle('low'),
              }}
              onPress={toggleEditMode}
           >
             <Ionicons name="create-outline" size={20} color={themeColors.primary} />
             <Text
               style={{
                 color: themeColors.text,
                 marginLeft: 8,
                 fontSize: 15,
                 fontWeight: "500",
               }}
             >
               {t("common.edit")}
             </Text>
           </TouchableOpacity>
         )}
       </View>

       {/* Phần danh mục chi tiêu */}
       <View
         style={{ marginBottom: 24, backgroundColor: "transparent" }}
         onLayout={(event) => {
           const layout = event.nativeEvent.layout;
           setExpenseSectionY(layout.y);
         }}
       >
         {/* Tiêu đề phần chi tiêu */}
         <View
           style={{
             flexDirection: "row",
             alignItems: "center",
             paddingHorizontal: 16,
             marginBottom: 12,
             backgroundColor: "transparent",
           }}
         >
           <View
             style={{
               width: 4,
               height: 20,
               backgroundColor: themeColors.danger,
               marginRight: 10,
               borderRadius: 2,
             }}
           />
           <Text
             style={{
               color: themeColors.danger,
               fontSize: 18,
               fontWeight: "bold",
             }}
           >
             {t("categories.expenseCategories")} ({categoriesExpense.length})
           </Text>
         </View>

         {/* Danh sách danh mục chi tiêu */}
         <FlatList
           data={categoriesExpense}
           keyExtractor={(item) => item.id.toString()}
           renderItem={renderCategoryItem}
           contentContainerStyle={{
             paddingHorizontal: 16,
             paddingBottom: 16,
             paddingTop: 4,
             backgroundColor: "transparent",
           }}
           showsVerticalScrollIndicator={false}
           numColumns={viewMode === "grid" ? 2 : 1}
           key={"expense-" + viewMode}
           scrollEnabled={false} // Vô hiệu hóa cuộn cho FlatList vì đã có ScrollView cha
           columnWrapperStyle={
             viewMode === "grid" ? styles.gridColumnWrapper : undefined
           }
           extraData={[
             selectedCategoryId,
             viewMode,
             isEditMode,
             isMoneyHidden,
           ]}
           ListEmptyComponent={
             <View
               style={[
                 styles.emptyContainer,
                 getCardStyle('medium'),
                 { 
                   marginHorizontal: 4,
                   marginVertical: 10,
                 },
               ]}
             >
               <Ionicons
                 name="pricetags-outline"
                 size={40}
                 color={isDark ? "#64B5F6" : "#90CAF9"}
               />
               <Text
                 style={{
                   color: themeColors.secondaryText,
                   fontSize: 16,
                   marginTop: 12,
                 }}
               >
                 {t("categories.noCategories")}
               </Text>
             </View>
           }
         />
       </View>

       {/* Phần danh mục thu nhập */}
       <View
         style={{ backgroundColor: "transparent" }}
         onLayout={(event) => {
           const layout = event.nativeEvent.layout;
           setIncomeSectionY(layout.y);
         }}
       >
         {/* Tiêu đề phần thu nhập */}
         <View
           style={{
             flexDirection: "row",
             alignItems: "center",
             paddingHorizontal: 16,
             marginBottom: 12,
             backgroundColor: "transparent",
           }}
         >
           <View
             style={{
               width: 4,
               height: 20,
               backgroundColor: themeColors.primary,
               marginRight: 10,
               borderRadius: 2,
             }}
           />
           <Text
             style={{
               color: themeColors.primary,
               fontSize: 18,
               fontWeight: "bold",
             }}
           >
             {t("categories.incomeCategories")} ({categoriesIncome.length})
           </Text>
         </View>

         {/* Danh sách danh mục thu nhập */}
         <FlatList
           data={categoriesIncome}
           keyExtractor={(item) => item.id.toString()}
           renderItem={renderCategoryItem}
           contentContainerStyle={{
             paddingHorizontal: 16,
             paddingBottom: 30,
             paddingTop: 4,
             backgroundColor: "transparent",
           }}
           showsVerticalScrollIndicator={false}
           numColumns={viewMode === "grid" ? 2 : 1}
           key={"income-" + viewMode}
           scrollEnabled={false} // Vô hiệu hóa cuộn cho FlatList vì đã có ScrollView cha
           columnWrapperStyle={
             viewMode === "grid" ? styles.gridColumnWrapper : undefined
           }
           extraData={[
             selectedCategoryId,
             viewMode,
             isEditMode,
             isMoneyHidden,
           ]}
           ListEmptyComponent={
             <View
               style={[
                 styles.emptyContainer,
                 getCardStyle('medium'),
                 { 
                   marginHorizontal: 4,
                   marginVertical: 10,
                 },
               ]}
             >
               <Ionicons
                 name="pricetags-outline"
                 size={40}
                 color={isDark ? "#64B5F6" : "#90CAF9"}
               />
               <Text
                 style={{
                   color: themeColors.secondaryText,
                   fontSize: 16,
                   marginTop: 12,
                 }}
               >
                 {t("categories.noCategories")}
               </Text>
             </View>
           }
         />
       </View>
     </ScrollView>
   </SafeAreaView>
 );
}

const styles = StyleSheet.create({
 container: {
   flex: 1,
 },
 header: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "space-between",
   paddingHorizontal: 16,
   paddingVertical: 12,
 },
 headerTitle: {
   fontSize: 18,
   fontWeight: "bold",
 },
 headerRightButtons: {
   flexDirection: "row",
   alignItems: "center",
 },
 viewModeButton: {
   padding: 8,
   width: 40,
   height: 40,
   justifyContent: "center",
   alignItems: "center",
   borderRadius: 10,
 },
 backButton: {
   padding: 8,
   borderRadius: 20,
 },
 // Wrapper cho category item với progress bar
 categoryItemWrapper: {
   marginBottom: 10,
 },
 categoryItemList: {
   flexDirection: "row",
   alignItems: "center",
   paddingVertical: 12,
   paddingHorizontal: 16,
   borderRadius: 12,
 },
 // Container cho progress bar trong list view
 limitProgressContainer: {
   paddingHorizontal: 16,
   paddingTop: 4,
 },
 // Wrapper cho grid item
 gridItemWrapper: {
   flex: 1,
   margin: 8,
 },
 // Container cho progress bar trong grid view
 gridLimitProgressContainer: {
   paddingHorizontal: 4,
 },
 iconContainer: {
   width: 46,
   height: 46,
   borderRadius: 23,
   justifyContent: "center",
   alignItems: "center",
   marginRight: 16,
   shadowOffset: { width: 0, height: 2 },
   shadowRadius: 3,
   elevation: 4,
 },
 categoryContent: {
   flex: 1,
   flexDirection: "row",
   justifyContent: "space-between",
   alignItems: "center",
   backgroundColor: "transparent",
 },
 categoryName: {
   fontSize: 16,
   fontWeight: "600",
 },
 categoryUsage: {
   fontSize: 15,
   fontWeight: "600",
 },
 buttonContainer: {
   flexDirection: "row",
   backgroundColor: "transparent",
   marginLeft: 8,
 },
 editButton: {
   padding: 8,
   borderRadius: 8,
   marginRight: 6,
 },
 deleteButton: {
   padding: 8,
   borderRadius: 8,
 },
 categoryItemGrid: {
   padding: 16,
   height: 160,
   alignItems: "center",
   justifyContent: "center",
   borderRadius: 16,
 },
 gridColumnWrapper: {
   justifyContent: "space-between",
   marginHorizontal: -8,
 },
 categoryIconContainerGrid: {
   width: 56,
   height: 56,
   borderRadius: 28,
   justifyContent: "center",
   alignItems: "center",
   marginBottom: 8,
   shadowOffset: { width: 0, height: 2 },
   shadowRadius: 3,
   elevation: 4,
 },
 categoryNameGrid: {
   fontSize: 16,
   fontWeight: "600",
   textAlign: "center",
   marginTop: 10,
   marginBottom: 6,
   paddingHorizontal: 6,
   maxWidth: "100%",
 },
 categoryUsageGrid: {
   fontSize: 14,
   fontWeight: "600",
   textAlign: "center",
   marginBottom: 6,
 },
 gridButtonsContainer: {
   position: "absolute",
   top: 8,
   right: 8,
   flexDirection: "row",
   borderRadius: 8,
 },
 iconButtonGrid: {
   padding: 6,
   marginLeft: 4,
   borderRadius: 6,
 },
 emptyContainer: {
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 40,
   backgroundColor: "transparent",
 },
 loadingContainer: {
   flex: 1,
   justifyContent: "center",
   alignItems: "center",
 },
 loadingText: {
   marginTop: 16,
   fontSize: 16,
 },
 errorContainer: {
   flex: 1,
   justifyContent: "center",
   alignItems: "center",
   padding: 20,
 },
 errorText: {
   marginTop: 16,
   fontSize: 16,
   textAlign: "center",
   marginBottom: 20,
 },
 retryButton: {
   paddingHorizontal: 20,
   paddingVertical: 10,
   borderRadius: 12,
   shadowColor: "#000",
   shadowOffset: { width: 0, height: 2 },
   shadowOpacity: 0.2,
   shadowRadius: 3,
   elevation: 3,
 },
 retryButtonText: {
   color: "#fff",
   fontSize: 16,
   fontWeight: "600",
 },
});