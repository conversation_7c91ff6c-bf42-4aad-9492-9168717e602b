import { Text, View } from "@/components/Themed";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { StyleSheet, TouchableOpacity } from "react-native";

export default function CongratulationsScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";

  const handleStart = () => {
    // Chuyển đến màn hình chính của ứng dụng
    router.replace("/(tabs)");
  };

  const bgColor = isDark ? "#121212" : "#f7f7f7";
  const textColor = isDark ? "#FFFFFF" : "#333333";
  const cardBgColor = isDark ? "#242424" : "#FFFFFF";

  return (
    <View style={[styles.container, { backgroundColor: bgColor }]}>
      <View style={[styles.content, { backgroundColor: "transparent" }]}>
        <View
          style={[styles.iconContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="checkmark-circle"
            size={100}
            color="#4CAF50"
            style={styles.icon}
          />
        </View>

        <Text style={[styles.title, { color: textColor, paddingTop: 7 }]}>
          Chúc mừng!
        </Text>
        <Text style={[styles.description, { color: isDark ? "#BBB" : "#666" }]}>
          Bạn đã hoàn thành thiết lập ban đầu cho AI Money. Giờ đây bạn có thể
          bắt đầu quản lý tài chính cá nhân của mình.
        </Text>

        <View
          style={[styles.stepsComplete, { backgroundColor: "transparent" }]}
        >
          <View style={[styles.stepItem, { backgroundColor: cardBgColor }]}>
            <View style={[styles.stepIcon, { backgroundColor: "#4CAF50" }]}>
              <Ionicons name="wallet-outline" size={24} color="white" />
            </View>
            <Text style={[styles.stepText, { color: textColor }]}>
              Đã tạo ví
            </Text>
          </View>

          <View style={[styles.stepItem, { backgroundColor: cardBgColor }]}>
            <View style={[styles.stepIcon, { backgroundColor: "#2196F3" }]}>
              <Ionicons name="list-outline" size={24} color="white" />
            </View>
            <Text style={[styles.stepText, { color: textColor }]}>
              Đã cài đặt danh mục
            </Text>
          </View>
        </View>

        <TouchableOpacity style={styles.button} onPress={handleStart}>
          <Text style={styles.buttonText}>Bắt đầu sử dụng</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  content: {
    width: "100%",
    maxWidth: 500,
    alignItems: "center",
  },
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 30,
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 30,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 40,
    lineHeight: 24,
  },
  stepsComplete: {
    width: "100%",
    marginBottom: 30,
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: "#f8f8f8",
    borderRadius: 12,
  },
  stepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
  },
  stepText: {
    fontSize: 16,
    fontWeight: "500",
  },
  button: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginTop: 10,
    width: "100%",
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
});
