import { useColorScheme } from "@/hooks/useColorScheme";
import { WalletModel, type WalletCreateInput } from "@/lib/models/wallet";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { Text, View } from "../../components/Themed";

// Available wallet types
const WALLET_TYPES = [
  { id: "cash", name: "Tiền mặt", icon: "cash-outline" },
  { id: "bank", name: "<PERSON><PERSON> hàng", icon: "card-outline" },
  { id: "ewallet", name: "<PERSON><PERSON> điện tử", icon: "wallet-outline" },
];

// Available colors for wallets
const WALLET_COLORS = [
  "#4CAF50", // Green
  "#2196F3", // Blue
  "#9C27B0", // Purple
  "#F44336", // Red
  "#FF9800", // Orange
  "#795548", // Brown
  "#607D8B", // Blue Grey
  "#E91E63", // Pink
  "#3F51B5", // Indigo
];

// Định nghĩa kiểu cho ví tạm thời
interface TempWallet {
  id?: string;
  name: string;
  balance: number;
  type: string;
  icon: string;
  color: string;
  is_default: boolean;
}

// Hàm định dạng số tiền với dấu phân cách hàng nghìn
const formatCurrency = (amount: number): string => {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
};

export default function CreateWalletScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";

  // Danh sách ví tạm thời (chỉ lưu trong bộ nhớ)
  const [tempWallets, setTempWallets] = useState<TempWallet[]>([]);

  const [name, setName] = useState("");
  const [balance, setBalance] = useState("");
  const [selectedType, setSelectedType] = useState(WALLET_TYPES[0].id);
  const [selectedColor, setSelectedColor] = useState(WALLET_COLORS[0]);
  const [loading, setLoading] = useState(false);

  // Xử lý khi thay đổi giá trị số dư
  const handleBalanceChange = (text: string) => {
    // Chỉ cho phép nhập số
    const cleanedText = text.replace(/[^0-9]/g, "");
    setBalance(cleanedText);
  };

  // Thêm ví vào danh sách tạm thời
  const handleAddWallet = () => {
    // Validate inputs
    if (!name.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập tên ví");
      return;
    }

    if (!balance.trim() || isNaN(Number(balance))) {
      Alert.alert("Lỗi", "Vui lòng nhập số dư hợp lệ");
      return;
    }

    // Tạo ví tạm thời
    const newWallet: TempWallet = {
      name: name.trim(),
      balance: parseFloat(balance),
      type: selectedType,
      icon:
        WALLET_TYPES.find((type) => type.id === selectedType)?.icon ||
        "wallet-outline",
      color: selectedColor,
      is_default: tempWallets.length === 0, // Ví đầu tiên là mặc định
    };

    // Thêm vào danh sách tạm thời
    setTempWallets([...tempWallets, newWallet]);

    // Reset form
    setName("");
    setBalance("");
    setSelectedType(WALLET_TYPES[0].id);
    setSelectedColor(WALLET_COLORS[0]);
  };

  // Tạo ví vào cơ sở dữ liệu và chuyển đến trang tiếp theo
  const goToCategories = async () => {
    if (tempWallets.length === 0) {
      Alert.alert("Lỗi", "Vui lòng thêm ít nhất một ví");
      return;
    }

    try {
      setLoading(true);

      // Lưu tất cả ví vào cơ sở dữ liệu
      await Promise.all(
        tempWallets.map((wallet) => {
          const walletData: WalletCreateInput = {
            name: wallet.name,
            balance: wallet.balance,
            type: wallet.type as "cash" | "bank" | "ewallet",
            color: wallet.color,
            icon: wallet.icon,
            is_default: wallet.is_default,
          };
          return WalletModel.create(walletData);
        })
      );

      // Chuyển đến trang tiếp theo
      router.push("/(setup)/create-categories");
    } catch (error) {
      console.error("Lỗi khi tạo ví:", error);
      Alert.alert("Thất bại", "Có lỗi xảy ra khi lưu thông tin ví");
    } finally {
      setLoading(false);
    }
  };

  const bgColor = isDark ? "#121212" : "#f7f7f7";

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1, backgroundColor: bgColor }}
    >
      <ScrollView style={[styles.scrollView, { backgroundColor: bgColor }]}>
        <View style={[styles.container, { backgroundColor: "transparent" }]}>
          <View style={[styles.header, { backgroundColor: "transparent" }]}>
            <Text
              style={[
                styles.title,
                { color: isDark ? "#FFFFFF" : "#333333", paddingTop: 4 },
              ]}
            >
              Tạo ví của bạn
            </Text>
            <Text
              style={[styles.subtitle, { color: isDark ? "#BBB" : "#666" }]}
            >
              Hãy tạo ít nhất một ví để quản lý chi tiêu
            </Text>
          </View>

          {/* Danh sách ví tạm thời */}
          {tempWallets.length > 0 && (
            <View style={styles.walletsList}>
              <Text
                style={[
                  styles.sectionTitle,
                  { color: isDark ? "#FFFFFF" : "#333333", paddingTop: 2 },
                ]}
              >
                Các ví đã tạo
              </Text>
              {tempWallets.map((wallet, index) => (
                <View
                  key={index}
                  style={[
                    styles.walletItem,
                    {
                      borderLeftColor: wallet.color,
                      borderLeftWidth: 4,
                      backgroundColor: isDark ? "#242424" : "#FFFFFF",
                    },
                  ]}
                >
                  <View
                    style={[
                      styles.walletIcon,
                      { backgroundColor: wallet.color },
                    ]}
                  >
                    <Ionicons
                      name={wallet.icon as any}
                      size={20}
                      color="white"
                    />
                  </View>
                  <View
                    style={[
                      styles.walletInfo,
                      { backgroundColor: "transparent" },
                    ]}
                  >
                    <Text
                      style={[
                        styles.walletName,
                        { color: isDark ? "#FFFFFF" : "#333333" },
                      ]}
                    >
                      {wallet.name}
                      {wallet.is_default && " (Mặc định)"}
                    </Text>
                    <Text
                      style={[
                        styles.walletBalance,
                        { color: isDark ? "#BBB" : "#666" },
                      ]}
                    >
                      {formatCurrency(wallet.balance)} VND
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}

          {/* Form thêm ví */}
          <View
            style={[
              styles.card,
              { backgroundColor: isDark ? "#242424" : "#FFFFFF" },
            ]}
          >
            <Text
              style={[
                styles.sectionTitle,
                { color: isDark ? "#FFFFFF" : "#333333", paddingTop: 2 },
              ]}
            >
              Thêm ví mới
            </Text>

            {/* Wallet Name */}
            <View style={styles.formGroup}>
              <Text
                style={[
                  styles.label,
                  { color: isDark ? "#FFFFFF" : "#333333" },
                ]}
              >
                Tên ví
              </Text>
              <View
                style={[styles.inputWrapper, isDark && styles.inputWrapperDark]}
              >
                <Ionicons
                  name="wallet-outline"
                  size={24}
                  color={isDark ? "#BBB" : "#888"}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={[
                    styles.input,
                    isDark && styles.inputDark,
                    { color: isDark ? "#FFFFFF" : "#333333" },
                  ]}
                  placeholder="Nhập tên ví"
                  placeholderTextColor={isDark ? "#888" : "#AAA"}
                  value={name}
                  onChangeText={setName}
                  editable={!loading}
                />
              </View>
            </View>

            {/* Initial Balance */}
            <View style={styles.formGroup}>
              <Text
                style={[
                  styles.label,
                  { color: isDark ? "#FFFFFF" : "#333333" },
                ]}
              >
                Số dư ban đầu
              </Text>
              <View
                style={[styles.inputWrapper, isDark && styles.inputWrapperDark]}
              >
                <Ionicons
                  name="cash-outline"
                  size={24}
                  color={isDark ? "#BBB" : "#888"}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={[
                    styles.input,
                    isDark && styles.inputDark,
                    { color: isDark ? "#FFFFFF" : "#333333" },
                  ]}
                  placeholder="Nhập số dư"
                  placeholderTextColor={isDark ? "#888" : "#AAA"}
                  value={balance}
                  onChangeText={handleBalanceChange}
                  keyboardType="numeric"
                  editable={!loading}
                />
              </View>
              {balance ? (
                <Text style={styles.formattedBalance}>
                  {formatCurrency(Number(balance))} VND
                </Text>
              ) : null}
            </View>

            {/* Wallet Type */}
            <View style={styles.formGroup}>
              <Text
                style={[
                  styles.label,
                  { color: isDark ? "#FFFFFF" : "#333333" },
                ]}
              >
                Loại ví
              </Text>
              <View
                style={[
                  styles.typeContainer,
                  { backgroundColor: "transparent" },
                ]}
              >
                {WALLET_TYPES.map((type) => (
                  <TouchableOpacity
                    key={type.id}
                    style={[
                      styles.typeOption,
                      isDark && styles.typeOptionDark,
                      selectedType === type.id && styles.selectedType,
                      selectedType === type.id &&
                        isDark &&
                        styles.selectedTypeDark,
                    ]}
                    onPress={() => setSelectedType(type.id)}
                    disabled={loading}
                  >
                    <Ionicons
                      name={type.icon as any}
                      size={24}
                      color={
                        selectedType === type.id
                          ? "white"
                          : isDark
                          ? "#BBB"
                          : "#666"
                      }
                    />
                    <Text
                      style={[
                        styles.typeText,
                        selectedType === type.id && styles.selectedTypeText,
                        {
                          color:
                            selectedType === type.id
                              ? "white"
                              : isDark
                              ? "#BBB"
                              : "#666",
                        },
                      ]}
                    >
                      {type.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Color Selection */}
            <View style={styles.formGroup}>
              <Text
                style={[
                  styles.label,
                  { color: isDark ? "#FFFFFF" : "#333333" },
                ]}
              >
                Màu sắc
              </Text>
              <FlatList
                data={WALLET_COLORS}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={(item) => item}
                style={styles.colorList}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.colorItem,
                      { backgroundColor: item },
                      item === selectedColor && styles.selectedColorItem,
                    ]}
                    onPress={() => setSelectedColor(item)}
                    disabled={loading}
                  >
                    {item === selectedColor && (
                      <Ionicons name="checkmark" size={20} color="white" />
                    )}
                  </TouchableOpacity>
                )}
              />
            </View>

            {/* Add Wallet Button */}
            <TouchableOpacity
              style={[
                styles.addButton,
                (!name || !balance || loading) && styles.addButtonDisabled,
              ]}
              onPress={handleAddWallet}
              disabled={!name || !balance || loading}
            >
              {loading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <Text style={styles.addButtonText}>Thêm vào danh sách</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Continue Button */}
          <TouchableOpacity
            style={[
              styles.continueButton,
              {
                backgroundColor: tempWallets.length > 0 ? "#4CAF50" : "#CCCCCC",
              },
            ]}
            onPress={goToCategories}
            disabled={tempWallets.length === 0 || loading}
          >
            {loading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text style={[styles.continueButtonText, { color: "white" }]}>
                Tiếp tục
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: "center",
    marginTop: 60,
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: "#888",
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
  },
  walletsList: {
    marginBottom: 25,
    padding: 12,
    borderRadius: 12,
  },
  walletItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f8f8",
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  walletIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  walletInfo: {
    flex: 1,
  },
  walletName: {
    fontSize: 16,
    fontWeight: "600",
  },
  walletBalance: {
    fontSize: 14,
    color: "#888",
    marginTop: 2,
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  formGroup: {
    marginBottom: 20,
    backgroundColor: "transparent",
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 12,
    paddingHorizontal: 16,
    backgroundColor: "#f8f8f8",
  },
  inputWrapperDark: {
    borderColor: "#444",
    backgroundColor: "#333",
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: "#333",
  },
  inputDark: {
    color: "#FFF",
  },
  typeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  typeOption: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#EEE",
    marginHorizontal: 4,
    backgroundColor: "#f8f8f8",
    justifyContent: "center",
  },
  typeOptionDark: {
    backgroundColor: "#333",
    borderColor: "#444",
  },
  selectedType: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  selectedTypeDark: {
    backgroundColor: "#0066CC",
    borderColor: "#0066CC",
  },
  typeText: {
    textAlign: "center",
    marginTop: 8,
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  selectedTypeText: {
    color: "white",
    fontWeight: "500",
  },
  colorList: {
    marginTop: 10,
    marginBottom: 10,
  },
  colorItem: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 15,
    justifyContent: "center",
    alignItems: "center",
    margin: 4,
  },
  selectedColorItem: {
    borderWidth: 3,
    borderColor: "white",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  addButton: {
    backgroundColor: "#007AFF",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 10,
  },
  addButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
  continueButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 10,
    marginBottom: 30,
  },
  continueButtonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
  addButtonDisabled: {
    backgroundColor: "#CCC",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  formattedBalance: {
    marginTop: 8,
    fontSize: 14,
    color: "#4CAF50",
    fontWeight: "500",
  },
});
