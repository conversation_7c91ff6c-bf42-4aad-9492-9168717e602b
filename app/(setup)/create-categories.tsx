import { useColorScheme } from "@/hooks/useColorScheme";
import { CategoryModel, type CategoryCreateInput } from "@/lib/models/category";
import { supabase } from "@/lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  View as RNView,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { Text, View } from "../../components/Themed";

// Interface cho danh mục
interface Category {
  id: number | string;
  name: string;
  icon: string;
  color: string;
  selected: boolean;
  type: "expense" | "income";
}

// Danh mục chi tiêu
const EXPENSE_CATEGORIES: Category[] = [
  {
    id: 1,
    name: "Ăn uống",
    icon: "restaurant-outline",
    color: "#FF9800",
    selected: true,
    type: "expense",
  },
  {
    id: 2,
    name: "<PERSON>ể<PERSON>",
    icon: "car-outline",
    color: "#9C27B0",
    selected: true,
    type: "expense",
  },
  {
    id: 3,
    name: "<PERSON><PERSON> sắm",
    icon: "cart-outline",
    color: "#F44336",
    selected: true,
    type: "expense",
  },
  {
    id: 4,
    name: "Hóa đơn",
    icon: "document-text-outline",
    color: "#2196F3",
    selected: true,
    type: "expense",
  },
  {
    id: 5,
    name: "Nhà cửa",
    icon: "home-outline",
    color: "#FF5722",
    selected: true,
    type: "expense",
  },
  {
    id: 6,
    name: "Giải trí",
    icon: "film-outline",
    color: "#673AB7",
    selected: true,
    type: "expense",
  },
  {
    id: 7,
    name: "Sức khỏe",
    icon: "fitness-outline",
    color: "#4CAF50",
    selected: true,
    type: "expense",
  },
  {
    id: 8,
    name: "Giáo dục",
    icon: "school-outline",
    color: "#009688",
    selected: true,
    type: "expense",
  },
];

// Danh mục thu nhập
const INCOME_CATEGORIES: Category[] = [
  {
    id: 1,
    name: "Lương",
    icon: "cash-outline",
    color: "#4CAF50",
    selected: true,
    type: "income",
  },
  {
    id: 2,
    name: "Thưởng",
    icon: "gift-outline",
    color: "#2196F3",
    selected: true,
    type: "income",
  },
  {
    id: 3,
    name: "Đầu tư",
    icon: "trending-up-outline",
    color: "#9C27B0",
    selected: true,
    type: "income",
  },
  {
    id: 4,
    name: "Khác",
    icon: "wallet-outline",
    color: "#607D8B",
    selected: true,
    type: "income",
  },
];

export default function CreateCategoriesScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";

  const [expenseCategories, setExpenseCategories] =
    useState<Category[]>(EXPENSE_CATEGORIES);
  const [incomeCategories, setIncomeCategories] =
    useState<Category[]>(INCOME_CATEGORIES);
  const [activeTab, setActiveTab] = useState("expense"); // 'expense' hoặc 'income'
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Tải danh mục từ Supabase khi vào trang
  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setInitialLoading(true);
      const data = await CategoryModel.getAll();

      if (data && data.length > 0) {
        // Đã có danh mục, phân loại và đánh dấu tất cả là selected
        const expense: Category[] = [];
        const income: Category[] = [];

        data.forEach((cat) => {
          const category: Category = {
            id: cat.id,
            name: cat.name,
            icon: cat.icon,
            color: cat.color,
            selected: true,
            type: cat.type as "expense" | "income",
          };

          if (cat.type === "expense") {
            expense.push(category);
          } else {
            income.push(category);
          }
        });

        if (expense.length > 0) setExpenseCategories(expense);
        if (income.length > 0) setIncomeCategories(income);
      }
    } catch (error) {
      console.error("Lỗi khi tải danh mục:", error);
      Alert.alert("Lỗi", "Không thể tải danh mục");
    } finally {
      setInitialLoading(false);
    }
  };

  const toggleCategorySelection = (
    categoryId: number | string,
    type: "expense" | "income"
  ) => {
    if (type === "expense") {
      setExpenseCategories(
        expenseCategories.map((cat) =>
          cat.id === categoryId ? { ...cat, selected: !cat.selected } : cat
        )
      );
    } else {
      setIncomeCategories(
        incomeCategories.map((cat) =>
          cat.id === categoryId ? { ...cat, selected: !cat.selected } : cat
        )
      );
    }
  };

  const saveCategories = async () => {
    try {
      setLoading(true);

      // Kiểm tra trạng thái đăng nhập
      const { data: sessionData } = await supabase.auth.getSession();
      console.log("Session data:", sessionData);

      if (!sessionData.session?.user) {
        Alert.alert(
          "Lỗi",
          "Người dùng chưa đăng nhập. Vui lòng đăng nhập lại."
        );
        return;
      }

      // Lấy tất cả danh mục đã chọn
      const selectedExpenseCategories = expenseCategories.filter(
        (cat) => cat.selected
      );
      const selectedIncomeCategories = incomeCategories.filter(
        (cat) => cat.selected
      );

      // Xóa tất cả danh mục hiện tại
      await CategoryModel.deleteAll();

      // Chèn danh mục mới
      const newCategories: CategoryCreateInput[] = [
        ...selectedExpenseCategories.map((cat) => ({
          name: cat.name,
          icon: cat.icon,
          color: cat.color,
          type: cat.type as "expense",
        })),
        ...selectedIncomeCategories.map((cat) => ({
          name: cat.name,
          icon: cat.icon,
          color: cat.color,
          type: cat.type as "income",
        })),
      ];

      console.log(
        "Categories to create:",
        JSON.stringify(newCategories, null, 2)
      );

      try {
        await CategoryModel.createMany(newCategories);
        console.log("Categories created successfully");
      } catch (insertError) {
        console.error("Error details:", insertError);
        Alert.alert("Lỗi khi tạo danh mục", JSON.stringify(insertError));
        return;
      }

      // Chuyển đến trang chúc mừng
      router.push("/(setup)/congratulations");
    } catch (error) {
      console.error("Lỗi khi lưu danh mục:", error);
      Alert.alert("Thất bại", "Có lỗi xảy ra khi lưu danh mục");
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    const selectedExpense = expenseCategories.filter((cat) => cat.selected);
    const selectedIncome = incomeCategories.filter((cat) => cat.selected);

    if (selectedExpense.length === 0) {
      Alert.alert("Lỗi", "Vui lòng chọn ít nhất một danh mục chi tiêu");
      setActiveTab("expense");
      return;
    }

    if (selectedIncome.length === 0) {
      Alert.alert("Lỗi", "Vui lòng chọn ít nhất một danh mục thu nhập");
      setActiveTab("income");
      return;
    }

    // Lưu danh mục vào Supabase và chuyển đến trang chúc mừng
    saveCategories();
  };

  const renderCategories = (
    categories: Category[],
    type: "expense" | "income"
  ) => {
    return (
      <RNView style={styles.categoriesGrid}>
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryItem,
              { backgroundColor: isDark ? "#222" : "#fff" },
              category.selected && {
                borderColor: category.color,
                borderWidth: 2,
              },
            ]}
            onPress={() => toggleCategorySelection(category.id, type)}
          >
            <RNView
              style={[
                styles.categoryIcon,
                { backgroundColor: category.color },
                !category.selected && { opacity: 0.5 },
              ]}
            >
              <Ionicons name={category.icon as any} size={24} color="white" />
            </RNView>
            <Text
              style={[
                styles.categoryName,
                { color: isDark ? "#FFFFFF" : "#333333" },
                !category.selected && { color: isDark ? "#888" : "#aaa" },
              ]}
            >
              {category.name}
            </Text>
            {category.selected && (
              <RNView style={styles.checkmarkContainer}>
                <Ionicons
                  name="checkmark-circle"
                  size={22}
                  color={category.color}
                />
              </RNView>
            )}
          </TouchableOpacity>
        ))}
      </RNView>
    );
  };

  if (initialLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={{ marginTop: 20, color: isDark ? "#BBB" : "#666" }}>
          Đang tải danh mục...
        </Text>
      </View>
    );
  }

  const bgColor = isDark ? "#121212" : "#f7f7f7";

  return (
    <View style={[styles.container, { backgroundColor: bgColor }]}>
      <ScrollView style={styles.scrollView}>
        <View style={[styles.header, { backgroundColor: "transparent" }]}>
          <Text
            style={[
              styles.title,
              { color: isDark ? "#FFFFFF" : "#333333", paddingTop: 4 },
            ]}
          >
            Chọn danh mục
          </Text>
          <Text style={[styles.subtitle, { color: isDark ? "#BBB" : "#888" }]}>
            Chọn các danh mục phù hợp với nhu cầu của bạn
          </Text>
        </View>

        {/* Type Selector */}
        <View style={[styles.tabContainer, { backgroundColor: "transparent" }]}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              { backgroundColor: isDark ? "#333" : "#f1f1f1" },
              activeTab === "expense" && styles.activeTab,
              activeTab === "expense" && isDark && styles.activeTabDark,
            ]}
            onPress={() => setActiveTab("expense")}
            disabled={loading}
          >
            <Text
              style={[
                styles.tabText,
                { color: isDark ? "#BBB" : "#666" },
                activeTab === "expense" && styles.activeTabText,
              ]}
            >
              Chi tiêu
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              { backgroundColor: isDark ? "#333" : "#f1f1f1" },
              activeTab === "income" && styles.activeTab,
              activeTab === "income" && isDark && styles.activeTabDark,
            ]}
            onPress={() => setActiveTab("income")}
            disabled={loading}
          >
            <Text
              style={[
                styles.tabText,
                { color: isDark ? "#BBB" : "#666" },
                activeTab === "income" && styles.activeTabText,
              ]}
            >
              Thu nhập
            </Text>
          </TouchableOpacity>
        </View>

        <View
          style={[
            styles.categoriesContainer,
            { backgroundColor: "transparent" },
          ]}
        >
          <Text
            style={[
              styles.sectionTitle,
              { color: isDark ? "#FFFFFF" : "#333333" },
            ]}
          >
            {activeTab === "expense"
              ? "Danh mục chi tiêu"
              : "Danh mục thu nhập"}
          </Text>

          {activeTab === "expense"
            ? renderCategories(expenseCategories, "expense")
            : renderCategories(incomeCategories, "income")}

          <Text
            style={[styles.helperText, { color: isDark ? "#888" : "#888" }]}
          >
            Nhấn vào các danh mục để chọn hoặc bỏ chọn
          </Text>
        </View>
      </ScrollView>

      <View style={[styles.footer, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            loading && { opacity: 0.7, backgroundColor: "#007AFF80" },
          ]}
          onPress={handleContinue}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text style={styles.continueButtonText}>Tiếp tục</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: "center",
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: "#888",
    textAlign: "center",
  },
  tabContainer: {
    flexDirection: "row",
    marginHorizontal: 20,
    marginVertical: 20,
    borderRadius: 12,
    overflow: "hidden",
  },
  tabButton: {
    flex: 1,
    padding: 15,
    alignItems: "center",
    backgroundColor: "#f1f1f1",
  },
  activeTab: {
    backgroundColor: "#007AFF",
  },
  activeTabDark: {
    backgroundColor: "#0066CC",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#666",
  },
  activeTabText: {
    color: "white",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
    marginHorizontal: 20,
  },
  categoriesContainer: {
    paddingHorizontal: 10,
    paddingBottom: 20,
  },
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 10,
  },
  categoryItem: {
    width: "30%",
    borderRadius: 12,
    padding: 12,
    alignItems: "center",
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: "relative",
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    textAlign: "center",
  },
  checkmarkContainer: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "white",
    borderRadius: 12,
  },
  helperText: {
    textAlign: "center",
    color: "#888",
    marginTop: 20,
    fontSize: 14,
  },
  footer: {
    padding: 20,
    paddingBottom: 35,
  },
  continueButton: {
    backgroundColor: "#007AFF",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  continueButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
});
