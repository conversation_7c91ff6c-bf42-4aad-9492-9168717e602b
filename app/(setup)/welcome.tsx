// File nằm ở: welcome.tsx
import { useLocalization } from "@/context/LocalizationContext";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useEffect, useState } from "react";
import {
  Animated,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View as RNView,
  ScrollView,
} from "react-native";
import { Text, View } from "../../components/Themed";

export default function WelcomeScreen() {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";
  const { t } = useLocalization();

  // Animation values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];
  const scaleAnim = useState(new Animated.Value(0.9))[0];
  const optionsAnim = useState(new Animated.Value(0))[0];

  useEffect(() => {
    // Run animations sequentially
    Animated.sequence([
      // Fade in and slide up the main content
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      // Then animate options
      Animated.timing(optionsAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const bgColor = isDark ? "#121212" : "#f7f7f7";
  const textColor = isDark ? "#ffffff" : "#333333";
  const stepBgColor = isDark ? "#2c2c2c" : "#f0f0f0";
  const cardBgColor = isDark ? "#2c2c2c" : "#FFFFFF";
  const cardBorderColor = isDark ? "#444444" : "#EEEEEE";
  const primaryColor = isDark ? "#64B5F6" : "#2196F3";

  const handleGoToWallet = () => {
    // Chuyển đến màn hình tạo ví
    router.push("/wallet/add?fromSetup=true");  // Chuyển đến trang add.tsx thay vì create-wallet.tsx
  };

  const handleGoToMoving = () => {
    // Chuyển đến màn hình chuyển App
    router.push("/moving?fromSetup=true"); // Thêm tham số query
  };

  return (
    <View style={[styles.container, { backgroundColor: bgColor }]}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} />

      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
            },
          ]}
        >
          <View
            style={[styles.iconContainer, { backgroundColor: "transparent" }]}
          >
            <Ionicons
              name="rocket"
              size={100}
              color={isDark ? "#64B5F6" : "#2196F3"}
              style={styles.icon}
            />
          </View>

          

          {/* Options section (Merged from Modal) */}
          <Animated.View
            style={[
              styles.optionsSection,
              {
                opacity: optionsAnim,
                transform: [
                  {
                    translateY: optionsAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text
              style={[
                styles.optionsTitle,
                { color: textColor, marginBottom: 50 },
              ]}
            >
              Bạn có đang sử dụng App{"\n"}{"\n"}nào khác không?
            </Text>

            <TouchableOpacity
              style={[
                styles.optionCard,
                {
                  backgroundColor: cardBgColor,
                  borderColor: cardBorderColor,
                },
              ]}
              onPress={handleGoToMoving}
            >
              <RNView
                style={[
                  styles.optionIconContainer,
                  { backgroundColor: primaryColor },
                ]}
              >
                <Ionicons
                  name="swap-horizontal"
                  size={30}
                  color="white"
                />
              </RNView>
              <RNView style={styles.optionContent}>
                <Text
                  style={[
                    styles.optionTitle,
                    { color: textColor },
                  ]}
                >
                  Đã có ứng dụng khác
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    { color: isDark ? "#BBB" : "#666" },
                  ]}
                >
                  Chuyển dữ liệu từ ứng dụng khác sang AI Money bằng AI
                </Text>
              </RNView>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={isDark ? "#666" : "#999"}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionCard,
                {
                  backgroundColor: cardBgColor,
                  borderColor: cardBorderColor,
                },
              ]}
              onPress={handleGoToWallet}
            >
              <RNView
                style={[
                  styles.optionIconContainer,
                  { backgroundColor: "#4CAF50" },
                ]}
              >
                <Ionicons name="add-circle" size={30} color="white" />
              </RNView>
              <RNView style={styles.optionContent}>
                <Text
                  style={[
                    styles.optionTitle,
                    { color: textColor },
                  ]}
                >
                  Tôi là người mới
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    { color: isDark ? "#BBB" : "#666" },
                  ]}
                >
                  Tạo ví mới và bắt đầu sử dụng AI Money
                </Text>
              </RNView>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={isDark ? "#666" : "#999"}
              />
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
    paddingBottom: 40, // Thêm padding ở dưới để tránh bị che khuất
  },
  content: {
    width: "100%",
    maxWidth: 500,
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: 30,
    alignItems: "center",
    justifyContent: "center",
  },
  icon: {
    marginBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 30,
    lineHeight: 24,
  },
  steps: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 24,
    alignSelf: "flex-start",
  },
  stepContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    width: "100%",
  },
  stepIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  stepNumber: {
    color: "white",
    fontWeight: "bold",
    fontSize: 18,
  },
  stepCard: {
    flex: 1,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  stepText: {
    fontSize: 16,
    fontWeight: "500",
    flex: 1,
  },
  stepIcon: {
    marginLeft: 10,
  },
  // Options Styles
  optionsSection: {
    width: "100%",
    marginTop: 0,
  },
  optionsTitle: {
    fontSize: 22,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 30,
  },
  optionCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 16,
    marginBottom: 30,
    borderWidth: 1,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  optionIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  button: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    width: "100%",
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 5,
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
  buttonIcon: {
    marginLeft: 8,
  },
});