// File: app/utils/calc-utils.tsx
// File này chứa các hàm tính toán cho máy tính tài chính
// File này liên quan đến: app/(more)/finance-calculator.tsx

/**
 * Tính lãi suất tiết kiệm đơn giản
 * @param principal Số tiền gốc
 * @param rate Lãi suất hàng năm (phần trăm)
 * @param time Thời gian (năm)
 * @returns Số tiền lãi
 */
export const calculateSimpleInterest = (principal: number, rate: number, time: number): number => {
  return principal * (rate / 100) * time;
};

/**
 * Tính lãi suất tiết kiệm kép
 * @param principal Số tiền gốc
 * @param rate Lãi suất hàng năm (phần trăm)
 * @param time Thời gian (năm)
 * @param frequency Tần suất gộp lãi (mặc định là 1 lần/năm)
 * @returns Tổng số tiền (gốc + lãi)
 */
export const calculateCompoundInterest = (
  principal: number,
  rate: number,
  time: number,
  frequency: number = 1
): number => {
  return principal * Math.pow(1 + rate / 100 / frequency, frequency * time);
};

/**
 * Tính trả góp khoản vay theo phương pháp EMI
 * @param principal Số tiền vay
 * @param rate Lãi suất hàng năm (phần trăm)
 * @param time Thời gian (năm)
 * @returns Số tiền trả hàng tháng
 */
export const calculateLoanEMI = (principal: number, rate: number, time: number): number => {
  const monthlyRate = rate / 12 / 100;
  const months = time * 12;
  return (
    principal *
    monthlyRate *
    (Math.pow(1 + monthlyRate, months) / (Math.pow(1 + monthlyRate, months) - 1))
  );
};

/**
 * Tính lịch trả nợ khoản vay
 * @param principal Số tiền vay
 * @param rate Lãi suất hàng năm (phần trăm)
 * @param time Thời gian (năm)
 * @returns Mảng các khoản thanh toán theo tháng
 */
export const calculateAmortizationSchedule = (
  principal: number,
  rate: number,
  time: number
): Array<{
  month: number;
  payment: number;
  principal: number;
  interest: number;
  balance: number;
}> => {
  const monthlyRate = rate / 12 / 100;
  const months = time * 12;
  const monthlyPayment = calculateLoanEMI(principal, rate, time);

  let balance = principal;
  const schedule = [];

  for (let i = 1; i <= months; i++) {
    const interest = balance * monthlyRate;
    const principalPaid = monthlyPayment - interest;
    balance -= principalPaid;

    schedule.push({
      month: i,
      payment: monthlyPayment,
      principal: principalPaid,
      interest: interest,
      balance: Math.max(0, balance), // Đảm bảo không có số dư âm do làm tròn
    });
  }

  return schedule;
};

/**
 * Định dạng số thành chuỗi tiền tệ
 * @param value Giá trị số
 * @param currency Mã tiền tệ
 * @returns Chuỗi đã định dạng
 */
export const formatCurrency = (value: number, currency: string = "VND"): string => {
  // Định dạng số theo tiền tệ
  switch (currency) {
    case "VND":
      // VND thường không hiển thị phần thập phân
      return new Intl.NumberFormat("vi-VN").format(Math.round(value)) + " ₫";
    case "JPY":
    case "KRW":
      // Một số tiền tệ không dùng số thập phân
      return new Intl.NumberFormat("en-US").format(Math.round(value)) + " " + currency;
    case "USD":
      return new Intl.NumberFormat("en-US", { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      }).format(value) + " $";
    case "EUR":
      return new Intl.NumberFormat("de-DE", { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      }).format(value) + " €";
    case "GBP":
      return new Intl.NumberFormat("en-GB", { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      }).format(value) + " £";
    default:
      return new Intl.NumberFormat("en-US", { 
        minimumFractionDigits: 2, 
        maximumFractionDigits: 2 
      }).format(value) + " " + currency;
  }
};