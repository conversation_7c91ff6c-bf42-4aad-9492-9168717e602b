// File: app/utils/currency-api.tsx
// File này chứa các hàm gọi API để lấy tỷ giá hối đoái
// File này liên quan đến: app/(more)/finance-calculator.tsx

// Tỷ gi<PERSON> cố định (đ<PERSON><PERSON><PERSON> cập nhật vào 21/05/2025)
const FIXED_RATES = {
  USD: 1,
  EUR: 0.93,
  GBP: 0.79,
  JPY: 157.52,
  CNY: 7.24,
  SGD: 1.35,
  AUD: 1.52,
  CAD: 1.36,
  KRW: 1385.45,
  VND: 25050,
  THB: 36.28,
  MYR: 4.75,
  IDR: 16145,
  HKD: 7.81,
  INR: 83.28
};

// Danh sách các tiền tệ phổ biến
export const popularCurrencies = [
  { code: "USD", name: "US Dollar", symbol: "$" },
  { code: "EUR", name: "Euro", symbol: "€" },
  { code: "GBP", name: "British Pound", symbol: "£" },
  { code: "JPY", name: "Japanese Yen", symbol: "¥" },
  { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
  { code: "SGD", name: "Singapore Dollar", symbol: "S$" },
  { code: "VND", name: "Vietnamese Dong", symbol: "₫" },
  { code: "KRW", name: "Korean Won", symbol: "₩" },
];

// Danh sách các cặp tiền tệ phổ biến từ USD
export const popularCurrencyPairs = [
  { from: "USD", to: "EUR", name: "USD/EUR" },
  { from: "USD", to: "GBP", name: "USD/GBP" },
  { from: "USD", to: "JPY", name: "USD/JPY" },
  { from: "USD", to: "CNY", name: "USD/CNY" },
  { from: "USD", to: "SGD", name: "USD/SGD" },
  { from: "USD", to: "VND", name: "USD/VND" },
  { from: "EUR", to: "USD", name: "EUR/USD" },
  { from: "GBP", to: "USD", name: "GBP/USD" },
];

// Hàm lấy tỷ giá USD tới các tiền tệ khác
export const getRates = async (): Promise<Record<string, number>> => {
  try {
    // Trong môi trường thực tế, bạn sẽ gọi API
    // Ví dụ: const response = await fetch('https://open.er-api.com/v6/latest/USD');
    // const data = await response.json();
    // return data.rates;
    
    // Trả về tỷ giá cố định
    return FIXED_RATES;
  } catch (error) {
    console.error("Lỗi khi lấy tỷ giá:", error);
    // Nếu lỗi, trả về tỷ giá cố định
    return FIXED_RATES;
  }
};

// Hàm chuyển đổi giữa các tiền tệ
export const convertCurrency = async (
  amount: number,
  fromCurrency: string,
  toCurrency: string
): Promise<number> => {
  try {
    // Nếu cùng một loại tiền tệ, không cần chuyển đổi
    if (fromCurrency === toCurrency) {
      return amount;
    }
    
    // Lấy tỷ giá (Đây là tỷ giá so với USD)
    const rates = await getRates();
    
    // Tỷ giá từ fromCurrency sang USD
    const fromRate = rates[fromCurrency];
    
    // Tỷ giá từ USD sang toCurrency
    const toRate = rates[toCurrency];
    
    if (!fromRate || !toRate) {
      throw new Error(`Không thể tìm thấy tỷ giá cho ${fromCurrency} hoặc ${toCurrency}`);
    }
    
    // Chuyển đổi thông qua USD
    // 1. Đổi từ fromCurrency sang USD
    const amountInUSD = amount / fromRate;
    // 2. Đổi từ USD sang toCurrency
    return amountInUSD * toRate;
  } catch (error) {
    console.error("Lỗi chuyển đổi tiền tệ:", error);
    throw new Error(`Không thể chuyển đổi từ ${fromCurrency} sang ${toCurrency}`);
  }
};

// Hàm lấy tỷ giá của một cặp tiền tệ
export const getExchangeRate = async (fromCurrency: string, toCurrency: string): Promise<number> => {
  try {
    if (fromCurrency === toCurrency) {
      return 1;
    }
    
    const rates = await getRates();
    const fromRate = rates[fromCurrency];
    const toRate = rates[toCurrency];
    
    if (!fromRate || !toRate) {
      throw new Error(`Không thể tìm thấy tỷ giá cho ${fromCurrency} hoặc ${toCurrency}`);
    }
    
    // Tỷ giá: 1 đơn vị fromCurrency = ? đơn vị toCurrency
    return toRate / fromRate;
  } catch (error) {
    console.error("Lỗi lấy tỷ giá:", error);
    throw error;
  }
};