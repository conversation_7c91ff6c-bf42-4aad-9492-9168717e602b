// File: app/stats.tsx
// <PERSON><PERSON><PERSON> hình thống kê được di chuyển ra khỏi (tabs) để có thể truy cập từ More
// File này liên quan đến: context/ThemeContext.tsx, context/LocalizationContext.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { CategoryModel } from "@/lib/models/category";
import { TransactionModel } from "@/lib/models/transaction";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect, router } from "expo-router";
import { useCallback, useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Circle, G, Path, Svg } from "react-native-svg";

// Tạo mảng các tháng
const MONTHS = [
  "T1",
  "T2",
  "T3",
  "T4",
  "T5",
  "T6",
  "T7",
  "T8",
  "T9",
  "T10",
  "T11",
  "T12",
];

// Interface cho dữ liệu thống kê
interface CategoryStat {
  category: string;
  amount: number;
  color: string;
  icon: string;
  id: string;
}

const screenWidth = Dimensions.get("window").width;

export default function StatsScreen() {
  const [activeTab, setActiveTab] = useState("expense"); // 'expense' hoặc 'income'
  const [timeRange, setTimeRange] = useState("month"); // 'week', 'month', 'year'
  const [isLoading, setIsLoading] = useState(true);
  const [categoryStats, setCategoryStats] = useState<CategoryStat[]>([]);
  const [monthlyStats, setMonthlyStats] = useState({
    income: 0,
    expense: 0,
  });
  const [monthlyTrends, setMonthlyTrends] = useState<
    { month: number; income: number; expense: number }[]
  >([]);

  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle, getIconContainerStyle } = useTheme();
  const { t } = useLocalization();
  const translateCategoryName = useCategoryTranslation();

  // Fetch data using useFocusEffect
  useFocusEffect(
    useCallback(() => {
      const fetchData = async () => {
        try {
          setIsLoading(true);

          // Get current date
          const now = new Date();
          const currentMonth = now.getMonth() + 1;
          const currentYear = now.getFullYear();

          // Fetch monthly statistics
          const stats = await TransactionModel.getMonthlyStats(
            currentMonth,
            currentYear
          );
          setMonthlyStats(stats);

          // Load categories
          const categories = await CategoryModel.getAll();

          // Fetch transactions
          let transactions;
          if (timeRange === "month") {
            // Use current month
            transactions = await TransactionModel.getAll();
          } else if (timeRange === "week") {
            // Get start of current week (Sunday)
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay());
            startOfWeek.setHours(0, 0, 0, 0);
            transactions = await TransactionModel.getAll();
            transactions = transactions.filter(
              (t) => new Date(t.date) >= startOfWeek
            );
          } else {
            // Year - get all transactions for the year
            const startOfYear = new Date(currentYear, 0, 1);
            transactions = await TransactionModel.getAll();
            transactions = transactions.filter(
              (t) => new Date(t.date) >= startOfYear
            );
          }

          // Prepare monthly trend data
          const trends: { month: number; income: number; expense: number }[] =
            [];
          for (let m = 1; m <= 12; m++) {
            trends.push({
              month: m,
              income: 0,
              expense: 0,
            });
          }

          // Group transactions by category and count amounts
          const categoryAmounts: Record<string, number> = {};

          // Process transactions
          transactions.forEach((transaction) => {
            const transactionDate = new Date(transaction.date);
            const month = transactionDate.getMonth() + 1;

            // Add to monthly trends
            if (transaction.amount > 0) {
              trends[month - 1].income += transaction.amount;
            } else {
              trends[month - 1].expense += Math.abs(transaction.amount);
            }

            // Only process transactions matching the active tab
            if (
              (activeTab === "expense" && transaction.amount < 0) ||
              (activeTab === "income" && transaction.amount > 0)
            ) {
              const categoryId = transaction.category_id;
              if (!categoryAmounts[categoryId]) {
                categoryAmounts[categoryId] = 0;
              }
              categoryAmounts[categoryId] += Math.abs(transaction.amount);
            }
          });

          // Convert to array of category stats
          const categoryStatsArray: CategoryStat[] = [];
          for (const [categoryId, amount] of Object.entries(categoryAmounts)) {
            const category = categories.find((c) => c.id === categoryId);
            if (category) {
              categoryStatsArray.push({
                id: categoryId,
                category: category.name,
                amount,
                color: category.color,
                icon: category.icon,
              });
            }
          }

          // Sort by amount (highest first)
          categoryStatsArray.sort((a, b) => b.amount - a.amount);

          setCategoryStats(categoryStatsArray);
          setMonthlyTrends(trends);
        } catch (error) {
          console.error("Error fetching stats data:", error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchData();

      return () => {};
    }, [activeTab, timeRange])
  );

  // Calculate total amount
  const totalAmount = categoryStats.reduce((sum, item) => sum + item.amount, 0);

  // Helper function to get maximum value for trends chart scaling
  const getMaxTrendValue = () => {
    const values = monthlyTrends.map((m) =>
      activeTab === "expense" ? m.expense : m.income
    );
    return Math.max(...values, 1);
  };

  // Tạo dữ liệu cho biểu đồ tròn sử dụng SVG
  const renderPieChart = () => {
    if (categoryStats.length === 0) return null;
    const size = screenWidth * 0.35;
    const radius = size / 2;
    const center = size / 2;
    const strokeWidth = 25; // Reduced from 28 for better visibility
    let startAngle = 0;

    // Hàm chuyển đổi góc sang tọa độ trên hình tròn
    const polarToCartesian = (
      centerX: number,
      centerY: number,
      radius: number,
      angleInDegrees: number
    ) => {
      const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
      return {
        x: centerX + radius * Math.cos(angleInRadians),
        y: centerY + radius * Math.sin(angleInRadians),
      };
    };

    // Hàm tạo path cho từng segment
    const describeArc = (
      x: number,
      y: number,
      radius: number,
      startAngle: number,
      endAngle: number
    ) => {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
      const d = [
        `M ${start.x} ${start.y}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 0 ${end.x} ${end.y}`,
        `L ${x} ${y}`,
        "Z",
      ].join(" ");
      return d;
    };

    return (
      <View style={[styles.pieChartWrapper, { backgroundColor: 'transparent' }]}>
        <Svg width={size} height={size}>
          <G>
            {categoryStats.length === 1 ? (
              // Special case for single category - draw a full circle
              <Circle
                cx={center}
                cy={center}
                r={radius - strokeWidth / 2}
                fill="none"
                stroke={categoryStats[0].color}
                strokeWidth={strokeWidth}
              />
            ) : (
              // Normal case with multiple categories
              categoryStats.map((item, index) => {
                const percentage = item.amount / totalAmount;
                const angle = percentage * 360;
                const endAngle = startAngle + angle;
                const path = describeArc(
                  center,
                  center,
                  radius,
                  startAngle,
                  endAngle
                );
                const segment = (
                  <Path
                    key={index}
                    d={path}
                    fill={item.color}
                    stroke={item.color}
                    strokeWidth={1}
                  />
                );
                startAngle = endAngle;
                return segment;
              })
            )}
            {/* Vẽ lỗ tròn ở giữa để tạo hiệu ứng donut */}
            <Circle
              cx={center}
              cy={center}
              r={radius - strokeWidth - 2}
              fill={themeColors.card}
            />
          </G>
        </Svg>
      </View>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: 'transparent' }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("stats.title")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        
        <View
          style={[styles.loadingContainer, { backgroundColor: 'transparent' }]}
        >
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 16, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Get current month and year
  const now = new Date();
  const currentMonth = now.getMonth() + 1;
  const currentYear = now.getFullYear();

  // Format time range text
  const getTimeRangeText = () => {
    if (timeRange === "month") {
      return `Tháng ${currentMonth}/${currentYear}`;
    } else if (timeRange === "week") {
      // Get start of week
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      return `Tuần ${startOfWeek.getDate()}/${
        startOfWeek.getMonth() + 1
      } - ${now.getDate()}/${now.getMonth() + 1}`;
    } else {
      return `Năm ${currentYear}`;
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
      edges={["top", "left", "right"]}
    >
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("stats.title")}
        </Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView
        style={{ flex: 1, backgroundColor: 'transparent' }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }} // Tăng giá trị paddingBottom để không bị che khuất
      >
        {/* Tabs */}
        <View 
          style={[
            styles.tabContainer, 
            getCardStyle('medium'),
            { 
              marginHorizontal: 16,
              marginBottom: 16,
              padding: 4,
            }
          ]}
        >
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === "expense" && [
                styles.activeTab,
                { 
                  backgroundColor: activeTab === "expense" ? themeColors.primary : "transparent",
                  borderRadius: 12,
                }
              ],
            ]}
            onPress={() => setActiveTab("expense")}
          >
            <Text
              style={[
                styles.tabText,
                { color: themeColors.secondaryText },
                activeTab === "expense" && [
                  styles.activeTabText,
                  { color: "white" },
                ],
              ]}
            >
              {t("stats.expense")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === "income" && [
                styles.activeTab,
                { 
                  backgroundColor: activeTab === "income" ? themeColors.primary : "transparent",
                  borderRadius: 12,
                }
              ],
            ]}
            onPress={() => setActiveTab("income")}
          >
            <Text
              style={[
                styles.tabText,
                { color: themeColors.secondaryText },
                activeTab === "income" && [
                  styles.activeTabText,
                  { color: "white" },
                ],
              ]}
            >
              {t("stats.income")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Time Range Selector */}
        <View
          style={[
            styles.timeRangeContainer,
            { backgroundColor: "transparent" },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.timeRangeButton,
              getCardStyle('light'),
              {
                backgroundColor: timeRange === "week" ? themeColors.primary : themeColors.card,
                shadowOpacity: timeRange === "week" ? 0.3 : 0.1,
                elevation: timeRange === "week" ? 3 : 1,
              },
            ]}
            onPress={() => setTimeRange("week")}
          >
            <Text
              style={[
                styles.timeRangeText,
                { 
                  color: timeRange === "week" ? "white" : themeColors.secondaryText,
                  fontWeight: timeRange === "week" ? "600" : "500",
                },
              ]}
            >
              {t("stats.week")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.timeRangeButton,
              getCardStyle('light'),
              {
                backgroundColor: timeRange === "month" ? themeColors.primary : themeColors.card,
                shadowOpacity: timeRange === "month" ? 0.3 : 0.1,
                elevation: timeRange === "month" ? 3 : 1,
              },
            ]}
            onPress={() => setTimeRange("month")}
          >
            <Text
              style={[
                styles.timeRangeText,
                { 
                  color: timeRange === "month" ? "white" : themeColors.secondaryText, 
                  fontWeight: timeRange === "month" ? "600" : "500",
                },
              ]}
            >
              {t("stats.month")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.timeRangeButton,
              getCardStyle('light'),
              {
                backgroundColor: timeRange === "year" ? themeColors.primary : themeColors.card,
                shadowOpacity: timeRange === "year" ? 0.3 : 0.1,
                elevation: timeRange === "year" ? 3 : 1,
              },
            ]}
            onPress={() => setTimeRange("year")}
          >
            <Text
              style={[
                styles.timeRangeText,
                { 
                  color: timeRange === "year" ? "white" : themeColors.secondaryText, 
                  fontWeight: timeRange === "year" ? "600" : "500",
                },
              ]}
            >
              {t("stats.year")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Total Amount Card */}
        <View
          style={[
            styles.totalContainer,
            getCardStyle('medium'),
            { marginHorizontal: 16, marginBottom: 20 },
          ]}
        >
          <Text
            style={[styles.totalLabel, { color: themeColors.secondaryText }]}
          >
            {activeTab === "expense"
              ? t("stats.totalExpense")
              : t("stats.totalIncome")}
          </Text>
          <Text style={[styles.totalAmount, { color: themeColors.text }]}>
            {activeTab === "expense"
              ? monthlyStats.expense.toLocaleString()
              : monthlyStats.income.toLocaleString()}
            đ
          </Text>
          <Text
            style={[styles.totalPeriod, { color: themeColors.secondaryText }]}
          >
            {getTimeRangeText()}
          </Text>
        </View>

        {/* Chart Section */}
        <View style={[styles.chartSection, getCardStyle('medium'), { marginHorizontal: 16 }]}>
          <View
            style={[styles.chartHeader, { backgroundColor: "transparent" }]}
          >
            <Text style={[styles.chartTitle, { color: themeColors.text }]}>
              {activeTab === "expense"
                ? t("stats.expenseByCategory")
                : t("stats.incomeBySource")}
            </Text>
          </View>

          {categoryStats.length === 0 ? (
            <View
              style={[
                styles.emptyContainer,
                { backgroundColor: "transparent" },
              ]}
            >
              <Text style={{ color: themeColors.secondaryText, textAlign: "center", fontSize: 16 }}>
                {activeTab === "expense"
                  ? t("stats.noExpenseData")
                  : t("stats.noIncomeData")}
              </Text>
            </View>
          ) : (
            <View
              style={[styles.chartContent, { backgroundColor: "transparent" }]}
            >
              {/* Pie Chart Visualization */}
              {renderPieChart()}

              {/* Legend */}
              <View
                style={[
                  styles.legendContainer,
                  { backgroundColor: "transparent" },
                ]}
              >
                {categoryStats.map((item, index) => (
                  <View
                    key={index}
                    style={[
                      styles.legendItem,
                      { backgroundColor: "transparent" },
                    ]}
                  >
                    <View
                      style={[
                        styles.legendColorBox,
                        { 
                          backgroundColor: item.color,
                          shadowColor: item.color,
                          shadowOffset: { width: 0, height: 1 },
                          shadowOpacity: 0.2,
                          shadowRadius: 1,
                          elevation: 1,
                        },
                      ]}
                    />
                    <Text style={[styles.legendText, { color: themeColors.text }]}>
                      {translateCategoryName(item.category)} (
                      {Math.round((item.amount / totalAmount) * 100)}%)
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        </View>

        {/* Categories Breakdown */}
        {categoryStats.length > 0 && (
          <View
            style={[
              styles.categoriesContainer,
              { backgroundColor: "transparent" },
            ]}
          >
            <Text style={[styles.sectionTitle, { color: themeColors.text, paddingHorizontal: 16 }]}>
              {t("stats.categoryDetails")}
            </Text>

            <View style={[getCardStyle('medium'), styles.categoriesCard, { marginHorizontal: 16 }]}>
              {categoryStats.map((item, index) => {
                // Tính phần trăm
                const percentage = Math.round(
                  (item.amount / totalAmount) * 100
                );

                return (
                  <View
                    key={index}
                    style={[
                      styles.categoryItem,
                      {
                        backgroundColor: "transparent",
                        borderBottomWidth:
                          index === categoryStats.length - 1 ? 0 : 1,
                        borderBottomColor: themeColors.border,
                      },
                    ]}
                  >
                    <View
                      style={[
                        styles.categoryHeader,
                        { backgroundColor: "transparent" },
                      ]}
                    >
                      <View
                        style={[
                          styles.categoryLeft,
                          { backgroundColor: "transparent" },
                        ]}
                      >
                        <View
                          style={[
                            styles.categoryIcon,
                            getIconContainerStyle(),
                            {
                              backgroundColor: item.color,
                              shadowColor: item.color,
                            },
                          ]}
                        >
                          <Ionicons
                            name={item.icon as any}
                            size={18}
                            color="white"
                          />
                        </View>
                        <Text
                          style={[styles.categoryName, { color: themeColors.text }]}
                        >
                          {translateCategoryName(item.category)}
                        </Text>
                      </View>
                      <Text
                        style={[
                          styles.categoryPercentage,
                          { color: themeColors.secondaryText, fontWeight: "600" },
                        ]}
                      >
                        {percentage}%
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.progressBarContainer,
                        { backgroundColor: isDark ? "#213A61" : "#F5F9FF" },
                      ]}
                    >
                      <View
                        style={[
                          styles.progressBar,
                          {
                            width: `${percentage}%`,
                            backgroundColor: item.color,
                          },
                        ]}
                      />
                    </View>
                    <View
                      style={[
                        styles.amountRow,
                        { backgroundColor: "transparent" },
                      ]}
                    >
                      <Text
                        style={[
                          styles.categoryAmountLabel,
                          { color: themeColors.secondaryText },
                        ]}
                      >
                        {t("stats.amount")}
                      </Text>
                      <Text
                        style={[styles.categoryAmount, { color: themeColors.text, fontWeight: "600" }]}
                      >
                        {item.amount.toLocaleString()}đ
                      </Text>
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    padding: 30,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  tabContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    margin: 4,
  },
  activeTab: {
    borderRadius: 16,
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
  },
  activeTabText: {
    fontWeight: "600",
  },
  timeRangeContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    marginBottom: 16,
    justifyContent: "space-between",
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 16,
    alignItems: "center",
    marginHorizontal: 4,
  },
  timeRangeText: {
    fontSize: 15,
  },
  totalContainer: {
    padding: 20,
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: "bold",
    marginVertical: 8,
    paddingTop: 12,
  },
  totalPeriod: {
    fontSize: 14,
    marginTop: 4,
  },
  chartSection: {
    padding: 20,
    marginBottom: 20,
  },
  chartHeader: {
    marginBottom: 20,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  chartContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  pieChartWrapper: {
    width: screenWidth * 0.35,
    height: screenWidth * 0.35,
    position: "relative",
  },
  pieChart: {
    width: "100%",
    height: "100%",
    borderRadius: 1000,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
  },
  pieChartCenter: {
    position: "absolute",
    width: "60%",
    height: "60%",
    borderRadius: 1000,
    zIndex: 10,
  },
  legendContainer: {
    flex: 1,
    paddingLeft: 16,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  legendColorBox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 10,
  },
  legendText: {
    fontSize: 14,
    fontWeight: "500",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
  },
  categoriesContainer: {
    marginTop: 8,
    marginBottom: 16,
    paddingTop: 16,
  },
  categoriesCard: {
    overflow: "hidden",
  },
  categoryItem: {
    padding: 16,
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  categoryLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  categoryIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: "600",
  },
  categoryPercentage: {
    fontSize: 16,
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    borderRadius: 4,
  },
  amountRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
  },
  categoryAmountLabel: {
    fontSize: 14,
  },
  categoryAmount: {
    fontSize: 16,
  },
  trendContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
    marginBottom: 24,
  },
  trendTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 24,
  },
  trendChart: {
    flexDirection: "row",
    height: 200,
    justifyContent: "space-between",
    alignItems: "flex-end",
  },
  trendBarContainer: {
    flex: 1,
    height: "100%",
    alignItems: "center",
    justifyContent: "flex-end",
  },
  trendBar: {
    width: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  trendBarLabel: {
    fontSize: 12,
  },
  trendBarLabelActive: {
    fontWeight: "bold",
  },
});