// File: app/(tabs)/chatbot.tsx
// Những file liên quan đến file này: context/ThemeContext.tsx, lib/services/ai-service.ts, 
// lib/services/chatbot-actions.ts, lib/helpers/chat-helpers.ts, lib/types/chatbot-types.ts

import { Text, View } from "@/components/Themed";
import { Colors } from "@/constants/Colors";
import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  Alert,
  AppState,
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  Platform,
  Animated as RNAnimated,
  View as RNView,
  StyleSheet,
  TextInput,
  TouchableOpacity
} from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";

// Import các services và helpers
import { 
  initializeOpenAI, 
  isApiKeyConfigured, 
  processMessageWithAI 
} from '@/lib/services/ai-service';
import {
  handleTransferMoney,
  handleCreateTransaction,
  handleCreateWallet,
  handleUpdateWallet,
  handleDeleteTransaction,
  handleViewWallets,
  handleViewTransactions,
  handleViewMonthlyStats,
  handleViewCategories,
  getBotPronoun
} from '@/lib/services/chatbot-actions';
import {
  USER_PRONOUNS,
  findWalletByName,
  formatCurrency,
  getFullDateTime
} from '@/lib/helpers/chat-helpers';
import { 
  Message, 
  ChatButton,
  AIAnalysisResult 
} from '@/lib/types/chatbot-types';
import TransactionTemplateModel from "@/lib/models/transaction_template";

// Constants cho giới hạn
const DAILY_MESSAGE_LIMIT = 20;
const MESSAGE_CHARACTER_LIMIT = 200;

// Helper functions
const formatTime = (date: Date) => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const getTodayKey = () => {
  return new Date().toDateString();
};

const { width: SCREEN_WIDTH } = Dimensions.get("window");

export default function ChatbotScreen() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState("");
  const [showHelp, setShowHelp] = useState(false);
  const [results, setResults] = useState<string[]>([]);
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef<TextInput>(null);
  
  // Sử dụng theme mới từ ThemeContext
  const {
    isDark,
    themeColors,
    getCardStyle,
    getIconContainerStyle,
    getShadowStyle,
  } = useTheme();
  const [forceUpdate, setForceUpdate] = useState(0);
  const systemTheme = Colors[isDark ? "dark" : "light"];
  const [pulseAnim] = useState(new RNAnimated.Value(1));

  // Thêm state để kiểm soát trạng thái focus của ô nhập
  const [isInputFocused, setIsInputFocused] = useState(false);
  
  // State cho modal mẫu tin nhắn
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // Sử dụng useSharedValue từ Reanimated để animation mượt hơn
  const inputFocusAnim = useSharedValue(0);

  // Thêm state cho cách xưng hô
  const [userPronoun, setUserPronoun] = useState<string>("Bạn");
  const [botPronoun, setBotPronoun] = useState<string>("tôi");

  // Thêm state để theo dõi yêu cầu chuyển tiền đang xử lý
  const [pendingTransfer, setPendingTransfer] = useState<any>(null);

  // Thêm state để theo dõi trạng thái ứng dụng
  const appState = useRef<string>(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState<string>(
    appState.current
  );

  // Tạo animated style cho input wrapper khi focus
  const animatedInputWrapperStyle = useAnimatedStyle(() => {
    return {
      borderColor:
        inputFocusAnim.value === 1 ? themeColors.primary : themeColors.border,
    };
  });

  // Xử lý khi focus/blur vào ô nhập
  const handleInputFocus = useCallback(() => {
    setIsInputFocused(true);
    inputFocusAnim.value = withTiming(1, { duration: 200 });
  }, [inputFocusAnim]);

  const handleInputBlur = useCallback(() => {
    setIsInputFocused(false);
    inputFocusAnim.value = withTiming(0, { duration: 200 });
  }, [inputFocusAnim]);

  // Kiểm tra xem có thể gửi tin nhắn không (chỉ kiểm tra độ dài)
  const canSendMessage = () => {
    if (inputText.trim().length > MESSAGE_CHARACTER_LIMIT) {
      Alert.alert(
        "Tin nhắn quá dài", 
        `Tin nhắn không được vượt quá ${MESSAGE_CHARACTER_LIMIT} ký tự. Hiện tại: ${inputText.trim().length} ký tự.`
      );
      return false;
    }
    
    return true;
  };

  // Xử lý thay đổi text input
  const handleTextChange = (text: string) => {
    setInputText(text);
  };

  // Load cài đặt xưng hô từ AsyncStorage
  useEffect(() => {
    const loadUserPronoun = async () => {
      try {
        const savedUserPronoun = await AsyncStorage.getItem("user_pronoun");
        if (savedUserPronoun) {
          setUserPronoun(savedUserPronoun);

          // Tìm cách AI sẽ xưng tương ứng
          const pronounPair = USER_PRONOUNS.find(
            (p) => p.calling === savedUserPronoun
          );
          if (pronounPair) {
            setBotPronoun(pronounPair.selfCalling);
          }
        }
      } catch (error) {
        console.error("Lỗi khi đọc cài đặt xưng hô:", error);
      }
    };

    loadUserPronoun();
  }, []);

  // Cập nhật tin nhắn mặc định khi userPronoun hoặc botPronoun thay đổi
  useEffect(() => {
    // Cập nhật lại tin nhắn chào mừng với cách xưng hô đúng
    const INITIAL_MESSAGES: Message[] = [
      {
        id: "1",
        text: `Xin chào! ${botPronoun} là trợ lý tài chính thông minh của AI Money. ${botPronoun} có thể giúp gì cho ${userPronoun}?`,
        isUser: false,
        timestamp: new Date(),
      },
      {
        id: "2",
        text: `Thử một vài câu như:\n- "Ăn sáng 150k"\n- "Xem tất cả ví"\n- "Xem thu chi tháng này"\n- "5tr vpbank sang tech"\n\nNhấn nút ℹ️ để xem thêm.\n\n⚠️ Giới hạn:\n ${DAILY_MESSAGE_LIMIT} tin nhắn/ngày\n Tối đa ${MESSAGE_CHARACTER_LIMIT} ký tự/tin nhắn.`,
        isUser: false,
        timestamp: new Date(),
      },
    ];

    // Chỉ cập nhật tin nhắn nếu chưa có tin nhắn nào (để tránh reset tin nhắn)
    if (messages.length === 0) {
      setMessages(INITIAL_MESSAGES);
    }
  }, [userPronoun, botPronoun, messages.length]);

  // Force re-render when theme changes
  useEffect(() => {
    setForceUpdate((prev) => prev + 1);
  }, [isDark]);

  // Load templates khi mở modal
  const loadTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const loadedTemplates = await TransactionTemplateModel.getAll();
      setTemplates(loadedTemplates);
    } catch (error) {
      console.error("Lỗi khi tải mẫu tin nhắn:", error);
      Alert.alert("Lỗi", "Không thể tải danh sách mẫu tin nhắn");
    } finally {
      setLoadingTemplates(false);
    }
  };

  // Mở modal chọn mẫu tin nhắn
  const openTemplateModal = async () => {
    await loadTemplates();
    setShowTemplateModal(true);
  };

  // Chọn mẫu tin nhắn
  const selectTemplate = (template: any) => {
    const content = template.content.substring(0, MESSAGE_CHARACTER_LIMIT);
    setInputText(content);
    setShowTemplateModal(false);
  };

  // Function to refresh UI when app comes back to foreground
  const refreshUIOnForeground = useCallback(() => {
    console.log("Refreshing UI after app comes to foreground");
    // Force re-render
    setForceUpdate((prev) => prev + 1);

    // Reset input focus state
    handleInputBlur();

    // Scroll to bottom of messages with a slight delay to ensure rendering is complete
    if (flatListRef.current) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: false });
      }, 100);
    }
  }, [flatListRef, handleInputBlur]);

  // Handle app state changes (background/foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        console.log("App has come to the foreground!");
        refreshUIOnForeground();
      }

      appState.current = nextAppState;
      setAppStateVisible(nextAppState);
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription.remove();
    };
  }, [refreshUIOnForeground]);

  // Tạo component để hiển thị tin nhắn với định dạng
  const FormattedMessage = ({ message }: { message: string }) => {
    if (!message.includes("{{") && !message.includes("}}")) {
      // Không có định dạng đặc biệt, trả về text thông thường
      return <Text style={{ color: themeColors.text }}>{message}</Text>;
    }

    // Phân tích cú pháp định dạng {{color:red|text}} và {{bold|text}}
    const parts = [];
    let lastIndex = 0;
    let currentIndex = 0;
    const regex = /\{\{(.*?)\|(.*?)\}\}/g;
    let match;

    while ((match = regex.exec(message)) !== null) {
      // Thêm văn bản trước phần định dạng
      if (match.index > lastIndex) {
        parts.push({
          type: "regular",
          content: message.substring(lastIndex, match.index),
        });
      }

      // Phân tích kiểu định dạng
      const format = match[1]; // color:red hoặc bold
      const content = match[2]; // nội dung cần định dạng

      if (format.startsWith("color:")) {
        const color = format.split(":")[1];
        parts.push({
          type: "colored",
          content,
          color,
        });
      } else if (format === "bold") {
        parts.push({
          type: "bold",
          content,
        });
      }

      lastIndex = match.index + match[0].length;
    }

    // Thêm phần còn lại của văn bản
    if (lastIndex < message.length) {
      parts.push({
        type: "regular",
        content: message.substring(lastIndex),
      });
    }

    // Tạo element với các thành phần định dạng
    return (
      <Text style={{ color: themeColors.text }}>
        {parts.map((part, index) => {
          if (part.type === "regular") {
            return <Text key={index}>{part.content}</Text>;
          } else if (part.type === "colored") {
            return (
              <Text key={index} style={{ color: part.color }}>
                {part.content}
              </Text>
            );
          } else if (part.type === "bold") {
            return (
              <Text key={index} style={{ fontWeight: "bold" }}>
                {part.content}
              </Text>
            );
          }
          return null;
        })}
      </Text>
    );
  };

  // Xử lý khi người dùng nhấn nút xác nhận
  const handleConfirmAction = async (data: any) => {
    if (!data || !data.action) return;

    // Thêm tin nhắn của người dùng
    const userMessage: Message = {
      id: Date.now().toString(),
      text: "Đồng ý",
      isUser: true,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);

    // Add a loading message
    const loadingMessageId = (Date.now() + 1).toString();
    const loadingMessage: Message = {
      id: loadingMessageId,
      text: "Đang xử lý...",
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages((prev) => [...prev, loadingMessage]);

    let botResponse = "";

    try {
      if (data.action === "create_wallet") {
        // Create new wallet
        botResponse = await handleCreateWallet(data.data, botPronoun);
      } else if (data.action === "update_wallet") {
        // Update existing wallet
        botResponse = await handleUpdateWallet(data.data.wallet_id, 
          { balance: data.data.balance }, 
          botPronoun);
      } else if (data.action === "transfer") {
        // Handle transfer money
        botResponse = await handleTransferMoney(data.data, botPronoun);
        // Xóa yêu cầu chuyển tiền đang chờ
        setPendingTransfer(null);
      } else {
        botResponse = `Không có hành động nào cần xác nhận.`;
      }

      // Update message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessageId
            ? {
                id: loadingMessageId,
                text: botResponse,
                isUser: false,
                timestamp: new Date(),
                isLoading: false,
              }
            : msg
        )
      );
    } catch (error) {
      console.error("Error in handleConfirmAction:", error);

      // Replace loading message with error message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessageId
            ? {
                id: loadingMessageId,
                text: "Đã xảy ra lỗi. Vui lòng thử lại.",
                isUser: false,
                timestamp: new Date(),
                isLoading: false,
              }
            : msg
        )
      );
    }
  };

  // Thêm hàm hủy bỏ
  const handleCancelAction = () => {
    const cancelMessage: Message = {
      id: Date.now().toString(),
      text: "Hủy",
      isUser: true,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, cancelMessage]);

    // Thêm phản hồi từ bot
    const botResponse: Message = {
      id: (Date.now() + 1).toString(),
      text: `${botPronoun} đã hủy thao tác. ${userPronoun} cần giúp gì khác không?`,
      isUser: false,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, botResponse]);

    // Nếu có yêu cầu chuyển tiền đang chờ, xóa nó
    if (pendingTransfer) {
      setPendingTransfer(null);
    }
  };

  // Xử lý khi gửi tin nhắn
  const handleSend = async () => {
    if (inputText.trim() === "") return;

    // Kiểm tra giới hạn trước khi gửi
    if (!canSendMessage()) return;

    // Thêm tin nhắn của người dùng
    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputText("");

    // Add a loading message
    const loadingMessageId = (Date.now() + 1).toString();
    const loadingMessage: Message = {
      id: loadingMessageId,
      text: "Đang xử lý...",
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages((prev) => [...prev, loadingMessage]);

    try {
      // Kiểm tra xem người dùng đang xác nhận một hành động nào đó không
      if (
        pendingTransfer &&
        (userMessage.text.toLowerCase().includes("đồng ý") ||
          userMessage.text.toLowerCase().includes("xác nhận") ||
          userMessage.text.toLowerCase().includes("chuyển") ||
          userMessage.text.toLowerCase().includes("ok"))
      ) {
        // Xử lý xác nhận chuyển tiền
        const result = await handleTransferMoney(pendingTransfer, botPronoun);

        // Cập nhật tin nhắn
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === loadingMessageId
              ? {
                  id: loadingMessageId,
                  text: result,
                  isUser: false,
                  timestamp: new Date(),
                  isLoading: false,
                }
              : msg
          )
        );

        // Xóa yêu cầu chuyển tiền đang chờ
        setPendingTransfer(null);
        return;
      }
      // Kiểm tra hủy bỏ hành động đang chờ
      else if (
        pendingTransfer &&
        (userMessage.text.toLowerCase().includes("hủy") ||
          userMessage.text.toLowerCase().includes("không") ||
          userMessage.text.toLowerCase().includes("thôi"))
      ) {
        // Xóa yêu cầu chuyển tiền đang chờ
        setPendingTransfer(null);

        // Cập nhật tin nhắn
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === loadingMessageId
              ? {
                  id: loadingMessageId,
                  text: `${botPronoun} đã hủy yêu cầu chuyển tiền.`,
                  isUser: false,
                  timestamp: new Date(),
                  isLoading: false,
                }
              : msg
          )
        );
        return;
      }

      // Xử lý tin nhắn bằng OpenAI - truyền thêm thông tin xưng hô
      const aiResult: AIAnalysisResult = await processMessageWithAI(
        userMessage.text,
        userPronoun,
        botPronoun
      );
      console.log("AI Result:", aiResult);

      let botResponse = "";
      let responseData = null;
      let transactionId = null; // Thêm biến để lưu trữ ID của giao dịch nếu có

      // Kiểm tra lỗi API key
      if (aiResult && aiResult.action === "api_key_missing") {
        botResponse = `Chức năng AI chatbot chưa được kích hoạt. Vui lòng cấu hình OpenAI API key trong mã nguồn.`;

        // Replace the loading message with the error response
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === loadingMessageId
              ? {
                  id: loadingMessageId,
                  text: botResponse,
                  isUser: false,
                  timestamp: new Date(),
                  isLoading: false,
                }
              : msg
          )
        );
        return;
      }

      // Nếu OpenAI trả về kết quả hợp lệ
      if (aiResult && aiResult.action) {
        // Xử lý theo loại hành động
        switch (aiResult.action) {
          case "transfer":
            // Xử lý chuyển tiền
            const transferData = aiResult.data;

            // Nếu yêu cầu chuyển tiền đầy đủ, xử lý ngay
            if (transferData.isComplete) {
              if (transferData.sourceWalletId === transferData.destWalletId) {
                botResponse = `Không thể chuyển tiền đến cùng một ví. Vui lòng chọn hai ví khác nhau.`;
              } else {
                // Hỏi xác nhận trước khi thực hiện chuyển tiền
                botResponse = `${userPronoun} muốn chuyển ${formatCurrency(
                  transferData.amount
                )} từ ví "${transferData.sourceWalletName}" sang ví "${
                  transferData.destWalletName
                }"?`;

                // Lưu trữ yêu cầu chuyển tiền để xử lý sau khi xác nhận
                setPendingTransfer(transferData);

                // Thêm nút xác nhận và hủy bỏ
                const updatedMsg: Message = {
                  id: loadingMessageId,
                  text: botResponse,
                  isUser: false,
                  timestamp: new Date(),
                  isLoading: false,
                  data: { action: "transfer", data: transferData },
                  buttons: [
                    {
                      text: "Đồng ý",
                      onPress: async () => {
                        // Thêm tin nhắn xác nhận
                        const confirmMessage: Message = {
                          id: Date.now().toString(),
                          text: "Đồng ý",
                          isUser: true,
                          timestamp: new Date(),
                        };
                        setMessages((prev) => [...prev, confirmMessage]);

                        // Thêm tin nhắn đang xử lý
                        const processingMsgId = (Date.now() + 1).toString();
                        const processingMsg: Message = {
                          id: processingMsgId,
                          text: "Đang xử lý chuyển tiền...",
                          isUser: false,
                          timestamp: new Date(),
                          isLoading: true,
                        };
                        setMessages((prev) => [...prev, processingMsg]);

                        // Thực hiện chuyển tiền
                        const result = await handleTransferMoney(transferData, botPronoun);

                        // Cập nhật tin nhắn
                        setMessages((prev) =>
                          prev.map((msg) =>
                            msg.id === processingMsgId
                              ? {
                                  id: processingMsgId,
                                  text: result,
                                  isUser: false,
                                  timestamp: new Date(),
                                  isLoading: false,
                                }
                              : msg
                          )
                        );

                        // Xóa yêu cầu chuyển tiền đang chờ
                        setPendingTransfer(null);
                      },
                      type: "confirm",
                    },
                    {
                      text: "Hủy",
                      onPress: () => {
                        // Thêm tin nhắn hủy
                        const cancelMessage: Message = {
                          id: Date.now().toString(),
                          text: "Hủy",
                          isUser: true,
                          timestamp: new Date(),
                        };
                        setMessages((prev) => [...prev, cancelMessage]);

                        // Thêm phản hồi hủy
                        const cancelResponseMsg: Message = {
                          id: (Date.now() + 1).toString(),
                          text: `${botPronoun} đã hủy yêu cầu chuyển tiền.`,
                          isUser: false,
                          timestamp: new Date(),
                        };
                        setMessages((prev) => [...prev, cancelResponseMsg]);

                        // Xóa yêu cầu chuyển tiền đang chờ
                        setPendingTransfer(null);
                      },
                      type: "cancel",
                    },
                  ],
                };

                // Cập nhật tin nhắn
                setMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === loadingMessageId ? updatedMsg : msg
                  )
                );
                return;
              }
            } else {
              // Nếu chưa đủ thông tin, thông báo lỗi
              if (!transferData.sourceWalletId) {
                botResponse = `${botPronoun} không tìm thấy ví "${transferData.sourceWalletName}". Vui lòng kiểm tra lại tên ví hoặc tạo ví mới.`;
              } else if (!transferData.destWalletId) {
                botResponse = `${botPronoun} không tìm thấy ví "${transferData.destWalletName}". Vui lòng kiểm tra lại tên ví hoặc tạo ví mới.`;
              } else {
                botResponse = `${botPronoun} không thể thực hiện yêu cầu chuyển tiền. ${
                  transferData.message || "Vui lòng thử lại."
                }`;
              }
            }
            break;

          case "update_wallet":
            const updateWalletData = aiResult.data;
            // Tìm ví theo tên
            const walletToUpdate = await findWalletByName(
              updateWalletData.name
            );

            if (!walletToUpdate) {
              botResponse = `${botPronoun} không tìm thấy ví "${updateWalletData.name}". Vui lòng kiểm tra lại tên ví hoặc tạo ví mới.`;
              break;
            }

            // Lấy thông tin ví hiện tại
            const currentWallet = await WalletModel.getById(walletToUpdate);
            if (!currentWallet) {
              botResponse = `${botPronoun} không tìm thấy thông tin ví "${updateWalletData.name}". Vui lòng thử lại.`;
              break;
            }

            botResponse = `${userPronoun} muốn cập nhật ví "${
              currentWallet.name
            }" với số dư mới là ${formatCurrency(updateWalletData.balance)}?`;

            responseData = {
              action: "update_wallet",
              data: {
                wallet_id: currentWallet.id,
                name: currentWallet.name,
                balance: updateWalletData.balance,
                type: currentWallet.type,
                icon: currentWallet.icon,
                color: currentWallet.color,
              },
            };
            break;

          case "create_wallet":
            const walletData = aiResult.data;
            botResponse = `${userPronoun} muốn tạo ví "${
              walletData.name
            }" với số dư ${formatCurrency(walletData.balance)}?`;
            responseData = {
              action: "create_wallet",
              data: {
                name: walletData.name,
                balance: walletData.balance,
                type: walletData.type || "bank",
                icon: "wallet-outline",
                color: "#3498db",
              },
            };
            break;

          case "create_transaction":
            // Không hỏi xác nhận mà tạo giao dịch luôn
            const transactionData = aiResult.data;

            // Thêm botPronoun vào transactionData để sử dụng trong message
            const updatedTransactionData = {
              ...transactionData,
              botPronoun
            };

            // Tạo giao dịch mới trực tiếp
            const transactionResult = await handleCreateTransaction(updatedTransactionData);

            botResponse = transactionResult.message;
            transactionId = transactionResult.transactionId; // Lưu lại ID giao dịch nếu tạo thành công
            break;

          case "view_wallets":
            botResponse = await handleViewWallets(userPronoun);
            break;

          case "view_transactions":
            // Số lượng giao dịch cần hiển thị (mặc định: 5)
            const limit = aiResult.data?.limit || 5;
            botResponse = await handleViewTransactions(limit, userPronoun);
            break;

          case "view_statistics":
            // Get current month and year
            const now = new Date();
            const currentMonth = now.getMonth() + 1; // JS months are 0-indexed
            const currentYear = now.getFullYear();

            // Extract specific month if mentioned in AI result
            let targetMonth = aiResult.data?.month || currentMonth;
            let targetYear = aiResult.data?.year || currentYear;

            botResponse = await handleViewMonthlyStats(targetMonth, targetYear, userPronoun);
            break;

          case "view_categories":
            botResponse = await handleViewCategories(aiResult.data?.type || null, userPronoun);
            break;

          case "confirm":
            // Tìm xác nhận gần nhất
            if (messages.length > 0) {
              const lastBotMessage = messages.filter((m) => !m.isUser).pop();
              if (lastBotMessage?.data?.action === "create_wallet") {
                // Create new wallet
                botResponse = await handleCreateWallet(lastBotMessage.data.data, botPronoun);
              } else if (lastBotMessage?.data?.action === "update_wallet") {
                // Update existing wallet
                botResponse = await handleUpdateWallet(
                  lastBotMessage.data.data.wallet_id,
                  { balance: lastBotMessage.data.data.balance },
                  botPronoun
                );
              } else if (pendingTransfer) {
                // Xử lý xác nhận chuyển tiền
                botResponse = await handleTransferMoney(pendingTransfer, botPronoun);
                setPendingTransfer(null); // Xóa yêu cầu chuyển tiền đang chờ
              } else {
                botResponse = `Không có hành động nào cần xác nhận.`;
              }
            } else {
              botResponse = `Không có hành động nào cần xác nhận.`;
            }
            break;

          default:
            // Câu trả lời mặc định khi OpenAI không hiểu yêu cầu
            botResponse = `${botPronoun} chưa hiểu yêu cầu của ${userPronoun}. ${userPronoun} có thể thử các câu lệnh: \n- "Thêm giao dịch ăn trưa 150k"\n- "Xem ví của ${botPronoun}"\n- "Xem các giao dịch gần đây"\n- "5tr vpbank sang tech"`;
            break;
        }
      } else {
        // Nếu OpenAI không hiểu hoặc có lỗi, sử dụng xử lý hiện tại
        botResponse = `${botPronoun} không hiểu yêu cầu của ${userPronoun}. Vui lòng thử lại với câu đơn giản hơn.`;
      }

      // Chuẩn bị tin nhắn phản hồi
      let updatedMsg: Message = {
        id: loadingMessageId,
        text: botResponse,
        isUser: false,
        timestamp: new Date(),
        isLoading: false,
        data: responseData,
      };

      // Nếu là tin nhắn tạo giao dịch và có ID giao dịch, thêm nút xóa
      if (transactionId) {
        updatedMsg.transaction_id = transactionId;
        updatedMsg.showDeleteButton = true;
      }

      // Thêm nút xác nhận cho tạo ví mới và cập nhật ví, không hỏi xác nhận cho giao dịch
      if (
        responseData &&
        (responseData.action === "create_wallet" ||
          responseData.action === "update_wallet")
      ) {
        updatedMsg.buttons = [
          {
            text: "Đồng ý",
            onPress: () => handleConfirmAction(responseData),
            type: "confirm",
          },
          {
            text: "Hủy",
            onPress: () => handleCancelAction(),
            type: "cancel",
          },
        ];
      }

      // Replace the loading message with the response
      setMessages((prev) =>
        prev.map((msg) => (msg.id === loadingMessageId ? updatedMsg : msg))
      );
    } catch (error) {
      console.error("Error in handleSend:", error);

      // Replace loading message with error message
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === loadingMessageId
            ? {
                id: loadingMessageId,
                text: "Đã xảy ra lỗi. Vui lòng thử lại.",
                isUser: false,
                timestamp: new Date(),
                isLoading: false,
              }
            : msg
        )
      );
    }
  };

  // Thêm hàm xóa giao dịch
  const handleDeleteTransactionWithConfirm = async (transactionId: string) => {
    // Hiển thị hộp thoại xác nhận trước khi xóa
    Alert.alert(
      "Xác nhận xóa",
      "Anh có chắc chắn muốn xóa giao dịch này không?",
      [
        {
          text: "Hủy",
          style: "cancel",
        },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              const success = await handleDeleteTransaction(transactionId);
              
              if (success) {
                // Thêm thông báo xóa thành công
                const deleteConfirmMessage: Message = {
                  id: Date.now().toString(),
                  text: `${botPronoun} đã xóa giao dịch thành công.`,
                  isUser: false,
                  timestamp: new Date(),
                };
                setMessages((prev) => [...prev, deleteConfirmMessage]);
              } else {
                Alert.alert(
                  "Lỗi",
                  "Không thể xóa giao dịch. Vui lòng thử lại sau."
                );
              }
            } catch (error) {
              console.error("Lỗi khi xóa giao dịch:", error);
              Alert.alert(
                "Lỗi",
                "Không thể xóa giao dịch. Vui lòng thử lại sau."
              );
            }
          },
        },
      ]
    );
  };

  // Hiển thị hướng dẫn chi tiết
  const toggleHelp = () => {
    setShowHelp(!showHelp);
  };

  // Khởi tạo OpenAI client khi component mount
  useEffect(() => {
    const setupOpenAI = async () => {
      const initialized = await initializeOpenAI();
      if (!initialized) {
        // Có thể hiển thị thông báo lỗi hoặc xử lý khi không thể khởi tạo
        console.warn("Không thể khởi tạo OpenAI client, một số tính năng có thể không hoạt động");
      }
    };
    
    setupOpenAI();
  }, []);

  // Cuộn xuống tin nhắn mới nhất
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Cập nhật hướng dẫn chi tiết để phù hợp với cách xưng hô
  const DETAILED_HELP = `
📌 Hướng dẫn sử dụng chatbot AI Money:

GIAO DỊCH:
- "Ăn sáng 100k" (vào ví mặc định)
- "Ăn sáng 100k vib" (vào ví được chỉ định)
- "Nhận lương 10 triệu"
- "Xem 5 giao dịch gần nhất"
- "Xem giao dịch hôm nay"

VÍ:
- "Tạo ví MB Bank 500k"
- "Xem tất cả ví của ${botPronoun}"
- "Tạo ví tiền mặt với 1 triệu"

CHUYỂN TIỀN:
- "5tr vpbank sang tech" (chuyển 5 triệu từ ví vpbank sang ví tech)
- "500k từ ví tiền mặt vào mb bank"

DANH MỤC:
- "Xem các danh mục chi tiêu"
- "Xem danh mục thu nhập"

BÁO CÁO:
- "Xem thu chi tháng này"
- "Tổng thu nhập tháng 7"

⚠️ GIỚI HẠN:
- Tối đa ${DAILY_MESSAGE_LIMIT} tin nhắn mỗi ngày
- Mỗi tin nhắn tối đa ${MESSAGE_CHARACTER_LIMIT} ký tự

Lưu ý: AI Money sẽ hỏi xác nhận trước khi thực hiện các thay đổi quan trọng.
`;

  return (
    <SafeAreaView
      edges={["top"]}
      style={{ flex: 1, backgroundColor: themeColors.background }}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        <View
          style={[
            styles.header,
            {
              backgroundColor: themeColors.background,
              borderBottomColor: themeColors.border,
            },
          ]}
        >
          <View
            style={[styles.titleContainer, { backgroundColor: "transparent" }]}
          >
            <Text style={[styles.title, { color: themeColors.text }]}>
              AI Chatbot
            </Text>
            <View style={styles.onlineIndicator} />
          </View>
          <TouchableOpacity style={styles.helpButton} onPress={toggleHelp}>
            <Ionicons
              name="information-circle"
              size={26}
              color={themeColors.primary}
            />
          </TouchableOpacity>
        </View>

        {showHelp ? (
          <View style={[styles.helpContainer, getCardStyle("medium")]}>
            <Text style={[styles.helpText, { color: themeColors.text }]}>
              {DETAILED_HELP}
            </Text>
            <TouchableOpacity
              style={styles.closeHelpButton}
              onPress={toggleHelp}
            >
              <Text style={styles.closeHelpButtonText}>Đóng</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <FlatList
              ref={flatListRef}
              data={messages}
              keyExtractor={(item) => item.id}
              style={{ backgroundColor: themeColors.background }}
              contentContainerStyle={styles.messagesList}
              renderItem={({ item }) => (
                <View
                  style={[
                    styles.messageContainer,
                    { backgroundColor: themeColors.background },
                    item.isUser
                      ? styles.userMessageContainer
                      : styles.botMessageContainer,
                  ]}
                >
                  {!item.isUser && (
                    <View
                      style={[
                        styles.avatarContainer,
                        { backgroundColor: "transparent" },
                      ]}
                    >
                      <View style={[styles.botAvatar]}>
                        <Ionicons
                          name="chatbubble-ellipses"
                          size={16}
                          color="white"
                        />
                      </View>
                    </View>
                  )}

                  {/* Bong bóng chat */}
                  {item.isUser ? (
                    // Tin nhắn người dùng
                    <View
                      style={[
                        styles.messageContent, 
                        styles.userMessageBubble,
                        { backgroundColor: themeColors.primary }
                      ]}
                    >
                      <Text style={[styles.messageText, { color: "white" }]}>
                        {item.text}
                      </Text>
                      <Text style={styles.timestampText}>
                        {formatTime(item.timestamp)}
                      </Text>
                    </View>
                  ) : (
                    // Tin nhắn bot
                    <View
                      style={[
                        styles.messageContent,
                        styles.botMessageBubble,
                        getCardStyle("low"),
                      ]}
                    >
                      {/* Nút xóa giao dịch nếu có */}
                      {item.showDeleteButton && item.transaction_id && (
                        <TouchableOpacity
                          style={styles.deleteButton}
                          onPress={() =>
                            handleDeleteTransactionWithConfirm(item.transaction_id!)
                          }
                        >
                          <Ionicons
                            name="close-circle"
                            size={22}
                            color={themeColors.danger}
                          />
                        </TouchableOpacity>
                      )}

                      <RNView style={{ width: "100%" }}>
                        {/* Kiểm tra nếu tin nhắn có định dạng đặc biệt */}
                        {item.text && item.text.includes("{{") ? (
                          <FormattedMessage message={item.text} />
                        ) : (
                          <Text
                            style={[
                              styles.messageText,
                              { color: themeColors.text },
                            ]}
                          >
                            {item.text}
                          </Text>
                        )}
                      </RNView>

                      {/* Hiển thị buttons nếu có */}
                      {item.buttons && item.buttons.length > 0 && (
                        <RNView style={styles.buttonsContainer}>
                          {item.buttons.map(
                            (button: ChatButton, index: number) => (
                              <TouchableOpacity
                                key={index}
                                style={styles.messageButton}
                                onPress={button.onPress}
                              >
                                <View
                                  style={[
                                    styles.buttonGradient,
                                    {
                                      backgroundColor: 
                                        button.type === "confirm"
                                          ? "#4CAF50"
                                          : button.type === "cancel"
                                          ? themeColors.danger
                                          : "#9E9E9E"
                                    }
                                  ]}
                                >
                                  <RNView
                                    style={{
                                      flexDirection: "row",
                                      alignItems: "center",
                                    }}
                                  >
                                    <Ionicons
                                      name={
                                        button.type === "confirm"
                                          ? "checkmark-circle"
                                          : button.type === "cancel"
                                          ? "close-circle"
                                          : "help-circle"
                                      }
                                      size={16}
                                      color="white"
                                      style={{ marginRight: 4 }}
                                    />
                                    <Text style={styles.buttonText}>
                                      {button.text}
                                    </Text>
                                  </RNView>
                                </View>
                              </TouchableOpacity>
                            )
                          )}
                        </RNView>
                      )}

                      <Text style={styles.timestampText}>
                        {formatTime(item.timestamp)}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            />

            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: themeColors.background,
                  borderTopColor: themeColors.border,
                },
              ]}
            >
              <Animated.View
                style={[
                  styles.inputWrapper,
                  {
                    backgroundColor: themeColors.cardBackground,
                    borderColor: themeColors.border,
                  },
                  animatedInputWrapperStyle,
                ]}
              >
                {/* Nút chọn mẫu tin nhắn */}
                <TouchableOpacity
                  style={styles.templateButton}
                  onPress={openTemplateModal}
                >
                  <Ionicons
                    name="document-text-outline"
                    size={24}
                    color={
                      isInputFocused
                        ? themeColors.primary
                        : isDark
                        ? "#7CB9F8"
                        : "#64B5F6"
                    }
                  />
                </TouchableOpacity>

                <TextInput
                  ref={inputRef}
                  style={[styles.input, { color: themeColors.text }]}
                  value={inputText}
                  onChangeText={handleTextChange}
                  placeholder="Nhập tin nhắn..."
                  placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                  multiline
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  maxLength={MESSAGE_CHARACTER_LIMIT}
                />
                
                {/* Nút xóa văn bản */}
                {inputText.length > 0 && (
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => setInputText("")}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <Ionicons
                      name="close-circle"
                      size={20}
                      color={themeColors.secondaryText}
                    />
                  </TouchableOpacity>
                )}
              </Animated.View>

              <TouchableOpacity
                onPress={handleSend}
                disabled={inputText.trim() === ""}
              >
                <View
                  style={[
                    styles.sendButton,
                    { 
                      backgroundColor: 
                        inputText.trim()
                          ? themeColors.primary 
                          : themeColors.iconBackground,
                      opacity: 
                        inputText.trim() 
                          ? 1 
                          : 0.6 
                    }
                  ]}
                >
                  <Ionicons
                    name="send"
                    size={22}
                    color={
                      inputText.trim()
                        ? "white"
                        : themeColors.secondaryText
                    }
                  />
                </View>
              </TouchableOpacity>
            </View>
          </>
        )}
        
        {/* Modal chọn mẫu tin nhắn */}
        <Modal
          visible={showTemplateModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowTemplateModal(false)}
        >
          <TouchableOpacity 
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowTemplateModal(false)}
          >
            <TouchableOpacity 
              activeOpacity={1} 
              onPress={(e) => e.stopPropagation()}
              style={[
                styles.modalContainer,
                { backgroundColor: themeColors.cardBackground }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: themeColors.text }]}>
                  Chọn mẫu tin nhắn
                </Text>
                <TouchableOpacity 
                  onPress={() => setShowTemplateModal(false)}
                  style={styles.closeButton}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close" size={24} color={themeColors.text} />
                </TouchableOpacity>
              </View>
              
              {loadingTemplates ? (
                <View style={styles.loadingContainer}>
                  <Text style={{ color: themeColors.secondaryText }}>Đang tải...</Text>
                </View>
              ) : templates.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Text style={{ color: themeColors.secondaryText }}>
                    Chưa có mẫu tin nhắn nào. Vui lòng thêm mẫu trong màn hình Mẫu giao dịch.
                  </Text>
                </View>
              ) : (
                <FlatList
                  data={templates}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={[
                        styles.templateItem,
                        { borderBottomColor: themeColors.border }
                      ]}
                      onPress={() => selectTemplate(item)}
                    >
                      <Text style={[styles.templateText, { color: themeColors.text }]}>
                        {item.content.length > MESSAGE_CHARACTER_LIMIT 
                          ? `${item.content.substring(0, MESSAGE_CHARACTER_LIMIT)}...` 
                          : item.content}
                      </Text>
                      {item.content.length > MESSAGE_CHARACTER_LIMIT && (
                        <Text style={[styles.templateWarning, { color: themeColors.danger }]}>
                          Sẽ bị cắt bớt xuống {MESSAGE_CHARACTER_LIMIT} ký tự
                        </Text>
                      )}
                    </TouchableOpacity>
                  )}
                  style={{ maxHeight: 400 }}
                />
              )}
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    fontSize: 22,
    fontWeight: "bold",
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#4CAF50",
    marginLeft: 8,
  },
  helpButton: {
    width: 44,
    height: 44,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 22,
  },
  helpContainer: {
    flex: 1,
    padding: 20,
    margin: 16,
  },
  helpText: {
    fontSize: 15,
    lineHeight: 22,
  },
  closeHelpButton: {
    alignSelf: "center",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
    marginTop: 20,
    backgroundColor: "#2196F3",
  },
  closeHelpButtonText: {
    color: "white",
    fontWeight: "600",
  },
  messagesList: {
    padding: 12,
    paddingBottom: 16,
  },
  messageContainer: {
    marginVertical: 8,
    maxWidth: "85%",
    flexDirection: "column",
    minWidth: 60,
  },
  userMessageContainer: {
    alignSelf: "flex-end",
    marginLeft: 50,
  },
  botMessageContainer: {
    alignSelf: "flex-start",
    marginRight: 50,
    flexDirection: "row",
  },
  avatarContainer: {
    marginRight: 8,
    marginLeft: 4,
    alignSelf: "flex-end",
  },
  botAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#4CAF50",
  },
  messageContent: {
    flex: 0,
    padding: 12,
  },
  userMessageBubble: {
    borderRadius: 16,
  },
  botMessageBubble: {
    borderRadius: 16,
    paddingVertical: 10,
    paddingHorizontal: 14,
    minWidth: 100,
    flexShrink: 1,
    position: 'relative',
  },
  messageText: {
    fontSize: 15,
    lineHeight: 22,
    letterSpacing: 0.3,
    flexShrink: 1,
    flexWrap: "wrap",
  },
  timestampText: {
    fontSize: 10,
    marginTop: 6,
    alignSelf: "flex-end",
    color: "rgba(255, 255, 255, 0.8)",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 24,
    paddingHorizontal: 8,
    marginRight: 8,
    borderWidth: 1,
    position: 'relative',
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
    marginLeft: 4,
    paddingRight: 30,
  },
  clearButton: {
    padding: 4,
    position: 'absolute',
    right: 8,
    top: 14,
  },
  templateButton: {
    padding: 6,
    borderRadius: 20,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  buttonsContainer: {
    flexDirection: "row",
    marginTop: 12,
    marginBottom: 4,
  },
  messageButton: {
    marginRight: 8,
    borderRadius: 16,
    overflow: "hidden",
  },
  buttonGradient: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    minWidth: 100,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 16,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 14,
  },
  deleteButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    zIndex: 1,
    width: 28,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Styles cho modal mẫu tin nhắn
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingBottom: 30,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
    borderRadius: 20,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  templateItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  templateText: {
    fontSize: 16,
    lineHeight: 22,
  },
  templateWarning: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
});