// File: app/(tabs)/more.tsx
// File này có 2 tab: <PERSON><PERSON><PERSON> (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> Học) và <PERSON>hêm (các tính năng còn lại)
// File này liên quan đến: context/ThemeContext.tsx, app/template.tsx, app/(more)/finance-calculator.tsx, app/(more)/calendar.tsx, app/(more)/more-settings.tsx, app/(more)/ai-learning.tsx, app/(more)/spending-limits.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, useFocusEffect } from "expo-router";
import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function MoreScreen() {
  // Sử dụng theme từ ThemeContext với các helpers mới
  const { isDark, themeColors, getCardStyle, getIconContainerStyle } =
    useTheme();
  const { t } = useLocalization();

  // Tab hiện tại (0: Chính, 1: Thêm)
  const [activeTab, setActiveTab] = useState(0);
  
  // Mặc định là grid
  const [isGridView, setIsGridView] = useState(true);

  // State để quản lý ID của các mục trong từng tab
  const [mainTabItems, setMainTabItems] = useState(['wallets', 'categories', 'stats', 'aiLearning']);
  const [moreTabItems, setMoreTabItems] = useState(['spendingLimits', 'debt', 'templates', 'calendar', 'moving', 'financeCalculator']);

  // Load settings khi component mount
  useEffect(() => {
    loadViewMode();
    loadTabSettings();
  }, []);

  // Reload settings khi màn hình được focus lại
  useFocusEffect(
    useCallback(() => {
      loadTabSettings();
    }, [])
  );

  // Load chế độ hiển thị từ storage
  const loadViewMode = async () => {
    try {
      const savedViewMode = await AsyncStorage.getItem('more_view_mode');
      if (savedViewMode !== null) {
        setIsGridView(savedViewMode === 'grid');
      }
    } catch (error) {
      console.log('Error loading view mode:', error);
    }
  };

  // Load cài đặt tab
  const loadTabSettings = async () => {
    try {
      const savedMainTab = await AsyncStorage.getItem('main_tab_items');
      const savedMoreTab = await AsyncStorage.getItem('more_tab_items');
      
      if (savedMainTab !== null) {
        setMainTabItems(JSON.parse(savedMainTab));
      }
      if (savedMoreTab !== null) {
        setMoreTabItems(JSON.parse(savedMoreTab));
      }
    } catch (error) {
      console.log('Error loading tab settings:', error);
    }
  };

  // Save chế độ hiển thị vào storage
  const saveViewMode = async (isGrid: boolean) => {
    try {
      await AsyncStorage.setItem('more_view_mode', isGrid ? 'grid' : 'list');
    } catch (error) {
      console.log('Error saving view mode:', error);
    }
  };

  // Hàm chuyển đổi giữa chế độ list và grid
  const toggleViewMode = () => {
    const newGridView = !isGridView;
    setIsGridView(newGridView);
    saveViewMode(newGridView);
  };

  // Tất cả các mục có thể có (THÊM SPENDING LIMITS)
  const allItems = {
    wallets: {
      id: "wallets",
      title: t("more.wallets") || "Ví",
      icon: "wallet-outline",
      description: t("more.walletsDescription") || "Quản lý ví của bạn",
      route: "/wallet",
    },
    categories: {
      id: "categories",
      title: t("more.categories") || "Danh mục",
      icon: "pricetags-outline",
      description: t("more.categoriesDescription") || "Tùy chỉnh danh mục thu chi",
      route: "/category",
    },
    stats: {
      id: "stats",
      title: t("stats.title") || "Thống kê",
      icon: "stats-chart-outline",
      description: t("more.statsDescription") || "Xem thống kê thu chi của bạn",
      route: "/stats",
    },
    aiLearning: {
      id: "aiLearning",
      title: "AI Học",
      icon: "school-outline",
      description: "Đào tạo AI phân loại giao dịch đúng danh mục",
      route: "/ai-learning",
    },
    spendingLimits: {
      id: "spendingLimits",
      title: "Giới hạn chi tiêu",
      icon: "speedometer-outline",
      description: "Đặt hạn mức chi tiêu cho từng danh mục và nhận cảnh báo",
      route: "/spending-limits",
    },
    debt: {
      id: "debt",
      title: "Sổ Nợ",
      icon: "cash-outline",
      description: "Quản lý khoản nợ phải trả và cho vay",
      route: "/debt",
    },
    templates: {
      id: "templates",
      title: t("more.templates") || "Mẫu giao dịch",
      icon: "document-text-outline",
      description: t("more.templatesDescription") || "Quản lý mẫu giao dịch cho chatbot",
      route: "/template",
    },
    calendar: {
      id: "calendar",
      title: "Lịch",
      icon: "calendar-outline",
      description: "Xem chi tiêu theo lịch hàng ngày/tuần/tháng",
      route: "/calendar",
    },
    moving: {
      id: "moving",
      icon: "swap-horizontal",
      title: t("more.movingFeature") || "Chuyển App",
      description: t("more.movingDescription") || "Chuyển dữ liệu từ ứng dụng tài chính khác",
      route: "/moving" as const,
    },
    financeCalculator: {
      id: "financeCalculator",
      title: "Máy tính tài chính",
      icon: "calculator-outline",
      description: "Máy tính lãi suất và chuyển đổi tiền tệ",
      route: "/finance-calculator",
    },
  };

  // Lấy danh sách items theo tab
  const getItemsForTab = (itemIds: string[]) => {
    return itemIds.map(id => allItems[id as keyof typeof allItems]).filter(Boolean);
  };

  // Lấy danh sách menu hiện tại dựa vào tab đang hiển thị
  const menuItems = activeTab === 0 
    ? getItemsForTab(mainTabItems) 
    : getItemsForTab(moreTabItems);

  // Render tabs
  const renderTabs = () => {
    return (
      <View style={[styles.tabContainer, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 0 && { 
              borderBottomColor: themeColors.primary, 
              borderBottomWidth: 2 
            },
          ]}
          onPress={() => setActiveTab(0)}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 0 ? themeColors.primary : themeColors.secondaryText },
            ]}
          >
            {t("tabs.main") || "Chính"}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === 1 && { 
              borderBottomColor: themeColors.primary, 
              borderBottomWidth: 2 
            },
          ]}
          onPress={() => setActiveTab(1)}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 1 ? themeColors.primary : themeColors.secondaryText },
            ]}
          >
            {t("tabs.more") || "Thêm"}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Render item theo kiểu grid
  const renderGridItems = () => {
    return (
      <View style={[styles.gridContainer, { backgroundColor: "transparent" }]}>
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.gridCard, getCardStyle("medium")]}
            onPress={() => router.push(item.route)}
          >
            <View style={[styles.gridIconContainer, getIconContainerStyle()]}>
              <Ionicons
                name={item.icon as any}
                size={28}
                color={themeColors.primary}
              />
            </View>
            <Text style={[styles.gridTitle, { color: themeColors.text }]}>
              {item.title}
            </Text>
            <Text
              style={[
                styles.gridDescription,
                { color: themeColors.secondaryText },
              ]}
              numberOfLines={2}
            >
              {item.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render item theo kiểu list
  const renderListItems = () => {
    return (
      <View style={[styles.menuContainer, { backgroundColor: "transparent" }]}>
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[styles.menuCard, getCardStyle("medium")]}
            onPress={() => router.push(item.route)}
          >
            <View style={[styles.iconContainer, getIconContainerStyle()]}>
              <Ionicons
                name={item.icon as any}
                size={28}
                color={themeColors.primary}
              />
            </View>
            <View style={[styles.menuInfo, { backgroundColor: "transparent" }]}>
              <Text style={[styles.menuTitle, { color: themeColors.text }]}>
                {item.title}
              </Text>
              <Text
                style={[
                  styles.menuDescription,
                  { color: themeColors.secondaryText },
                ]}
              >
                {item.description}
              </Text>
            </View>
            <Ionicons
              name="chevron-forward"
              size={24}
              color={isDark ? "#7CB9F8" : "#90CAF9"}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      <View style={[styles.header, { backgroundColor: "transparent" }]}>
        <Text style={[styles.title, { color: themeColors.text }]}>
          {t("more.title") || "Tính năng"}
        </Text>

        {/* Container cho các nút bên phải */}
        <View style={[styles.headerButtons, { backgroundColor: "transparent" }]}>
          {/* Nút cài đặt */}
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: "transparent" }]}
            onPress={() => router.push("/more-settings")}
          >
            <Ionicons
              name="settings-outline"
              size={24}
              color={themeColors.primary}
            />
          </TouchableOpacity>

          {/* Nút chuyển đổi chế độ hiển thị */}
          <TouchableOpacity
            style={[styles.headerButton, { backgroundColor: "transparent" }]}
            onPress={toggleViewMode}
          >
            <Ionicons
              name={isGridView ? "list-outline" : "grid-outline"}
              size={24}
              color={themeColors.primary}
            />
          </TouchableOpacity>
        </View>
      </View>

      {renderTabs()}

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {isGridView ? renderGridItems() : renderListItems()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  headerButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    padding: 4,
    marginLeft: 8,
  },
  // Styles cho tabs
  tabContainer: {
    flexDirection: "row",
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
  },
  // Styles cho danh sách menu
  menuContainer: {
    padding: 16,
  },
  menuCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 16,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  menuInfo: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 4,
  },
  menuDescription: {
    fontSize: 14,
  },
  // Style cho chế độ hiển thị grid
  gridContainer: {
    padding: 16,
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  gridCard: {
    width: "48%",
    padding: 16,
    marginBottom: 16,
    alignItems: "center",
  },
  gridIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  gridTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
    textAlign: "center",
  },
  gridDescription: {
    fontSize: 12,
    textAlign: "center",
  },
  card: {
    borderWidth: 1,
  },
});