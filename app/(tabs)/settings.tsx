// File: app/(tabs)/settings.tsx
// File này được cập nhật để thêm section mã giới thiệu
// File này liên quan đến: components/ReferralCodeInput.tsx, lib/services/referral-code-service.ts, context/ThemeContext.tsx, app/(more)/upgrade-premium.tsx, app/(more)/affiliate-dashboard.tsx

import { Text, View } from "@/components/Themed";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/context/AuthContext";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { UserModel } from "@/lib/models/user";
import { getCurrentVersion, handleCheckUpdate, openDeveloperApps } from "@/utils/updateService";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Linking from 'expo-linking';
import { router, useFocusEffect } from "expo-router";
import React, { useCallback, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function SettingsScreen() {
  const { signOut, avatar } = useAuth();
  const { currency, symbol } = useCurrency();
  // Sử dụng theme từ ThemeContext với các helpers mới
  const {
    isDark,
    themeColors,
    toggleTheme,
    getCardStyle,
    getIconContainerStyle,
    getShadowStyle,
  } = useTheme();
  const { t, locale, setLocale, locales } = useLocalization();

  // Loading state
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState(null);
  const [checkingUpdate, setCheckingUpdate] = useState(false);

  // State cho cách AI xưng hô với người dùng
  const [userPronoun, setUserPronoun] = useState("Bạn");
  const [showPronounModal, setShowPronounModal] = useState(false);

  // Danh sách cách gọi người dùng và AI sẽ xưng
  const USER_PRONOUNS = [
    { calling: "Bạn", selfCalling: "tôi" },
    { calling: "Anh", selfCalling: "em" },
    { calling: "Chị", selfCalling: "em" },
    { calling: "Em", selfCalling: "anh" },
    { calling: "Chú", selfCalling: "cháu" },
    { calling: "Cô", selfCalling: "cháu" },
    { calling: "Bố", selfCalling: "con" },
    { calling: "Ba", selfCalling: "con" },
    { calling: "Mẹ", selfCalling: "con" },
    { calling: "Má", selfCalling: "con" },
    { calling: "Bà", selfCalling: "tui" },
  ];

  // Danh sách cài đặt tài khoản
  const ACCOUNT_ITEMS = [
    {
      id: "profile",
      title: t("settings.profile"),
      icon: "person-outline",
      description: t("settings.updateProfile"),
    },
    {
      id: "changePassword",
      title: t("settings.changePassword"),
      icon: "lock-closed-outline",
      description: t("settings.updatePassword"),
    },
  ];

  // Danh sách cài đặt giao diện
  const SETTINGS_ITEMS = [
    {
      id: "currency",
      title: t("settings.currency"),
      icon: "cash-outline",
      description: `${currency} (${symbol})`,
    },
    {
      id: "home_settings",
      title: t("settings.homeOptions") || "Tùy chọn trang chủ",
      icon: "home-outline",
      description:
        t("settings.homeOptionsDescription") ||
        "Tùy chỉnh số lượng ví và giao dịch hiển thị",
    },
    {
      id: "user_pronoun",
      title: "Trợ lý AI gọi bạn là",
      icon: "chatbubble-ellipses-outline",
      description: `"${userPronoun}" (AI xưng ${
        USER_PRONOUNS.find((p) => p.calling === userPronoun)?.selfCalling ||
        "tôi"
      })`,
    },
  ];

  // Danh sách mục "Premium & Kiếm tiền"
  const PREMIUM_ITEMS = [
    {
      id: "upgrade_premium",
      title: "Nâng cấp Premium",
      icon: "diamond-outline",
      description: "Mở khóa tất cả tính năng cao cấp, không quảng cáo",
      color: "#FFD700",
    },
    {
      id: "affiliate_program",
      title: "Chương trình Affiliate",
      icon: "people-outline", 
      description: "Kiếm tiền bằng cách giới thiệu AI Money Premium (50% hoa hồng)",
      color: "#4CAF50",
    },
  ];
  
  // Danh sách mục "Về chúng tôi"
  const ABOUT_ITEMS = [
    // Mục "Liên hệ - Góp ý"
    {
      id: "contact_feedback",
      title: "Liên hệ - Góp ý",
      icon: "call-outline",
      description: "Thông tin liên hệ và gửi góp ý",
    },
    // Thêm mục "Chia sẻ ứng dụng"
    {
      id: "share_app",
      title: "Chia sẻ ứng dụng",
      icon: "share-social-outline",
      description: "Giới thiệu AI Money với bạn bè",
    },
    // Thêm mục "Đánh giá ứng dụng"
    {
      id: "rate_app",
      title: "Đánh giá ứng dụng",
      icon: "star-outline",
      description: "Đánh giá AI Money trên App Store",
    },
    // Mục "App khác"
    {
      id: "other_apps",
      title: "App khác",
      icon: "apps-outline",
      description: "Xem các ứng dụng khác từ Manh Vũ", 
    },
    // Mục "Kiểm tra cập nhật"
    {
      id: "check_update",
      title: "Kiểm tra cập nhật",
      icon: "refresh-outline",
      description: `Phiên bản hiện tại: ${getCurrentVersion()}`,
    },
  ];

  // Load profile data và cài đặt khi màn hình được focus
  useFocusEffect(
    useCallback(() => {
      const loadProfileAndSettings = async () => {
        try {
          setLoading(true);

          // Tải thông tin profile
          const userProfile = await UserModel.getCurrentProfile();
          setProfile(userProfile);

          // Tải cài đặt xưng hô từ AsyncStorage
          const savedUserPronoun = await AsyncStorage.getItem("user_pronoun");
          if (savedUserPronoun) {
            setUserPronoun(savedUserPronoun);
          }
        } catch (error) {
          console.error("Error loading profile or settings:", error);
        } finally {
          setLoading(false);
        }
      };

      loadProfileAndSettings();
    }, [])
  );



  // Lưu cài đặt xưng hô khi thay đổi
  const saveUserPronoun = async (value) => {
    try {
      await AsyncStorage.setItem("user_pronoun", value);
      setUserPronoun(value);
      setShowPronounModal(false);
    } catch (error) {
      console.error("Error saving user pronoun setting:", error);
      Alert.alert("Lỗi", "Không thể lưu cài đặt. Vui lòng thử lại sau.");
    }
  };

  // Kiểm tra nếu đang ở chế độ tối
  const systemTheme = Colors[isDark ? "dark" : "light"];

  // Xử lý chuyển đổi giữa chế độ sáng và tối
  const handleToggleTheme = () => {
    toggleTheme();
  };

  // Chuyển đổi ngôn ngữ
  const toggleLanguage = () => {
    const newLocale = locale === "vi" ? "en" : "vi";
    setLocale(newLocale);
    setTimeout(() => {
      setLoading(true);
      setTimeout(() => setLoading(false), 5);
    }, 50);
  };

  // Xử lý đánh giá ứng dụng
  const handleRateApp = async () => {
    try {
      // URL trực tiếp đến trang đánh giá trên iOS
      const appId = '6745965337';
      const ratingUrl = `itms-apps://apps.apple.com/app/id${appId}?action=write-review`;
      
      const canOpen = await Linking.canOpenURL(ratingUrl);
      if (canOpen) {
        await Linking.openURL(ratingUrl);
      } else {
        // Fallback to regular App Store URL
        const appStoreUrl = `https://apps.apple.com/app/id${appId}`;
        await Linking.openURL(appStoreUrl);
      }
    } catch (error) {
      console.error('Lỗi khi mở trang đánh giá:', error);
      Alert.alert(
        'Lỗi',
        'Không thể mở trang đánh giá. Vui lòng thử lại sau.',
        [{ text: 'OK' }]
      );
    }
  };

  // Xử lý chia sẻ ứng dụng
  const handleShareApp = () => {
    // Điều hướng đến màn hình chia sẻ ứng dụng mới
    router.push("/(app)/share-app");
  };

  // Xử lý mở trang App khác
  const handleOpenOtherApps = async () => {
    try {
      await openDeveloperApps();
    } catch (error) {
      console.error('Lỗi khi mở trang App khác:', error);
      Alert.alert(
        'Lỗi',
        'Không thể mở trang App Store. Vui lòng thử lại sau.',
        [{ text: 'OK' }]
      );
    }
  };

  // Xử lý kiểm tra cập nhật
  const handleCheckForUpdate = async () => {
    try {
      // Sử dụng hàm từ updateService.js để kiểm tra cập nhật
      await handleCheckUpdate(setCheckingUpdate);
    } catch (error) {
      console.error('Lỗi khi kiểm tra cập nhật:', error);
      setCheckingUpdate(false);
      Alert.alert(
        'Lỗi',
        'Không thể kiểm tra cập nhật. Vui lòng thử lại sau.',
        [{ text: 'OK' }]
      );
    }
  };

  // Handle settings item press
  const handleSettingPress = (id) => {
    switch (id) {
      case "profile":
        router.push("/(app)/profile");
        break;
      case "changePassword":
        router.push("/(app)/profile/change-password");
        break;
      case "clearNotifications":
        // Hiển thị hộp thoại xác nhận xóa thông báo
        Alert.alert(
          t("notifications.clearTitle"),
          t("notifications.clearConfirm"),
          [
            {
              text: t("common.cancel"),
              style: "cancel",
            },
            {
              text: t("common.delete"),
              style: "destructive",
              onPress: () => {
                // Xử lý xóa thông báo ở đây
                console.log("Đã xóa tất cả thông báo");
              },
            },
          ]
        );
        break;
      case "backup":
        // Xử lý sao lưu
        console.log("Backup functionality");
        break;
      case "currency":
        // Sửa lại đường dẫn đến màn hình currency
        router.push("/currency");
        break;
      case "home_settings":
        // Điều hướng đến màn hình "Tùy chọn trang chủ" mới
        router.push("/(app)/home-settings");
        break;
      case "user_pronoun":
        // Hiển thị modal chọn cách xưng hô
        setShowPronounModal(true);
        break;
      case "upgrade_premium":
        // Điều hướng đến màn hình nâng cấp Premium
        router.push("/(more)/upgrade-premium");
        break;
      case "affiliate_program":
        // Điều hướng đến màn hình Affiliate Dashboard
        router.push("/(more)/affiliate-dashboard");
        break;
      case "contact_feedback":
        // Điều hướng đến màn hình "Liên hệ - Góp ý" mới
        router.push("/(app)/contact-feedback");
        break;
      case "other_apps":
        // Mở trang nhà phát triển trên App Store
        handleOpenOtherApps();
        break;
      case "check_update":
        // Xử lý kiểm tra cập nhật
        handleCheckForUpdate();
        break;
      case "rate_app":
        // Xử lý đánh giá ứng dụng
        handleRateApp();
        break;
      case "share_app":
        // Xử lý chia sẻ ứng dụng
        handleShareApp();
        break;
      default:
        console.log("Pressed setting:", id);
    }
  };

  const handleLogout = () => {
    Alert.alert(t("settings.logout"), t("settings.logoutConfirm"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("settings.logout"),
        style: "destructive",
        onPress: async () => {
          try {
            await signOut();
            router.replace("/(auth)/login");
          } catch (error) {
            console.error("Error signing out:", error);
            Alert.alert(t("common.error"), t("auth.logoutError"));
          }
        },
      },
    ]);
  };

  // Style cho nút logout
  const logoutButtonStyle = {
    backgroundColor: themeColors.dangerBackground,
    borderWidth: 1,
    borderColor: themeColors.dangerBorder,
    ...getShadowStyle("medium"),
  };



  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
      edges={["top", "left", "right"]}
    >
      <View
        style={[styles.header, { backgroundColor: themeColors.background }]}
      >
        <Text style={[styles.title, { color: themeColors.text }]}>
          {t("settings.title")}
        </Text>
      </View>

      <ScrollView
        style={{ flex: 1, backgroundColor: themeColors.background }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 60 }}
      >
        {/* User Profile Section */}
        <TouchableOpacity
          style={[
            styles.profileContainer,
            getCardStyle("medium"),
            { marginHorizontal: 16, marginBottom: 20 },
          ]}
          onPress={() => router.push({ pathname: "/(app)/profile" })}
        >
          <View
            style={[
              styles.profileImageContainer,
              getIconContainerStyle(),
              { ...getShadowStyle("low") },
            ]}
          >
            {loading ? (
              <ActivityIndicator size="small" color={themeColors.primary} />
            ) : avatar ? (
              <Image
                source={{ uri: avatar }}
                style={styles.profileImage}
                resizeMode="cover"
              />
            ) : (
              <Ionicons name="person" size={40} color={themeColors.primary} />
            )}
          </View>
          <View
            style={[styles.profileInfo, { backgroundColor: "transparent" }]}
          >
            <Text style={[styles.profileName, { color: themeColors.text }]}>
              {loading
                ? t("common.loading")
                : profile?.full_name || t("auth.user")}
            </Text>
            <Text
              style={[
                styles.profileEmail,
                { color: themeColors.secondaryText },
              ]}
            >
              {profile?.email || ""}
            </Text>
          </View>
          <Ionicons
            name="chevron-forward"
            size={24}
            color={isDark ? "#7CB9F8" : "#90CAF9"}
          />
        </TouchableOpacity>



        {/* Account Settings Section */}
        <View
          style={[styles.sectionContainer, { backgroundColor: "transparent" }]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            {t("settings.account")}
          </Text>
          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {ACCOUNT_ITEMS.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.settingItem,
                  {
                    backgroundColor: "transparent",
                    borderBottomColor: themeColors.border,
                    borderBottomWidth:
                      index === ACCOUNT_ITEMS.length - 1 ? 0 : 1,
                  },
                ]}
                onPress={() => handleSettingPress(item.id)}
              >
                <View
                  style={[
                    styles.settingLeft,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <View style={[styles.iconContainer, getIconContainerStyle()]}>
                    <Ionicons
                      name={item.icon}
                      size={22}
                      color={themeColors.primary}
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent" }}>
                    <Text
                      style={[styles.settingTitle, { color: themeColors.text }]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.settingDescription,
                        { color: themeColors.secondaryText },
                      ]}
                    >
                      {item.description}
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={isDark ? "#7CB9F8" : "#90CAF9"}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Premium & Earn Money Section */}
        <View
          style={[styles.sectionContainer, { backgroundColor: "transparent" }]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            Premium & Kiếm tiền
          </Text>
          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {PREMIUM_ITEMS.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.settingItem,
                  {
                    backgroundColor: "transparent",
                    borderBottomColor: themeColors.border,
                    borderBottomWidth:
                      index === PREMIUM_ITEMS.length - 1 ? 0 : 1,
                  },
                ]}
                onPress={() => handleSettingPress(item.id)}
              >
                <View
                  style={[
                    styles.settingLeft,
                    { backgroundColor: "transparent", flex: 1 },
                  ]}
                >
                  <View style={[styles.iconContainer, getIconContainerStyle()]}>
                    <Ionicons
                      name={item.icon}
                      size={22}
                      color={item.color}
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent", flex: 1 }}>
                    <Text
                      style={[styles.settingTitle, { color: themeColors.text }]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.settingDescription,
                        {
                          color: themeColors.secondaryText,
                          width: "80%",
                        },
                      ]}
                    >
                      {item.description}
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={isDark ? "#7CB9F8" : "#90CAF9"}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Display Settings Section */}
        <View
          style={[styles.sectionContainer, { backgroundColor: "transparent" }]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            {t("settings.interface")}
          </Text>
          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {/* Dark Mode Toggle */}
            <View
              style={[
                styles.settingToggleItem,
                { backgroundColor: "transparent" },
              ]}
            >
              <View
                style={[styles.settingLeft, { backgroundColor: "transparent" }]}
              >
                <View style={[styles.iconContainer, getIconContainerStyle()]}>
                  <Ionicons
                    name={isDark ? "moon" : "sunny"}
                    size={22}
                    color={isDark ? "#FFD700" : "#f39c12"}
                  />
                </View>
                <View style={{ backgroundColor: "transparent" }}>
                  <Text
                    style={[styles.settingTitle, { color: themeColors.text }]}
                  >
                    {t("settings.darkMode")}
                  </Text>
                  <Text
                    style={[
                      styles.settingDescription,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {isDark ? t("settings.on") : t("settings.off")}
                  </Text>
                </View>
              </View>
              <Switch
                value={isDark}
                onValueChange={handleToggleTheme}
                trackColor={{ false: "#ddd", true: `${themeColors.primary}88` }}
                thumbColor={isDark ? themeColors.primary : "#f4f3f4"}
              />
            </View>

            {/* Language Toggle */}
            <View
              style={[
                styles.settingToggleItem,
                {
                  backgroundColor: "transparent",
                  borderTopWidth: 1,
                  borderTopColor: themeColors.border,
                },
              ]}
            >
              <View
                style={[styles.settingLeft, { backgroundColor: "transparent" }]}
              >
                <View style={[styles.iconContainer, getIconContainerStyle()]}>
                  <Ionicons
                    name="language"
                    size={22}
                    color={themeColors.primary}
                  />
                </View>
                <View style={{ backgroundColor: "transparent" }}>
                  <Text
                    style={[styles.settingTitle, { color: themeColors.text }]}
                  >
                    {t("settings.language")}
                  </Text>
                  <Text
                    style={[
                      styles.settingDescription,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {locale === "vi" ? "Tiếng Việt" : "English"}
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={[
                  styles.languageButton,
                  {
                    backgroundColor: themeColors.iconBackground,
                    borderWidth: 1,
                    borderColor: themeColors.border,
                    ...getShadowStyle("low"),
                  },
                ]}
                onPress={toggleLanguage}
              >
                <Text
                  style={[styles.languageText, { color: themeColors.primary }]}
                >
                  {locale === "vi" ? "EN" : "VI"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* General Settings Section */}
        <View
          style={[styles.sectionContainer, { backgroundColor: "transparent" }]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            {t("settings.general")}
          </Text>
          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {SETTINGS_ITEMS.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.settingItem,
                  {
                    backgroundColor: "transparent",
                    borderBottomColor: themeColors.border,
                    borderBottomWidth:
                      index === SETTINGS_ITEMS.length - 1 ? 0 : 1,
                  },
                ]}
                onPress={() => handleSettingPress(item.id)}
              >
                <View
                  style={[
                    styles.settingLeft,
                    { backgroundColor: "transparent", flex: 1 },
                  ]}
                >
                  <View style={[styles.iconContainer, getIconContainerStyle()]}>
                    <Ionicons
                      name={item.icon}
                      size={22}
                      color={themeColors.primary}
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent", flex: 1 }}>
                    <Text
                      style={[styles.settingTitle, { color: themeColors.text }]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.settingDescription,
                        {
                          color: themeColors.secondaryText,
                          width: "80%",
                        },
                      ]}
                    >
                      {item.description}
                    </Text>
                  </View>
                </View>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={isDark ? "#7CB9F8" : "#90CAF9"}
                />
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* About Us Section */}
        <View
          style={[styles.sectionContainer, { backgroundColor: "transparent" }]}
        >
          <Text style={[styles.sectionTitle, { color: themeColors.primary }]}>
            Về chúng tôi
          </Text>
          <View style={[styles.sectionCard, getCardStyle("medium")]}>
            {ABOUT_ITEMS.map((item, index) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.settingItem,
                  {
                    backgroundColor: "transparent",
                    borderBottomColor: themeColors.border,
                    borderBottomWidth:
                      index === ABOUT_ITEMS.length - 1 ? 0 : 1,
                  },
                ]}
                onPress={() => handleSettingPress(item.id)}
              >
                <View
                  style={[
                    styles.settingLeft,
                    { backgroundColor: "transparent", flex: 1 },
                  ]}
                >
                  <View style={[styles.iconContainer, getIconContainerStyle()]}>
                    <Ionicons
                      name={item.icon}
                      size={22}
                      color={themeColors.primary}
                    />
                  </View>
                  <View style={{ backgroundColor: "transparent", flex: 1 }}>
                    <Text
                      style={[styles.settingTitle, { color: themeColors.text }]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.settingDescription,
                        {
                          color: themeColors.secondaryText,
                          width: "80%",
                        },
                      ]}
                    >
                      {item.description}
                    </Text>
                  </View>
                </View>
                {/* Hiển thị icon loading khi đang kiểm tra cập nhật */}
                {item.id === "check_update" && checkingUpdate ? (
                  <ActivityIndicator size="small" color={themeColors.primary} />
                ) : (
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={isDark ? "#7CB9F8" : "#90CAF9"}
                  />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={[
            styles.logoutButton,
            logoutButtonStyle,
            {
              marginHorizontal: 16,
              marginTop: 12,
              borderRadius: 16,
            },
          ]}
          onPress={handleLogout}
        >
          <Ionicons
            name="log-out-outline"
            size={22}
            color={themeColors.danger}
          />
          <Text style={[styles.logoutText, { color: themeColors.danger }]}>
            {t("settings.logout")}
          </Text>
        </TouchableOpacity>

        {/* App Info */}
        <View
          style={[styles.appInfoContainer, { backgroundColor: "transparent" }]}
        >
          <Text
            style={[styles.appInfoText, { color: themeColors.secondaryText }]}
          >
            AI Money v{getCurrentVersion()}
          </Text>
          <Text
            style={[styles.appInfoText, { color: themeColors.secondaryText }]}
          >
            © 2025 AI Money - Mạnh Vũ
          </Text>
        </View>
      </ScrollView>

      {/* Modal chọn cách AI xưng hô với người dùng */}
      <Modal
        visible={showPronounModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowPronounModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, getCardStyle("high")]}>
            <View
              style={[
                styles.modalHeader,
                {
                  backgroundColor: themeColors.cardBackground,
                  borderBottomColor: themeColors.border,
                },
              ]}
            >
              <Text style={[styles.modalTitle, { color: themeColors.text }]}>
                Trợ lý AI gọi bạn là
              </Text>
              <TouchableOpacity onPress={() => setShowPronounModal(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={themeColors.secondaryText}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.pronounsList}>
              {USER_PRONOUNS.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.pronounItem,
                    {
                      backgroundColor:
                        userPronoun === item.calling
                          ? themeColors.iconBackground
                          : "transparent",
                      borderBottomColor: themeColors.border,
                      borderBottomWidth:
                        index === USER_PRONOUNS.length - 1 ? 0 : 1,
                    },
                  ]}
                  onPress={() => saveUserPronoun(item.calling)}
                >
                  <View
                    style={[
                      styles.pronounDetail,
                      { backgroundColor: "transparent" },
                    ]}
                  >
                    <Text
                      style={[styles.pronounText, { color: themeColors.text }]}
                    >
                      {`"${item.calling}"`}
                    </Text>
                    <Text
                      style={[
                        styles.pronounSubtext,
                        { color: themeColors.secondaryText },
                      ]}
                    >
                      {`(AI sẽ xưng ${item.selfCalling})`}
                    </Text>
                  </View>

                  {userPronoun === item.calling && (
                    <Ionicons
                      name="checkmark"
                      size={22}
                      color={themeColors.primary}
                    />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[
                styles.modalButton,
                {
                  backgroundColor: themeColors.primary,
                  marginTop: 10,
                  marginBottom: 16,
                  ...getShadowStyle("low"),
                },
              ]}
              onPress={() => setShowPronounModal(false)}
            >
              <Text style={styles.modalButtonText}>Đóng</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  profileContainer: {
    flexDirection: "row",
    padding: 20,
    alignItems: "center",
  },
  profileImageContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  profileImage: {
    width: "100%",
    height: "100%",
    borderRadius: 999,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 6,
  },
  profileEmail: {
    fontSize: 15,
  },
  sectionContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 10,
    paddingLeft: 4,
  },
  sectionCard: {
    overflow: "hidden",
  },
  settingToggleItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  settingsListContainer: {
    paddingHorizontal: 16,
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  settingLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  languageButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
  },
  languageText: {
    fontSize: 15,
    fontWeight: "600",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 10,
  },
  appInfoContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 40,
  },
  appInfoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    fontSize: 16,
  },
  settingValue: {
    fontSize: 16,
  },

  // Styles cho modal xưng hô
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalContainer: {
    width: "90%",
    maxHeight: "70%",
    borderRadius: 16,
    overflow: "hidden",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  pronounsList: {
    paddingVertical: 8,
  },
  pronounItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  pronounDetail: {
    flex: 1,
  },
  pronounText: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  pronounSubtext: {
    fontSize: 14,
  },
  modalButton: {
    marginHorizontal: 16,
    padding: 14,
    borderRadius: 12,
    alignItems: "center",
  },
  modalButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});