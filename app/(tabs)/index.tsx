// File: app/(tabs)/index.tsx
// <PERSON><PERSON><PERSON> hình Home đã đư<PERSON><PERSON> cập nhật để sử dụng hệ thống theme tập trung từ ThemeContext
// Thêm section hiển thị giới hạn chi tiêu
// File này liên quan đến: context/ThemeContext.tsx, lib/services/SpendingLimitService.ts, components/LimitProgressBar.tsx

import { LimitProgressBar } from "@/components/LimitProgressBar";
import TabScreenContainer from "@/components/TabScreenContainer";
import { Colors } from "@/constants/Colors";
import { BankLogos, isImageIcon } from "@/constants/WalletData"; // Cập nhật import từ WalletData.ts
import { useAuth } from "@/context/AuthContext";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Category, CategoryModel } from "@/lib/models/category";
import { TransactionModel, type Transaction } from "@/lib/models/transaction";
import { UserModel, UserProfile } from "@/lib/models/user";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { WalletPositionModel } from "@/lib/models/wallet_position"; // Thêm import WalletPositionModel
import { LimitUsage, SpendingLimitService } from "@/lib/services/SpendingLimitService";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { getIconForWalletType } from "@/utils/iconHelper";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage"; // Thêm import AsyncStorage
import { Image } from "expo-image"; // Thêm import Image từ expo-image
import { router, useFocusEffect } from "expo-router";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import {
  ActivityIndicator,
  PixelRatio,
  Platform,
  Pressable,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Định nghĩa các chế độ hiển thị ví
enum WalletDisplayMode {
  SCROLL = "scroll", // Cuộn ngang (mặc định)
  GRID = "grid", // Dạng lưới
  LIST = "list", // Danh sách
}

// Khóa lưu trữ cho AsyncStorage
const WALLET_DISPLAY_MODE_KEY = "wallet_display_mode";
const HOME_MAX_WALLETS_KEY = "home_max_wallets";
const HOME_MAX_TRANSACTIONS_KEY = "home_max_transactions";
const SHOW_SPENDING_LIMITS_KEY = 'show_spending_limits_on_home';
const HOME_SPENDING_LIMITS_COUNT_KEY = 'home_spending_limits_count';

export default function HomeScreen() {
  const { top } = useSafeAreaInsets();
  const { currency, symbol } = useCurrency();
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null); // null means all wallets
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [monthlyStats, setMonthlyStats] = useState({ income: 0, expense: 0 });
  const [hideBalance, setHideBalance] = useState(false);
  const { user, avatar } = useAuth();
  // Sử dụng theme mới từ ThemeContext với các helpers
  const {
    isDark,
    themeColors,
    getCardStyle,
    getIconContainerStyle,
    getShadowStyle,
  } = useTheme();
  const { t, locale } = useLocalization();
  const translateCategoryName = useCategoryTranslation();
  const scrollViewRef = useRef(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);

  // State cho chế độ hiển thị ví
  const [walletDisplayMode, setWalletDisplayMode] = useState<WalletDisplayMode>(
    WalletDisplayMode.SCROLL
  );

  // Thêm state cho tùy chọn trang chủ
  const [maxWalletsDisplay, setMaxWalletsDisplay] = useState<number | null>(
    null
  );
  const [maxTransactionsDisplay, setMaxTransactionsDisplay] = useState<
    number | null
  >(null);

  // State cho spending limits
  const [showSpendingLimits, setShowSpendingLimits] = useState(false);
  const [spendingLimitsCount, setSpendingLimitsCount] = useState(5);
  const [spendingLimitsData, setSpendingLimitsData] = useState<(
    LimitUsage & { categoryName: string; categoryColor: string; categoryIcon: string }
  )[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  // Use refs to track data freshness and last update time
  const dataTimestampRef = useRef<{
    profile: number;
    wallets: number;
    transactions: number;
    stats: number;
    spendingLimits: number;
  }>({
    profile: 0,
    wallets: 0,
    transactions: 0,
    stats: 0,
    spendingLimits: 0,
  });

  // Hàm tải cài đặt tùy chọn trang chủ từ AsyncStorage
  const loadHomeSettings = useCallback(async () => {
    try {
      const savedMaxWallets = await AsyncStorage.getItem(HOME_MAX_WALLETS_KEY);
      const savedMaxTransactions = await AsyncStorage.getItem(
        HOME_MAX_TRANSACTIONS_KEY
      );

      // Kiểm tra chuỗi có hợp lệ không trước khi parse
      if (savedMaxWallets) {
        const parsedValue = parseInt(savedMaxWallets);
        setMaxWalletsDisplay(isNaN(parsedValue) ? null : parsedValue);
      } else {
        setMaxWalletsDisplay(null);
      }

      if (savedMaxTransactions) {
        const parsedValue = parseInt(savedMaxTransactions);
        setMaxTransactionsDisplay(isNaN(parsedValue) ? 10 : parsedValue);
      } else {
        setMaxTransactionsDisplay(10);
      }

      // Load spending limits settings
      const savedShow = await AsyncStorage.getItem(SHOW_SPENDING_LIMITS_KEY);
      const savedCount = await AsyncStorage.getItem(HOME_SPENDING_LIMITS_COUNT_KEY);
      
      if (savedShow !== null) {
        setShowSpendingLimits(JSON.parse(savedShow));
      }
      if (savedCount !== null) {
        setSpendingLimitsCount(parseInt(savedCount));
      }
    } catch (error) {
      console.error("Error loading home settings:", error);
      // Sử dụng giá trị mặc định nếu không thể tải cài đặt
      setMaxWalletsDisplay(null);
      setMaxTransactionsDisplay(10);
      setShowSpendingLimits(false);
      setSpendingLimitsCount(5);
    }
  }, []);

  // Đọc chế độ hiển thị ví từ AsyncStorage khi khởi động
  useEffect(() => {
    const loadWalletDisplayMode = async () => {
      try {
        const savedMode = await AsyncStorage.getItem(WALLET_DISPLAY_MODE_KEY);
        if (savedMode) {
          setWalletDisplayMode(savedMode as WalletDisplayMode);
        }
      } catch (error) {
        console.error("Error loading wallet display mode:", error);
      }
    };

    loadWalletDisplayMode();
  }, []);

  // Lưu chế độ hiển thị ví khi thay đổi
  useEffect(() => {
    const saveWalletDisplayMode = async () => {
      try {
        await AsyncStorage.setItem(WALLET_DISPLAY_MODE_KEY, walletDisplayMode);
      } catch (error) {
        console.error("Error saving wallet display mode:", error);
      }
    };

    saveWalletDisplayMode();
  }, [walletDisplayMode]);

  // Load profile data và cài đặt khi màn hình được focus
  useFocusEffect(
    useCallback(() => {
      const loadProfileAndSettings = async () => {
        try {
          const userProfile = await UserModel.getCurrentProfile();
          setProfile(userProfile);
        } catch (error) {
          console.error("Error loading profile or settings:", error);
        }
      };

      loadProfileAndSettings();
    }, [])
  );

  // Hàm chuyển đổi chế độ hiển thị ví
  const toggleWalletDisplayMode = useCallback(() => {
    setWalletDisplayMode((prevMode) => {
      switch (prevMode) {
        case WalletDisplayMode.SCROLL:
          return WalletDisplayMode.GRID;
        case WalletDisplayMode.GRID:
          return WalletDisplayMode.LIST;
        case WalletDisplayMode.LIST:
          return WalletDisplayMode.SCROLL;
        default:
          return WalletDisplayMode.SCROLL;
      }
    });
  }, []);

  // Determine if data needs refresh (after 60 seconds)
  const needsRefresh = (
    type: "profile" | "wallets" | "transactions" | "stats" | "spendingLimits"
  ) => {
    const now = Date.now();
    const lastUpdate = dataTimestampRef.current[type];
    return now - lastUpdate > 60000; // 1 minute cache
  };

  // Colors based on theme - đảm bảo tương thích với theme cũ
  const systemTheme = Colors[isDark ? "dark" : "light"];

  // Create a theme object with proper typing
  const extendedTheme = {
    ...systemTheme,
    tsDark: isDark ? themeColors.background : "white",
  };

  // Đảm bảo tint luôn có giá trị mặc định
  const themeTint = extendedTheme.tint || (isDark ? "#007AFF" : "#007AFF");

  // Sử dụng useMemo để tính toán các giá trị màu sắc từ themeColors
  const colors = useMemo(
    () => ({
      bgColor: themeColors.background,
      headerBgColor: themeColors.background,
      cardBgColor: themeColors.cardBackground,
      textColor: themeColors.text,
      cardTextColor: themeColors.text,
      accentColor: themeColors.primary,
      errorColor: themeColors.danger,
      successColor: isDark ? "#81C784" : "#4CAF50",
      secondaryTextColor: themeColors.secondaryText,
      hintColor: isDark ? "#7CB9F8" : "#90CAF9",
      borderColor: themeColors.border,
      shadowColor: themeColors.shadowColor,
      shadowOpacity: isDark ? 0.3 : 0.2,
      transparentBgDark: isDark ? "rgba(255,255,255,0.12)" : "rgba(0,0,0,0.05)",
      transparentBgLight: isDark
        ? "rgba(255,255,255,0.08)"
        : "rgba(0,0,0,0.03)",
      buttonPrimary: themeColors.primary,
      buttonSecondary: themeColors.cardBackground,
      starColor: "#FFB700", // Màu cho icon ngôi sao
    }),
    [isDark, themeColors]
  );

  // Fetch categories (expense) on mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const cats = await CategoryModel.getByType("expense");
        setCategories(cats);
      } catch (error) {
        console.error("Error loading categories:", error);
      }
    };
    fetchCategories();
  }, []);

  // Function to fetch spending limits data
  const fetchSpendingLimits = useCallback(async () => {
    if (!showSpendingLimits) return;

    try {
      // Lấy tất cả usage data
      const allUsages = await SpendingLimitService.calculateAllUsages();
      // Map thêm thông tin category
      const usagesWithCategory = allUsages.map((usage) => {
        const cat = categories.find((c) => c.id === usage.categoryId);
        return {
          ...usage,
          categoryName: cat?.name || "",
          categoryColor: cat?.color || "#ccc",
          categoryIcon: cat?.icon || "pricetag-outline",
        };
      });
      // Sắp xếp theo % và trạng thái như cũ
      const sortedUsages = usagesWithCategory.sort((a, b) => {
        const priorityA = a.status === 'exceeded' ? 4 : a.status === 'danger' ? 3 : a.status === 'warning' ? 2 : 1;
        const priorityB = b.status === 'exceeded' ? 4 : b.status === 'danger' ? 3 : b.status === 'warning' ? 2 : 1;
        if (priorityA !== priorityB) {
          return priorityB - priorityA;
        }
        return b.percentage - a.percentage;
      });
      // Áp dụng giới hạn số lượng
      const limitedUsages = spendingLimitsCount === 0 
        ? sortedUsages 
        : sortedUsages.slice(0, spendingLimitsCount);
      setSpendingLimitsData(limitedUsages);
      dataTimestampRef.current.spendingLimits = Date.now();
    } catch (error) {
      console.error("Error fetching spending limits:", error);
      setSpendingLimitsData([]);
    }
  }, [showSpendingLimits, spendingLimitsCount, categories]);

  // Function to fetch all data
  const fetchData = useCallback(
    async (forceRefresh = false) => {
      try {
        // Only show loading indicator on first load
        if (
          wallets.length === 0 &&
          recentTransactions.length === 0 &&
          !refreshing
        ) {
          setIsLoading(true);
        }

        const refreshWallets =
          forceRefresh || wallets.length === 0 || needsRefresh("wallets");
        const refreshTransactions =
          forceRefresh ||
          recentTransactions.length === 0 ||
          needsRefresh("transactions");
        const refreshStats = forceRefresh || needsRefresh("stats");
        const refreshSpendingLimits = 
          forceRefresh || needsRefresh("spendingLimits");

        // Only fetch wallets if needed
        if (refreshWallets) {
          // Lấy danh sách ví
          const walletsData = await WalletModel.getAll();

          // Lấy thứ tự vị trí từ bảng wallet_positions
          let walletPositions = [];
          try {
            walletPositions = await WalletPositionModel.getAll();
          } catch (error) {
            console.log("Không có dữ liệu vị trí hoặc lỗi:", error);
          }

          // Tạo map để tra cứu vị trí nhanh
          const positionMap = {};
          walletPositions.forEach((pos) => {
            positionMap[pos.wallet_id] = pos.position;
          });

          // Sắp xếp ví theo vị trí từ bảng wallet_positions (nếu có)
          // Nếu không có vị trí thì sắp xếp theo thời gian tạo (mặc định)
          const sortedWallets = [...walletsData].sort((a, b) => {
            const posA =
              positionMap[a.id] !== undefined ? positionMap[a.id] : 999;
            const posB =
              positionMap[b.id] !== undefined ? positionMap[b.id] : 999;
            if (posA !== 999 || posB !== 999) {
              return posA - posB;
            }
            // Sắp xếp theo thời gian tạo nếu không có vị trí (mới nhất lên đầu)
            return (
              new Date(b.created_at).getTime() -
              new Date(a.created_at).getTime()
            );
          });

          setWallets(sortedWallets);
          dataTimestampRef.current.wallets = Date.now();
        }

        // Only fetch transactions if needed
        if (refreshTransactions) {
          // Áp dụng giới hạn số lượng giao dịch từ cài đặt
          const limit =
            maxTransactionsDisplay !== null ? maxTransactionsDisplay : 10;
          const transactionsData = await TransactionModel.getRecent(limit);
          setRecentTransactions(transactionsData);
          dataTimestampRef.current.transactions = Date.now();
        }

        // Only fetch stats if needed
        if (refreshStats) {
          // Get current month and year
          const now = new Date();
          const currentMonth = now.getMonth() + 1;
          const currentYear = now.getFullYear();

          // Fetch monthly statistics
          const stats = await TransactionModel.getMonthlyStats(
            currentMonth,
            currentYear
          );
          setMonthlyStats(stats);
          dataTimestampRef.current.stats = Date.now();
        }

        // Fetch spending limits if enabled and needed
        if (showSpendingLimits && refreshSpendingLimits) {
          await fetchSpendingLimits();
        }
      } catch (error) {
        console.error("Error fetching home data:", error);
      } finally {
        setIsLoading(false);
        setRefreshing(false);
      }
    },
    [
      refreshing,
      wallets.length,
      recentTransactions.length,
      maxTransactionsDisplay,
      showSpendingLimits,
      fetchSpendingLimits,
    ]
  );

  // Handle pull-to-refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchData(true);
  }, [fetchData]);

  // Fetch data using useFocusEffect to refresh when screen is focused
  useFocusEffect(
    useCallback(() => {
      // Luôn tải lại dữ liệu giao dịch khi màn hình được focus
      // Đặt forceRefresh=true để đảm bảo dữ liệu luôn được cập nhật sau khi thêm giao dịch mới
      fetchData(true);
      loadHomeSettings(); // Thêm dòng này để tải cài đặt tùy chọn trang chủ
      return () => {};
    }, [fetchData, loadHomeSettings])
  );

  // Calculate total balance
  const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);

  // Hàm để ẩn/hiện số dư
  const toggleHideBalance = useCallback(() => {
    setHideBalance((prev) => !prev);
  }, []);

  // Format số tiền
  const formatCurrency = useCallback(
    (amount: number) => {
      if (hideBalance) {
        return "•••••••";
      }
      return amount.toLocaleString() + symbol;
    },
    [hideBalance, symbol]
  );

  // Function to get the display name from the user profile
  const displayName =
    profile?.full_name || user?.user_metadata?.full_name || "User";

  // Get wallet icon based on type
  const getWalletIcon = useCallback((type: string): string => {
    return getIconForWalletType(type);
  }, []);

  // Render spending limits section
  const renderSpendingLimitsSection = useCallback(() => {
    if (!showSpendingLimits || spendingLimitsData.length === 0) {
      return null;
    }

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons
              name="speedometer-outline"
              size={20}
              color={themeColors.primary}
              style={{ marginRight: 8 }}
            />
            <Text
              style={{
                fontSize: 18 * scale,
                fontWeight: "600",
                color: themeColors.text,
              }}
            >
              Giới hạn chi tiêu
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.viewAllButton,
              {
                backgroundColor: isDark
                  ? "rgba(255,255,255,0.1)"
                  : "rgba(0,122,255,0.1)",
              },
            ]}
            onPress={() => router.push("/spending-limits")}
          >
            <Text
              style={{
                color: themeColors.primary,
                fontWeight: "600",
                fontSize: 13,
              }}
            >
              Xem tất cả
            </Text>
            <Ionicons
              name="chevron-forward"
              size={13}
              color={themeColors.primary}
            />
          </TouchableOpacity>
        </View>

        {/* Hiển thị thông tin số lượng nếu có giới hạn */}
        {spendingLimitsCount > 0 && spendingLimitsData.length === spendingLimitsCount && (
          <Text
            style={[
              styles.limitInfoText,
              {
                color: themeColors.secondaryText,
                marginHorizontal: 16,
                marginBottom: 8,
              },
            ]}
          >
            Hiển thị {spendingLimitsCount} danh mục có mức độ ưu tiên cao nhất
          </Text>
        )}

        {/* Danh sách spending limits */}
        <View style={styles.spendingLimitsList}>
          {spendingLimitsData.map((usage) => (
            <View key={usage.categoryId} style={[styles.categoryItem, getCardStyle('medium')]}> 
              {/* Category info */}
              <View style={[styles.categoryHeader, { backgroundColor: 'transparent' }]}> 
                <View style={[styles.categoryInfo, { backgroundColor: 'transparent' }]}> 
                  <View
                    style={[
                      styles.categoryIcon,
                      { backgroundColor: usage.categoryColor },
                    ]}
                  >
                    <Ionicons name={usage.categoryIcon as any} size={24} color="white" />
                  </View>
                  <View style={{ backgroundColor: 'transparent', flex: 1 }}>
                    <Text style={[styles.categoryName, { color: themeColors.text }]}> 
                      {translateCategoryName(usage.categoryName)}
                    </Text>
                    <Text style={[styles.periodText, { color: themeColors.secondaryText }]}> 
                      Giới hạn {SpendingLimitService.formatPeriod(usage.period).toLowerCase()}
                    </Text>
                  </View>
                </View>
              </View>
              {/* Progress bar */}
              <View style={{ backgroundColor: 'transparent', marginTop: 12 }}>
                <LimitProgressBar
                  usage={usage}
                  size="medium"
                  showDetails={true}
                />
              </View>
            </View>
          ))}
        </View>

        {/* Empty state khi không có limits */}
        {spendingLimitsData.length === 0 && (
          <View
            style={[
              styles.emptyContainer,
              getCardStyle("medium"),
              { borderRadius: 16, marginHorizontal: 16 },
            ]}
          >
            <View style={[styles.emptyIconContainer, getIconContainerStyle()]}> 
              <Ionicons
                name="speedometer-outline"
                size={32}
                color={themeColors.primary}
              />
            </View>
            <Text style={[styles.emptyText, { color: themeColors.text }]}> 
              Chưa có giới hạn chi tiêu nào
            </Text>
            <TouchableOpacity
              style={[
                styles.emptyButton,
                { backgroundColor: themeColors.primary, borderRadius: 12 },
              ]}
              onPress={() => router.push("/spending-limits")}
            >
              <Ionicons
                name="speedometer-outline"
                size={16}
                color="#FFFFFF"
                style={{ marginRight: 6 }}
              />
              <Text style={styles.emptyButtonText}> 
                Đặt giới hạn chi tiêu
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  }, [
    showSpendingLimits,
    spendingLimitsData,
    spendingLimitsCount,
    themeColors,
    isDark,
    router,
    getCardStyle,
    getIconContainerStyle,
    translateCategoryName,
  ]);

  // Render một card ví (được sử dụng lại cho cả 3 chế độ hiển thị)
  const renderWalletCard = useCallback(
    (wallet: Wallet, index: number, customStyles = {}) => {
      const borderLeftColorWallet =
        selectedWallet === wallet.id ? wallet.color : themeColors.border;

      const scaleTransform =
        Platform.OS === "ios"
          ? [{ scale: selectedWallet === wallet.id ? 1.03 : 1 }]
          : undefined;

      // Kiểm tra xem đây có phải là chế độ LIST không để điều chỉnh layout
      const isList = walletDisplayMode === WalletDisplayMode.LIST;

      // Xác định xem ví có phải là ví mặc định hay không
      const isDefault = wallet.is_default === true;

      // Sử dụng hàm isImageIcon từ WalletData.ts
      const isIconImage = isImageIcon(wallet.icon);

      return (
        <TouchableOpacity
          key={wallet.id}
          style={[
            styles.walletCard,
            getCardStyle("medium"),
            {
              borderLeftWidth: 3,
              borderLeftColor: borderLeftColorWallet,
              transform: scaleTransform,
            },
            customStyles, // Áp dụng các styles tùy chỉnh
          ]}
          onPress={() => {
            setSelectedWallet(wallet.id);
            router.push(`/wallet/${wallet.id}`);
          }}
          activeOpacity={0.7}
        >
          {/* Icon ngôi sao cho ví mặc định - đã chuyển sang góc phải */}
          {isDefault && (
            <View style={styles.defaultBadge}>
              <Ionicons name="star" size={14} color="white" />
            </View>
          )}

          <View
            style={[
              styles.walletContent,
              isList && {
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              },
            ]}
          >
            <View style={[styles.walletHeader, isList && { flex: 1 }]}>
              <View
                style={[
                  styles.walletIconContainer,
                  {
                    backgroundColor: wallet.color,
                    shadowColor: wallet.color,
                    shadowOpacity: colors.shadowOpacity,
                  },
                  isList && { width: 40, height: 40 },
                ]}
              >
                {isIconImage ? (
                  // Hiển thị logo ngân hàng/ví điện tử nếu có
                  <Image
                    source={BankLogos[wallet.icon]}
                    style={[
                      styles.bankLogo,
                      isList && { width: 24, height: 24 },
                    ]}
                    contentFit="contain"
                  />
                ) : (
                  // Hiển thị icon thông thường nếu không phải logo ngân hàng
                  <Ionicons
                    name={getWalletIcon(wallet.type) as any}
                    size={isList ? 24 : 20}
                    color="white"
                  />
                )}
              </View>
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <Text
                  style={[
                    styles.walletNameText,
                    { color: themeColors.text },
                    isList && { fontSize: 16 * scale },
                  ]}
                  numberOfLines={1}
                >
                  {wallet.name}
                </Text>

                {/* Icon ngôi sao nhỏ bên cạnh tên ví (tuỳ chọn thay thế) */}
                {isDefault && (
                  <Ionicons
                    name="star"
                    size={12}
                    color={colors.starColor}
                    style={styles.nameStarIcon}
                  />
                )}
              </View>
            </View>

            <Text
              style={[
                styles.walletBalanceText,
                // Điều chỉnh màu sắc dựa trên số dư thay vì màu của ví
                {
                  color:
                    wallet.balance >= 0
                      ? colors.successColor
                      : colors.errorColor,
                },
                isList && { marginTop: 0, fontSize: 18 * scale },
              ]}
              numberOfLines={1}
            >
              {formatCurrency(wallet.balance)}
            </Text>
          </View>
        </TouchableOpacity>
      );
    },
    [
      colors,
      formatCurrency,
      getWalletIcon,
      router,
      selectedWallet,
      walletDisplayMode,
      themeColors,
      getCardStyle,
    ]
  );

  // Render danh sách ví dựa trên chế độ hiển thị
  const renderWalletSection = useCallback(() => {
    // Hiển thị hình ảnh cho nút chuyển đổi chế độ dựa trên chế độ hiện tại
    const getDisplayModeIcon = () => {
      switch (walletDisplayMode) {
        case WalletDisplayMode.SCROLL:
          return "grid-outline"; // Biểu tượng cho chế độ kế tiếp là GRID
        case WalletDisplayMode.GRID:
          return "list-outline"; // Biểu tượng cho chế độ kế tiếp là LIST
        case WalletDisplayMode.LIST:
          return "swap-horizontal-outline"; // Biểu tượng cho chế độ kế tiếp là SCROLL
        default:
          return "grid-outline";
      }
    };

    // Áp dụng giới hạn số lượng ví từ cài đặt
    const displayWallets =
      maxWalletsDisplay && maxWalletsDisplay > 0
        ? wallets.slice(0, maxWalletsDisplay)
        : wallets;

    // Tính toán một lần tổng số ví
    const totalWalletCount = wallets.length;

    // Hiển thị thông báo "Đang hiển thị X/Y ví" nếu đang có giới hạn
    const showLimitInfo =
      maxWalletsDisplay &&
      maxWalletsDisplay > 0 &&
      totalWalletCount > maxWalletsDisplay;

    return (
      <View style={[styles.section, { marginTop: 16 }]}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionTitleContainer}>
            <Ionicons
              name="wallet-outline"
              size={20}
              color={themeColors.primary}
              style={{ marginRight: 8 }}
            />
            <Text
              style={{
                fontSize: 18 * scale,
                fontWeight: "600",
				color: themeColors.text,
             }}
           >
             {t("home.myWallets")}
           </Text>

           {/* Nút chuyển đổi chế độ hiển thị */}
           <TouchableOpacity
             style={[
               styles.displayModeButton,
               {
                 backgroundColor: isDark
                   ? "rgba(255,255,255,0.1)"
                   : "rgba(0,122,255,0.1)",
               },
             ]}
             onPress={toggleWalletDisplayMode}
           >
             <Ionicons
               name={getDisplayModeIcon()}
               size={18}
               color={themeColors.primary}
             />
           </TouchableOpacity>
         </View>

         <TouchableOpacity
           style={[
             styles.viewAllButton,
             {
               backgroundColor: isDark
                 ? "rgba(255,255,255,0.1)"
                 : "rgba(0,122,255,0.1)",
             },
           ]}
           onPress={() => router.push("/wallet")}
         >
           <Text
             style={{
               color: themeColors.primary,
               fontWeight: "600",
               fontSize: 13,
             }}
           >
             {t("home.viewAll")}
           </Text>
           <Ionicons
             name="chevron-forward"
             size={13}
             color={themeColors.primary}
           />
         </TouchableOpacity>
       </View>

       {/* Hiển thị thông báo giới hạn ví nếu có */}
       {showLimitInfo && (
         <Text
           style={[
             styles.limitInfoText,
             {
               color: themeColors.secondaryText,
               marginHorizontal: 16,
               marginBottom: 8,
             },
           ]}
         >
           {t("home.displayingXofYWallets", {
             x: maxWalletsDisplay,
             y: totalWalletCount,
           }) || `Đang hiển thị ${maxWalletsDisplay}/${totalWalletCount} ví`}
         </Text>
       )}

       {/* Hiển thị ví theo chế độ đã chọn */}
       {walletDisplayMode === WalletDisplayMode.SCROLL && (
         <ScrollView
           horizontal
           showsHorizontalScrollIndicator={false}
           style={styles.walletScroll}
           contentContainerStyle={{ paddingRight: 16 }}
         >
           {displayWallets.map((wallet, index) =>
             renderWalletCard(wallet, index)
           )}
         </ScrollView>
       )}

       {walletDisplayMode === WalletDisplayMode.GRID && (
         <View style={styles.walletGrid}>
           {displayWallets.map((wallet, index) =>
             renderWalletCard(wallet, index, {
               width: "48%", // Chiều rộng cho hiển thị dạng lưới
               marginRight: index % 2 === 0 ? "2%" : 0,
               marginLeft: index % 2 === 0 ? 0 : "2%",
               marginBottom: 12,
             })
           )}
         </View>
       )}

       {walletDisplayMode === WalletDisplayMode.LIST && (
         <View style={styles.walletList}>
           {displayWallets.map((wallet, index) =>
             renderWalletCard(wallet, index, {
               width: "100%", // Chiều rộng đầy đủ cho hiển thị dạng danh sách
               marginBottom: 12,
               height: 100, // Chiều cao dài hơn theo yêu cầu
               paddingVertical: 16, // Thêm padding để nội dung không bị chật
             })
           )}
         </View>
       )}
     </View>
   );
 }, [
   wallets,
   walletDisplayMode,
   colors,
   isDark,
   renderWalletCard,
   router,
   t,
   toggleWalletDisplayMode,
   maxWalletsDisplay,
   themeColors,
 ]);

 // Hàm nhóm các giao dịch theo ngày
 const groupTransactionsByDate = useCallback((transactions: Transaction[]) => {
   const groups: Record<string, Transaction[]> = {};

   transactions.forEach((transaction) => {
     const date = new Date(transaction.date);
     const year = date.getFullYear();
     const month = date.getMonth() + 1;
     const day = date.getDate();

     const dateKey = `${year}-${month.toString().padStart(2, "0")}-${day
       .toString()
       .padStart(2, "0")}`;

     if (!groups[dateKey]) {
       groups[dateKey] = [];
     }

     groups[dateKey].push(transaction);
   });

   return Object.keys(groups)
     .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
     .map((dateKey) => {
       return {
         date: dateKey,
         transactions: groups[dateKey],
       };
     });
 }, []);

 // Hàm định dạng ngày hiển thị theo dạng thân thiện
 const formatDateHeader = useCallback(
   (dateString: string) => {
     const date = new Date(dateString);
     const today = new Date();
     const yesterday = new Date();
     yesterday.setDate(yesterday.getDate() - 1);

     if (
       date.getDate() === today.getDate() &&
       date.getMonth() === today.getMonth() &&
       date.getFullYear() === today.getFullYear()
     ) {
       return t("dateTime.today");
     }

     if (
       date.getDate() === yesterday.getDate() &&
       date.getMonth() === yesterday.getMonth() &&
       date.getFullYear() === yesterday.getFullYear()
     ) {
       return t("dateTime.yesterday");
     }

     const options: Intl.DateTimeFormatOptions = {
       weekday: "long",
       year: "numeric",
       month: "long",
       day: "numeric",
     };
     return date.toLocaleDateString(
       locale === "vi" ? "vi-VN" : "en-US",
       options
     );
   },
   [t, locale]
 );

 // Hàm định dạng thời gian
 const formatTime = useCallback((date: Date) => {
   const hours = date.getHours().toString().padStart(2, "0");
   const minutes = date.getMinutes().toString().padStart(2, "0");
   return `${hours}:${minutes}`;
 }, []);

 // Hàm điều hướng đến chi tiết giao dịch
 const navigateToTransaction = useCallback(
   (transactionId: string) => {
     router.push(`/transaction/${transactionId}`);
   },
   [router]
 );

 // Render một transaction item đơn giản (đã loại bỏ tính năng chạm giữ)
 const TransactionItem = useCallback(
   ({ transaction }: { transaction: Transaction }) => {
     // Lấy thông tin giao dịch
     const walletName = transaction.wallet?.name || transaction?.description;
     const categoryName =
       transaction.category?.name || transaction?.description;
     const translatedCategoryName = translateCategoryName(categoryName);

     // Xác định màu sắc dựa vào loại giao dịch
     let iconBackgroundColor =
       transaction.amount > 0 ? colors.successColor : colors.errorColor;
     let borderLeftColor =
       transaction.amount > 0 ? colors.successColor : colors.errorColor;

     if (transaction.category?.color) {
       iconBackgroundColor = transaction.category.color;
       borderLeftColor = transaction.category.color;
     }

     return (
       <Pressable
         style={[
           styles.transactionItem,
           getCardStyle("medium"),
           {
             borderLeftWidth: 3,
             borderLeftColor: borderLeftColor,
           },
         ]}
         onPress={() => navigateToTransaction(transaction.id)}
       >
         <View style={styles.transactionInner}>
           {/* Icon */}
           <View
             style={[
               styles.categoryIcon,
               { backgroundColor: iconBackgroundColor },
             ]}
           >
             <Ionicons
               name={
                 (transaction.category?.icon || "help-circle-outline") as any
               }
               size={18}
               color="white"
             />
           </View>

           {/* Content */}
           <View style={styles.transactionDetails}>
             <View style={{ flex: 1, gap: 8 }}>
               {/* Hiển thị ghi chú thay vì danh mục ở vị trí chính */}
               <Text
                 style={{
                   fontSize: 16 * scale,
                   fontWeight: "700",
                   color: themeColors.text,
                   includeFontPadding: false,
                   letterSpacing: 0.2,
                 }}
                 ellipsizeMode="tail"
                 numberOfLines={1}
               >
                 {transaction?.description || translatedCategoryName}
               </Text>

               {/* Hiển thị danh mục và thời gian ở dòng thứ 2 */}
               <View style={styles.metadataRow}>
                 {/* Hiển thị danh mục */}
                 <Text
                   style={[
                     styles.categoryText,
                     {
                       color: themeColors.secondaryText,
                       fontSize: 12 * scale,
                     },
                   ]}
                   numberOfLines={1}
                 >
                   {translatedCategoryName}
                 </Text>

                 {/* Thêm một dấu chấm tròn nhỏ để phân cách */}
                 <Text
                   style={{
                     color: themeColors.secondaryText,
                     fontSize: 12 * scale,
                     marginHorizontal: 4,
                   }}
                 >
                   •
                 </Text>

                 {/* Hiển thị thời gian */}
                 <Text
                   style={[
                     styles.timeText,
                     {
                       color: themeColors.secondaryText,
                       fontSize: 12 * scale,
                     },
                   ]}
                 >
                   {formatTime(new Date(transaction.date))}
                 </Text>
               </View>
             </View>

             <View style={styles.amountContainer}>
               <Text
                 style={[
                   styles.amountText,
                   {
                     color:
                       transaction.amount > 0
                         ? colors.successColor
                         : colors.errorColor,
                   },
                 ]}
               >
                 {(transaction.amount > 0 ? "+" : "") +
                   Math.abs(transaction.amount).toLocaleString() +
                   symbol}
               </Text>
               <Text
                 style={[
                   styles.walletText,
                   { color: themeColors.secondaryText },
                 ]}
               >
                 {walletName}
               </Text>
             </View>
           </View>
         </View>
       </Pressable>
     );
   },
   [
     colors,
     formatTime,
     translateCategoryName,
     navigateToTransaction,
     symbol,
     themeColors,
     getCardStyle,
   ]
 );

 // Tối ưu render cho loading state
 if (isLoading && wallets.length === 0 && recentTransactions.length === 0) {
   return (
     <TabScreenContainer>
       <View
         style={{
           flex: 1,
           justifyContent: "center",
           alignItems: "center",
           backgroundColor: themeColors.background,
         }}
       >
         <ActivityIndicator size="large" color={themeColors.primary} />
       </View>
     </TabScreenContainer>
   );
 }

 return (
   <ScrollView
     ref={scrollViewRef}
     refreshControl={
       <RefreshControl
         refreshing={refreshing}
         onRefresh={onRefresh}
         colors={[themeColors.primary]}
         tintColor={themeColors.primary}
       />
     }
     style={[
       styles.scrollView,
       { backgroundColor: themeColors.background, paddingTop: top },
     ]}
     contentContainerStyle={{ paddingBottom: 120 }}
     showsVerticalScrollIndicator={false}
     // Thêm các thuộc tính để đảm bảo scroll hoạt động tốt trên mọi thiết bị
     scrollEventThrottle={16}
     bounces={true}
     alwaysBounceVertical={true}
   >
     {/* Header - Có thể cuộn */}
     <View
       style={[
         styles.headerContainer,
         {
           backgroundColor: themeColors.background,
         },
       ]}
     >
       <View style={styles.headerTop}>
         <View>
           <Text
             style={{
               fontSize: 16 * scale,
               color: themeColors.text,
             }}
           >
             {t("home.greeting")}
           </Text>
           <Text
             style={{
               fontSize: 20 * scale,
               fontWeight: "bold",
               color: themeColors.text,
             }}
           >
             {displayName}
           </Text>
         </View>
         <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
           <TouchableOpacity
             style={[
               styles.settingsButton,
               getIconContainerStyle(),
               { ...getShadowStyle("low") },
             ]}
             onPress={() => router.push("/(app)/home-settings")}
           >
             <Ionicons name="settings-outline" size={22} color={themeColors.primary} />
           </TouchableOpacity>
           <TouchableOpacity
             style={[
               styles.profileButton,
               getIconContainerStyle(),
               { ...getShadowStyle("low") },
             ]}
             onPress={() => router.push({ pathname: "/(app)/profile" })}
           >
             {isLoading ? (
               <ActivityIndicator size="small" color={themeColors.primary} />
             ) : avatar ? (
               <Image
                 source={{ uri: avatar }}
                 style={styles.profileImage}
                 contentFit="cover"
               />
             ) : (
               <Ionicons name="person" size={28} color={themeColors.primary} />
             )}
           </TouchableOpacity>
         </View>
       </View>

       {/* Card số dư tổng */}
       <View
         style={[styles.totalCard, getCardStyle("high"), { borderRadius: 16 }]}
       >
         <View style={styles.totalLabelContainer}>
           <Text
             style={{
               fontSize: 14 * scale,
               fontWeight: "500",
               color: themeColors.secondaryText,
               marginRight: 6,
             }}
           >
             {t("home.yourBalance")}
           </Text>
           <TouchableOpacity
             onPress={toggleHideBalance}
             style={styles.eyeButton}
           >
             <Ionicons
               name={hideBalance ? "eye-off-outline" : "eye-outline"}
               size={18}
               color={themeColors.secondaryText}
             />
           </TouchableOpacity>
         </View>

         <View style={styles.balanceContainer}>
           <TouchableOpacity
             onPress={() => router.push("/wallet")}
             style={{ alignItems: "center" }}
           >
             <Text
               style={{
                 fontSize: 22 * scale,
                 fontWeight: "700",
                 textAlign: "center",
                 includeFontPadding: false,
                 color: isDark ? themeColors.text : themeColors.primary,
               }}
             >
               {formatCurrency(totalBalance)}
             </Text>
           </TouchableOpacity>
         </View>

         <View
           style={[
             styles.categoryStats,
             {
               borderTopColor: themeColors.border,
             },
           ]}
         >
           <TouchableOpacity
             style={styles.statItem}
             onPress={() => router.push("/category?type=expense")}
           >
             <Text
               style={{
                 fontSize: 13 * scale,
                 color: colors.successColor,
               }}
             >
               {t("home.thisMonthIncome")}
             </Text>
             <Text
               style={{
                 fontSize: 17 * scale,
                 fontWeight: "bold",
                 color: colors.successColor,
               }}
             >
               {hideBalance
                 ? "•••••••"
                 : "+" + monthlyStats.income.toLocaleString() + symbol}
             </Text>
           </TouchableOpacity>

           <TouchableOpacity
             style={styles.statItem}
             onPress={() => router.push("/category?type=income")}
           >
             <Text
               style={{
                 fontSize: 13 * scale,
                 color: colors.errorColor,
               }}
             >
               {t("home.thisMonthExpense")}
             </Text>
             <Text
               style={{
                 fontSize: 17 * scale,
                 fontWeight: "bold",
                 color: colors.errorColor,
               }}
             >
               {hideBalance
                 ? "•••••••"
                 : "-" + monthlyStats.expense.toLocaleString() + symbol}
             </Text>
           </TouchableOpacity>
         </View>
       </View>
     </View>

     {/* Wallets Section - Đã được cập nhật với 3 chế độ hiển thị */}
     {renderWalletSection()}

     {/* Spending Limits Section - MỚI */}
     {renderSpendingLimitsSection()}

     {/* Recent Transactions */}
     <View style={styles.section}>
       <View style={styles.sectionHeader}>
         <View style={styles.sectionTitleContainer}>
           <Ionicons
             name="time-outline"
             size={20}
             color={themeColors.primary}
             style={{ marginRight: 8 }}
           />
           <Text
             style={{
               fontSize: 18 * scale,
               fontWeight: "600",
               color: themeColors.text,
             }}
           >
             {t("home.recentTransactions")}
           </Text>
         </View>

         <TouchableOpacity
           style={[
             styles.viewAllButton,
             {
               backgroundColor: isDark
                 ? "rgba(255,255,255,0.1)"
                 : "rgba(0,122,255,0.1)",
             },
           ]}
           onPress={() => router.push("/transaction/list")}
         >
           <Text
             style={{
               color: themeColors.primary,
               fontWeight: "600",
               fontSize: 13,
             }}
           >
             {t("home.viewAll")}
           </Text>
           <Ionicons
             name="chevron-forward"
             size={13}
             color={themeColors.primary}
           />
         </TouchableOpacity>
       </View>

       {/* Hiển thị thông báo giới hạn giao dịch nếu có */}
       {maxTransactionsDisplay &&
         maxTransactionsDisplay < 10 &&
         recentTransactions.length === maxTransactionsDisplay && (
           <Text
             style={[
               styles.limitInfoText,
               {
                 color: themeColors.secondaryText,
                 marginHorizontal: 16,
                 marginBottom: 8,
               },
             ]}
           >
             {`Đang hiển thị ${maxTransactionsDisplay} giao dịch gần nhất`}
           </Text>
         )}

       {/* Hiển thị giao dịch theo ngày */}
       {recentTransactions.length === 0 ? (
         <View
           style={[
             styles.emptyContainer,
             getCardStyle("medium"),
             { borderRadius: 16 },
           ]}
         >
           <View style={[styles.emptyIconContainer, getIconContainerStyle()]}>
             <Ionicons
               name="receipt-outline"
               size={40}
               color={themeColors.primary}
             />
           </View>
           <Text style={[styles.emptyText, { color: themeColors.text }]}>
             {t("home.emptyTransactionMessage")}
           </Text>
           <TouchableOpacity
             style={[
               styles.emptyButton,
               { backgroundColor: themeColors.primary, borderRadius: 12 },
             ]}
             onPress={() => router.push("/(tabs)/add")}
           >
             <Ionicons
               name="add-circle-outline"
               size={18}
               color="#FFFFFF"
               style={{ marginRight: 6 }}
             />
             <Text style={styles.emptyButtonText}>
               {t("home.addTransaction")}
             </Text>
           </TouchableOpacity>
         </View>
       ) : (
         <View style={styles.transactionList}>
           {/* Nhóm giao dịch theo ngày */}
           {groupTransactionsByDate(recentTransactions).map((group) => (
             <View key={group.date} style={styles.dayGroup}>
               {/* Header cho mỗi ngày */}
               <View style={styles.dayHeader}>
                 <View
                   style={[
                     styles.dayIndicator,
                     { backgroundColor: themeColors.primary },
                   ]}
                 />
                 <Text
                   style={[
                     styles.dayText,
                     { color: themeColors.secondaryText },
                   ]}
                 >
                   {formatDateHeader(group.date)}
                 </Text>
               </View>

               {/* Danh sách giao dịch trong ngày - đã loại bỏ tính năng chạm giữ */}
               {group.transactions.map((transaction) => (
                 <TransactionItem
                   key={transaction.id}
                   transaction={transaction}
                 />
               ))}
             </View>
           ))}
         </View>
       )}
     </View>
   </ScrollView>
 );
}

const fontScale = PixelRatio.getFontScale();
const scale = Math.min(1, 1 / fontScale);

const styles = StyleSheet.create({
 scrollView: {
   flex: 1,
 },
 headerContainer: {
   paddingBottom: 16,
   borderBottomLeftRadius: 0, // Đã bỏ bo góc để khi toàn trang có cùng màu nền
   borderBottomRightRadius: 0, // Đã bỏ bo góc để khi toàn trang có cùng màu nền
 },
 headerTop: {
   flexDirection: "row",
   justifyContent: "space-between",
   alignItems: "center",
   padding: 16,
   paddingTop: 8,
   paddingBottom: 12,
 },
 profileButton: {
   width: 45,
   height: 45,
   borderRadius: 23,
   justifyContent: "center",
   alignItems: "center",
   overflow: "hidden",
 },
 profileImage: {
   width: "100%",
   height: "100%",
   borderRadius: 999,
 },
 totalCard: {
   marginHorizontal: 16,
   paddingHorizontal: 16,
   paddingVertical: 16, // Tăng padding để card rộng hơn
   alignItems: "center",
   marginBottom: 6,
 },
 totalLabelContainer: {
   flexDirection: "row",
   alignItems: "center",
   marginBottom: 4,
 },
 eyeButton: {
   padding: 2,
 },
 balanceContainer: {
   marginVertical: 8,
   alignItems: "center",
   justifyContent: "center",
 },
 categoryStats: {
   flexDirection: "row",
   width: "100%",
   justifyContent: "space-around",
   borderTopWidth: 1,
   marginTop: 10,
   paddingTop: 12,
 },
 statItem: {
   alignItems: "center",
 },
 section: {
   paddingVertical: 16,
 },
 sectionHeader: {
   flexDirection: "row",
   justifyContent: "space-between",
   alignItems: "center",
   marginHorizontal: 16,
   marginBottom: 12,
 },
 sectionTitleContainer: {
   flexDirection: "row",
   alignItems: "center",
 },
 displayModeButton: {
   width: 32,
   height: 32,
   borderRadius: 16,
   marginLeft: 10,
   justifyContent: "center",
   alignItems: "center",
 },
 viewAllButton: {
   flexDirection: "row",
   alignItems: "center",
   paddingVertical: 4,
   paddingHorizontal: 10,
   borderRadius: 12,
 },
 limitInfoText: {
   fontSize: 12,
   fontStyle: "italic",
   marginBottom: 5,
 },
 // Spending Limits Section Styles
 spendingLimitsList: {
   backgroundColor: 'transparent',
 },
 spendingLimitItem: {
   backgroundColor: 'transparent',
 },
 walletScroll: {
   paddingLeft: 16,
   paddingRight: 4,
   marginBottom: 8,
 },
 walletGrid: {
   flexDirection: "row",
   flexWrap: "wrap",
   paddingHorizontal: 16,
   marginBottom: 8,
 },
 walletList: {
   paddingHorizontal: 16,
   marginBottom: 8,
 },
 walletCard: {
   padding: 14,
   marginRight: 12,
   width: 160,
   height: 90,
   position: "relative",
   overflow: "hidden",
 },
 walletContent: {
   flex: 1,
   justifyContent: "space-between",
 },
 walletHeader: {
   flexDirection: "row",
   alignItems: "center",
 },
 walletIconContainer: {
   width: 32,
   height: 32,
   borderRadius: 8,
   justifyContent: "center",
   alignItems: "center",
   shadowOffset: { width: 0, height: 2 },
   shadowRadius: 3,
   elevation: Platform.OS === "android" ? 2 : 0,
 },
 walletNameText: {
   fontSize: 14 * scale,
   fontWeight: "700",
   marginLeft: 8,
   flex: 1,
 },
 walletBalanceText: {
   fontSize: 17 * scale,
   fontWeight: "700",
   marginTop: 12,
 },
 transactionList: {
   paddingHorizontal: 16,
 },
 dayGroup: {
   marginBottom: 16,
 },
 dayHeader: {
   flexDirection: "row",
   alignItems: "center",
   marginBottom: 8,
   marginTop: 8,
 },
 dayIndicator: {
   width: 4,
   height: 16,
   marginRight: 8,
   borderRadius: 2,
 },
 dayText: {
   fontSize: 14 * scale,
   fontWeight: "600",
 },
 // Đã đổi tên từ transactionContent sang transactionItem
 transactionItem: {
   borderLeftWidth: 3,
   marginBottom: 8,
   overflow: "hidden",
 },
 transactionInner: {
   flexDirection: "row",
   alignItems: "center",
   paddingVertical: 12,
   paddingHorizontal: 12,
 },
 categoryIcon: {
   width: 36,
   height: 36,
   borderRadius: 10,
   justifyContent: "center",
   alignItems: "center",
   shadowOffset: { width: 0, height: 2 },
   shadowOpacity: 0.2,
   shadowRadius: 3,
   elevation: Platform.OS === "android" ? 4 : 0,
 },
 transactionDetails: {
   flex: 1,
   flexDirection: "row",
   justifyContent: "space-between",
   alignItems: "center",
   marginLeft: 10,
 },
 metadataRow: {
   flexDirection: "row",
   alignItems: "center",
 },
 categoryText: {
   fontSize: 12 * scale,
   fontWeight: "500",
 },
 timeText: {
   fontSize: 13 * scale,
 },
 amountContainer: {
   alignItems: "flex-end",
 },
 amountText: {
   fontSize: 16 * scale,
   fontWeight: "700",
 },
 walletText: {
   fontSize: 13 * scale,
   textAlign: "right",
 },
 emptyContainer: {
   padding: 40,
   marginTop: 8,
   alignItems: "center",
   justifyContent: "center",
   marginHorizontal: 16,
 },
 emptyIconContainer: {
   width: 80,
   height: 80,
   borderRadius: 40,
   justifyContent: "center",
   alignItems: "center",
 },
 emptyText: {
   fontSize: 16,
   marginTop: 16,
   textAlign: "center",
   fontWeight: "500",
 },
 emptyButton: {
   paddingHorizontal: 20,
   paddingVertical: 10,
   flexDirection: "row",
   alignItems: "center",
   marginTop: 16,
   shadowColor: "#000000",
   shadowOffset: { width: 0, height: 2 },
   shadowOpacity: Platform.OS === "ios" ? 0.2 : 0,
   shadowRadius: 3,
   elevation: Platform.OS === "android" ? 3 : 0,
 },
 emptyButtonText: {
   color: "#FFFFFF",
   fontWeight: "600",
 },
 // Các style đã sửa cho biểu tượng ngôi sao ví mặc định - đã chuyển sang góc phải
 defaultBadge: {
   position: "absolute",
   top: 0,
   right: 0, // Đã thay đổi từ left: 0 thành right: 0
   backgroundColor: "#FFB700",
   paddingHorizontal: 6,
   paddingVertical: 2,
   borderTopRightRadius: 15, // Đã thay đổi từ borderTopLeftRadius thành borderTopRightRadius
   borderBottomLeftRadius: 10, // Đã thay đổi từ borderBottomRightRadius thành borderBottomLeftRadius
   zIndex: 10, // Đảm bảo badge hiển thị trên cùng
 },
 nameStarIcon: {
   marginLeft: 4,
 },
 // Thêm style cho logo ngân hàng
 bankLogo: {
   width: 20,
   height: 20,
 },
 categoryItem: {
   padding: 16,
   marginBottom: 12,
   marginHorizontal: 16,
   borderRadius: 12,
 },
 categoryHeader: {
   flexDirection: 'row',
   alignItems: 'center',
   justifyContent: 'space-between',
 },
 categoryInfo: {
   flexDirection: 'row',
   alignItems: 'center',
   flex: 1,
 },
 categoryName: {
   fontSize: 16,
   fontWeight: '600',
   marginBottom: 2,
 },
 periodText: {
   fontSize: 12,
   fontStyle: 'italic',
 },
 settingsButton: {
   width: 40,
   height: 40,
   borderRadius: 20,
   justifyContent: "center",
   alignItems: "center",
 },
});