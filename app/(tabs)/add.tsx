// File: app/(tabs)/add.tsx
// <PERSON><PERSON>n hình thêm giao dịch mới với popup chọn danh mục và ví, c<PERSON> bàn phím số tùy chỉnh
// File này liên quan đến: context/ThemeContext.tsx, constants/WalletData.ts, components/CustomKeypad.tsx

import CustomKeypad from "@/components/CustomKeypad";
import TabScreenContainer from "@/components/TabScreenContainer";
import { Text, View } from "@/components/Themed";
import { BankLogos, isImageIcon } from "@/constants/WalletData";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { CategoryModel } from "@/lib/models/category";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { WalletPositionModel } from "@/lib/models/wallet_position";
import { supabase } from "@/lib/supabase";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useFocusEffect } from "@react-navigation/native";
import { Image } from "expo-image";
import { router } from "expo-router";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Interface cho category
interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  type: "expense" | "income" | "transfer";
}

export default function AddTransactionScreen() {
  // Sử dụng themeContext thay vì Colors từ constants
  const {
    isDark,
    themeColors,
    getCardStyle,
    getIconContainerStyle,
    getShadowStyle,
  } = useTheme();
  const { t } = useLocalization();
  const translateCategoryName = useCategoryTranslation();

  // Màu sắc từ themeColors
  const backgroundColor = themeColors.background;
  const primaryColor = themeColors.primary;
  const cardBgColor = themeColors.cardBackground;
  const textColor = themeColors.text;
  const borderColor = themeColors.border;
  const secondaryTextColor = themeColors.secondaryText;
  const inputBgColor = themeColors.iconBackground;
  const shadowColor = themeColors.shadowColor;
  const modalBgColor = isDark ? "rgba(0, 0, 0, 0.8)" : "rgba(0, 0, 0, 0.5)";

  // Màu cho dấu -/+
  const expenseColor = themeColors.danger; // Màu đỏ cho chi tiêu (-)
  const incomeColor = "#4CAF50"; // Màu xanh lá cho thu nhập (+)

  const [transactionType, setTransactionType] = useState("expense"); // 'expense', 'income', or 'transfer'
  const [amount, setAmount] = useState("");
  const [description, setDescription] = useState("");
  const [selectedWallet, setSelectedWallet] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedToWallet, setSelectedToWallet] = useState<string | null>(null); // For transfer: destination wallet
  const [date, setDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [mode, setMode] = useState<"date" | "time">("date");
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // State mới cho popup chọn danh mục và ví
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [showToWalletModal, setShowToWalletModal] = useState(false);

  // State cho bàn phím tùy chỉnh
  const [showCustomKeypad, setShowCustomKeypad] = useState(false);
  const [isAmountFocused, setIsAmountFocused] = useState(false);

  // Animated value cho toast
  const toastAnimation = useRef(new Animated.Value(0)).current;

  // Animation cho modal
  const modalAnimation = useRef(new Animated.Value(0)).current;

  const { currency } = useCurrency();

  // State để lưu wallets và categories từ cơ sở dữ liệu
  const [wallets, setWallets] = useState<Wallet[]>([]);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Use useMemo to filter categories based on transaction type
  // This avoids unnecessary re-renders and calculations
  const categories = useMemo(() => {
    if (transactionType === "transfer") return [];
    return allCategories.filter((cat) => cat.type === transactionType);
  }, [allCategories, transactionType]);

  // When transaction type changes, reset selected category
  useEffect(() => {
    setSelectedCategory(null);
    setSelectedToWallet(null);
  }, [transactionType]);

  // Lấy kích thước màn hình để định vị toast ở giữa
  const { width, height } = Dimensions.get("window");

  // Animation cho modal khi mở
  const animateModal = (show: boolean) => {
    Animated.timing(modalAnimation, {
      toValue: show ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Hàm hiển thị thông báo
  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);

    // Animation hiển thị toast
    Animated.sequence([
      // Hiện lên
      Animated.timing(toastAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Giữ 3 giây
      Animated.delay(3000),
      // Ẩn đi
      Animated.timing(toastAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowToast(false);
    });
  };

  // Hàm chuyển đổi loại giao dịch khi bấm vào dấu +/-
  const toggleTransactionTypeSign = () => {
    // Chỉ chuyển đổi giữa expense và income
    if (transactionType === "expense") {
      setTransactionType("income");
    } else if (transactionType === "income") {
      setTransactionType("expense");
    }
    // Không thay đổi nếu là transfer
  };

  // Xử lý lưu giao dịch
  const handleSave = async () => {
    // Validate inputs
    if (!amount.trim() || isNaN(Number(amount))) {
      Alert.alert(t("common.error"), t("addTransaction.error.amountRequired"));
      return;
    }

    if (transactionType !== "transfer" && selectedCategory === null) {
      Alert.alert(
        t("common.error"),
        t("addTransaction.error.categoryRequired")
      );
      return;
    }

    if (selectedWallet === null) {
      Alert.alert(t("common.error"), t("addTransaction.error.walletRequired"));
      return;
    }

    if (transactionType === "transfer" && selectedToWallet === null) {
      Alert.alert(
        t("common.error"),
        t("addTransaction.error.toWalletRequired")
      );
      return;
    }

    if (transactionType === "transfer" && selectedWallet === selectedToWallet) {
      Alert.alert(t("common.error"), t("addTransaction.error.sameWallet"));
      return;
    }

    try {
      // Lấy thông tin người dùng hiện tại
      const { data: sessionData } = await supabase.auth.getSession();
      const user_id = sessionData.session?.user.id;

      if (!user_id) {
        Alert.alert(t("common.error"), t("addTransaction.error.loginRequired"));
        return;
      }

      if (transactionType === "transfer") {
        // Tạo 2 giao dịch cho chuyển tiền: 1 chi và 1 thu
        const amountValue = Math.abs(Number(amount));
        const sourceWalletName =
          wallets.find((w) => w.id === selectedWallet)?.name || "";
        const destWalletName =
          wallets.find((w) => w.id === selectedToWallet)?.name || "";

        // Lấy thông tin ví nguồn và ví đích hiện tại
        const sourceWallet = await WalletModel.getById(selectedWallet!);
        const destWallet = await WalletModel.getById(selectedToWallet!);

        if (!sourceWallet || !destWallet) {
          throw new Error("Không tìm thấy thông tin ví");
        }

        // Tạo giao dịch chi từ ví nguồn - sử dụng loại "expense"
        const { error: sourceError } = await supabase
          .from("transactions")
          .insert({
            user_id,
            wallet_id: selectedWallet,
            amount: -amountValue, // Trừ tiền từ ví nguồn (số âm)
            type: "expense", // Sử dụng "expense" thay vì "transfer_out"
            description:
              description ||
              t("addTransaction.transferTo") + " " + destWalletName,
            date: date.toISOString(),
          });

        if (sourceError) throw sourceError;

        // Tạo giao dịch thu cho ví đích - sử dụng loại "income"
        const { error: destError } = await supabase
          .from("transactions")
          .insert({
            user_id,
            wallet_id: selectedToWallet,
            amount: amountValue, // Cộng tiền vào ví đích (số dương)
            type: "income", // Sử dụng "income" thay vì "transfer_in"
            description:
              description ||
              t("addTransaction.transferFrom") + " " + sourceWalletName,
            date: date.toISOString(),
          });

        if (destError) throw destError;

        // Cập nhật số dư của ví nguồn và ví đích
        const newSourceBalance = sourceWallet.balance - amountValue;
        const newDestBalance = destWallet.balance + amountValue;

        // Cập nhật số dư ví nguồn
        const { error: updateSourceError } = await supabase
          .from("wallets")
          .update({ balance: newSourceBalance })
          .eq("id", selectedWallet);

        if (updateSourceError) throw updateSourceError;

        // Cập nhật số dư ví đích
        const { error: updateDestError } = await supabase
          .from("wallets")
          .update({ balance: newDestBalance })
          .eq("id", selectedToWallet);

        if (updateDestError) throw updateDestError;
      } else {
        // Lấy thông tin ví hiện tại
        const wallet = await WalletModel.getById(selectedWallet!);

        if (!wallet) {
          throw new Error("Không tìm thấy thông tin ví");
        }

        // Số tiền giao dịch
        const amountValue = Math.abs(Number(amount));

        // Tạo giao dịch thu/chi thông thường
        const { error } = await supabase.from("transactions").insert({
          user_id,
          wallet_id: selectedWallet,
          category_id: selectedCategory,
          amount:
            transactionType === "expense"
              ? -Math.abs(Number(amount))
              : Math.abs(Number(amount)),
          type: transactionType,
          description: description,
          date: date.toISOString(),
        });

        if (error) {
          throw error;
        }

        // Cập nhật số dư ví
        const newBalance =
          transactionType === "expense"
            ? wallet.balance - amountValue
            : wallet.balance + amountValue;

        const { error: updateError } = await supabase
          .from("wallets")
          .update({ balance: newBalance })
          .eq("id", selectedWallet);

        if (updateError) throw updateError;
      }

      // Hiển thị thông báo thành công
      showToastMessage(t("addTransaction.success.message"));

      // Reset form state
      resetFormState();

      // Chuyển về màn hình trước đó sau 0.1 giây và đảm bảo reload dữ liệu
      setTimeout(() => {
        // Sử dụng router.replace thay vì router.back hoặc router.navigate
        // router.replace sẽ thay thế màn hình hiện tại bằng màn hình index
        // và đảm bảo useFocusEffect được kích hoạt để tải lại dữ liệu
        router.replace("/(tabs)");
      }, 100);
    } catch (error: any) {
      console.error("Error saving transaction:", error);
      Alert.alert(
        t("common.error"),
        error.message || t("addTransaction.error.saveFailed")
      );
    }
  };

  // Hàm reset form state sau khi lưu thành công
  const resetFormState = () => {
    setTransactionType("expense");
    setAmount("");
    setDescription("");
    setSelectedWallet(null);
    setSelectedCategory(null);
    setSelectedToWallet(null);
    setDate(new Date());
    setShowCustomKeypad(false);
    setIsAmountFocused(false);
  };

  const loadData = async () => {
    try {
      setIsLoading(true);

      // Load wallets
      const walletsData = await WalletModel.getAll();

      // Lấy thứ tự vị trí từ bảng wallet_positions (giống như trong file index.tsx)
      let walletPositions = [];
      try {
        walletPositions = await WalletPositionModel.getAll();
      } catch (error) {
        console.log("Không có dữ liệu vị trí hoặc lỗi:", error);
      }

      // Tạo map để tra cứu vị trí nhanh
      const positionMap = {};
      walletPositions.forEach((pos) => {
        positionMap[pos.wallet_id] = pos.position;
      });

      // Sắp xếp ví theo vị trí từ bảng wallet_positions (nếu có)
      // Nếu không có vị trí thì sắp xếp theo thời gian tạo (mặc định)
      const sortedWallets = [...walletsData].sort((a, b) => {
        const posA = positionMap[a.id] !== undefined ? positionMap[a.id] : 999;
        const posB = positionMap[b.id] !== undefined ? positionMap[b.id] : 999;
        if (posA !== 999 || posB !== 999) {
          return posA - posB;
        }
        // Sắp xếp theo thời gian tạo nếu không có vị trí (mới nhất lên đầu)
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      });

      setWallets(sortedWallets);

      // Load all categories at once
      const categoriesData = await CategoryModel.getAll();
      setAllCategories(categoriesData as Category[]);
    } catch (error) {
      console.error("Error loading data:", error);
      Alert.alert(t("common.error"), t("addTransaction.error.loadFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  // Load data only once when component mounts or when screen is focused
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  // Xử lý số tiền nhập vào từ bàn phím tùy chỉnh
  const handleKeypadInput = (key: string) => {
    if (key === 'C') {
      setAmount("");
    } else if (key === 'backspace') {
      setAmount(prev => prev.slice(0, -1));
    } else if (key === ',') {
      // Xử lý dấu phẩy - có thể thêm logic riêng nếu cần
      if (!amount.includes(',')) {
        setAmount(prev => prev + ',');
      }
    } else if (['÷', '×', '+', '-'].includes(key)) {
      // Xử lý các phép tính - có thể thêm logic tính toán nếu cần
      // Hiện tại chỉ thêm vào string
      setAmount(prev => prev + key);
    } else if (key === '000') {
      setAmount(prev => prev + '000');
    } else {
      // Số thông thường
      setAmount(prev => prev + key);
    }
  };

  // Xử lý chuyển đổi loại giao dịch từ bàn phím
  const handleKeypadToggleType = (type: 'expense' | 'income') => {
    if (transactionType !== 'transfer') {
      setTransactionType(type);
    }
  };

  // Xử lý khi bấm nút "Xong" trên bàn phím
  const handleKeypadDone = () => {
    setShowCustomKeypad(false);
    setIsAmountFocused(false);
  };

  // Xử lý khi focus vào ô nhập số tiền
  const handleAmountFocus = () => {
    setIsAmountFocused(true);
    setShowCustomKeypad(true);
  };

  // Chuyển đổi giá trị amount sang định dạng có dấu phân cách hàng nghìn cho hiển thị
  const formattedAmount = useMemo(() => {
    if (!amount) return "";
    
    // Tách phần số và phần không phải số
    const numericPart = amount.replace(/[^0-9]/g, "");
    const nonNumericPart = amount.replace(/[0-9]/g, "");
    
    if (numericPart) {
      const formatted = parseInt(numericPart, 10).toLocaleString();
      return formatted + nonNumericPart;
    }
    
    return amount;
  }, [amount]);

  // Xử lý chọn ngày
  const showDateTimePicker = (currentMode: "date" | "time") => {
    setMode(currentMode);
    if (Platform.OS === "android") {
      setShowDatePicker(true);
    } else {
      if (currentMode === "date") {
        setShowDatePicker(true);
      } else {
        setShowTimePicker(true);
      }
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || date;

    if (Platform.OS === "android") {
      setShowDatePicker(false);
      if (mode === "date" && event.type !== "dismissed") {
        setDate(currentDate);
        // On Android, after setting date, automatically show time picker
        setTimeout(() => {
          showDateTimePicker("time");
        }, 500);
      } else if (mode === "time" && event.type !== "dismissed") {
        setDate(currentDate);
      }
    } else {
      // iOS behavior
      if (mode === "date") {
        setShowDatePicker(false);
      } else {
        setShowTimePicker(false);
      }
      setDate(currentDate);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${t("dateTime.today")}`;
    } else if (date.toDateString() === yesterday.toDateString()) {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${t("dateTime.yesterday")}`;
    } else {
      return `${date.getHours()}:${String(date.getMinutes()).padStart(
        2,
        "0"
      )} - ${date.getDate()}/${date.getMonth() + 1}`;
    }
  };

  // Hàm mở/đóng modal chọn category
  const toggleCategoryModal = (show: boolean) => {
    if (show) {
      setShowCategoryModal(true);
      animateModal(true);
    } else {
      animateModal(false);
      setTimeout(() => setShowCategoryModal(false), 300);
    }
  };

  // Hàm mở/đóng modal chọn wallet
  const toggleWalletModal = (show: boolean) => {
    if (show) {
      setShowWalletModal(true);
      animateModal(true);
    } else {
      animateModal(false);
      setTimeout(() => setShowWalletModal(false), 300);
    }
  };

  // Hàm mở/đóng modal chọn to wallet
  const toggleToWalletModal = (show: boolean) => {
    if (show) {
      setShowToWalletModal(true);
      animateModal(true);
    } else {
      animateModal(false);
      setTimeout(() => setShowToWalletModal(false), 300);
    }
  };

  // Lấy thông tin của category đã chọn
  const selectedCategoryInfo = useMemo(() => {
    return categories.find((cat) => cat.id === selectedCategory);
  }, [categories, selectedCategory]);

  // Lấy thông tin của wallet đã chọn
  const selectedWalletInfo = useMemo(() => {
    return wallets.find((wallet) => wallet.id === selectedWallet);
  }, [wallets, selectedWallet]);

  // Lấy thông tin của to wallet đã chọn
  const selectedToWalletInfo = useMemo(() => {
    return wallets.find((wallet) => wallet.id === selectedToWallet);
  }, [wallets, selectedToWallet]);

  // Hiển thị màn hình loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <TabScreenContainer style={{ backgroundColor: backgroundColor }}>
        <View
          style={[
            styles.loadingContainer,
            { backgroundColor: backgroundColor },
          ]}
        >
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={{ marginTop: 16, color: textColor }}>
            {t("addTransaction.loading")}
          </Text>
        </View>
      </TabScreenContainer>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={{ flex: 1, backgroundColor: backgroundColor, flexGrow: 1 }}
    >
      {/* Toast Notification ở giữa màn hình */}
      {showToast && (
        <Animated.View
          style={[
            styles.toastContainer,
            {
              transform: [
                {
                  translateY: toastAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
              opacity: toastAnimation,
            },
          ]}
        >
          <View
            style={[
              styles.toast,
              {
                backgroundColor: primaryColor,
                ...getShadowStyle("medium"),
              },
            ]}
          >
            <Ionicons name="checkmark-circle-outline" size={24} color="white" />
            <Text style={styles.toastMessage}>{toastMessage}</Text>
          </View>
        </Animated.View>
      )}

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <View style={[styles.header, { backgroundColor: backgroundColor }]}>
          <Text style={[styles.title, { color: textColor }]}>
            {transactionType === "transfer"
              ? t("addTransaction.transferTitle")
              : t("addTransaction.title")}
          </Text>
          <TouchableOpacity
            style={[
              styles.saveHeaderButton,
              {
                backgroundColor:
                  amount &&
                  (transactionType === "transfer"
                    ? selectedWallet !== null && selectedToWallet !== null
                    : selectedCategory !== null && selectedWallet !== null)
                    ? primaryColor
                    : isDark
                    ? "#375980"
                    : "#BBDEFB",
                ...getShadowStyle("low"),
              },
            ]}
            onPress={handleSave}
          >
            <Text
              style={{
                color:
                  amount &&
                  (transactionType === "transfer"
                    ? selectedWallet !== null && selectedToWallet !== null
                    : selectedCategory !== null && selectedWallet !== null)
                    ? "white"
                    : isDark
                    ? secondaryTextColor
                    : "#90CAF9",
                fontWeight: "600",
              }}
            >
              {t("addTransaction.save")}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={{ backgroundColor: backgroundColor }}
          showsVerticalScrollIndicator={false}
        >
          {/* Chọn loại giao dịch */}
          <View
            style={[styles.typeSelector, { backgroundColor: backgroundColor }]}
          >
            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    transactionType === "expense" ? primaryColor : inputBgColor,
                  borderColor: borderColor,
                  borderWidth: 1,
                  ...getShadowStyle(
                    transactionType === "expense" ? "medium" : "low"
                  ),
                },
              ]}
              onPress={() => setTransactionType("expense")}
            >
              <Ionicons
                name="arrow-down-circle"
                size={24}
                color={
                  transactionType === "expense" ? "white" : themeColors.danger
                }
              />
              <Text
                style={[
                  styles.typeText,
                  {
                    color: transactionType === "expense" ? "white" : textColor,
                  },
                ]}
              >
                {t("addTransaction.expenseTab")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    transactionType === "income" ? primaryColor : inputBgColor,
                  borderColor: borderColor,
                  borderWidth: 1,
                  ...getShadowStyle(
                    transactionType === "income" ? "medium" : "low"
                  ),
                },
              ]}
              onPress={() => setTransactionType("income")}
            >
              <Ionicons
                name="arrow-up-circle"
                size={24}
                color={transactionType === "income" ? "white" : "#4CAF50"}
              />
              <Text
                style={[
                  styles.typeText,
                  {
                    color: transactionType === "income" ? "white" : textColor,
                  },
                ]}
              >
                {t("addTransaction.incomeTab")}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor:
                    transactionType === "transfer"
                      ? primaryColor
                      : inputBgColor,
                  borderColor: borderColor,
                  borderWidth: 1,
                  ...getShadowStyle(
                    transactionType === "transfer" ? "medium" : "low"
                  ),
                },
              ]}
              onPress={() => setTransactionType("transfer")}
            >
              <Ionicons
                name="swap-horizontal"
                size={24}
                color={transactionType === "transfer" ? "white" : "#FF9800"}
              />
              <Text
                style={[
                  styles.typeText,
                  {
                    color: transactionType === "transfer" ? "white" : textColor,
                  },
                ]}
              >
                {t("addTransaction.transferTab")}
              </Text>
            </TouchableOpacity>
          </View>

          <View
            style={[
              styles.card,
              getCardStyle("high"),
              {
                marginHorizontal: 16,
              },
            ]}
          >
            {/* Nhập số tiền với dấu +/- */}
            <View style={[styles.section, { backgroundColor: cardBgColor }]}>
              <Text style={[styles.label, { color: textColor }]}>
                {t("addTransaction.amount")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.amountContainer,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: isAmountFocused ? primaryColor : borderColor,
                    borderWidth: isAmountFocused ? 2 : 1,
                    ...getShadowStyle("low"),
                  },
                ]}
                onPress={handleAmountFocus}
              >
                {/* Nút dấu +/- - đã tăng kích thước lên 40% và căn giữa hoàn hảo */}
                {transactionType !== "transfer" && (
                  <TouchableOpacity
                    style={styles.signButton}
                    onPress={toggleTransactionTypeSign}
                  >
                    <Text
                      style={[
                        styles.sign,
                        {
                          color:
                            transactionType === "expense"
                              ? expenseColor
                              : incomeColor,
                          fontSize: 34, // Tăng kích thước lên 40% (từ 24 lên 34)
                        },
                      ]}
                    >
                      {transactionType === "expense" ? "-" : "+"}
                    </Text>
                  </TouchableOpacity>
                )}

                <View style={styles.amountInputContainer}>
                  <Text
                    style={[styles.amountDisplay, { color: textColor }]}
                  >
                    {formattedAmount || "0"}
                  </Text>
                </View>
                <Text style={[styles.currency, { color: secondaryTextColor }]}>
                  {currency}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Hàng ngang chứa ví nguồn và danh mục cho giao dịch thu/chi */}
            {transactionType !== "transfer" && (
              <View
                style={[styles.rowContainer, { backgroundColor: cardBgColor }]}
              >
                {/* Phần ví bên trái */}
                <View
                  style={[
                    styles.columnSection,
                    { backgroundColor: cardBgColor },
                  ]}
                >
                  <Text style={[styles.label, { color: textColor }]}>
                    {t("addTransaction.wallet")}
                  </Text>

                  {/* Nút chọn ví nguồn - đã làm nhỏ hơn và bỏ mũi tên */}
                  <TouchableOpacity
                    style={[
                      styles.selectorButton,
                      {
                        backgroundColor: inputBgColor,
                        borderColor: selectedWallet
                          ? selectedWalletInfo?.color || borderColor
                          : borderColor,
                        borderWidth: 1,
                        ...getShadowStyle("low"),
                        height: 60, // Giảm từ 70 xuống 60
                      },
                    ]}
                    onPress={() => toggleWalletModal(true)}
                  >
                    {selectedWallet ? (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <View
                          style={[
                            styles.smallWalletIcon, // Thay đổi kích thước icon
                            {
                              backgroundColor:
                                selectedWalletInfo?.color || primaryColor,
                              ...getShadowStyle("medium"),
                            },
                          ]}
                        >
                          {isImageIcon(selectedWalletInfo?.icon || "") ? (
                            <Image
                              source={BankLogos[selectedWalletInfo?.icon || ""]}
                              style={{ width: 20, height: 20 }}
                              contentFit="contain"
                            />
                          ) : (
                            <Ionicons
                              name={
                                (selectedWalletInfo?.icon as any) || "wallet"
                              }
                              size={18}
                              color="white"
                            />
                          )}
                        </View>
                        <View style={{ backgroundColor: "transparent" }}>
                          <Text
                            style={[
                              styles.smallWalletName,
                              { color: textColor },
                            ]}
                          >
                            {selectedWalletInfo?.name || ""}
                          </Text>
                          <Text
                            style={[
                              styles.smallWalletBalance,
                              { color: secondaryTextColor },
                            ]}
                          >
                            {selectedWalletInfo?.balance.toLocaleString() ||
                              "0"}
                            đ
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          {
                            backgroundColor: inputBgColor,
                          },
                        ]}
                      >
                        <Ionicons
                          name="wallet-outline"
                          size={22}
                          color={secondaryTextColor}
                        />
                        <Text
                          numberOfLines={2}
                          style={[
                            styles.smallPlaceholderText,
                            { color: secondaryTextColor },
                          ]}
                        >
                          {t("addTransaction.selectFromWallet") || "Chọn ví"}
                        </Text>
                      </View>
                    )}
                    {/* Đã bỏ mũi tên xuống */}
                  </TouchableOpacity>
                </View>

                {/* Phần danh mục bên phải */}
                <View
                  style={[
                    styles.columnSection,
                    { backgroundColor: cardBgColor },
                  ]}
                >
                  <Text style={[styles.label, { color: textColor }]}>
                    {t("addTransaction.category")}
                  </Text>

                  {/* Nút chọn danh mục - đã làm nhỏ hơn và bỏ mũi tên */}
                  <TouchableOpacity
                    style={[
                      styles.selectorButton,
                      {
                        backgroundColor: inputBgColor,
                        borderColor: selectedCategory
                          ? selectedCategoryInfo?.color || borderColor
                          : borderColor,
                        borderWidth: 1,
                        ...getShadowStyle("low"),
                        height: 60, // Giảm từ 70 xuống 60
                      },
                    ]}
                    onPress={() => toggleCategoryModal(true)}
                  >
                    {selectedCategory ? (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <View
                          style={[
                            styles.smallCategoryIcon, // Thay đổi kích thước icon
                            {
                              backgroundColor:
                                selectedCategoryInfo?.color || primaryColor,
                              ...getShadowStyle("medium"),
                            },
                          ]}
                        >
                          <Ionicons
                            name={
                              (selectedCategoryInfo?.icon as any) ||
                              "help-circle"
                            }
                            size={20}
                            color="white"
                          />
                        </View>
                        <Text
                          style={[
                            styles.smallSelectedItemText,
                            { color: textColor },
                          ]}
                        >
                          {translateCategoryName(
                            selectedCategoryInfo?.name || ""
                          )}
                        </Text>
                      </View>
                    ) : (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <Ionicons
                          name="list"
                          size={22}
                          color={secondaryTextColor}
                        />
                        <Text
                          style={[
                            styles.smallPlaceholderText,
                            { color: secondaryTextColor },
                          ]}
                        >
                          {t("addTransaction.selectCategory") ||
                            "Chọn danh mục"}
                        </Text>
                      </View>
                    )}
                    {/* Đã bỏ mũi tên xuống */}
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Trường hợp giao dịch chuyển khoản - hiển thị ví nguồn và ví đích */}
            {transactionType === "transfer" && (
              <View
                style={[styles.rowContainer, { backgroundColor: cardBgColor }]}
              >
                {/* Ví nguồn */}
                <View
                  style={[
                    styles.columnSection,
                    { backgroundColor: cardBgColor },
                  ]}
                >
                  <Text style={[styles.label, { color: textColor }]}>
                    {t("addTransaction.fromWallet")}
                  </Text>

                  <TouchableOpacity
                    style={[
                      styles.selectorButton,
                      {
                        backgroundColor: inputBgColor,
                        borderColor: selectedWallet
                          ? selectedWalletInfo?.color || borderColor
                          : borderColor,
                        borderWidth: 1,
                        ...getShadowStyle("low"),
                        height: 60, // Giảm từ 70 xuống 60
                      },
                    ]}
                    onPress={() => toggleWalletModal(true)}
                  >
                    {selectedWallet ? (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <View
                          style={[
                            styles.smallWalletIcon, // Thay đổi kích thước icon
                            {
                              backgroundColor:
                                selectedWalletInfo?.color || primaryColor,
                              ...getShadowStyle("medium"),
                            },
                          ]}
                        >
                          {isImageIcon(selectedWalletInfo?.icon || "") ? (
                            <Image
                              source={BankLogos[selectedWalletInfo?.icon || ""]}
                              style={{ width: 20, height: 20 }}
                              contentFit="contain"
                            />
                          ) : (
                            <Ionicons
                              name={
                                (selectedWalletInfo?.icon as any) || "wallet"
                              }
                              size={18}
                              color="white"
                            />
                          )}
                        </View>
                        <View style={{ backgroundColor: "transparent" }}>
                          <Text
                            style={[
                              styles.smallWalletName,
                              { color: textColor },
                            ]}
                          >
                            {selectedWalletInfo?.name || ""}
                          </Text>
                          <Text
                            style={[
                              styles.smallWalletBalance,
                              { color: secondaryTextColor },
                            ]}
                          >
                            {selectedWalletInfo?.balance.toLocaleString() ||
                              "0"}
                            đ
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <Ionicons
                          name="wallet-outline"
                          size={22}
                          color={secondaryTextColor}
                        />
                        <Text
                          numberOfLines={2}
                          style={[
                            styles.smallPlaceholderText,
                            { color: secondaryTextColor },
                          ]}
                        >
                          {t("addTransaction.selectFromWallet") || "Ví nguồn"}
                        </Text>
                      </View>
                    )}
                    {/* Đã bỏ mũi tên xuống */}
                  </TouchableOpacity>
                </View>

                {/* Ví đích */}
                <View
                  style={[
                    styles.columnSection,
                    { backgroundColor: cardBgColor },
                  ]}
                >
                  <Text style={[styles.label, { color: textColor }]}>
                    {t("addTransaction.toWallet")}
                  </Text>

                  <TouchableOpacity
                    style={[
                      styles.selectorButton,
                      {
                        backgroundColor: inputBgColor,
                        borderColor: selectedToWallet
                          ? selectedToWalletInfo?.color || borderColor
                          : borderColor,
                        borderWidth: 1,
                        ...getShadowStyle("low"),
                        height: 60, // Giảm từ 70 xuống 60
                      },
                    ]}
                    onPress={() => toggleToWalletModal(true)}
                  >
                    {selectedToWallet ? (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <View
                          style={[
                            styles.smallWalletIcon, // Thay đổi kích thước icon
                            {
                              backgroundColor:
                                selectedToWalletInfo?.color || primaryColor,
                              ...getShadowStyle("medium"),
                            },
                          ]}
                        >
                          {isImageIcon(selectedToWalletInfo?.icon || "") ? (
                            <Image
                              source={
                                BankLogos[selectedToWalletInfo?.icon || ""]
                              }
                              style={{ width: 20, height: 20 }}
                              contentFit="contain"
                            />
                          ) : (
                            <Ionicons
                              name={
                                (selectedToWalletInfo?.icon as any) || "wallet"
                              }
                              size={18}
                              color="white"
                            />
                          )}
                        </View>
                        <View style={{ backgroundColor: "transparent" }}>
                          <Text
                            style={[
                              styles.smallWalletName,
                              { color: textColor },
                            ]}
                          >
                            {selectedToWalletInfo?.name || ""}
                          </Text>
                          <Text
                            style={[
                              styles.smallWalletBalance,
                              { color: secondaryTextColor },
                            ]}
                          >
                            {selectedToWalletInfo?.balance.toLocaleString() ||
                              "0"}
                            đ
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <View
                        style={[
                          styles.selectedItemContainer,
                          { backgroundColor: inputBgColor },
                        ]}
                      >
                        <Ionicons
                          name="wallet-outline"
                          size={22}
                          color={secondaryTextColor}
                        />
                        <Text
                          style={[
                            styles.smallPlaceholderText,
                            { color: secondaryTextColor },
                          ]}
                        >
                          {t("addTransaction.selectToWallet") || "Ví đích"}
                        </Text>
                      </View>
                    )}
                    {/* Đã bỏ mũi tên xuống */}
                  </TouchableOpacity>
                </View>
              </View>
            )}

            {/* Mô tả - đã giảm kích thước */}
            <View style={[styles.section, { backgroundColor: cardBgColor }]}>
              <Text style={[styles.label, { color: textColor }]}>
                {t("addTransaction.note")}
              </Text>
              <View
                style={[
                  styles.inputContainer,
                  styles.textAreaContainer,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: borderColor,
                    borderWidth: 1,
                    ...getShadowStyle("low"),
                    height: 80, // Giảm từ 120 xuống 80
                  },
                ]}
              >
                <TextInput
                  style={[
                    styles.input,
                    styles.textArea,
                    {
                      color: textColor,
                      height: 80, // Giảm từ 120 xuống 80
                    },
                  ]}
                  multiline
                  numberOfLines={3} // Giảm từ 4 xuống 3
                  placeholder={
                    t("addTransaction.note") + " (" + t("common.optional") + ")"
                  }
                  placeholderTextColor={secondaryTextColor}
                  value={description}
                  onChangeText={setDescription}
                  textAlignVertical="top"
                />
              </View>
            </View>

            {/* Ngày giao dịch - đưa xuống dưới và làm nhỏ hơn */}
            <View
              style={[styles.smallSection, { backgroundColor: cardBgColor }]}
            >
              <Text style={[styles.smallLabel, { color: textColor }]}>
                {t("addTransaction.transactionDate")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.smallDatePickerButton,
                  {
                    backgroundColor: inputBgColor,
                    borderColor: borderColor,
                    borderWidth: 1,
                    ...getShadowStyle("low"),
                  },
                ]}
                onPress={() => showDateTimePicker("date")}
              >
                <Ionicons
                  name="calendar-outline"
                  size={18}
                  color={secondaryTextColor}
                />
                <Text style={[styles.smallDateText, { color: textColor }]}>
                  {formatDate(date)}
                </Text>
              </TouchableOpacity>

              {/* Date Picker */}
              {showDatePicker && (
                <DateTimePicker
                  testID="dateTimePicker"
                  value={date}
                  mode={mode}
                  is24Hour={true}
                  display={Platform.OS === "ios" ? "spinner" : "default"}
                  onChange={handleDateChange}
                  themeVariant={isDark ? "dark" : "light"}
                />
              )}

              {/* Time Picker (iOS only) */}
              {Platform.OS === "ios" && showTimePicker && (
                <DateTimePicker
                  testID="timeTimePicker"
                  value={date}
                  mode="time"
                  is24Hour={true}
                  display="spinner"
                  onChange={handleDateChange}
                  themeVariant={isDark ? "dark" : "light"}
                />
              )}
            </View>
          </View>

          {/* Nút lưu */}
          <TouchableOpacity
            style={[
              styles.saveButton,
              {
                backgroundColor:
                  amount &&
                  (transactionType === "transfer"
                    ? selectedWallet !== null && selectedToWallet !== null
                    : selectedCategory !== null && selectedWallet !== null)
                    ? primaryColor
                    : isDark
                    ? "#375980"
                    : "#BBDEFB",
                marginTop: 20,
                ...getShadowStyle("medium"),
              },
            ]}
            onPress={handleSave}
            disabled={
              !amount ||
              (transactionType === "transfer"
                ? selectedWallet === null || selectedToWallet === null
                : selectedCategory === null || selectedWallet === null)
            }
          >
            <Text
              style={[
                styles.saveButtonText,
                {
                  color:
                    amount &&
                    (transactionType === "transfer"
                      ? selectedWallet !== null && selectedToWallet !== null
                      : selectedCategory !== null && selectedWallet !== null)
                      ? "white"
                      : secondaryTextColor,
                },
              ]}
            >
              {transactionType === "transfer"
                ? t("addTransaction.createTransfer")
                : t("addTransaction.createTransaction")}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Bàn phím tùy chỉnh */}
      <CustomKeypad
        visible={showCustomKeypad}
        onKeyPress={handleKeypadInput}
        onDone={handleKeypadDone}
        onToggleType={handleKeypadToggleType}
      />

      {/* Modal chọn danh mục */}
      <Modal
        visible={showCategoryModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => toggleCategoryModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => toggleCategoryModal(false)}>
          <View
            style={[styles.modalOverlay, { backgroundColor: modalBgColor }]}
          >
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  backgroundColor: cardBgColor,
                  transform: [
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [500, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, { color: textColor }]}>
                      {t("addTransaction.selectCategory") || "Chọn danh mục"}
                    </Text>
                    <TouchableOpacity
                      onPress={() => toggleCategoryModal(false)}
                    >
                      <Ionicons name="close" size={24} color={textColor} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.modalContent}>
                    {categories.length === 0 ? (
                      <Text
                        style={{
                          color: textColor,
                          textAlign: "center",
                          width: "100%",
                          padding: 20,
                        }}
                      >
                        {t("addTransaction.noCategoriesMessage")}
                      </Text>
                    ) : (
                      <ScrollView style={{ maxHeight: height * 0.5 }}>
                        <View style={styles.categoriesGrid}>
                          {categories.map((category) => (
                            <TouchableOpacity
                              key={category.id}
                              style={[
                                styles.categoryItem,
                                {
                                  backgroundColor: inputBgColor,
                                  borderColor:
                                    selectedCategory === category.id
                                      ? category.color
                                      : borderColor,
                                  borderWidth: 1,
                                  ...getShadowStyle(
                                    selectedCategory === category.id
                                      ? "medium"
                                      : "low"
                                  ),
                                },
                              ]}
                              onPress={() => {
                                setSelectedCategory(category.id);
                                toggleCategoryModal(false);
                              }}
                            >
                              <View
                                style={[
                                  styles.categoryIcon,
                                  {
                                    backgroundColor: category.color,
                                    ...getShadowStyle("medium"),
                                  },
                                ]}
                              >
                                <Ionicons
                                  name={category.icon as any}
                                  size={24}
                                  color="white"
                                />
                              </View>
                              <Text
                                style={[
                                  styles.categoryName,
                                  { color: textColor },
                                ]}
                              >
                                {translateCategoryName(category.name)}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    )}
                  </View>

                  <View style={styles.modalFooter}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: primaryColor },
                      ]}
                      onPress={() => toggleCategoryModal(false)}
                    >
                      <Text style={styles.closeButtonText}>
                        {t("addTransaction.closeButton") || "Đóng"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Modal chọn ví - Đã sửa thành dạng grid */}
      <Modal
        visible={showWalletModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => toggleWalletModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => toggleWalletModal(false)}>
          <View
            style={[styles.modalOverlay, { backgroundColor: modalBgColor }]}
          >
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  backgroundColor: cardBgColor,
                  transform: [
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [500, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, { color: textColor }]}>
                      {t("addTransaction.selectFromWallet") || "Chọn ví nguồn"}
                    </Text>
                    <TouchableOpacity onPress={() => toggleWalletModal(false)}>
                      <Ionicons name="close" size={24} color={textColor} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.modalContent}>
                    {wallets.length === 0 ? (
                      <Text
                        style={{
                          color: textColor,
                          textAlign: "center",
                          width: "100%",
                          padding: 20,
                        }}
                      >
                        {t("addTransaction.noWalletsMessage")}
                      </Text>
                    ) : (
                      <ScrollView style={{ maxHeight: height * 0.6 }}>
                        <View style={styles.walletsGrid}>
                          {wallets.map((wallet) => (
                            <TouchableOpacity
                              key={wallet.id}
                              style={[
                                styles.walletItem,
                                {
                                  backgroundColor: inputBgColor,
                                  borderColor:
                                    selectedWallet === wallet.id
                                      ? wallet.color
                                      : borderColor,
                                  borderWidth: 1,
                                  ...getShadowStyle(
                                    selectedWallet === wallet.id
                                      ? "medium"
                                      : "low"
                                  ),
                                  // Mờ đi nếu wallet này đã được chọn làm ví đích
                                  opacity:
                                    transactionType === "transfer" &&
                                    selectedToWallet === wallet.id
                                      ? 0.5
                                      : 1,
                                },
                              ]}
                              onPress={() => {
                                setSelectedWallet(wallet.id);
                                toggleWalletModal(false);
                              }}
                              disabled={
                                transactionType === "transfer" &&
                                selectedToWallet === wallet.id
                              }
                            >
                              <View
                                style={[
                                  styles.walletIconGrid,
                                  {
                                    backgroundColor: wallet.color,
                                    ...getShadowStyle("medium"),
                                  },
                                ]}
                              >
                                {isImageIcon(wallet.icon) ? (
                                  <Image
                                    source={BankLogos[wallet.icon]}
                                    style={{ width: 30, height: 30 }}
                                    contentFit="contain"
                                  />
                                ) : (
                                  <Ionicons
                                    name={wallet.icon as any}
                                    size={24}
                                    color="white"
                                  />
                                )}
                              </View>
                              <Text
                                style={[
                                  styles.walletNameGrid,
                                  { color: textColor },
                                ]}
                              >
                                {wallet.name}
                              </Text>
                              <Text
                                style={[
                                  styles.walletBalanceGrid,
                                  { color: secondaryTextColor },
                                ]}
                              >
                                {wallet.balance.toLocaleString()}đ
                              </Text>
                              {selectedWallet === wallet.id && (
                                <View style={styles.checkmarkContainer}>
                                  <Ionicons
                                    name="checkmark-circle"
                                    size={24}
                                    color={wallet.color}
                                  />
                                </View>
                              )}
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    )}
                  </View>

                  <View style={styles.modalFooter}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: primaryColor },
                      ]}
                      onPress={() => toggleWalletModal(false)}
                    >
                      <Text style={styles.closeButtonText}>
                        {t("addTransaction.closeButton") || "Đóng"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>

      {/* Modal chọn ví đích (cho chuyển khoản) - Đã sửa thành dạng grid */}
      <Modal
        visible={showToWalletModal}
        transparent={true}
        animationType="none"
        onRequestClose={() => toggleToWalletModal(false)}
      >
        <TouchableWithoutFeedback onPress={() => toggleToWalletModal(false)}>
          <View
            style={[styles.modalOverlay, { backgroundColor: modalBgColor }]}
          >
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  backgroundColor: cardBgColor,
                  transform: [
                    {
                      translateY: modalAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [500, 0],
                      }),
                    },
                  ],
                },
              ]}
            >
              <TouchableWithoutFeedback>
                <View>
                  <View style={styles.modalHeader}>
                    <Text style={[styles.modalTitle, { color: textColor }]}>
                      {t("addTransaction.selectToWallet") || "Chọn ví đích"}
                    </Text>
                    <TouchableOpacity
                      onPress={() => toggleToWalletModal(false)}
                    >
                      <Ionicons name="close" size={24} color={textColor} />
                    </TouchableOpacity>
                  </View>

                  <View style={styles.modalContent}>
                    {wallets.length === 0 ? (
                      <Text
                        style={{
                          color: textColor,
                          textAlign: "center",
                          width: "100%",
                          padding: 20,
                        }}
                      >
                        {t("addTransaction.noWalletsMessage")}
                      </Text>
                    ) : (
                      <ScrollView style={{ maxHeight: height * 0.6 }}>
                        <View style={styles.walletsGrid}>
                          {wallets.map((wallet) => (
                            <TouchableOpacity
                              key={wallet.id}
                              style={[
                                styles.walletItem,
                                {
                                  backgroundColor: inputBgColor,
                                  borderColor:
                                    selectedToWallet === wallet.id
                                      ? wallet.color
                                      : borderColor,
                                  borderWidth: 1,
                                  ...getShadowStyle(
                                    selectedToWallet === wallet.id
                                      ? "medium"
                                      : "low"
                                  ),
                                  // Mờ đi nếu wallet này đã được chọn làm ví nguồn
                                  opacity:
                                    selectedWallet === wallet.id ? 0.5 : 1,
                                },
                              ]}
                              onPress={() => {
                                setSelectedToWallet(wallet.id);
                                toggleToWalletModal(false);
                              }}
                              disabled={selectedWallet === wallet.id}
                            >
                              <View
                                style={[
                                  styles.walletIconGrid,
                                  {
                                    backgroundColor: wallet.color,
                                    ...getShadowStyle("medium"),
                                  },
                                ]}
                              >
                                {isImageIcon(wallet.icon) ? (
                                  <Image
                                    source={BankLogos[wallet.icon]}
                                    style={{ width: 30, height: 30 }}
                                    contentFit="contain"
                                  />
                                ) : (
                                  <Ionicons
                                    name={wallet.icon as any}
                                    size={24}
                                    color="white"
                                  />
                                )}
                              </View>
                              <Text
                                style={[
                                  styles.walletNameGrid,
                                  { color: textColor },
                                ]}
                              >
                                {wallet.name}
                              </Text>
                              <Text
                                style={[
                                  styles.walletBalanceGrid,
                                  { color: secondaryTextColor },
                                ]}
                              >
                                {wallet.balance.toLocaleString()}đ
                              </Text>
                              {selectedToWallet === wallet.id && (
                                <View style={styles.checkmarkContainer}>
                                  <Ionicons
                                    name="checkmark-circle"
                                    size={24}
                                    color={wallet.color}
                                  />
                                </View>
                              )}
                            </TouchableOpacity>
                          ))}
                        </View>
                      </ScrollView>
                    )}
                  </View>

                  <View style={styles.modalFooter}>
                    <TouchableOpacity
                      style={[
                        styles.closeButton,
                        { backgroundColor: primaryColor },
                      ]}
                      onPress={() => toggleToWalletModal(false)}
                    >
                      <Text style={styles.closeButtonText}>
                        {t("addTransaction.closeButton") || "Đóng"}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  saveHeaderButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 10,
  },
  card: {
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 4,
    overflow: "hidden",
  },
  typeSelector: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
    gap: 10,
  },
  typeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    paddingVertical: 12,
    borderRadius: 16,
  },
  typeText: {
    marginLeft: 6,
    fontWeight: "600",
    fontSize: 14,
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  // Styles mới cho section nhỏ hơn
  smallSection: {
    paddingHorizontal: 16,
    paddingVertical: 8, // Giảm từ 12 xuống 8
  },
  smallLabel: {
    fontSize: 14, // Giảm từ 16 xuống 14
    fontWeight: "600",
    marginBottom: 8, // Giảm từ 10 xuống 8
    paddingLeft: 4,
  },
  smallDatePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12, // Giảm từ 14 xuống 12
    padding: 12, // Giảm từ 16 xuống 12
    height: 50, // Giảm từ 60 xuống 50
  },
  smallDateText: {
    marginLeft: 8, // Giảm từ 10 xuống 8
    fontSize: 14, // Giảm từ 16 xuống 14
  },
  // Styles mới cho icon và chữ nhỏ hơn
  smallWalletIcon: {
    width: 36, // Giảm từ 42 xuống 36
    height: 36, // Giảm từ 42 xuống 36
    borderRadius: 18, // Phải bằng 1/2 kích thước
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10, // Giảm từ 14 xuống 10
  },
  smallWalletName: {
    fontSize: 14, // Giảm từ 16 xuống 14
    fontWeight: "600",
  },
  smallWalletBalance: {
    fontSize: 12, // Giảm từ 14 xuống 12
    marginTop: 2, // Giảm từ 4 xuống 2
  },
  smallPlaceholderText: {
    fontSize: 14, // Giảm từ 16 xuống 14
    marginLeft: 10, // Giảm từ 12 xuống 10
  },
  smallSelectedItemText: {
    fontSize: 14, // Giảm từ 16 xuống 14
    fontWeight: "600",
    marginLeft: 10, // Giảm từ 12 xuống 10
  },
  smallCategoryIcon: {
    width: 38, // Giảm từ 46 xuống 38
    height: 38, // Giảm từ 46 xuống 38
    borderRadius: 19, // Phải bằng 1/2 kích thước
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10, // Mới thêm
  },
  // Styles cho container row
  rowContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: "space-between",
  },
  // Style cho mỗi cột trong row container
  columnSection: {
    width: "48%", // Chiều rộng 48% để có khoảng trống giữa 2 cột
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
    paddingLeft: 4,
  },
  labelWithButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
    paddingLeft: 4,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  amountContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    height: 60,
    borderRadius: 14,
  },
  // Styles mới cho nút dấu +/- đã sửa để căn giữa hoàn hảo
  signButton: {
    paddingRight: 8,
    paddingLeft: 12,
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    display: "flex",
  },
  sign: {
    fontSize: 24,
    fontWeight: "bold",
  },
  // Styles mới cho bàn phím tùy chỉnh
  amountInputContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  amountDisplay: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: 'left',
  },
  currency: {
    fontSize: 16,
    fontWeight: "600",
  },
  inputContainer: {
    borderRadius: 14,
    height: 60,
  },
  input: {
    flex: 1,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  textAreaContainer: {
    height: 120,
  },
  textArea: {
    paddingTop: 16,
    height: 120,
    textAlignVertical: "top",
  },
  // Style cho nút chọn
  selectorButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 14,
    height: 70,
  },
  selectedItemContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  selectedItemText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 12,
  },
  placeholderText: {
    fontSize: 16,
    marginLeft: 12,
  },
  // Styles cho category trong modal
  categoriesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 8,
    paddingVertical: 8,
    justifyContent: "space-between",
  },
  categoryItem: {
    width: "30%",
    padding: 10,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 14,
    margin: 4,
  },
  categoryIcon: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 6,
  },
  categoryName: {
    fontSize: 11,
    textAlign: "center",
    fontWeight: "500",
  },
  // Styles cũ cho wallets trong modal (dạng list)
  walletsList: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 10,
  },
  walletItemModal: {
    flexDirection: "row",
    alignItems: "center",
    padding: 14,
    borderRadius: 14,
  },
  // Styles cho wallets trong modal (dạng grid)
  walletsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingHorizontal: 8,
    paddingVertical: 8,
    justifyContent: "space-between",
  },
  walletItem: {
    width: "46%", // 2 cột thay vì 3 để có không gian hiển thị số dư
    padding: 12,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 14,
    margin: 6,
    position: "relative",
    minHeight: 130, // Cao hơn so với category để hiển thị đầy đủ thông tin
  },
  walletIconGrid: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  walletNameGrid: {
    fontSize: 14,
    textAlign: "center",
    fontWeight: "600",
    marginBottom: 5,
  },
  walletBalanceGrid: {
    fontSize: 13,
    textAlign: "center",
    fontWeight: "500",
  },
  checkmarkContainer: {
    position: "absolute",
    top: 5,
    right: 5,
  },
  // Styles chung
  walletInfo: {
    flex: 1,
  },
  walletIcon: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 14,
  },
  walletName: {
    fontSize: 16,
    fontWeight: "600",
  },
  walletBalance: {
    fontSize: 14,
    marginTop: 4,
  },
  datePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 14,
    padding: 16,
    height: 60,
  },
  dateText: {
    marginLeft: 10,
    fontSize: 16,
  },
  saveButton: {
    marginHorizontal: 16,
    marginBottom: 30,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
  },
  saveButtonText: {
    fontSize: 18,
    fontWeight: "700",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  // Styles cho toast notification ở giữa màn hình
  toastContainer: {
    position: "absolute",
    top: "50%",
    left: 0,
    right: 0,
    zIndex: 9999,
    alignItems: "center",
    paddingHorizontal: 16,
    transform: [{ translateY: -50 }], // Để đưa nó lên chính giữa màn hình
  },
  toast: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 250,
    maxWidth: "80%", // Giới hạn chiều rộng tối đa
  },
  toastMessage: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 12,
    flex: 1,
  },
  // Styles cho modal
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalContainer: {
    width: "100%",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: "hidden",
    paddingBottom: Platform.OS === "ios" ? 20 : 0, // Đảm bảo không bị che bởi thanh home trên iOS
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(150, 150, 150, 0.2)",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
  },
  modalContent: {
    padding: 16,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "rgba(150, 150, 150, 0.2)",
    alignItems: "center",
  },
  closeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    minWidth: 120,
  },
  closeButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
}); 