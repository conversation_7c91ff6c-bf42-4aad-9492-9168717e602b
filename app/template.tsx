// File: app/template.tsx
// Những file liên quan đến file này: lib/models/transaction_template.ts, context/ThemeContext.tsx

import { Text } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import TransactionTemplateModel from "@/lib/models/transaction_template";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Thêm interface cho Template
interface Template {
  id: string;
  content: string;
}

// Màn hình quản lý mẫu giao dịch
export default function TemplateScreen() {
  const { isDark, themeColors, getCardStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [newTemplate, setNewTemplate] = useState("");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState("");
  const [refreshing, setRefreshing] = useState(false);

  // Lấy danh sách mẫu giao dịch khi vào màn hình
  useEffect(() => {
    loadTemplates();
  }, []);

  // Lấy danh sách mẫu giao dịch từ AsyncStorage
  const loadTemplates = async () => {
    try {
      setLoading(true);
      const data = await TransactionTemplateModel.getAll();
      setTemplates(data);
    } catch (error) {
      console.error("Error loading templates:", error);
      Alert.alert(
        "Lỗi",
        "Không thể tải danh sách mẫu giao dịch. Vui lòng thử lại sau."
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Làm mới danh sách
  const handleRefresh = () => {
    setRefreshing(true);
    loadTemplates();
  };

  // Thêm mẫu giao dịch mới
  const handleAddTemplate = async () => {
    if (!newTemplate.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập nội dung mẫu giao dịch");
      return;
    }

    try {
      setLoading(true);
      await TransactionTemplateModel.create({ content: newTemplate.trim() });
      setNewTemplate("");
      loadTemplates();
    } catch (error) {
      console.error("Error adding template:", error);
      Alert.alert(
        "Lỗi",
        "Không thể thêm mẫu giao dịch. Vui lòng thử lại sau."
      );
      setLoading(false);
    }
  };

  // Xử lý khi bấm nút sửa
  const handleEditTemplate = (id: string, content: string) => {
    setEditingId(id);
    setEditingContent(content);
  };

  // Lưu mẫu giao dịch đã sửa
  const handleSaveEdit = async () => {
    if (!editingId || !editingContent.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập nội dung mẫu giao dịch");
      return;
    }

    try {
      setLoading(true);
      await TransactionTemplateModel.update(editingId, {
        content: editingContent.trim(),
      });
      setEditingId(null);
      setEditingContent("");
      loadTemplates();
    } catch (error) {
      console.error("Error updating template:", error);
      Alert.alert(
        "Lỗi",
        "Không thể cập nhật mẫu giao dịch. Vui lòng thử lại sau."
      );
      setLoading(false);
    }
  };

  // Xử lý khi bấm nút xóa
  const handleDeleteTemplate = (id: string) => {
    Alert.alert(
      "Xác nhận xóa",
      "Bạn có chắc chắn muốn xóa mẫu giao dịch này?",
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              setLoading(true);
              await TransactionTemplateModel.delete(id);
              loadTemplates();
            } catch (error) {
              console.error("Error deleting template:", error);
              Alert.alert(
                "Lỗi",
                "Không thể xóa mẫu giao dịch. Vui lòng thử lại sau."
              );
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  // Hủy chỉnh sửa
  const cancelEdit = () => {
    setEditingId(null);
    setEditingContent("");
  };

  // Render item cho mỗi mẫu giao dịch
  const renderTemplateItem = ({ item }: { item: Template }) => {
    const isEditing = editingId === item.id;

    return (
      <TouchableOpacity
        onPress={() => !isEditing && handleEditTemplate(item.id, item.content)}
        activeOpacity={0.7}
      >
        <View
          style={[
            styles.templateItem,
            getCardStyle("medium"),
            { marginBottom: 12 },
          ]}
        >
          {isEditing ? (
            // Form chỉnh sửa
            <View style={styles.editForm}>
              <TextInput
                style={[
                  styles.editInput,
                  { color: themeColors.text, borderColor: themeColors.border },
                ]}
                value={editingContent}
                onChangeText={setEditingContent}
                multiline
                placeholder="Nhập nội dung mẫu giao dịch"
                placeholderTextColor={themeColors.secondaryText}
                autoFocus
              />
              <View style={styles.editButtons}>
                <TouchableOpacity
                  style={[
                    styles.editButton,
                    { backgroundColor: themeColors.primary },
                  ]}
                  onPress={handleSaveEdit}
                >
                  <Text style={styles.editButtonText}>Lưu</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.editButton,
                    { backgroundColor: themeColors.primary },
                  ]}
                  onPress={cancelEdit}
                >
                  <Text style={styles.editButtonText}>Hủy</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.editButton,
                    { backgroundColor: themeColors.danger },
                  ]}
                  onPress={() => handleDeleteTemplate(item.id)}
                >
                  <Text style={styles.editButtonText}>Xóa</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            // Hiển thị mẫu giao dịch
            <>
              <Text style={[styles.templateText, { color: themeColors.text }]}>
                {item.content}
              </Text>
            </>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
      edges={["top"]}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        {/* Header */}
        <View
          style={[
            styles.header,
            {
              backgroundColor: themeColors.background,
              borderBottomColor: themeColors.border,
            },
          ]}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={themeColors.primary}
            />
          </TouchableOpacity>
          <Text style={[styles.title, { color: themeColors.text }]}>
            Mẫu giao dịch
          </Text>
          <View style={styles.addButton} />
        </View>

        {/* Form thêm mẫu mới - luôn hiển thị */}
        <View
          style={[
            styles.addFormContainer,
            {
              backgroundColor: themeColors.cardBackground,
              borderBottomColor: themeColors.border,
            },
          ]}
        >
          <TextInput
            style={[styles.addInput, { color: themeColors.text }]}
            value={newTemplate}
            onChangeText={setNewTemplate}
            placeholder="Nhập mẫu giao dịch mới..."
            placeholderTextColor={themeColors.secondaryText}
          />
          <TouchableOpacity
            style={[
              styles.saveButton,
              { backgroundColor: themeColors.primary },
            ]}
            onPress={handleAddTemplate}
          >
            <Text style={styles.saveButtonText}>Lưu</Text>
          </TouchableOpacity>
        </View>

        {/* Danh sách mẫu giao dịch */}
        {loading && !refreshing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator
              size="large"
              color={themeColors.primary}
            />
            <Text
              style={[
                styles.loadingText,
                { color: themeColors.secondaryText },
              ]}
            >
              Đang tải...
            </Text>
          </View>
        ) : templates.length === 0 ? (
          <ScrollView
            contentContainerStyle={styles.emptyContainer}
          >
            <Ionicons
              name="document-text-outline"
              size={64}
              color={themeColors.secondaryText}
            />
            <Text
              style={[
                styles.emptyText,
                { color: themeColors.secondaryText },
              ]}
            >
              Chưa có mẫu giao dịch nào
            </Text>
            <Text
              style={[
                styles.emptySubText,
                { color: themeColors.secondaryText },
              ]}
            >
              Bấm nút + để thêm mẫu giao dịch mới
            </Text>
          </ScrollView>
        ) : (
          <FlatList
            data={templates}
            renderItem={renderTemplateItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.list}
            showsVerticalScrollIndicator={false}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
  },
  addButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "bold",
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 15,
    marginTop: 8,
    textAlign: "center",
  },
  list: {
    padding: 16,
  },
  templateItem: {
    padding: 16,
    borderRadius: 12,
  },
  templateText: {
    fontSize: 16,
    marginBottom: 8,
    flex: 1,
  },
  templateActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  addFormContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    height: 60,
  },
  addInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  saveButtonText: {
    color: "white",
    fontWeight: "bold",
  },
  editForm: {
    width: "100%",
  },
  editInput: {
    fontSize: 16,
    padding: 10,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 10,
  },
  editButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 8,
  },
  editButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  editButtonText: {
    color: "white",
    fontWeight: "bold",
  },
});