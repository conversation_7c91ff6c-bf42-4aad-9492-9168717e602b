// File: app/(more)/ai-learning.tsx
// File này liên quan đến: context/ThemeContext.tsx, lib/services/ai-learning-service.ts, lib/models/transaction.ts, lib/models/category.ts
// File này dùng để đào tạo AI chatbot phân loại giao dịch đúng danh mục theo ý muốn người dùng

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Category, CategoryModel } from "@/lib/models/category";
import { TransactionModel } from "@/lib/models/transaction";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, useFocusEffect } from "expo-router";
import React, { useCallback, useState, useMemo } from "react";
import {
  ActivityIndicator,
  <PERSON><PERSON>,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Key lưu trữ dữ liệu học máy
const AI_LEARNING_DATA_KEY = '@ai_learning_data';

// Interface cho dữ liệu học máy
interface LearningData {
  description: string;
  correctCategoryId: string;
  correctCategoryName: string;
  timestamp: string;
}

// Interface cho giao dịch được đề xuất để học
interface SuggestedTransaction {
  id: string;
  description: string;
  currentCategoryId: string;
  currentCategoryName: string;
  amount: number;
  date: string;
}

export default function AILearningScreen() {
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  // States chính
  const [learningData, setLearningData] = useState<LearningData[]>([]);
  const [suggestedTransactions, setSuggestedTransactions] = useState<SuggestedTransaction[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'suggested' | 'learned'>('suggested');
  
  // States cho tìm kiếm
  const [searchQuery, setSearchQuery] = useState("");
  
  // States cho form thêm mới
  const [showAddForm, setShowAddForm] = useState(false);
  const [newDescription, setNewDescription] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState("");
  
  // States cho học từ giao dịch đề xuất
  const [selectedTransaction, setSelectedTransaction] = useState<SuggestedTransaction | null>(null);
  const [showSuggestedForm, setShowSuggestedForm] = useState(false);
  const [suggestedCategoryId, setSuggestedCategoryId] = useState("");

  // Load dữ liệu khi màn hình được focus
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [])
  );

  // Lọc dữ liệu theo tìm kiếm
  const filteredLearningData = useMemo(() => {
    if (!searchQuery.trim()) return learningData;
    
    const query = searchQuery.toLowerCase().trim();
    return learningData.filter(item => 
      item.description.toLowerCase().includes(query) ||
      item.correctCategoryName.toLowerCase().includes(query)
    );
  }, [learningData, searchQuery]);

  const filteredSuggestedTransactions = useMemo(() => {
    if (!searchQuery.trim()) return suggestedTransactions;
    
    const query = searchQuery.toLowerCase().trim();
    return suggestedTransactions.filter(item => 
      item.description.toLowerCase().includes(query) ||
      item.currentCategoryName.toLowerCase().includes(query)
    );
  }, [suggestedTransactions, searchQuery]);

  // Load tất cả dữ liệu cần thiết
  const loadData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadLearningData(),
        loadCategories(),
        loadSuggestedTransactions()
      ]);
    } catch (error) {
      console.error("Lỗi khi tải dữ liệu:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load dữ liệu học máy từ AsyncStorage
  const loadLearningData = async () => {
    try {
      const stored = await AsyncStorage.getItem(AI_LEARNING_DATA_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        setLearningData(data);
      }
    } catch (error) {
      console.error("Lỗi khi tải dữ liệu học máy:", error);
    }
  };

  // Load danh sách danh mục
  const loadCategories = async () => {
    try {
      const allCategories = await CategoryModel.getAll();
      setCategories(allCategories);
    } catch (error) {
      console.error("Lỗi khi tải danh mục:", error);
    }
  };

  // Load giao dịch gần đây để đề xuất học - Giới hạn tối đa 20 giao dịch
  const loadSuggestedTransactions = async () => {
    try {
      const recentTransactions = await TransactionModel.getRecent(50); // Lấy nhiều hơn để lọc
      
      // Chỉ lấy giao dịch chi tiêu và có mô tả, giới hạn 20 giao dịch
      const suggested = recentTransactions
        .filter(t => t.type === 'expense' && t.description && t.description.trim() !== '')
        .slice(0, 20) // Giới hạn tối đa 20 giao dịch
        .map(t => ({
          id: t.id,
          description: t.description,
          currentCategoryId: t.category_id,
          currentCategoryName: t.category?.name || 'Không có danh mục',
          amount: Math.abs(t.amount),
          date: t.date
        }));

      setSuggestedTransactions(suggested);
    } catch (error) {
      console.error("Lỗi khi tải giao dịch đề xuất:", error);
    }
  };

  // Lưu dữ liệu học máy vào AsyncStorage
  const saveLearningData = async (data: LearningData[]) => {
    try {
      await AsyncStorage.setItem(AI_LEARNING_DATA_KEY, JSON.stringify(data));
      setLearningData(data);
    } catch (error) {
      console.error("Lỗi khi lưu dữ liệu học máy:", error);
      Alert.alert("Lỗi", "Không thể lưu dữ liệu học máy");
    }
  };

  // Mở form thêm dữ liệu mới
  const openAddForm = () => {
    setNewDescription("");
    setSelectedCategoryId("");
    setShowAddForm(true);
  };

  // Đóng form thêm dữ liệu mới
  const closeAddForm = () => {
    setShowAddForm(false);
    setNewDescription("");
    setSelectedCategoryId("");
  };

  // Thêm dữ liệu học mới
  const handleAddLearningData = async () => {
    if (!newDescription.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập mô tả giao dịch");
      return;
    }

    if (!selectedCategoryId) {
      Alert.alert("Lỗi", "Vui lòng chọn danh mục");
      return;
    }

    const category = categories.find(c => c.id === selectedCategoryId);
    if (!category) {
      Alert.alert("Lỗi", "Danh mục không hợp lệ");
      return;
    }

    const newData: LearningData = {
      description: newDescription.trim(),
      correctCategoryId: selectedCategoryId,
      correctCategoryName: category.name,
      timestamp: new Date().toISOString()
    };

    const updatedData = [...learningData, newData];
    await saveLearningData(updatedData);

    closeAddForm();
    Alert.alert("Thành công", "Đã thêm dữ liệu học mới");
  };

  // Mở form học từ giao dịch đề xuất
  const openSuggestedForm = (transaction: SuggestedTransaction) => {
    setSelectedTransaction(transaction);
    setSuggestedCategoryId("");
    setShowSuggestedForm(true);
  };

  // Đóng form học từ giao dịch đề xuất
  const closeSuggestedForm = () => {
    setShowSuggestedForm(false);
    setSelectedTransaction(null);
    setSuggestedCategoryId("");
  };

  // Xác nhận học từ giao dịch đề xuất
  const handleLearnFromSuggested = async () => {
    if (!selectedTransaction || !suggestedCategoryId) return;

    const category = categories.find(c => c.id === suggestedCategoryId);
    if (!category) {
      Alert.alert("Lỗi", "Danh mục không hợp lệ");
      return;
    }

    const newData: LearningData = {
      description: selectedTransaction.description,
      correctCategoryId: suggestedCategoryId,
      correctCategoryName: category.name,
      timestamp: new Date().toISOString()
    };

    const updatedData = [...learningData, newData];
    await saveLearningData(updatedData);

    // Xóa giao dịch khỏi danh sách đề xuất
    setSuggestedTransactions(prev => 
      prev.filter(t => t.id !== selectedTransaction.id)
    );

    closeSuggestedForm();
    Alert.alert("Thành công", "AI đã học từ giao dịch này");
  };

  // Xóa dữ liệu học
  const deleteLearningData = async (index: number) => {
    Alert.alert(
      "Xác nhận xóa",
      "Anh có chắc muốn xóa dữ liệu học này không?",
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            const updatedData = learningData.filter((_, i) => i !== index);
            await saveLearningData(updatedData);
          }
        }
      ]
    );
  };

  // Xóa tất cả dữ liệu học
  const clearAllLearningData = () => {
    Alert.alert(
      "Xóa tất cả dữ liệu học",
      "Điều này sẽ xóa toàn bộ dữ liệu đã học của AI. Anh có chắc chắn không?",
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa tất cả",
          style: "destructive",
          onPress: async () => {
            await saveLearningData([]);
            Alert.alert("Thành công", "Đã xóa tất cả dữ liệu học");
          }
        }
      ]
    );
  };

  // Render dropdown chọn danh mục
  const renderCategoryDropdown = (
    value: string, 
    onSelect: (categoryId: string) => void,
    placeholder: string = "Chọn danh mục"
  ) => {
    const selectedCategory = categories.find(c => c.id === value);
    
    return (
      <View style={[styles.dropdownContainer, getCardStyle('low')]}>
        <Text style={[styles.dropdownLabel, { color: themeColors.text }]}>
          Danh mục:
        </Text>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.categoriesScroll}
        >
          {categories
            .filter(c => c.type === 'expense')
            .map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryChip,
                  {
                    backgroundColor: value === category.id 
                      ? themeColors.primary 
                      : themeColors.iconBackground,
                    borderColor: value === category.id 
                      ? themeColors.primary 
                      : themeColors.border,
                  }
                ]}
                onPress={() => onSelect(category.id)}
              >
                <View
                  style={[
                    styles.categoryChipIcon,
                    { backgroundColor: category.color }
                  ]}
                >
                  <Ionicons name={category.icon as any} size={16} color="white" />
                </View>
                <Text style={[
                  styles.categoryChipText,
                  { 
                    color: value === category.id 
                      ? 'white' 
                      : themeColors.text 
                  }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
        </ScrollView>
      </View>
    );
  };

  // Render item dữ liệu học
  const renderLearningItem = ({ item, index }: { item: LearningData; index: number }) => (
    <View style={[styles.learningItem, getCardStyle('medium')]}>
      <View style={styles.learningItemContent}>
        <Text style={[styles.learningDescription, { color: themeColors.text }]}>
          "{item.description}"
        </Text>
        <Text style={[styles.learningCategory, { color: themeColors.primary }]}>
          → {item.correctCategoryName}
        </Text>
        <Text style={[styles.learningTime, { color: themeColors.secondaryText }]}>
          {new Date(item.timestamp).toLocaleString('vi-VN')}
        </Text>
      </View>
      <TouchableOpacity
        style={[styles.deleteButton, { backgroundColor: themeColors.dangerBackground }]}
        onPress={() => deleteLearningData(index)}
      >
        <Ionicons name="trash-outline" size={18} color={themeColors.danger} />
      </TouchableOpacity>
    </View>
  );

  // Render item giao dịch đề xuất
  const renderSuggestedItem = ({ item }: { item: SuggestedTransaction }) => (
    <TouchableOpacity
      style={[styles.suggestedItem, getCardStyle('low')]}
      onPress={() => openSuggestedForm(item)}
    >
      <View style={styles.suggestedContent}>
        <Text style={[styles.suggestedDescription, { color: themeColors.text }]}>
          "{item.description}"
        </Text>
        <Text style={[styles.suggestedCategory, { color: themeColors.secondaryText }]}>
          Hiện tại: {item.currentCategoryName}
        </Text>
        <Text style={[styles.suggestedAmount, { color: themeColors.danger }]}>
          -{item.amount.toLocaleString()}đ
        </Text>
      </View>
      <Ionicons name="school-outline" size={24} color={themeColors.primary} />
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView edges={["top"]} style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            AI Học
          </Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView edges={["top"]} style={[styles.container, { backgroundColor: themeColors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          AI Học
        </Text>
        <TouchableOpacity
          onPress={openAddForm}
          style={[styles.addButton, { backgroundColor: themeColors.primary }]}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: themeColors.background }]}>
        <View style={[
          styles.searchInputContainer, 
          { 
            backgroundColor: themeColors.cardBackground,
            borderColor: themeColors.border 
          }
        ]}>
          <Ionicons name="search" size={20} color={themeColors.secondaryText} />
          <TextInput
            style={[styles.searchInput, { color: themeColors.text }]}
            placeholder="Tìm kiếm mô tả hoặc danh mục..."
            placeholderTextColor={themeColors.secondaryText}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery("")}>
              <Ionicons name="close-circle" size={20} color={themeColors.secondaryText} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { 
        backgroundColor: themeColors.background,
        borderBottomColor: themeColors.border 
      }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'suggested' && { borderBottomColor: themeColors.primary }
          ]}
          onPress={() => setActiveTab('suggested')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'suggested' ? themeColors.primary : themeColors.secondaryText }
          ]}>
            Đề xuất ({filteredSuggestedTransactions.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'learned' && { borderBottomColor: themeColors.primary }
          ]}
          onPress={() => setActiveTab('learned')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'learned' ? themeColors.primary : themeColors.secondaryText }
          ]}>
            Đã học ({filteredLearningData.length})
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Thông tin */}
        <View style={[styles.infoCard, getCardStyle('medium')]}>
          <View style={[styles.infoIcon, { backgroundColor: themeColors.primary }]}>
            <Ionicons name="bulb-outline" size={24} color="white" />
          </View>
          <View style={styles.infoContent}>
            <Text style={[styles.infoTitle, { color: themeColors.text }]}>
              Đào tạo AI phân loại danh mục
            </Text>
            <Text style={[styles.infoDescription, { color: themeColors.secondaryText }]}>
              Dạy AI cách phân loại giao dịch vào đúng danh mục. 
              AI sẽ ưu tiên sử dụng dữ liệu đã học khi phân loại giao dịch mới.
            </Text>
          </View>
        </View>

        {/* Tab Content */}
        {activeTab === 'suggested' ? (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Đề xuất học từ giao dịch gần đây
            </Text>
            {filteredSuggestedTransactions.length === 0 ? (
              <View style={[styles.emptyState, getCardStyle('low')]}>
                <Ionicons name="document-text-outline" size={48} color={themeColors.secondaryText} />
                <Text style={[styles.emptyText, { color: themeColors.secondaryText }]}>
                  {searchQuery ? "Không tìm thấy giao dịch nào" : "Không có giao dịch đề xuất"}
                </Text>
                <Text style={[styles.emptySubtext, { color: themeColors.secondaryText }]}>
                  {searchQuery ? "Thử từ khóa khác" : "Thêm giao dịch mới để có đề xuất học"}
                </Text>
              </View>
            ) : (
              <FlatList
                data={filteredSuggestedTransactions}
                keyExtractor={(item) => item.id}
                renderItem={renderSuggestedItem}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            )}
          </View>
        ) : (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Dữ liệu đã học ({filteredLearningData.length})
              </Text>
              {learningData.length > 0 && (
                <TouchableOpacity
                  onPress={clearAllLearningData}
                  style={[styles.clearButton, { backgroundColor: themeColors.dangerBackground }]}
                >
                  <Text style={[styles.clearButtonText, { color: themeColors.danger }]}>
                    Xóa tất cả
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            
            {filteredLearningData.length === 0 ? (
              <View style={[styles.emptyState, getCardStyle('low')]}>
                <Ionicons name="school-outline" size={48} color={themeColors.secondaryText} />
                <Text style={[styles.emptyText, { color: themeColors.secondaryText }]}>
                  {searchQuery ? "Không tìm thấy dữ liệu nào" : "Chưa có dữ liệu học nào"}
                </Text>
                <Text style={[styles.emptySubtext, { color: themeColors.secondaryText }]}>
                  {searchQuery ? "Thử từ khóa khác" : "Nhấn nút + để thêm hoặc chọn từ giao dịch đề xuất"}
                </Text>
              </View>
            ) : (
              <FlatList
                data={filteredLearningData}
                keyExtractor={(_, index) => index.toString()}
                renderItem={renderLearningItem}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            )}
          </View>
        )}
      </ScrollView>

      {/* Modal form thêm dữ liệu mới */}
      <Modal
        visible={showAddForm}
        transparent={true}
        animationType="slide"
        onRequestClose={closeAddForm}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: themeColors.cardBackground }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Thêm dữ liệu học mới
            </Text>
            
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: themeColors.background,
                  borderColor: themeColors.border,
                  color: themeColors.text
                }
              ]}
              placeholder="Nhập mô tả giao dịch (VD: ăn trưa, đi taxi...)"
              placeholderTextColor={themeColors.secondaryText}
              value={newDescription}
              onChangeText={setNewDescription}
              multiline
              maxLength={100}
            />

            {renderCategoryDropdown(selectedCategoryId, setSelectedCategoryId)}

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: themeColors.border }]}
                onPress={closeAddForm}
              >
                <Text style={[styles.modalButtonText, { color: themeColors.text }]}>
                  Hủy
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalButton, 
                  { 
                    backgroundColor: (newDescription.trim() && selectedCategoryId) 
                      ? themeColors.primary 
                      : themeColors.border,
                    opacity: (newDescription.trim() && selectedCategoryId) ? 1 : 0.5
                  }
                ]}
                onPress={handleAddLearningData}
                disabled={!newDescription.trim() || !selectedCategoryId}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  Thêm
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Modal form học từ giao dịch đề xuất */}
      <Modal
        visible={showSuggestedForm}
        transparent={true}
        animationType="slide"
        onRequestClose={closeSuggestedForm}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: themeColors.cardBackground }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Chọn danh mục đúng cho:
            </Text>
            
            {selectedTransaction && (
              <View style={[styles.transactionInfo, getCardStyle('low')]}>
                <Text style={[styles.transactionText, { color: themeColors.text }]}>
                  "{selectedTransaction.description}"
                </Text>
                <Text style={[styles.transactionCurrent, { color: themeColors.secondaryText }]}>
                  Hiện tại: {selectedTransaction.currentCategoryName}
                </Text>
              </View>
            )}

            {renderCategoryDropdown(suggestedCategoryId, setSuggestedCategoryId)}

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: themeColors.border }]}
                onPress={closeSuggestedForm}
              >
                <Text style={[styles.modalButtonText, { color: themeColors.text }]}>
                  Hủy
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalButton,
                  {
                    backgroundColor: suggestedCategoryId ? themeColors.primary : themeColors.border,
                    opacity: suggestedCategoryId ? 1 : 0.5
                  }
                ]}
                onPress={handleLearnFromSuggested}
                disabled={!suggestedCategoryId}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  Học
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  infoCard: {
    margin: 16,
    padding: 16,
    flexDirection: "row",
    alignItems: "flex-start",
  },
  infoIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: "600",
  },
  learningItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 12,
  },
  learningItemContent: {
    flex: 1,
  },
  learningDescription: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  learningCategory: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  learningTime: {
    fontSize: 12,
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  suggestedItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginBottom: 12,
  },
  suggestedContent: {
    flex: 1,
  },
  suggestedDescription: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  suggestedCategory: {
    fontSize: 14,
    marginBottom: 4,
  },
  suggestedAmount: {
    fontSize: 14,
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: "500",
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: "center",
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: "90%",
    maxHeight: "80%",
    borderRadius: 16,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    minHeight: 80,
    textAlignVertical: "top",
  },
  dropdownContainer: {
    padding: 16,
    marginBottom: 20,
    borderRadius: 12,
  },
  dropdownLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  categoriesScroll: {
    flexDirection: 'row',
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryChipIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  transactionInfo: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  transactionText: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  transactionCurrent: {
    fontSize: 14,
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: "center",
    marginHorizontal: 4,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
});