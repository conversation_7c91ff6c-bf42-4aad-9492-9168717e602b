// File: app/(more)/more-settings.tsx
// File này cho phép người dùng di chuyển các mục giữa tab Ch<PERSON>h và tab Thêm
// File này liên quan đến: app/(tabs)/more.tsx, context/ThemeContext.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import { Alert, ScrollView, StyleSheet, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function MoreSettingsScreen() {
  const { themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();

  // State quản lý items trong từng tab
  const [mainTabItems, setMainTabItems] = useState(['wallets', 'categories', 'stats']);
  const [moreTabItems, setMoreTabItems] = useState(['debt', 'templates', 'calendar', 'moving', 'financeCalculator']);

  // Load cài đặt khi component mount
  useEffect(() => {
    loadTabSettings();
  }, []);

  // Load cài đặt tab từ AsyncStorage
  const loadTabSettings = async () => {
    try {
      const savedMainTab = await AsyncStorage.getItem('main_tab_items');
      const savedMoreTab = await AsyncStorage.getItem('more_tab_items');
      
      if (savedMainTab !== null) {
        setMainTabItems(JSON.parse(savedMainTab));
      }
      if (savedMoreTab !== null) {
        setMoreTabItems(JSON.parse(savedMoreTab));
      }
    } catch (error) {
      console.log('Error loading tab settings:', error);
    }
  };

  // Save cài đặt tab vào AsyncStorage
  const saveTabSettings = async (mainItems: string[], moreItems: string[]) => {
    try {
      await AsyncStorage.setItem('main_tab_items', JSON.stringify(mainItems));
      await AsyncStorage.setItem('more_tab_items', JSON.stringify(moreItems));
    } catch (error) {
      console.log('Error saving tab settings:', error);
    }
  };

  // Di chuyển item từ tab này sang tab khác
  const moveItem = (itemId: string, fromMain: boolean) => {
    if (fromMain) {
      // Di chuyển từ tab Chính sang tab Thêm
      if (mainTabItems.length <= 1) {
        Alert.alert('Thông báo', 'Tab Chính phải có ít nhất 1 mục');
        return;
      }
      const newMainItems = mainTabItems.filter(id => id !== itemId);
      const newMoreItems = [...moreTabItems, itemId];
      
      setMainTabItems(newMainItems);
      setMoreTabItems(newMoreItems);
      saveTabSettings(newMainItems, newMoreItems);
    } else {
      // Di chuyển từ tab Thêm sang tab Chính
      const newMoreItems = moreTabItems.filter(id => id !== itemId);
      const newMainItems = [...mainTabItems, itemId];
      
      setMainTabItems(newMainItems);
      setMoreTabItems(newMoreItems);
      saveTabSettings(newMainItems, newMoreItems);
    }
  };

  // Reset về mặc định
  const resetToDefault = () => {
    Alert.alert(
      'Xác nhận',
      'Bạn có muốn khôi phục về cài đặt mặc định không?',
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Đồng ý',
          onPress: () => {
            const defaultMain = ['wallets', 'categories', 'stats'];
            const defaultMore = ['debt', 'templates', 'calendar', 'moving', 'financeCalculator'];
            
            setMainTabItems(defaultMain);
            setMoreTabItems(defaultMore);
            saveTabSettings(defaultMain, defaultMore);
          }
        }
      ]
    );
  };

  // Tất cả các mục có thể có
  const allItems = {
    wallets: {
      id: "wallets",
      title: t("more.wallets") || "Ví",
      icon: "wallet-outline",
      description: t("more.walletsDescription") || "Quản lý ví của bạn",
    },
    categories: {
      id: "categories",
      title: t("more.categories") || "Danh mục",
      icon: "pricetags-outline",
      description: t("more.categoriesDescription") || "Tùy chỉnh danh mục thu chi",
    },
    stats: {
      id: "stats",
      title: t("stats.title") || "Thống kê",
      icon: "stats-chart-outline",
      description: t("more.statsDescription") || "Xem thống kê thu chi của bạn",
    },
    debt: {
      id: "debt",
      title: "Sổ Nợ",
      icon: "cash-outline",
      description: "Quản lý khoản nợ phải trả và cho vay",
    },
    templates: {
      id: "templates",
      title: t("more.templates") || "Mẫu giao dịch",
      icon: "document-text-outline",
      description: t("more.templatesDescription") || "Quản lý mẫu giao dịch cho chatbot",
    },
    calendar: {
      id: "calendar",
      title: "Lịch",
      icon: "calendar-outline",
      description: "Xem chi tiêu theo lịch hàng ngày/tuần/tháng",
    },
    moving: {
      id: "moving",
      icon: "swap-horizontal",
      title: t("more.movingFeature") || "Chuyển App",
      description: t("more.movingDescription") || "Chuyển dữ liệu từ ứng dụng tài chính khác",
    },
    financeCalculator: {
      id: "financeCalculator",
      title: "Máy tính tài chính",
      icon: "calculator-outline",
      description: "Máy tính lãi suất và chuyển đổi tiền tệ",
    },
  };

  // Render danh sách items cho một tab
  const renderTabItems = (itemIds: string[], isMainTab: boolean, tabTitle: string) => {
    const items = itemIds.map(id => allItems[id as keyof typeof allItems]).filter(Boolean);
    
    return (
      <View style={[styles.tabSection, { backgroundColor: "transparent" }]}>
        <View style={[styles.tabHeader, { backgroundColor: "transparent" }]}>
          <Text style={[styles.tabTitle, { color: themeColors.text, backgroundColor: "transparent" }]}>
            {tabTitle} ({items.length})
          </Text>
        </View>
        
        {items.map((item) => (
          <View
            key={item.id}
            style={[styles.itemCard, getCardStyle("medium")]}
          >
            <View style={[styles.itemLeft, { backgroundColor: "transparent" }]}>
              <View style={[styles.iconContainer, { backgroundColor: themeColors.primary + '20' }]}>
                <Ionicons
                  name={item.icon as any}
                  size={24}
                  color={themeColors.primary}
                />
              </View>
              <View style={[styles.itemInfo, { backgroundColor: "transparent" }]}>
                <Text style={[styles.itemTitle, { color: themeColors.text, backgroundColor: "transparent" }]}>
                  {item.title}
                </Text>
                <Text style={[styles.itemDescription, { color: themeColors.secondaryText, backgroundColor: "transparent" }]}>
                  {item.description}
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={[styles.moveButton, { backgroundColor: themeColors.primary + '20' }]}
              onPress={() => moveItem(item.id, isMainTab)}
            >
              <Ionicons
                name={isMainTab ? "arrow-down" : "arrow-up"}
                size={20}
                color={themeColors.primary}
              />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background },
      ]}
      edges={["top", "left", "right"]}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="arrow-back"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Tùy chỉnh Tab
        </Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={resetToDefault}
        >
          <Ionicons
            name="refresh-outline"
            size={24}
            color={themeColors.primary}
          />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {/* Mô tả */}
        <View style={[styles.descriptionCard, getCardStyle("medium")]}>
          <Text style={[styles.descriptionText, { color: themeColors.secondaryText }]}>
            Nhấn mũi tên để di chuyển tính năng giữa tab "Chính" và tab "Thêm". Tab Chính phải có ít nhất 1 mục.
          </Text>
        </View>

        {/* Tab Chính */}
        {renderTabItems(mainTabItems, true, "Tab Chính")}

        {/* Divider */}
        <View style={[styles.divider, { backgroundColor: themeColors.border }]} />

        {/* Tab Thêm */}
        {renderTabItems(moreTabItems, false, "Tab Thêm")}

        {/* Thông tin thêm */}
        <View style={[styles.infoCard, getCardStyle("medium")]}>
          <View style={styles.infoHeader}>
            <Ionicons
              name="information-circle-outline"
              size={20}
              color={themeColors.primary}
            />
            <Text style={[styles.infoTitle, { color: themeColors.text }]}>
              Hướng dẫn
            </Text>
          </View>
          <Text style={[styles.infoText, { color: themeColors.secondaryText }]}>
            • Nhấn mũi tên lên/xuống để di chuyển tính năng{'\n'}
            • Tab Chính sẽ hiển thị các tính năng quan trọng nhất{'\n'}
            • Các thay đổi được lưu tự động{'\n'}
            • Nhấn icon làm mới để khôi phục mặc định
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingBottom: 8,
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
  },
  resetButton: {
    padding: 4,
  },
  descriptionCard: {
    margin: 16,
    marginBottom: 8,
    padding: 16,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  tabSection: {
    padding: 16,
    paddingTop: 8,
  },
  tabHeader: {
    marginBottom: 12,
  },
  tabTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  itemCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    marginBottom: 12,
  },
  itemLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  itemDescription: {
    fontSize: 14,
  },
  moveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  divider: {
    height: 1,
    marginHorizontal: 16,
    marginVertical: 8,
  },
  infoCard: {
    margin: 16,
    padding: 16,
  },
  infoHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
});