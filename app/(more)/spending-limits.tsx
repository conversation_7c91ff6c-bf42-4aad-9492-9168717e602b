// File: app/(more)/spending-limits.tsx
// <PERSON>àn hình quản lý giới hạn chi tiêu
// File này liên quan đến: context/ThemeContext.tsx, lib/services/SpendingLimitService.ts, components/LimitProgressBar.tsx

import { Text, View } from "@/components/Themed";
import { LimitProgressBar } from "@/components/LimitProgressBar";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Category, CategoryModel } from "@/lib/models/category";
import { 
  SpendingLimitService, 
  SpendingLimit, 
  LimitUsage, 
  LimitSettings,
  LimitPeriod 
} from "@/lib/services/SpendingLimitService";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import React, { useCallback, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  TextInput,
  TouchableOpacity,
  Keyboard,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

interface CategoryWithLimit extends Category {
  limit?: SpendingLimit;
  usage?: LimitUsage;
}

export default function SpendingLimitsScreen() {
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();
  const translateCategoryName = useCategoryTranslation();

  // States
  const [categories, setCategories] = useState<CategoryWithLimit[]>([]);
  const [settings, setSettings] = useState<LimitSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'settings'>('overview');
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingLimit, setEditingLimit] = useState<SpendingLimit | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  
  // Form states
  const [limitAmount, setLimitAmount] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState<LimitPeriod>('monthly');
  const [warningEnabled, setWarningEnabled] = useState(true);
  const [dangerEnabled, setDangerEnabled] = useState(true);

  // Load data khi vào màn hình
  useFocusEffect(
    useCallback(() => {
      loadData();
      // Khởi tạo service nếu chưa có
      SpendingLimitService.initialize();
    }, [])
  );

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Khởi tạo SpendingLimitService trước
      await SpendingLimitService.initialize();
      
      // Load categories và limits
      const expenseCategories = await CategoryModel.getByType('expense');
      const allLimits = await SpendingLimitService.getAllLimits();
      const allUsages = await SpendingLimitService.calculateAllUsages();
      let currentSettings = await SpendingLimitService.getSettings();

      // Nếu không có settings, tạo default
      if (!currentSettings) {
        currentSettings = {
          defaultPeriod: 'monthly',
          defaultWarningEnabled: true,
          defaultDangerEnabled: true,
          checkFrequency: 'realtime',
          notificationTime: '09:00',
        };
        await SpendingLimitService.saveSettings(currentSettings);
      }

      // Combine data
      const categoriesWithLimits: CategoryWithLimit[] = expenseCategories.map(category => {
        const limit = allLimits.find(l => l.categoryId === category.id);
        const usage = allUsages.find(u => u.categoryId === category.id);
        
        return {
          ...category,
          limit,
          usage,
        };
      });

      setCategories(categoriesWithLimits);
      setSettings(currentSettings);
    } catch (error) {
      console.error('Lỗi load data:', error);
      Alert.alert('Lỗi', 'Không thể tải dữ liệu');
    } finally {
      setIsLoading(false);
    }
  };

  // Mở modal thêm limit
  const openAddModal = (category: Category) => {
    setSelectedCategory(category);
    setEditingLimit(null);
    
    // Set default values từ settings hoặc mặc định
    setSelectedPeriod(settings?.defaultPeriod || 'monthly');
    setWarningEnabled(settings?.defaultWarningEnabled ?? true);
    setDangerEnabled(settings?.defaultDangerEnabled ?? true);
    setLimitAmount('');
    
    setShowAddModal(true);
  };

  // Mở modal edit limit
  const openEditModal = (category: Category, limit: SpendingLimit) => {
    setSelectedCategory(category);
    setEditingLimit(limit);
    setSelectedPeriod(limit.period);
    setWarningEnabled(limit.notifications.warning);
    setDangerEnabled(limit.notifications.danger);
    setLimitAmount(limit.limit.toLocaleString('vi-VN')); // Format existing amount
    setShowAddModal(true);
  };

  // Hàm format số tiền với dấu phẩy
  const formatCurrency = (value: string) => {
    // Loại bỏ tất cả ký tự không phải số
    const numericValue = value.replace(/[^\d]/g, '');
    
    // Thêm dấu phẩy ngăn cách hàng nghìn
    if (numericValue) {
      return parseInt(numericValue).toLocaleString('vi-VN');
    }
    return '';
  };

  // Hàm xử lý khi thay đổi input
  const handleAmountChange = (text: string) => {
    const formatted = formatCurrency(text);
    setLimitAmount(formatted);
  };

  // Hàm lấy giá trị số từ formatted string
  const getNumericValue = (formattedValue: string) => {
    return parseFloat(formattedValue.replace(/[^\d]/g, '')) || 0;
  };
  const saveLimit = async () => {
    if (!selectedCategory || !limitAmount.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập đầy đủ thông tin');
      return;
    }

    const amount = parseFloat(limitAmount.replace(/[^\d]/g, ''));
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Lỗi', 'Số tiền không hợp lệ');
      return;
    }

    try {
      const limitData: Omit<SpendingLimit, 'createdAt' | 'updatedAt'> = {
        categoryId: selectedCategory.id,
        categoryName: selectedCategory.name,
        categoryColor: selectedCategory.color,
        categoryIcon: selectedCategory.icon,
        limit: amount,
        period: selectedPeriod,
        notifications: {
          warning: warningEnabled,
          danger: dangerEnabled,
        },
      };

      await SpendingLimitService.saveLimit(limitData);
      setShowAddModal(false);
      loadData(); // Reload data
      
      Alert.alert(
        'Thành công',
        editingLimit ? 'Đã cập nhật giới hạn' : 'Đã thêm giới hạn mới'
      );
    } catch (error) {
      console.error('Lỗi lưu limit:', error);
      Alert.alert('Lỗi', 'Không thể lưu giới hạn');
    }
  };

  // Xóa limit
  const deleteLimit = async (categoryId: string, categoryName: string) => {
    Alert.alert(
      'Xác nhận xóa',
      `Bạn có chắc muốn xóa giới hạn cho "${categoryName}"?`,
      [
        { text: 'Hủy', style: 'cancel' },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: async () => {
            try {
              await SpendingLimitService.deleteLimit(categoryId);
              loadData();
              Alert.alert('Thành công', 'Đã xóa giới hạn');
            } catch (error) {
              console.error('Lỗi xóa limit:', error);
              Alert.alert('Lỗi', 'Không thể xóa giới hạn');
            }
          },
        },
      ]
    );
  };

  // Lưu settings
  const saveSettings = async (newSettings: LimitSettings) => {
    try {
      await SpendingLimitService.saveSettings(newSettings);
      setSettings(newSettings);
      Alert.alert('Thành công', 'Đã lưu cài đặt');
    } catch (error) {
      console.error('Lỗi lưu settings:', error);
      Alert.alert('Lỗi', 'Không thể lưu cài đặt');
    }
  };

  // Render item cho danh sách categories
  const renderCategoryItem = ({ item }: { item: CategoryWithLimit }) => {
    const hasLimit = !!item.limit;
    
    return (
      <View style={[styles.categoryItem, getCardStyle('medium')]}>
        {/* Category info */}
        <View style={[styles.categoryHeader, { backgroundColor: 'transparent' }]}>
          <View style={[styles.categoryInfo, { backgroundColor: 'transparent' }]}>
            <View
              style={[
                styles.categoryIcon,
                { backgroundColor: item.color },
              ]}
            >
              <Ionicons name={item.icon as any} size={24} color="white" />
            </View>
            <View style={{ backgroundColor: 'transparent', flex: 1 }}>
              <Text style={[styles.categoryName, { color: themeColors.text }]}>
                {translateCategoryName(item.name)}
              </Text>
              {hasLimit && (
                <Text style={[styles.periodText, { color: themeColors.secondaryText }]}>
                  Giới hạn {SpendingLimitService.formatPeriod(item.limit!.period).toLowerCase()}
                </Text>
              )}
            </View>
          </View>

          {/* Actions */}
          <View style={[styles.actionButtons, { backgroundColor: 'transparent' }]}>
            {hasLimit ? (
              <View style={{ flexDirection: 'row', backgroundColor: 'transparent' }}>
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    {
                      backgroundColor: isDark ? themeColors.cardBackground : "#E3F2FD",
                      borderColor: themeColors.border,
                      borderWidth: 1,
                    }
                  ]}
                  onPress={() => openEditModal(item, item.limit!)}
                >
                  <Ionicons name="create-outline" size={18} color={themeColors.primary} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    {
                      backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                      borderColor: themeColors.dangerBorder,
                      borderWidth: 1,
                    }
                  ]}
                  onPress={() => deleteLimit(item.id, item.name)}
                >
                  <Ionicons name="trash-outline" size={18} color={themeColors.danger} />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                style={[
                  styles.addButton,
                  { backgroundColor: themeColors.primary },
                ]}
                onPress={() => openAddModal(item)}
              >
                <Ionicons name="add" size={18} color="white" />
                <Text style={[styles.addButtonText, { color: 'white' }]}>
                  Thêm
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Progress bar nếu có limit */}
        {hasLimit && item.usage && (
          <View style={{ backgroundColor: 'transparent', marginTop: 12 }}>
            <LimitProgressBar
              usage={item.usage}
              size="medium"
              showDetails={true}
            />
          </View>
        )}
      </View>
    );
  };

  // Render overview tab
  const renderOverview = () => {
    const categoriesWithLimits = categories.filter(c => c.limit);
    const categoriesWithoutLimits = categories.filter(c => !c.limit);

    return (
      <ScrollView 
        style={{ flex: 1, backgroundColor: 'transparent' }} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ backgroundColor: 'transparent', flexGrow: 1 }}
      >
        {/* Summary */}
        <View style={[styles.summaryCard, getCardStyle('high')]}>
          <Text style={[styles.summaryTitle, { color: themeColors.text }]}>
            Tổng quan
          </Text>
          <View style={[styles.summaryStats, { backgroundColor: 'transparent' }]}>
            <View style={[styles.statItem, { backgroundColor: 'transparent' }]}>
              <Text style={[styles.statNumber, { color: themeColors.primary }]}>
                {categoriesWithLimits.length}
              </Text>
              <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
                Có giới hạn
              </Text>
            </View>
            <View style={[styles.statDivider, { backgroundColor: themeColors.border }]} />
            <View style={[styles.statItem, { backgroundColor: 'transparent' }]}>
              <Text style={[styles.statNumber, { color: themeColors.danger }]}>
                {categoriesWithLimits.filter(c => c.usage?.status === 'warning' || c.usage?.status === 'danger' || c.usage?.status === 'exceeded').length}
              </Text>
              <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
                Cảnh báo
              </Text>
            </View>
          </View>
        </View>

        {/* Categories có limit */}
        {categoriesWithLimits.length > 0 && (
          <View style={{ backgroundColor: 'transparent' }}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Danh mục có giới hạn ({categoriesWithLimits.length})
            </Text>
            {categoriesWithLimits.map(item => (
              <View key={item.id} style={{ backgroundColor: 'transparent' }}>
                {renderCategoryItem({ item })}
              </View>
            ))}
          </View>
        )}

        {/* Categories chưa có limit */}
        {categoriesWithoutLimits.length > 0 && (
          <View style={{ backgroundColor: 'transparent' }}>
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              Chưa có giới hạn ({categoriesWithoutLimits.length})
            </Text>
            {categoriesWithoutLimits.map(item => (
              <View key={item.id} style={{ backgroundColor: 'transparent' }}>
                {renderCategoryItem({ item })}
              </View>
            ))}
          </View>
        )}

        <View style={{ height: 80, backgroundColor: 'transparent' }} />
      </ScrollView>
    );
  };

  // Render settings tab
  const renderSettings = () => {
    if (!settings) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải cài đặt...
          </Text>
        </View>
      );
    }

    return (
      <ScrollView 
        style={{ flex: 1, backgroundColor: 'transparent' }} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ backgroundColor: 'transparent', flexGrow: 1 }}
      >
        {/* Default Period */}
        <View style={[styles.settingCard, getCardStyle('medium')]}>
          <Text style={[styles.settingTitle, { color: themeColors.text }]}>
            Chu kỳ mặc định
          </Text>
          <View style={[styles.periodOptions, { backgroundColor: 'transparent' }]}>
            {(['weekly', 'monthly', 'yearly'] as LimitPeriod[]).map(period => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodOption,
                  {
                    backgroundColor: settings.defaultPeriod === period 
                      ? themeColors.primary 
                      : themeColors.cardBackground,
                    borderColor: themeColors.border,
                  }
                ]}
                onPress={() => {
                  const newSettings = { ...settings, defaultPeriod: period };
                  saveSettings(newSettings);
                }}
              >
                <Text
                  style={[
                    styles.periodOptionText,
                    {
                      color: settings.defaultPeriod === period 
                        ? 'white' 
                        : themeColors.text,
                    }
                  ]}
                >
                  {SpendingLimitService.formatPeriod(period)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Notification Settings */}
        <View style={[styles.settingCard, getCardStyle('medium')]}>
          <Text style={[styles.settingTitle, { color: themeColors.text }]}>
            Thông báo mặc định
          </Text>
          
          <View style={[styles.settingRow, { backgroundColor: 'transparent' }]}>
            <Text style={[styles.settingLabel, { color: themeColors.text }]}>
              Cảnh báo 80%
            </Text>
            <Switch
              value={settings.defaultWarningEnabled}
              onValueChange={(value) => {
                const newSettings = { ...settings, defaultWarningEnabled: value };
                saveSettings(newSettings);
              }}
              trackColor={{ false: themeColors.border, true: themeColors.primary + '50' }}
              thumbColor={settings.defaultWarningEnabled ? themeColors.primary : themeColors.secondaryText}
            />
          </View>

          <View style={[styles.settingRow, { backgroundColor: 'transparent' }]}>
            <Text style={[styles.settingLabel, { color: themeColors.text }]}>
              Cảnh báo 100%
            </Text>
            <Switch
              value={settings.defaultDangerEnabled}
              onValueChange={(value) => {
                const newSettings = { ...settings, defaultDangerEnabled: value };
                saveSettings(newSettings);
              }}
              trackColor={{ false: themeColors.border, true: themeColors.primary + '50' }}
              thumbColor={settings.defaultDangerEnabled ? themeColors.primary : themeColors.secondaryText}
            />
          </View>
        </View>

        {/* Check Frequency */}
        <View style={[styles.settingCard, getCardStyle('medium')]}>
          <Text style={[styles.settingTitle, { color: themeColors.text }]}>
            Tần suất kiểm tra
          </Text>
          <View style={[styles.frequencyOptions, { backgroundColor: 'transparent' }]}>
            <TouchableOpacity
              style={[
                styles.frequencyOption,
                {
                  backgroundColor: settings.checkFrequency === 'realtime' 
                    ? themeColors.primary 
                    : themeColors.cardBackground,
                  borderColor: themeColors.border,
                }
              ]}
              onPress={() => {
                const newSettings = { ...settings, checkFrequency: 'realtime' as const };
                saveSettings(newSettings);
              }}
            >
              <Text
                style={[
                  styles.frequencyOptionText,
                  {
                    color: settings.checkFrequency === 'realtime' 
                      ? 'white' 
                      : themeColors.text,
                  }
                ]}
              >
                Thời gian thực
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.frequencyOption,
                {
                  backgroundColor: settings.checkFrequency === 'daily' 
                    ? themeColors.primary 
                    : themeColors.cardBackground,
                  borderColor: themeColors.border,
                }
              ]}
              onPress={() => {
                const newSettings = { ...settings, checkFrequency: 'daily' as const };
                saveSettings(newSettings);
              }}
            >
              <Text
                style={[
                  styles.frequencyOptionText,
                  {
                    color: settings.checkFrequency === 'daily' 
                      ? 'white' 
                      : themeColors.text,
                  }
                ]}
              >
                Hàng ngày
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={{ height: 80, backgroundColor: 'transparent' }} />
      </ScrollView>
    );
  };

  // Render modal form
  const renderModal = () => (
    <Modal
      visible={showAddModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowAddModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: themeColors.background }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { backgroundColor: 'transparent' }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              {editingLimit ? 'Sửa giới hạn' : 'Thêm giới hạn'}
            </Text>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Ionicons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>

          {/* Category info */}
          {selectedCategory && (
            <View style={[styles.selectedCategory, { backgroundColor: themeColors.cardBackground, borderColor: themeColors.border }]}>
              <View
                style={[
                  styles.categoryIcon,
                  { backgroundColor: selectedCategory.color },
                ]}
              >
                <Ionicons name={selectedCategory.icon as any} size={20} color="white" />
              </View>
              <Text style={[styles.selectedCategoryName, { color: themeColors.text }]}>
                {translateCategoryName(selectedCategory.name)}
              </Text>
            </View>
          )}

          {/* Form */}
          <ScrollView showsVerticalScrollIndicator={false} style={{ backgroundColor: 'transparent' }}>
            {/* Amount Input */}
            <View style={[styles.formGroup, { backgroundColor: 'transparent' }]}>
              <Text style={[styles.formLabel, { color: themeColors.text }]}>
                Số tiền giới hạn
              </Text>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: themeColors.cardBackground,
                    borderColor: themeColors.border,
                    color: themeColors.text,
                  }
                ]}
                value={limitAmount}
                onChangeText={handleAmountChange}
                placeholder="Nhập số tiền..."
                placeholderTextColor={themeColors.secondaryText}
                keyboardType="numeric"
                returnKeyType="done"
                onSubmitEditing={() => Keyboard.dismiss()}
                blurOnSubmit={true}
              />
            </View>

            {/* Period Selection */}
            <View style={[styles.formGroup, { backgroundColor: 'transparent' }]}>
              <Text style={[styles.formLabel, { color: themeColors.text }]}>
                Chu kỳ
              </Text>
              <View style={[styles.periodOptions, { backgroundColor: 'transparent' }]}>
                {(['weekly', 'monthly', 'yearly'] as LimitPeriod[]).map(period => (
                  <TouchableOpacity
                    key={period}
                    style={[
                      styles.periodOption,
                      {
                        backgroundColor: selectedPeriod === period 
                          ? themeColors.primary 
                          : themeColors.cardBackground,
                        borderColor: themeColors.border,
                      }
                    ]}
                    onPress={() => setSelectedPeriod(period)}
                  >
                    <Text
                      style={[
                        styles.periodOptionText,
                        {
                          color: selectedPeriod === period 
                            ? 'white' 
                            : themeColors.text,
                        }
                      ]}
                    >
                      {SpendingLimitService.formatPeriod(period)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Notifications */}
            <View style={[styles.formGroup, { backgroundColor: 'transparent' }]}>
              <Text style={[styles.formLabel, { color: themeColors.text }]}>
                Thông báo
              </Text>
              
              <View style={[styles.switchRow, { backgroundColor: 'transparent' }]}>
                <Text style={[styles.switchLabel, { color: themeColors.text }]}>
                  Cảnh báo khi đạt 80%
                </Text>
                <Switch
                  value={warningEnabled}
                  onValueChange={setWarningEnabled}
                  trackColor={{ false: themeColors.border, true: themeColors.primary + '50' }}
                  thumbColor={warningEnabled ? themeColors.primary : themeColors.secondaryText}
                />
              </View>

              <View style={[styles.switchRow, { backgroundColor: 'transparent' }]}>
                <Text style={[styles.switchLabel, { color: themeColors.text }]}>
                  Cảnh báo khi đạt 100%
                </Text>
                <Switch
                  value={dangerEnabled}
                  onValueChange={setDangerEnabled}
                  trackColor={{ false: themeColors.border, true: themeColors.primary + '50' }}
                  thumbColor={dangerEnabled ? themeColors.primary : themeColors.secondaryText}
                />
              </View>
            </View>
          </ScrollView>

          {/* Buttons */}
          <View style={[styles.modalButtons, { backgroundColor: 'transparent', borderTopColor: themeColors.border }]}>
            <TouchableOpacity
              style={[
                styles.modalButton,
                styles.cancelButton,
                { backgroundColor: themeColors.cardBackground, borderColor: themeColors.border, borderWidth: 1 },
              ]}
              onPress={() => setShowAddModal(false)}
            >
              <Text style={[styles.buttonText, { color: themeColors.text }]}>
                Hủy
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.modalButton,
                styles.saveButton,
                { backgroundColor: themeColors.primary },
              ]}
              onPress={saveLimit}
            >
              <Text style={[styles.buttonText, { color: 'white' }]}>
                {editingLimit ? 'Cập nhật' : 'Thêm'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            Giới hạn chi tiêu
          </Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          Giới hạn chi tiêu
        </Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Tabs */}
      <View style={[styles.tabContainer, { backgroundColor: 'transparent' }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'overview' && { borderBottomColor: themeColors.primary, borderBottomWidth: 2 },
          ]}
          onPress={() => setSelectedTab('overview')}
        >
          <Text
            style={[
              styles.tabText,
              { color: selectedTab === 'overview' ? themeColors.primary : themeColors.secondaryText },
            ]}
          >
            Tổng quan
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            selectedTab === 'settings' && { borderBottomColor: themeColors.primary, borderBottomWidth: 2 },
          ]}
          onPress={() => setSelectedTab('settings')}
        >
          <Text
            style={[
              styles.tabText,
              { color: selectedTab === 'settings' ? themeColors.primary : themeColors.secondaryText },
            ]}
          >
            Cài đặt
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={{ flex: 1, padding: 16, backgroundColor: 'transparent' }}>
        {selectedTab === 'overview' ? renderOverview() : renderSettings()}
      </View>

      {/* Modal */}
      {renderModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  summaryCard: {
    padding: 20,
    marginBottom: 20,
    borderRadius: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  statDivider: {
    width: 1,
    height: 40,
    marginHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    marginTop: 8,
  },
  categoryItem: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  periodText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  settingCard: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  periodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  periodOption: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  periodOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  frequencyOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  frequencyOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  frequencyOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  selectedCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  selectedCategoryName: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  formGroup: {
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    marginRight: 8,
    backgroundColor: 'transparent',
  },
  saveButton: {
    marginLeft: 8,
    backgroundColor: 'transparent',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});