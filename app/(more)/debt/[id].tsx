// File: app/(more)/debt/[id].tsx
// File này hiển thị chi tiết khoản nợ và lịch sử thanh toán
// File này liên quan đến: lib/models/debt.ts, app/(more)/debt.tsx

import { Text, View } from "@/components/Themed";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Debt, DebtModel, DebtTransaction } from "@/lib/models/debt";
import { Ionicons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Modal,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Thêm hàm format số tiền
const formatNumber = (value: string) => {
  // Xóa tất cả dấu chấm hiện có
  const number = value.replace(/\./g, '');
  // Chỉ giữ lại các số
  const cleanNumber = number.replace(/[^\d]/g, '');
  // Thêm dấu chấm ngăn cách hàng nghìn
  return cleanNumber.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

// Thêm hàm chuyển đổi số tiền đã format về số
const parseFormattedNumber = (value: string) => {
  return Number(value.replace(/\./g, ''));
};

export default function DebtDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { isDark, themeColors, getCardStyle, getIconContainerStyle } = useTheme();
  const { t } = useLocalization();
  const { formatCurrency } = useCurrency();

  // States
  const [debt, setDebt] = useState<Debt | null>(null);
  const [transactions, setTransactions] = useState<DebtTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showBorrowMoreModal, setShowBorrowMoreModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [borrowAmount, setBorrowAmount] = useState('');
  const [paymentNote, setPaymentNote] = useState('');
  const [borrowNote, setBorrowNote] = useState('');
  const [processingPayment, setProcessingPayment] = useState(false);
  const [processingBorrow, setProcessingBorrow] = useState(false);

  // Load data
  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [debtData, transactionsData] = await Promise.all([
        DebtModel.getById(id),
        DebtModel.getTransactions(id)
      ]);
      
      setDebt(debtData);
      setTransactions(transactionsData || []);
    } catch (error) {
      console.error('Lỗi khi tải chi tiết nợ:', error);
      Alert.alert('Lỗi', 'Không thể tải thông tin khoản nợ');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleMarkAsPaid = async () => {
    if (!debt) return;

    Alert.alert(
      "Xác nhận",
      "Đánh dấu khoản nợ này đã hoàn thành?",
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xác nhận",
          onPress: async () => {
            try {
              await DebtModel.updateStatus(debt.id, 'paid');
              await loadData();
              Alert.alert("Thành công", "Đã đánh dấu hoàn thành");
            } catch (error) {
              console.error('Lỗi khi cập nhật trạng thái:', error);
              Alert.alert("Lỗi", "Không thể cập nhật trạng thái");
            }
          },
        },
      ]
    );
  };

  const handleDeleteDebt = async () => {
    if (!debt) return;

    Alert.alert(
      "Xác nhận xóa",
      `Bạn có chắc muốn xóa khoản nợ "${debt.name}"? Thao tác này không thể hoàn tác.`,
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              await DebtModel.delete(debt.id);
              Alert.alert("Thành công", "Đã xóa khoản nợ", [
                { text: "OK", onPress: () => router.back() }
              ]);
            } catch (error) {
              console.error('Lỗi khi xóa nợ:', error);
              Alert.alert("Lỗi", "Không thể xóa khoản nợ");
            }
          },
        },
      ]
    );
  };

  const handleMakePayment = async () => {
    if (!debt || !paymentAmount.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tiền thanh toán');
      return;
    }

    const amount = parseFormattedNumber(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Lỗi', 'Số tiền không hợp lệ');
      return;
    }

    if (amount > debt.remaining) {
      Alert.alert('Lỗi', 'Số tiền thanh toán không được vượt quá số tiền còn lại');
      return;
    }

    try {
      setProcessingPayment(true);
      
      await DebtModel.makePayment(
        debt.id,
        amount,
        paymentNote.trim() || undefined
      );
      
      setShowPaymentModal(false);
      setPaymentAmount('');
      setPaymentNote('');
      await loadData();
      
      Alert.alert("Thành công", "Đã ghi nhận thanh toán");
    } catch (error) {
      console.error('Lỗi khi thanh toán:', error);
      Alert.alert("Lỗi", "Không thể ghi nhận thanh toán");
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleBorrowMore = async () => {
    if (!debt || !borrowAmount.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tiền vay thêm');
      return;
    }

    const amount = parseFormattedNumber(borrowAmount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Lỗi', 'Số tiền không hợp lệ');
      return;
    }

    try {
      setProcessingBorrow(true);
      
      // Tạo giao dịch với số tiền âm
      await DebtModel.makePayment(
        debt.id,
        -amount, // Số âm để tăng nợ
        borrowNote.trim() || undefined
      );
      
      setShowBorrowMoreModal(false);
      setBorrowAmount('');
      setBorrowNote('');
      await loadData();
      
      Alert.alert("Thành công", "Đã ghi nhận vay thêm");
    } catch (error) {
      console.error('Lỗi khi vay thêm:', error);
      Alert.alert("Lỗi", "Không thể ghi nhận vay thêm");
    } finally {
      setProcessingBorrow(false);
    }
  };

  const handleDeleteTransaction = async (transaction: DebtTransaction) => {
    Alert.alert(
      "Xác nhận xóa",
      "Bạn có chắc muốn xóa giao dịch này?",
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              await DebtModel.deleteTransaction(transaction.id, transaction.debt_id);
              await loadData();
              Alert.alert("Thành công", "Đã xóa giao dịch");
            } catch (error) {
              console.error('Lỗi khi xóa giao dịch:', error);
              Alert.alert("Lỗi", "Không thể xóa giao dịch");
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.loadingContainer, { backgroundColor: "transparent" }]}>
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!debt) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.errorContainer, { backgroundColor: "transparent" }]}>
          <Text style={[styles.errorText, { color: themeColors.text }]}>
            Không tìm thấy khoản nợ
          </Text>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: themeColors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Quay lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const isOverdue = debt.due_date && new Date(debt.due_date) < new Date() && debt.status === 'active';
  const typeColor = debt.type === 'payable' ? '#F44336' : '#4CAF50';
  const typeIcon = debt.type === 'payable' ? 'arrow-up' : 'arrow-down';
  const progressPercentage = debt.amount > 0 ? ((debt.amount - debt.remaining) / debt.amount) * 100 : 0;

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: "transparent" }]}>
      <View style={[styles.titleContainer, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Chi tiết nợ
        </Text>
      </View>

      <View style={[styles.headerActions, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => router.push(`/debt/edit/${debt.id}`)}
        >
          <Ionicons
            name="create"
            size={20}
            color={themeColors.primary}
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleDeleteDebt}
        >
          <Ionicons
            name="trash"
            size={20}
            color={themeColors.danger}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderDebtInfo = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      {/* Alert for overdue */}
      {isOverdue && (
        <View style={[styles.alertCard, getCardStyle("medium"), {
          borderLeftWidth: 4,
          borderLeftColor: themeColors.danger,
          marginBottom: 16
        }]}>
          <Ionicons
            name="warning"
            size={20}
            color={themeColors.danger}
            style={{ marginRight: 8 }}
          />
          <Text style={[styles.alertText, { color: themeColors.danger }]}>
            Khoản nợ đã quá hạn
          </Text>
        </View>
      )}

      {/* Main info card */}
      <View style={[styles.debtCard, getCardStyle("medium")]}>
        <View style={[styles.debtHeader, { backgroundColor: "transparent" }]}>
          <View style={[styles.debtIcon, {
            backgroundColor: debt.type === 'payable' ? '#FFEBEE' : '#E8F5E8'
          }]}>
            <Ionicons
              name={typeIcon}
              size={24}
              color={typeColor}
            />
          </View>
          
          <View style={[styles.debtMainInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.debtName, { color: themeColors.text }]}>
              {debt.name}
            </Text>
            <Text style={[styles.debtType, { color: typeColor }]}>
              {debt.type === 'payable' ? 'Phải trả' : 'Cho vay'}
            </Text>
          </View>

          <View style={[
            styles.statusBadge,
            {
              backgroundColor: debt.status === 'active' ? themeColors.primary :
                              debt.status === 'paid' ? '#4CAF50' : '#757575'
            }
          ]}>
            <Text style={styles.statusText}>
              {debt.status === 'active' ? 'Đang nợ' :
               debt.status === 'paid' ? 'Đã trả' : 'Đã hủy'}
            </Text>
          </View>
        </View>

        {/* Amount info */}
        <View style={[styles.amountSection, { backgroundColor: "transparent" }]}>
          <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
            <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
              Tổng tiền:
            </Text>
            <Text style={[styles.totalAmount, { color: themeColors.text }]}>
              {formatCurrency(debt.amount)}
            </Text>
          </View>

          <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
            <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
              Đã {debt.type === 'payable' ? 'trả' : 'thu'}:
            </Text>
            <Text style={[styles.paidAmount, { color: '#4CAF50' }]}>
              {formatCurrency(debt.amount - debt.remaining)}
            </Text>
          </View>

          <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
            <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
              Còn lại:
            </Text>
            <Text style={[styles.remainingAmount, { color: typeColor }]}>
              {formatCurrency(debt.remaining)}
            </Text>
          </View>

          {/* Progress bar */}
          <View style={[styles.progressContainer, { backgroundColor: "transparent" }]}>
            <View style={[styles.progressBar, { backgroundColor: themeColors.border }]}>
              <View style={[
                styles.progressFill,
                { 
                  width: `${progressPercentage}%`,
                  backgroundColor: '#4CAF50'
                }
              ]} />
            </View>
            <Text style={[styles.progressText, { color: themeColors.secondaryText }]}>
              {progressPercentage.toFixed(1)}%
            </Text>
          </View>
        </View>

        {/* Additional info */}
        {debt.due_date && (
          <View style={[styles.infoRow, { backgroundColor: "transparent" }]}>
            <Ionicons
              name="calendar"
              size={16}
              color={isOverdue ? themeColors.danger : themeColors.secondaryText}
              style={{ marginRight: 8 }}
            />
            <Text style={[styles.infoText, { 
              color: isOverdue ? themeColors.danger : themeColors.text 
            }]}>
              Hạn trả: {new Date(debt.due_date).toLocaleDateString('vi-VN')}
              {isOverdue && ' (Quá hạn)'}
            </Text>
          </View>
        )}

        {debt.description && (
          <View style={[styles.infoRow, { backgroundColor: "transparent" }]}>
            <Ionicons
              name="document-text"
              size={16}
              color={themeColors.secondaryText}
              style={{ marginRight: 8 }}
            />
            <Text style={[styles.infoText, { color: themeColors.text }]}>
              {debt.description}
            </Text>
          </View>
        )}

        <View style={[styles.infoRow, { backgroundColor: "transparent" }]}>
          <Ionicons
            name="time"
            size={16}
            color={themeColors.secondaryText}
            style={{ marginRight: 8 }}
          />
          <Text style={[styles.infoText, { color: themeColors.secondaryText }]}>
            Tạo: {new Date(debt.created_at).toLocaleDateString('vi-VN')}
          </Text>
        </View>
      </View>

      {/* Action buttons */}
      {debt.status === 'active' && (
        <View style={[styles.actionButtons, { backgroundColor: "transparent" }]}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: themeColors.primary }]}
            onPress={() => setShowPaymentModal(true)}
          >
            <Ionicons name="card" size={18} color="white" />
            <Text style={styles.actionButtonText}>
              Thanh toán
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#FF9800' }]}
            onPress={() => setShowBorrowMoreModal(true)}
          >
            <Ionicons name="add-circle" size={18} color="white" />
            <Text style={styles.actionButtonText}>
              Vay thêm
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
            onPress={handleMarkAsPaid}
          >
            <Ionicons name="checkmark-circle" size={18} color="white" />
            <Text style={styles.actionButtonText}>
              Hoàn thành
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderTransactions = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <View style={[styles.sectionHeader, { backgroundColor: "transparent" }]}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          Lịch sử thanh toán
        </Text>
        <Text style={[styles.transactionCount, { color: themeColors.secondaryText }]}>
          {transactions.length} giao dịch
        </Text>
      </View>

      {transactions.length === 0 ? (
        <View style={[styles.emptyTransactions, getCardStyle("low")]}>
          <Ionicons
            name="receipt-outline"
            size={32}
            color={themeColors.secondaryText}
            style={{ marginBottom: 8 }}
          />
          <Text style={[styles.emptyText, { color: themeColors.secondaryText }]}>
            Chưa có giao dịch thanh toán nào
          </Text>
        </View>
      ) : (
        <View style={[styles.transactionsList, { backgroundColor: "transparent" }]}>
          {transactions.map((transaction) => (
            <View key={transaction.id} style={[styles.transactionCard, getCardStyle("low")]}>
              <View style={[styles.transactionHeader, { backgroundColor: "transparent" }]}>
                <View style={[styles.transactionIcon, {
                  backgroundColor: transaction.amount > 0 ? '#E8F5E8' : '#FFEBEE'
                }]}>
                  <Ionicons
                    name={transaction.amount > 0 ? "add" : "remove"}
                    size={16}
                    color={transaction.amount > 0 ? '#4CAF50' : '#F44336'}
                  />
                </View>
                
                <View style={[styles.transactionInfo, { backgroundColor: "transparent" }]}>
                  <Text style={[styles.transactionAmount, {
                    color: transaction.amount > 0 ? '#4CAF50' : '#F44336'
                  }]}>
                    {transaction.amount > 0 ? '+' : ''}{formatCurrency(Math.abs(transaction.amount))}
                  </Text>
                  <Text style={[styles.transactionDate, { color: themeColors.secondaryText }]}>
                    {new Date(transaction.transaction_date).toLocaleDateString('vi-VN')}
                  </Text>
                  {transaction.amount < 0 && (
                    <Text style={[styles.transactionType, { color: '#FF9800' }]}>
                      Vay thêm
                    </Text>
                  )}
                </View>

                <TouchableOpacity
                  style={styles.deleteTransaction}
                  onPress={() => handleDeleteTransaction(transaction)}
                >
                  <Ionicons
                    name="trash"
                    size={16}
                    color={themeColors.danger}
                  />
                </TouchableOpacity>
              </View>

              {transaction.note && (
                <Text style={[styles.transactionNote, { color: themeColors.text }]}>
                  {transaction.note}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}
    </View>
  );

  const renderPaymentModal = () => (
    <Modal
      visible={showPaymentModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowPaymentModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, getCardStyle("high"), {
          backgroundColor: themeColors.cardBackground,
          width: '90%',
          maxWidth: 400,
        }]}>
          <View style={[styles.modalHeader, { backgroundColor: "transparent" }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Thanh toán
            </Text>
            <TouchableOpacity
              onPress={() => setShowPaymentModal(false)}
            >
              <Ionicons
                name="close"
                size={24}
                color={themeColors.text}
              />
            </TouchableOpacity>
          </View>

          <View style={[styles.modalBody, { backgroundColor: "transparent" }]}>
            <Text style={[styles.remainingInfo, { color: themeColors.text }]}>
              Còn lại: {formatCurrency(debt.remaining)}
            </Text>

            <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
              <Text style={[styles.inputLabel, { color: themeColors.text }]}>
                Số tiền thanh toán *
              </Text>
              <View style={[styles.inputContainer, getCardStyle("low")]}>
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder="0"
                  placeholderTextColor={themeColors.secondaryText}
                  value={paymentAmount}
                  onChangeText={(text) => {
                    const formatted = formatNumber(text);
                    setPaymentAmount(formatted);
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
              <Text style={[styles.inputLabel, { color: themeColors.text }]}>
                Ghi chú
              </Text>
              <View style={[styles.inputContainer, getCardStyle("low"), { height: 60 }]}>
                <TextInput
                  style={[styles.input, { color: themeColors.text, textAlignVertical: 'top' }]}
                  placeholder="Thêm ghi chú..."
                  placeholderTextColor={themeColors.secondaryText}
                  value={paymentNote}
                  onChangeText={setPaymentNote}
                  multiline
                />
              </View>
            </View>

            <View style={[styles.modalActions, { backgroundColor: "transparent" }]}>
              <TouchableOpacity
                style={[styles.modalButton, getCardStyle("low")]}
                onPress={() => setShowPaymentModal(false)}
              >
                <Text style={[styles.modalButtonText, { color: themeColors.text }]}>
                  Hủy
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.modalButton,
                  { backgroundColor: themeColors.primary },
                  processingPayment && { opacity: 0.6 }
                ]}
                onPress={handleMakePayment}
                disabled={processingPayment}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  {processingPayment ? 'Đang xử lý...' : 'Thanh toán'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );

  const renderBorrowMoreModal = () => (
    <Modal
      visible={showBorrowMoreModal}
      transparent
      animationType="fade"
      onRequestClose={() => setShowBorrowMoreModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, getCardStyle("high"), {
          backgroundColor: themeColors.cardBackground,
          width: '90%',
          maxWidth: 400,
        }]}>
          <View style={[styles.modalHeader, { backgroundColor: "transparent" }]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Vay thêm
            </Text>
            <TouchableOpacity
              onPress={() => setShowBorrowMoreModal(false)}
            >
              <Ionicons
                name="close"
                size={24}
                color={themeColors.text}
              />
            </TouchableOpacity>
          </View>

          <View style={[styles.modalBody, { backgroundColor: "transparent" }]}>
            <Text style={[styles.remainingInfo, { color: themeColors.text }]}>
              Hiện tại còn nợ: {formatCurrency(debt.remaining)}
            </Text>

            <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
              <Text style={[styles.inputLabel, { color: themeColors.text }]}>
                Số tiền vay thêm *
              </Text>
              <View style={[styles.inputContainer, getCardStyle("low")]}>
                <TextInput
                  style={[styles.input, { color: themeColors.text }]}
                  placeholder="0"
                  placeholderTextColor={themeColors.secondaryText}
                  value={borrowAmount}
                  onChangeText={(text) => {
                    const formatted = formatNumber(text);
                    setBorrowAmount(formatted);
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
              <Text style={[styles.inputLabel, { color: themeColors.text }]}>
                Ghi chú
              </Text>
              <View style={[styles.inputContainer, getCardStyle("low"), { height: 60 }]}>
                <TextInput
                  style={[styles.input, { color: themeColors.text, textAlignVertical: 'top' }]}
                  placeholder="Lý do vay thêm..."
                  placeholderTextColor={themeColors.secondaryText}
                  value={borrowNote}
                  onChangeText={setBorrowNote}
                  multiline
                />
              </View>
            </View>

            <View style={[styles.modalActions, { backgroundColor: "transparent" }]}>
              <TouchableOpacity
                style={[styles.modalButton, getCardStyle("low")]}
                onPress={() => setShowBorrowMoreModal(false)}
              >
                <Text style={[styles.modalButtonText, { color: themeColors.text }]}>
                  Hủy
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.modalButton,
                  { backgroundColor: '#FF9800' },
                  processingBorrow && { opacity: 0.6 }
                ]}
                onPress={handleBorrowMore}
                disabled={processingBorrow}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  {processingBorrow ? 'Đang xử lý...' : 'Vay thêm'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      {renderHeader()}

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[themeColors.primary]}
            tintColor={themeColors.primary}
          />
        }
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {renderDebtInfo()}
        {renderTransactions()}
      </ScrollView>

      {renderPaymentModal()}
      {renderBorrowMoreModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
  },
  backButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerButton: {
    padding: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  section: {
    padding: 16,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  alertText: {
    fontSize: 14,
    fontWeight: '500',
  },
  debtCard: {
    padding: 20,
    marginBottom: 16,
  },
  debtHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  debtIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  debtMainInfo: {
    flex: 1,
  },
  debtName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  debtType: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  amountSection: {
    marginBottom: 16,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  amountLabel: {
    fontSize: 14,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  paidAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  remainingAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    gap: 6,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  transactionCount: {
    fontSize: 14,
  },
  emptyTransactions: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 14,
  },
  transactionsList: {
    gap: 8,
  },
  transactionCard: {
    padding: 16,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionType: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  deleteTransaction: {
    padding: 8,
  },
  transactionNote: {
    fontSize: 14,
    marginTop: 8,
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    borderRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalBody: {
    gap: 16,
  },
  remainingInfo: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 8,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  inputContainer: {
    padding: 12,
    height: 48,
  },
  input: {
    fontSize: 16,
    flex: 1,
    padding: 0,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});