// File: app/(more)/debt/add.tsx
// File này hiển thị form thêm khoản nợ mới
// File này liên quan đến: lib/models/debt.ts, app/(more)/debt.tsx

import { Text, View } from "@/components/Themed";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { CreateDebtData, DebtModel } from "@/lib/models/debt";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  Alert,
  Dimensions,
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  TextInput,
  TouchableOpacity
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Thêm hàm format số tiền
const formatNumber = (value: string) => {
  // Xóa tất cả dấu chấm hiện có
  const number = value.replace(/\./g, '');
  // Chỉ giữ lại các số
  const cleanNumber = number.replace(/[^\d]/g, '');
  // Thêm dấu chấm ngăn cách hàng nghìn
  return cleanNumber.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

// Thêm hàm chuyển đổi số tiền đã format về số
const parseFormattedNumber = (value: string) => {
  return Number(value.replace(/\./g, ''));
};

// Thêm component DatePickerModal
const DatePickerModal = ({ 
  visible, 
  onClose, 
  value, 
  onChange, 
  minimumDate, 
  maximumDate,
  title 
}: { 
  visible: boolean; 
  onClose: () => void; 
  value: Date; 
  onChange: (date: Date) => void; 
  minimumDate?: Date;
  maximumDate?: Date;
  title: string;
}) => {
  const { themeColors, getCardStyle } = useTheme();
  const [tempDate, setTempDate] = useState(value);
  const [mode, setMode] = useState<'date' | 'time'>('date');

  // Các nút chọn nhanh cho ngày
  const quickDateOptions = [
    { label: 'Hôm nay', value: 0 },
    { label: 'Ngày mai', value: 1 },
    { label: 'Tuần sau', value: 7 },
    { label: 'Tháng sau', value: 30 },
  ];

  // Các nút chọn nhanh cho giờ
  const quickTimeOptions = [
    { label: 'Sáng (8:00)', value: 8 },
    { label: 'Trưa (12:00)', value: 12 },
    { label: 'Chiều (15:00)', value: 15 },
    { label: 'Tối (20:00)', value: 20 },
  ];

  const handleQuickDateSelect = (days: number) => {
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + days);
    newDate.setHours(tempDate.getHours(), tempDate.getMinutes());
    setTempDate(newDate);
  };

  const handleQuickTimeSelect = (hour: number) => {
    const newDate = new Date(tempDate);
    newDate.setHours(hour, 0);
    setTempDate(newDate);
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (selectedDate) {
      // Always update tempDate regardless of mode
      setTempDate(selectedDate);
    }
  };

  const handleConfirm = () => {
    onChange(tempDate);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.datePickerModalOverlay}>
        <View style={[styles.datePickerModalContent, getCardStyle("high"), {
          backgroundColor: themeColors.cardBackground,
          width: Dimensions.get('window').width * 0.95,
        }]}>
          {/* Header */}
          <View style={[styles.datePickerModalHeader, { backgroundColor: "transparent" }]}>
            <Text style={[styles.datePickerModalTitle, { color: themeColors.text }]}>
              {title}
            </Text>
            <TouchableOpacity 
              onPress={onClose}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>

          {/* Mode Selector */}
          <View style={[styles.modeSelector, { backgroundColor: "transparent" }]}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'date' && { backgroundColor: themeColors.primary }
              ]}
              onPress={() => setMode('date')}
            >
              <Ionicons 
                name="calendar" 
                size={20} 
                color={mode === 'date' ? 'white' : themeColors.text} 
              />
              <Text style={[
                styles.modeButtonText,
                { color: mode === 'date' ? 'white' : themeColors.text }
              ]}>
                Ngày
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'time' && { backgroundColor: themeColors.primary }
              ]}
              onPress={() => setMode('time')}
            >
              <Ionicons 
                name="time" 
                size={20} 
                color={mode === 'time' ? 'white' : themeColors.text} 
              />
              <Text style={[
                styles.modeButtonText,
                { color: mode === 'time' ? 'white' : themeColors.text }
              ]}>
                Giờ
              </Text>
            </TouchableOpacity>
          </View>

          {/* Date/Time Display */}
          <View style={[styles.dateTimeDisplay, { backgroundColor: "transparent" }]}>
            <Text style={[styles.dateTimeText, { color: themeColors.text }]}>
              {tempDate.toLocaleDateString('vi-VN', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </Text>
            <Text style={[styles.dateTimeText, { color: themeColors.text }]}>
              {tempDate.toLocaleTimeString('vi-VN', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
              })}
            </Text>
          </View>

          {/* Quick Selection Buttons */}
          <View style={[styles.quickSelectionContainer, { backgroundColor: "transparent" }]}>
            <Text style={[styles.quickSelectionTitle, { color: themeColors.text }]}>
              {mode === 'date' ? 'Chọn nhanh ngày' : 'Chọn nhanh giờ'}
            </Text>
            <View style={styles.quickSelectionGrid}>
              {(mode === 'date' ? quickDateOptions : quickTimeOptions).map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[styles.quickSelectionButton, getCardStyle("low")]}
                  onPress={() => 
                    mode === 'date' 
                      ? handleQuickDateSelect(option.value)
                      : handleQuickTimeSelect(option.value)
                  }
                >
                  <Text style={[styles.quickSelectionButtonText, { color: themeColors.text }]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Date/Time Picker */}
          <View style={[styles.pickerContainer, { backgroundColor: "transparent" }]}>
            <DateTimePicker
              value={tempDate}
              mode={mode}
              display="default"
              onChange={handleDateChange}
              minimumDate={minimumDate}
              maximumDate={maximumDate}
              style={styles.datePicker}
            />
          </View>

          {/* Footer */}
          <View style={[styles.datePickerModalFooter, { backgroundColor: "transparent" }]}>
            <TouchableOpacity
              style={[styles.datePickerModalButton, { backgroundColor: themeColors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.datePickerModalButtonText, { color: themeColors.text }]}>
                Hủy
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.datePickerModalButton, { backgroundColor: themeColors.primary }]}
              onPress={handleConfirm}
            >
              <Text style={styles.datePickerModalButtonText}>
                Xác nhận
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default function AddDebtScreen() {
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();
  const { formatCurrency, currency } = useCurrency();

  // Form states
  const [name, setName] = useState('');
  const [amount, setAmount] = useState('');
  const [type, setType] = useState<'payable' | 'receivable'>('payable');
  const [description, setDescription] = useState('');
  const [hasReminder, setHasReminder] = useState(false);
  const [hasDueDate, setHasDueDate] = useState(false);
  const [dueDate, setDueDate] = useState(new Date());
  const [reminderDate, setReminderDate] = useState(new Date());

  // UI states
  const [loading, setLoading] = useState(false);
  const [showDueDateModal, setShowDueDateModal] = useState(false);
  const [showReminderDateModal, setShowReminderDateModal] = useState(false);

  const validateForm = (): boolean => {
    if (!name.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập tên khoản nợ');
      return false;
    }

    const amountValue = parseFormattedNumber(amount);
    if (!amount.trim() || isNaN(amountValue) || amountValue <= 0) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tiền hợp lệ');
      return false;
    }

    if (hasReminder && !hasDueDate) {
      Alert.alert('Lỗi', 'Cần có ngày đáo hạn để đặt nhắc nhở');
      return false;
    }

    if (hasReminder && reminderDate >= dueDate) {
      Alert.alert('Lỗi', 'Ngày nhắc nhở phải trước ngày đáo hạn');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const debtData: CreateDebtData = {
        name: name.trim(),
        amount: parseFormattedNumber(amount),
        type,
        description: description.trim() || undefined,
        due_date: hasDueDate ? dueDate.toISOString().split('T')[0] : undefined,
        reminder: hasReminder ? 1 : 0,
        reminder_date: hasReminder ? reminderDate.toISOString().split('T')[0] : undefined,
      };

      const result = await DebtModel.create(debtData);

      if (result) {
        Alert.alert(
          'Thành công',
          'Đã thêm khoản nợ mới',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        Alert.alert('Lỗi', 'Không thể tạo khoản nợ');
      }
    } catch (error) {
      console.error('Lỗi khi tạo nợ:', error);
      Alert.alert('Lỗi', 'Không thể tạo khoản nợ');
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: "transparent" }]}>
      <View style={[styles.titleContainer, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Thêm Khoản Nợ
        </Text>
      </View>

      <TouchableOpacity
        style={[
          styles.saveButton,
          { backgroundColor: themeColors.primary },
          loading && { opacity: 0.6 }
        ]}
        onPress={handleSave}
        disabled={loading}
      >
        <Text style={styles.saveButtonText}>
          {loading ? 'Đang lưu...' : 'Lưu'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderTypeSelector = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Loại khoản nợ
      </Text>
      <View style={[styles.typeSelector, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            getCardStyle("low"),
            type === 'payable' && { backgroundColor: '#F44336' }
          ]}
          onPress={() => setType('payable')}
        >
          <View style={[styles.typeIcon, {
            backgroundColor: type === 'payable' ? 'rgba(255,255,255,0.2)' : '#FFEBEE'
          }]}>
            <Ionicons
              name="arrow-up"
              size={20}
              color={type === 'payable' ? 'white' : '#F44336'}
            />
          </View>
          <Text style={[
            styles.typeText,
            { color: type === 'payable' ? 'white' : themeColors.text }
          ]}>
            Phải trả
          </Text>
          <Text style={[
            styles.typeDescription,
            { color: type === 'payable' ? 'rgba(255,255,255,0.8)' : themeColors.secondaryText }
          ]}>
            Tiền mình nợ người khác
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            getCardStyle("low"),
            type === 'receivable' && { backgroundColor: '#4CAF50' }
          ]}
          onPress={() => setType('receivable')}
        >
          <View style={[styles.typeIcon, {
            backgroundColor: type === 'receivable' ? 'rgba(255,255,255,0.2)' : '#E8F5E8'
          }]}>
            <Ionicons
              name="arrow-down"
              size={20}
              color={type === 'receivable' ? 'white' : '#4CAF50'}
            />
          </View>
          <Text style={[
            styles.typeText,
            { color: type === 'receivable' ? 'white' : themeColors.text }
          ]}>
            Cho vay
          </Text>
          <Text style={[
            styles.typeDescription,
            { color: type === 'receivable' ? 'rgba(255,255,255,0.8)' : themeColors.secondaryText }
          ]}>
            Tiền mình cho người khác vay
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderBasicInfo = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Thông tin cơ bản
      </Text>

      {/* Tên khoản nợ */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Tên khoản nợ *
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low")]}>
          <TextInput
            style={[styles.input, { color: themeColors.text }]}
            placeholder={type === 'payable' ? 'VD: Nợ tiền ăn trưa' : 'VD: Cho A vay tiền'}
            placeholderTextColor={themeColors.secondaryText}
            value={name}
            onChangeText={setName}
            maxLength={100}
          />
        </View>
      </View>

      {/* Số tiền */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Số tiền *
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low")]}>
          <Text style={[styles.currencyPrefix, { color: themeColors.secondaryText }]}>
            {currency === 'VND' ? '₫' : currency}
          </Text>
          <TextInput
            style={[styles.input, { color: themeColors.text, flex: 1 }]}
            placeholder="0"
            placeholderTextColor={themeColors.secondaryText}
            value={amount}
            onChangeText={(text) => {
              const formatted = formatNumber(text);
              setAmount(formatted);
            }}
            keyboardType="numeric"
          />
        </View>
      </View>

      {/* Ghi chú */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Ghi chú
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low"), { height: 80 }]}>
          <TextInput
            style={[styles.input, { color: themeColors.text, textAlignVertical: 'top' }]}
            placeholder="Thêm mô tả về khoản nợ..."
            placeholderTextColor={themeColors.secondaryText}
            value={description}
            onChangeText={setDescription}
            multiline
            maxLength={500}
          />
        </View>
      </View>
    </View>
  );

  const renderDateSettings = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Ngày hạn và nhắc nhở
      </Text>

      {/* Ngày đáo hạn */}
      <View style={[styles.switchRow, getCardStyle("low")]}>
        <View style={[styles.switchInfo, { backgroundColor: "transparent" }]}>
          <Text style={[styles.switchTitle, { color: themeColors.text }]}>
            Ngày đáo hạn
          </Text>
          <Text style={[styles.switchDescription, { color: themeColors.secondaryText }]}>
            Đặt ngày hạn trả nợ
          </Text>
        </View>
        <Switch
          value={hasDueDate}
          onValueChange={setHasDueDate}
          trackColor={{ false: themeColors.border, true: themeColors.primary }}
          thumbColor={isDark ? '#f4f3f4' : '#f4f3f4'}
        />
      </View>

      {hasDueDate && (
        <TouchableOpacity
          style={[styles.dateSelector, getCardStyle("low")]}
          onPress={() => setShowDueDateModal(true)}
        >
          <View style={[styles.dateIcon, { backgroundColor: themeColors.primary + '20' }]}>
            <Ionicons
              name="calendar"
              size={20}
              color={themeColors.primary}
            />
          </View>
          <View style={[styles.dateInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.dateLabel, { color: themeColors.text }]}>
              Ngày đáo hạn
            </Text>
            <Text style={[styles.dateValue, { color: themeColors.secondaryText }]}>
              {dueDate.toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {/* Nhắc nhở */}
      <View style={[styles.switchRow, getCardStyle("low")]}>
        <View style={[styles.switchInfo, { backgroundColor: "transparent" }]}>
          <Text style={[styles.switchTitle, { color: themeColors.text }]}>
            Nhắc nhở
          </Text>
          <Text style={[styles.switchDescription, { color: themeColors.secondaryText }]}>
            Nhận thông báo trước khi đến hạn
          </Text>
        </View>
        <Switch
          value={hasReminder}
          onValueChange={setHasReminder}
          disabled={!hasDueDate}
          trackColor={{ false: themeColors.border, true: themeColors.primary }}
          thumbColor={isDark ? '#f4f3f4' : '#f4f3f4'}
        />
      </View>

      {hasReminder && hasDueDate && (
        <TouchableOpacity
          style={[styles.dateSelector, getCardStyle("low")]}
          onPress={() => setShowReminderDateModal(true)}
        >
          <View style={[styles.dateIcon, { backgroundColor: themeColors.primary + '20' }]}>
            <Ionicons
              name="notifications"
              size={20}
              color={themeColors.primary}
            />
          </View>
          <View style={[styles.dateInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.dateLabel, { color: themeColors.text }]}>
              Ngày nhắc nhở
            </Text>
            <Text style={[styles.dateValue, { color: themeColors.secondaryText }]}>
              {reminderDate.toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      {renderHeader()}

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {renderTypeSelector()}
        {renderBasicInfo()}
        {renderDateSettings()}
      </ScrollView>

      {/* Date Pickers */}
      <DatePickerModal
        visible={showDueDateModal}
        onClose={() => setShowDueDateModal(false)}
        value={dueDate}
        onChange={(selectedDate) => {
          setDueDate(selectedDate);
          // Reset reminder date if it's after new due date
          if (reminderDate >= selectedDate) {
            const newReminderDate = new Date(selectedDate);
            newReminderDate.setDate(selectedDate.getDate() - 1);
            setReminderDate(newReminderDate);
          }
        }}
        minimumDate={new Date()}
        title="Chọn ngày đáo hạn"
      />

      <DatePickerModal
        visible={showReminderDateModal}
        onClose={() => setShowReminderDateModal(false)}
        value={reminderDate}
        onChange={setReminderDate}
        minimumDate={new Date()}
        maximumDate={new Date(dueDate.getTime() - 24 * 60 * 60 * 1000)}
        title="Chọn ngày nhắc nhở"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    padding: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 12,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    height: 48,
  },
  input: {
    fontSize: 16,
    flex: 1,
    padding: 0,
  },
  currencyPrefix: {
    fontSize: 16,
    marginRight: 8,
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
  },
  switchInfo: {
    flex: 1,
  },
  switchTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  switchDescription: {
    fontSize: 14,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
  },
  dateIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dateInfo: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  dateValue: {
    fontSize: 14,
  },
  datePickerModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerModalContent: {
    borderRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  datePickerModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  datePickerModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  modeSelector: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    gap: 8,
  },
  modeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  dateTimeDisplay: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  dateTimeText: {
    fontSize: 18,
    fontWeight: '500',
    marginVertical: 4,
  },
  quickSelectionContainer: {
    marginBottom: 20,
  },
  quickSelectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  quickSelectionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickSelectionButton: {
    flex: 1,
    minWidth: '48%',
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  quickSelectionButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  pickerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  datePicker: {
    width: '100%',
    height: 200,
  },
  datePickerModalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  datePickerModalButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    minWidth: 100,
    alignItems: 'center',
  },
  datePickerModalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});