// File: app/(more)/debt/edit/[id].tsx
// File này hiển thị form chỉnh sửa khoản nợ
// File này liên quan đến: lib/models/debt.ts, app/(more)/debt/[id].tsx

import { Text, View } from "@/components/Themed";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { Debt, DebtModel, UpdateDebtData } from "@/lib/models/debt";
import { Ionicons } from "@expo/vector-icons";
import DateTimePicker from "@react-native-community/datetimepicker";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
    Alert,
    ScrollView,
    StyleSheet,
    Switch,
    TextInput,
    TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Thêm hàm format số tiền
const formatNumber = (value: string) => {
  // Xóa tất cả dấu chấm hiện có
  const number = value.replace(/\./g, '');
  // Chỉ giữ lại các số
  const cleanNumber = number.replace(/[^\d]/g, '');
  // Thêm dấu chấm ngăn cách hàng nghìn
  return cleanNumber.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

// Thêm hàm chuyển đổi số tiền đã format về số
const parseFormattedNumber = (value: string) => {
  return Number(value.replace(/\./g, ''));
};

export default function EditDebtScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();
  const { formatCurrency, currency } = useCurrency();

  // Form states
  const [name, setName] = useState('');
  const [amount, setAmount] = useState('');
  const [type, setType] = useState<'payable' | 'receivable'>('payable');
  const [description, setDescription] = useState('');
  const [hasReminder, setHasReminder] = useState(false);
  const [hasDueDate, setHasDueDate] = useState(false);
  const [dueDate, setDueDate] = useState(new Date());
  const [reminderDate, setReminderDate] = useState(new Date());
  const [status, setStatus] = useState<'active' | 'paid' | 'canceled'>('active');

  // UI states
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [debt, setDebt] = useState<Debt | null>(null);
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderDatePicker, setShowReminderDatePicker] = useState(false);

  // Load data
  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id]);

  const loadData = async () => {
    try {
      setLoading(true);
      const debtData = await DebtModel.getById(id);
      
      if (debtData) {
        setDebt(debtData);
        populateForm(debtData);
      } else {
        Alert.alert('Lỗi', 'Không tìm thấy khoản nợ');
        router.back();
      }
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu:', error);
      Alert.alert('Lỗi', 'Không thể tải thông tin khoản nợ');
    } finally {
      setLoading(false);
    }
  };

  const populateForm = (debtData: Debt) => {
    setName(debtData.name);
    setAmount(formatNumber(debtData.amount.toString()));
    setType(debtData.type);
    setDescription(debtData.description || '');
    setStatus(debtData.status);

    // Set dates
    if (debtData.due_date) {
      setHasDueDate(true);
      setDueDate(new Date(debtData.due_date));
    }

    if (debtData.reminder_date && debtData.reminder === 1) {
      setHasReminder(true);
      setReminderDate(new Date(debtData.reminder_date));
    }
  };

  const validateForm = (): boolean => {
    if (!name.trim()) {
      Alert.alert('Lỗi', 'Vui lòng nhập tên khoản nợ');
      return false;
    }

    const amountValue = parseFormattedNumber(amount);
    if (!amount.trim() || isNaN(amountValue) || amountValue <= 0) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tiền hợp lệ');
      return false;
    }

    if (hasReminder && !hasDueDate) {
      Alert.alert('Lỗi', 'Cần có ngày đáo hạn để đặt nhắc nhở');
      return false;
    }

    if (hasReminder && reminderDate >= dueDate) {
      Alert.alert('Lỗi', 'Ngày nhắc nhở phải trước ngày đáo hạn');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm() || !debt) return;

    try {
      setSaving(true);

      const updateData: UpdateDebtData = {
        name: name.trim(),
        amount: parseFormattedNumber(amount),
        type,
        description: description.trim() || undefined,
        due_date: hasDueDate ? dueDate.toISOString().split('T')[0] : undefined,
        reminder: hasReminder ? 1 : 0,
        reminder_date: hasReminder ? reminderDate.toISOString().split('T')[0] : undefined,
        status,
      };

      // If amount changed, need to recalculate remaining
      if (parseFormattedNumber(amount) !== debt.amount) {
        const paidAmount = debt.amount - debt.remaining;
        updateData.remaining = Math.max(0, parseFormattedNumber(amount) - paidAmount);
      }

      const result = await DebtModel.update(debt.id, updateData);

      if (result) {
        Alert.alert(
          'Thành công',
          'Đã cập nhật khoản nợ',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        Alert.alert('Lỗi', 'Không thể cập nhật khoản nợ');
      }
    } catch (error) {
      console.error('Lỗi khi cập nhật nợ:', error);
      Alert.alert('Lỗi', 'Không thể cập nhật khoản nợ');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={[styles.loadingContainer, { backgroundColor: "transparent" }]}>
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: "transparent" }]}>
      <View style={[styles.titleContainer, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Chỉnh sửa nợ
        </Text>
      </View>

      <TouchableOpacity
        style={[
          styles.saveButton,
          { backgroundColor: themeColors.primary },
          saving && { opacity: 0.6 }
        ]}
        onPress={handleSave}
        disabled={saving}
      >
        <Text style={styles.saveButtonText}>
          {saving ? 'Đang lưu...' : 'Lưu'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderTypeSelector = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Loại khoản nợ
      </Text>
      <View style={[styles.typeSelector, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            getCardStyle("low"),
            type === 'payable' && { backgroundColor: '#F44336' }
          ]}
          onPress={() => setType('payable')}
        >
          <View style={[styles.typeIcon, {
            backgroundColor: type === 'payable' ? 'rgba(255,255,255,0.2)' : '#FFEBEE'
          }]}>
            <Ionicons
              name="arrow-up"
              size={20}
              color={type === 'payable' ? 'white' : '#F44336'}
            />
          </View>
          <Text style={[
            styles.typeText,
            { color: type === 'payable' ? 'white' : themeColors.text }
          ]}>
            Phải trả
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            getCardStyle("low"),
            type === 'receivable' && { backgroundColor: '#4CAF50' }
          ]}
          onPress={() => setType('receivable')}
        >
          <View style={[styles.typeIcon, {
            backgroundColor: type === 'receivable' ? 'rgba(255,255,255,0.2)' : '#E8F5E8'
          }]}>
            <Ionicons
              name="arrow-down"
              size={20}
              color={type === 'receivable' ? 'white' : '#4CAF50'}
            />
          </View>
          <Text style={[
            styles.typeText,
            { color: type === 'receivable' ? 'white' : themeColors.text }
          ]}>
            Cho vay
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderBasicInfo = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Thông tin cơ bản
      </Text>

      {/* Tên khoản nợ */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Tên khoản nợ *
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low")]}>
          <TextInput
            style={[styles.input, { color: themeColors.text }]}
            placeholder={type === 'payable' ? 'VD: Nợ tiền ăn trưa' : 'VD: Cho A vay tiền'}
            placeholderTextColor={themeColors.secondaryText}
            value={name}
            onChangeText={setName}
            maxLength={100}
          />
        </View>
      </View>

      {/* Số tiền */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Số tiền *
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low")]}>
          <Text style={[styles.currencyPrefix, { color: themeColors.secondaryText }]}>
            {currency === 'VND' ? '₫' : currency}
          </Text>
          <TextInput
            style={[styles.input, { color: themeColors.text, flex: 1 }]}
            placeholder="0"
            placeholderTextColor={themeColors.secondaryText}
            value={amount}
            onChangeText={(text) => {
              const formatted = formatNumber(text);
              setAmount(formatted);
            }}
            keyboardType="numeric"
          />
        </View>
      </View>

      {/* Ghi chú */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Ghi chú
        </Text>
        <View style={[styles.inputContainer, getCardStyle("low"), { height: 80 }]}>
          <TextInput
            style={[styles.input, { color: themeColors.text, textAlignVertical: 'top' }]}
            placeholder="Thêm mô tả về khoản nợ..."
            placeholderTextColor={themeColors.secondaryText}
            value={description}
            onChangeText={setDescription}
            multiline
            maxLength={500}
          />
        </View>
      </View>

      {/* Trạng thái */}
      <View style={[styles.inputGroup, { backgroundColor: "transparent" }]}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Trạng thái
        </Text>
        <View style={[styles.statusSelector, { backgroundColor: "transparent" }]}>
          <TouchableOpacity
            style={[
              styles.statusButton,
              getCardStyle("low"),
              status === 'active' && { backgroundColor: themeColors.primary }
            ]}
            onPress={() => setStatus('active')}
          >
            <Text style={[
              styles.statusText,
              { color: status === 'active' ? 'white' : themeColors.text }
            ]}>
              Đang nợ
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statusButton,
              getCardStyle("low"),
              status === 'paid' && { backgroundColor: '#4CAF50' }
            ]}
            onPress={() => setStatus('paid')}
          >
            <Text style={[
              styles.statusText,
              { color: status === 'paid' ? 'white' : themeColors.text }
            ]}>
              Đã trả
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.statusButton,
              getCardStyle("low"),
              status === 'canceled' && { backgroundColor: '#757575' }
            ]}
            onPress={() => setStatus('canceled')}
          >
            <Text style={[
              styles.statusText,
              { color: status === 'canceled' ? 'white' : themeColors.text }
            ]}>
              Đã hủy
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderDateSettings = () => (
    <View style={[styles.section, { backgroundColor: "transparent" }]}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Ngày hạn và nhắc nhở
      </Text>

      {/* Ngày đáo hạn */}
      <View style={[styles.switchRow, getCardStyle("low")]}>
        <View style={[styles.switchInfo, { backgroundColor: "transparent" }]}>
          <Text style={[styles.switchTitle, { color: themeColors.text }]}>
            Ngày đáo hạn
          </Text>
          <Text style={[styles.switchDescription, { color: themeColors.secondaryText }]}>
            Đặt ngày hạn trả nợ
          </Text>
        </View>
        <Switch
          value={hasDueDate}
          onValueChange={setHasDueDate}
          trackColor={{ false: themeColors.border, true: themeColors.primary }}
          thumbColor={isDark ? '#f4f3f4' : '#f4f3f4'}
        />
      </View>

      {hasDueDate && (
        <TouchableOpacity
          style={[styles.dateSelector, getCardStyle("low")]}
          onPress={() => setShowDueDatePicker(true)}
        >
          <View style={[styles.dateIcon, { backgroundColor: themeColors.primary + '20' }]}>
            <Ionicons
              name="calendar"
              size={20}
              color={themeColors.primary}
            />
          </View>
          <View style={[styles.dateInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.dateLabel, { color: themeColors.text }]}>
              Ngày đáo hạn
            </Text>
            <Text style={[styles.dateValue, { color: themeColors.secondaryText }]}>
              {dueDate.toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </TouchableOpacity>
      )}

      {/* Nhắc nhở */}
      <View style={[styles.switchRow, getCardStyle("low")]}>
        <View style={[styles.switchInfo, { backgroundColor: "transparent" }]}>
          <Text style={[styles.switchTitle, { color: themeColors.text }]}>
            Nhắc nhở
          </Text>
          <Text style={[styles.switchDescription, { color: themeColors.secondaryText }]}>
            Nhận thông báo trước khi đến hạn
          </Text>
        </View>
        <Switch
          value={hasReminder}
          onValueChange={setHasReminder}
          disabled={!hasDueDate}
          trackColor={{ false: themeColors.border, true: themeColors.primary }}
          thumbColor={isDark ? '#f4f3f4' : '#f4f3f4'}
        />
      </View>

      {hasReminder && hasDueDate && (
        <TouchableOpacity
          style={[styles.dateSelector, getCardStyle("low")]}
          onPress={() => setShowReminderDatePicker(true)}
        >
          <View style={[styles.dateIcon, { backgroundColor: themeColors.primary + '20' }]}>
            <Ionicons
              name="notifications"
              size={20}
              color={themeColors.primary}
            />
          </View>
          <View style={[styles.dateInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.dateLabel, { color: themeColors.text }]}>
              Ngày nhắc nhở
            </Text>
            <Text style={[styles.dateValue, { color: themeColors.secondaryText }]}>
              {reminderDate.toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      {renderHeader()}

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {renderTypeSelector()}
        {renderBasicInfo()}
        {renderDateSettings()}
      </ScrollView>

      {/* Date Pickers */}
      {showDueDatePicker && (
        <DateTimePicker
          value={dueDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowDueDatePicker(false);
            if (selectedDate) {
              setDueDate(selectedDate);
              if (reminderDate >= selectedDate) {
                const newReminderDate = new Date(selectedDate);
                newReminderDate.setDate(selectedDate.getDate() - 1);
                setReminderDate(newReminderDate);
              }
            }
          }}
        />
      )}

      {showReminderDatePicker && (
        <DateTimePicker
          value={reminderDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowReminderDatePicker(false);
            if (selectedDate) {
              setReminderDate(selectedDate);
            }
          }}
          maximumDate={new Date(dueDate.getTime() - 24 * 60 * 60 * 1000)}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  section: {
    padding: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    height: 48,
  },
  input: {
    fontSize: 16,
    flex: 1,
    padding: 0,
  },
  currencyPrefix: {
    fontSize: 16,
    marginRight: 8,
  },
  statusSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  statusButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginBottom: 12,
  },
  switchInfo: {
    flex: 1,
  },
  switchTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  switchDescription: {
    fontSize: 14,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
  },
  dateIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dateInfo: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  dateValue: {
    fontSize: 14,
  },
});