// File: app/(more)/affiliate-earnings.tsx
// File này liên quan đến: app/(more)/affiliate-dashboard.tsx, lib/services/affiliate-service.ts

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  FlatList,
  Alert,
  Modal,
  TextInput,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useTheme } from "@/context/ThemeContext";

interface EarningsData {
  totalEarnings: number;
  availableBalance: number;
  pendingBalance: number;
  totalWithdrawn: number;
  minimumWithdraw: number;
}

interface Transaction {
  id: string;
  type: "commission" | "withdrawal" | "bonus";
  amount: number;
  status: "completed" | "pending" | "failed";
  date: string;
  description: string;
  referralCode?: string;
}

interface WithdrawalMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
  minimumAmount: number;
  processingTime: string;
  fee: number;
}

const WITHDRAWAL_METHODS: WithdrawalMethod[] = [
  {
    id: "bank",
    name: "Chuyển khoản ngân hàng",
    icon: "card",
    description: "Chuyển trực tiếp vào tài khoản ngân hàng",
    minimumAmount: 100000,
    processingTime: "1-3 ngày làm việc",
    fee: 0,
  },
  {
    id: "momo",
    name: "Ví MoMo",
    icon: "wallet",
    description: "Chuyển vào ví MoMo",
    minimumAmount: 50000,
    processingTime: "15-30 phút",
    fee: 2000,
  },
  {
    id: "zalopay",
    name: "ZaloPay",
    icon: "card-outline",
    description: "Chuyển vào ví ZaloPay",
    minimumAmount: 50000,
    processingTime: "15-30 phút",
    fee: 2000,
  },
];

export default function AffiliateEarningsScreen() {
  const { themeColors, isDark, getCardStyle, getShadowStyle } = useTheme();
  const [loading, setLoading] = useState(true);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<WithdrawalMethod | null>(null);
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [bankDetails, setBankDetails] = useState({
    bankName: "",
    accountNumber: "",
    accountName: "",
  });

  const [earningsData, setEarningsData] = useState<EarningsData>({
    totalEarnings: 0,
    availableBalance: 0,
    pendingBalance: 0,
    totalWithdrawn: 0,
    minimumWithdraw: 50000,
  });

  const [transactions, setTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    loadEarningsData();
  }, []);

  const loadEarningsData = async () => {
    try {
      // TODO: Gọi API để load dữ liệu earnings
      // Giả lập dữ liệu
      setTimeout(() => {
        setEarningsData({
          totalEarnings: 1250000,
          availableBalance: 800000,
          pendingBalance: 450000,
          totalWithdrawn: 650000,
          minimumWithdraw: 50000,
        });

        setTransactions([
          {
            id: "1",
            type: "commission",
            amount: 125000,
            status: "completed",
            date: "2024-01-15",
            description: "Hoa hồng từ Premium Monthly",
            referralCode: "REF001",
          },
          {
            id: "2",
            type: "withdrawal",
            amount: -300000,
            status: "completed",
            date: "2024-01-10",
            description: "Rút tiền qua MoMo",
          },
          {
            id: "3",
            type: "commission",
            amount: 250000,
            status: "pending",
            date: "2024-01-08",
            description: "Hoa hồng từ Premium Yearly",
            referralCode: "REF002",
          },
          {
            id: "4",
            type: "bonus",
            amount: 50000,
            status: "completed",
            date: "2024-01-05",
            description: "Bonus milestone 10 referrals",
          },
        ]);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Error loading earnings data:", error);
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "#4CAF50";
      case "pending":
        return "#FF9800";
      case "failed":
        return "#F44336";
      default:
        return themeColors.secondaryText;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Hoàn thành";
      case "pending":
        return "Đang xử lý";
      case "failed":
        return "Thất bại";
      default:
        return status;
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case "commission":
        return "trending-up";
      case "withdrawal":
        return "arrow-down";
      case "bonus":
        return "gift";
      default:
        return "cash";
    }
  };

  const handleWithdraw = () => {
    if (earningsData.availableBalance < earningsData.minimumWithdraw) {
      Alert.alert(
        "Không đủ số dư",
        `Số dư khả dụng phải từ ${formatCurrency(earningsData.minimumWithdraw)} trở lên.`
      );
      return;
    }
    setShowWithdrawModal(true);
  };

  const processWithdrawal = async () => {
    const amount = parseFloat(withdrawAmount);
    
    if (!selectedMethod) {
      Alert.alert("Lỗi", "Vui lòng chọn phương thức rút tiền.");
      return;
    }

    if (amount < selectedMethod.minimumAmount) {
      Alert.alert(
        "Số tiền không hợp lệ",
        `Số tiền tối thiểu cho ${selectedMethod.name} là ${formatCurrency(selectedMethod.minimumAmount)}.`
      );
      return;
    }

    if (amount > earningsData.availableBalance) {
      Alert.alert("Lỗi", "Số tiền rút vượt quá số dư khả dụng.");
      return;
    }

    if (selectedMethod.id === "bank" && (!bankDetails.bankName || !bankDetails.accountNumber || !bankDetails.accountName)) {
      Alert.alert("Lỗi", "Vui lòng điền đầy đủ thông tin ngân hàng.");
      return;
    }

    try {
      // TODO: Gọi API xử lý rút tiền
      Alert.alert(
        "Yêu cầu rút tiền thành công",
        `Yêu cầu rút ${formatCurrency(amount)} qua ${selectedMethod.name} đã được gửi. Thời gian xử lý: ${selectedMethod.processingTime}.`,
        [
          {
            text: "OK",
            onPress: () => {
              setShowWithdrawModal(false);
              setWithdrawAmount("");
              setSelectedMethod(null);
              setBankDetails({ bankName: "", accountNumber: "", accountName: "" });
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert("Lỗi", "Không thể xử lý yêu cầu rút tiền. Vui lòng thử lại sau.");
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        onPress={() => router.back()}
        style={[
          styles.backButton,
          {
            backgroundColor: themeColors.iconBackground,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="arrow-back" size={24} color={themeColors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: themeColors.text }]}>
        Thu nhập Affiliate
      </Text>
      <TouchableOpacity
        onPress={handleWithdraw}
        style={[
          styles.withdrawButton,
          {
            backgroundColor: themeColors.primary,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="download" size={20} color="#fff" />
      </TouchableOpacity>
    </View>
  );

  const renderEarningsCards = () => (
    <View style={styles.earningsContainer}>
      <View style={styles.earningsRow}>
        <View
          style={[
            styles.earningsCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.earningsIcon}>
            <Ionicons name="wallet" size={24} color="#4CAF50" />
          </View>
          <Text style={[styles.earningsValue, { color: "#4CAF50" }]}>
            {formatCurrency(earningsData.availableBalance)}
          </Text>
          <Text style={[styles.earningsLabel, { color: themeColors.secondaryText }]}>
            Số dư khả dụng
          </Text>
        </View>

        <View
          style={[
            styles.earningsCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.earningsIcon}>
            <Ionicons name="hourglass" size={24} color="#FF9800" />
          </View>
          <Text style={[styles.earningsValue, { color: "#FF9800" }]}>
            {formatCurrency(earningsData.pendingBalance)}
          </Text>
          <Text style={[styles.earningsLabel, { color: themeColors.secondaryText }]}>
            Đang chờ xử lý
          </Text>
        </View>
      </View>

      <View style={styles.earningsRow}>
        <View
          style={[
            styles.earningsCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.earningsIcon}>
            <Ionicons name="trending-up" size={24} color="#2196F3" />
          </View>
          <Text style={[styles.earningsValue, { color: "#2196F3" }]}>
            {formatCurrency(earningsData.totalEarnings)}
          </Text>
          <Text style={[styles.earningsLabel, { color: themeColors.secondaryText }]}>
            Tổng thu nhập
          </Text>
        </View>

        <View
          style={[
            styles.earningsCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.earningsIcon}>
            <Ionicons name="arrow-down" size={24} color="#9C27B0" />
          </View>
          <Text style={[styles.earningsValue, { color: "#9C27B0" }]}>
            {formatCurrency(earningsData.totalWithdrawn)}
          </Text>
          <Text style={[styles.earningsLabel, { color: themeColors.secondaryText }]}>
            Đã rút
          </Text>
        </View>
      </View>
    </View>
  );

  const renderTransactionItem = ({ item }: { item: Transaction }) => (
    <View
      style={[
        styles.transactionItem,
        getCardStyle("low"),
        { backgroundColor: themeColors.cardBackground },
      ]}
    >
      <View style={styles.transactionLeft}>
        <View
          style={[
            styles.transactionIcon,
            {
              backgroundColor: `${getStatusColor(item.status)}20`,
            },
          ]}
        >
          <Ionicons
            name={getTransactionIcon(item.type)}
            size={20}
            color={getStatusColor(item.status)}
          />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={[styles.transactionDescription, { color: themeColors.text }]}>
            {item.description}
          </Text>
          <View style={styles.transactionMeta}>
            <Text style={[styles.transactionDate, { color: themeColors.secondaryText }]}>
              {formatDate(item.date)}
            </Text>
            {item.referralCode && (
              <Text style={[styles.transactionRef, { color: themeColors.primary }]}>
                • {item.referralCode}
              </Text>
            )}
          </View>
          <Text
            style={[
              styles.transactionStatus,
              { color: getStatusColor(item.status) },
            ]}
          >
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>
      <Text
        style={[
          styles.transactionAmount,
          {
            color: item.amount > 0 ? "#4CAF50" : "#F44336",
          },
        ]}
      >
        {item.amount > 0 ? "+" : ""}{formatCurrency(item.amount)}
      </Text>
    </View>
  );

  const renderWithdrawModal = () => (
    <Modal
      visible={showWithdrawModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowWithdrawModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.withdrawModal, getCardStyle("high")]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Rút tiền
            </Text>
            <TouchableOpacity onPress={() => setShowWithdrawModal(false)}>
              <Ionicons name="close" size={24} color={themeColors.secondaryText} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <Text style={[styles.balanceText, { color: themeColors.text }]}>
              Số dư khả dụng: {formatCurrency(earningsData.availableBalance)}
            </Text>

            <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
              Chọn phương thức rút tiền
            </Text>
            
            {WITHDRAWAL_METHODS.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.methodItem,
                  {
                    backgroundColor: selectedMethod?.id === method.id
                      ? themeColors.iconBackground
                      : "transparent",
                    borderColor: themeColors.border,
                  },
                ]}
                onPress={() => setSelectedMethod(method)}
              >
                <View style={styles.methodLeft}>
                  <Ionicons name={method.icon as any} size={24} color={themeColors.primary} />
                  <View style={styles.methodInfo}>
                    <Text style={[styles.methodName, { color: themeColors.text }]}>
                      {method.name}
                    </Text>
                    <Text style={[styles.methodDesc, { color: themeColors.secondaryText }]}>
                      {method.description}
                    </Text>
                    <Text style={[styles.methodDetails, { color: themeColors.secondaryText }]}>
                      Tối thiểu: {formatCurrency(method.minimumAmount)} • {method.processingTime}
                    </Text>
                  </View>
                </View>
                {selectedMethod?.id === method.id && (
                  <Ionicons name="checkmark-circle" size={24} color={themeColors.primary} />
                )}
              </TouchableOpacity>
            ))}

            {selectedMethod && (
              <View style={styles.withdrawForm}>
                <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
                  Số tiền rút
                </Text>
                <TextInput
                  style={[
                    styles.amountInput,
                    {
                      backgroundColor: themeColors.iconBackground,
                      color: themeColors.text,
                      borderColor: themeColors.border,
                    },
                  ]}
                  value={withdrawAmount}
                  onChangeText={setWithdrawAmount}
                  placeholder="Nhập số tiền"
                  placeholderTextColor={themeColors.secondaryText}
                  keyboardType="numeric"
                />

                {selectedMethod.id === "bank" && (
                  <View style={styles.bankDetails}>
                    <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
                      Thông tin ngân hàng
                    </Text>
                    <TextInput
                      style={[
                        styles.bankInput,
                        {
                          backgroundColor: themeColors.iconBackground,
                          color: themeColors.text,
                          borderColor: themeColors.border,
                        },
                      ]}
                      value={bankDetails.bankName}
                      onChangeText={(text) => setBankDetails({ ...bankDetails, bankName: text })}
                      placeholder="Tên ngân hàng"
                      placeholderTextColor={themeColors.secondaryText}
                    />
                    <TextInput
                      style={[
                        styles.bankInput,
                        {
                          backgroundColor: themeColors.iconBackground,
                          color: themeColors.text,
                          borderColor: themeColors.border,
                        },
                      ]}
                      value={bankDetails.accountNumber}
                      onChangeText={(text) => setBankDetails({ ...bankDetails, accountNumber: text })}
                      placeholder="Số tài khoản"
                      placeholderTextColor={themeColors.secondaryText}
                      keyboardType="numeric"
                    />
                    <TextInput
                      style={[
                        styles.bankInput,
                        {
                          backgroundColor: themeColors.iconBackground,
                          color: themeColors.text,
                          borderColor: themeColors.border,
                        },
                      ]}
                      value={bankDetails.accountName}
                      onChangeText={(text) => setBankDetails({ ...bankDetails, accountName: text })}
                      placeholder="Tên chủ tài khoản"
                      placeholderTextColor={themeColors.secondaryText}
                    />
                  </View>
                )}

                {selectedMethod.fee > 0 && (
                  <Text style={[styles.feeText, { color: themeColors.secondaryText }]}>
                    Phí giao dịch: {formatCurrency(selectedMethod.fee)}
                  </Text>
                )}
              </View>
            )}
          </ScrollView>

          <TouchableOpacity
            style={[
              styles.withdrawSubmitButton,
              { backgroundColor: themeColors.primary, ...getShadowStyle("low") },
            ]}
            onPress={processWithdrawal}
            disabled={!selectedMethod || !withdrawAmount}
          >
            <Text style={styles.withdrawSubmitText}>Xác nhận rút tiền</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải dữ liệu thu nhập...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      {renderHeader()}
      
      <ScrollView 
        style={{ flex: 1 }} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 30 }}
      >
        {renderEarningsCards()}

        <View style={styles.transactionsSection}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Lịch sử giao dịch
          </Text>
          
          <FlatList
            data={transactions}
            renderItem={renderTransactionItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>

      {renderWithdrawModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 1,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  withdrawButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  earningsContainer: {
    paddingHorizontal: 16,
    marginVertical: 20,
  },
  earningsRow: {
    flexDirection: "row",
    marginBottom: 12,
  },
  earningsCard: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    marginHorizontal: 6,
  },
  earningsIcon: {
    marginBottom: 8,
  },
  earningsValue: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  earningsLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  transactionsSection: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 4,
  },
  transactionMeta: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionRef: {
    fontSize: 12,
    marginLeft: 4,
  },
  transactionStatus: {
    fontSize: 12,
    fontWeight: "500",
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "bold",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  withdrawModal: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  balanceText: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 20,
    textAlign: "center",
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    marginTop: 20,
  },
  methodItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 8,
  },
  methodLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  methodInfo: {
    marginLeft: 12,
    flex: 1,
  },
  methodName: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  methodDesc: {
    fontSize: 12,
    marginBottom: 2,
  },
  methodDetails: {
    fontSize: 11,
  },
  withdrawForm: {
    marginTop: 20,
  },
  amountInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  bankDetails: {
    marginTop: 16,
  },
  bankInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    marginBottom: 8,
  },
  feeText: {
    fontSize: 12,
    textAlign: "center",
    marginTop: 8,
  },
  withdrawSubmitButton: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  withdrawSubmitText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});