// File: app/(more)/calendar.tsx
// File này hiển thị tính năng Lịch với khả năng xem chi tiêu theo ngày
// File này liên quan đến: components/CalendarView.tsx, context/ThemeContext.tsx, lib/models/transaction.ts

import React, { useState, useEffect, useMemo } from "react";
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  Switch,
  FlatList,
  Image,
  Animated,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, Stack } from "expo-router";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useTheme } from "@/context/ThemeContext";
import { useLocalization } from "@/context/LocalizationContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { TransactionModel, type Transaction } from "@/lib/models/transaction";
import { useCategoryTranslation } from "@/utils/categoryTranslation";

// Các chế độ xem lịch
type CalendarViewMode = "month" | "week";

// Dữ liệu các ngày Tết (1/1 âm lịch) từ 2020-2030
const TET_DATES: Record<number, Date> = {
  2020: new Date(2020, 0, 25), // 25/1/2020
  2021: new Date(2021, 1, 12), // 12/2/2021
  2022: new Date(2022, 1, 1),  // 1/2/2022
  2023: new Date(2023, 0, 22), // 22/1/2023
  2024: new Date(2024, 1, 10), // 10/2/2024
  2025: new Date(2025, 0, 29), // 29/1/2025
  2026: new Date(2026, 1, 17), // 17/2/2026
  2027: new Date(2027, 1, 6),  // 6/2/2027
  2028: new Date(2028, 0, 26), // 26/1/2028
  2029: new Date(2029, 1, 13), // 13/2/2029
  2030: new Date(2030, 1, 3),  // 3/2/2030
};

// Số ngày trong các tháng âm lịch (29 hoặc 30 ngày)
const LUNAR_MONTH_DAYS_2025: number[] = [29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30];
const LUNAR_MONTH_DAYS_2024: number[] = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

// Hàm chuyển đổi ngày dương lịch sang âm lịch chính xác
function convertToLunarDate(date: Date): {day: number, month: number, year: number} {
  const year = date.getFullYear();
  
  // Nếu không có dữ liệu cho năm này, dùng công thức ước lượng
  if (!TET_DATES[year] && !TET_DATES[year - 1] && !TET_DATES[year + 1]) {
    return convertToLunarDateEstimate(date);
  }
  
  // Tìm năm âm lịch gần nhất
  let lunarYear = year;
  let tetDate = TET_DATES[year];
  
  // Nếu ngày hiện tại trước Tết của năm này, thuộc năm âm lịch trước
  if (tetDate && date < tetDate) {
    lunarYear = year - 1;
    tetDate = TET_DATES[year - 1];
  }
  
  // Nếu không có dữ liệu Tết năm trước, dùng năm hiện tại
  if (!tetDate) {
    tetDate = TET_DATES[year] || new Date(year, 1, 1);
    lunarYear = year;
  }
  
  // Tính số ngày từ Tết
  const msPerDay = 24 * 60 * 60 * 1000;
  const daysDiff = Math.floor((date.getTime() - tetDate.getTime()) / msPerDay);
  
  if (daysDiff < 0) {
    // Ngày trước Tết - tính ngược từ cuối năm trước
    return calculateLunarDateBefore(Math.abs(daysDiff), lunarYear + 1);
  } else {
    // Ngày sau Tết - tính từ đầu năm âm lịch
    return calculateLunarDateAfter(daysDiff + 1, lunarYear);
  }
}

// Tính ngày âm lịch sau Tết
function calculateLunarDateAfter(totalDays: number, lunarYear: number): {day: number, month: number, year: number} {
  const monthDays = getMonthDays(lunarYear);
  
  let lunarMonth = 1;
  let lunarDay = totalDays;
  
  for (let month = 0; month < 12; month++) {
    const daysInMonth = monthDays[month];
    if (lunarDay <= daysInMonth) {
      break;
    }
    lunarDay -= daysInMonth;
    lunarMonth++;
    
    if (lunarMonth > 12) {
      lunarMonth = 1;
      lunarYear++;
    }
  }
  
  return {
    day: lunarDay,
    month: lunarMonth,
    year: lunarYear
  };
}

// Tính ngày âm lịch trước Tết
function calculateLunarDateBefore(daysBack: number, lunarYear: number): {day: number, month: number, year: number} {
  const prevYear = lunarYear - 1;
  const monthDays = getMonthDays(prevYear);
  
  let lunarMonth = 12;
  let remainingDays = daysBack;
  
  // Tính ngược từ cuối năm
  for (let month = 11; month >= 0; month--) {
    const daysInMonth = monthDays[month];
    if (remainingDays <= daysInMonth) {
      const lunarDay = daysInMonth - remainingDays + 1;
      return {
        day: lunarDay,
        month: month + 1,
        year: prevYear
      };
    }
    remainingDays -= daysInMonth;
  }
  
  // Fallback
  return {
    day: 1,
    month: 1,
    year: prevYear
  };
}

// Lấy số ngày trong các tháng của năm âm lịch
function getMonthDays(lunarYear: number): number[] {
  switch (lunarYear) {
    case 2024:
      return LUNAR_MONTH_DAYS_2024;
    case 2025:
      return LUNAR_MONTH_DAYS_2025;
    default:
      // Mặc định xen kẽ 29-30 ngày
      return [29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30];
  }
}

// Ước lượng ngày âm lịch khi không có dữ liệu chính xác
function convertToLunarDateEstimate(date: Date): {day: number, month: number, year: number} {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  
  // Ước lượng chênh lệch khoảng 18-33 ngày
  const baseOffset = 20;
  const monthOffset = Math.floor(month * 0.5); // Điều chỉnh theo tháng
  const offset = baseOffset + monthOffset;
  
  let lunarDay = day - offset;
  let lunarMonth = month;
  let lunarYear = year;
  
  if (lunarDay <= 0) {
    lunarMonth -= 1;
    if (lunarMonth <= 0) {
      lunarMonth = 12;
      lunarYear -= 1;
    }
    lunarDay += 30;
  }
  
  // Đảm bảo giá trị hợp lệ
  lunarDay = Math.max(1, Math.min(30, lunarDay));
  
  return {
    day: lunarDay,
    month: lunarMonth,
    year: lunarYear
  };
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const DAY_CELL_SIZE = SCREEN_WIDTH / 7 - 8;

export default function CalendarScreen() {
  const { themeColors, isDark } = useTheme();
  const { t } = useLocalization();
  const translateCategoryName = useCategoryTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<CalendarViewMode>("month");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [showLunarCalendar, setShowLunarCalendar] = useState(false);
  const [toggleAnimation] = useState(new Animated.Value(0));
  
  // Tải cài đặt lịch âm từ AsyncStorage
  useEffect(() => {
    const loadLunarCalendarSetting = async () => {
      try {
        const value = await AsyncStorage.getItem('@calendar_show_lunar');
        if(value !== null) {
          setShowLunarCalendar(value === 'true');
          Animated.timing(toggleAnimation, {
            toValue: value === 'true' ? 1 : 0,
            duration: 300,
            useNativeDriver: false
          }).start();
        }
      } catch(e) {
        console.error('Không thể tải cài đặt lịch âm', e);
      }
    };
    
    loadLunarCalendarSetting();
  }, []);

  // Tải dữ liệu giao dịch
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true);
        const data = await TransactionModel.getAll();
        setTransactions(data);
      } catch (error) {
        console.error('Lỗi khi tải dữ liệu giao dịch:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, []);
  
  // Lưu cài đặt khi thay đổi
  const toggleLunarCalendar = async (value: boolean) => {
    setShowLunarCalendar(value);
    Animated.timing(toggleAnimation, {
      toValue: value ? 1 : 0,
      duration: 300,
      useNativeDriver: false
    }).start();
    
    try {
      await AsyncStorage.setItem('@calendar_show_lunar', value.toString());
    } catch (e) {
      console.error('Không thể lưu cài đặt lịch âm', e);
    }
  };

  // Tính toán ngày trong tháng hiện tại
  const daysInMonth = useMemo(() => {
    return generateCalendarDays(currentMonth);
  }, [currentMonth]);

  // Tính toán ngày trong tuần hiện tại
  const daysInWeek = useMemo(() => {
    const startOfWeek = new Date(selectedDate);
    startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay());
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  }, [selectedDate]);

  // Danh sách ngày hiển thị dựa vào chế độ xem
  const daysToDisplay = useMemo(() => {
    switch (viewMode) {
      case "month":
        return daysInMonth;
      case "week":
        return daysInWeek;
      default:
        return daysInMonth;
    }
  }, [viewMode, daysInMonth, daysInWeek, selectedDate]);

  // Lấy tổng chi tiêu cho một ngày
  const getDailyTotal = (date: Date) => {
    const dayTransactions = transactions.filter(
      (t) => isSameDay(new Date(t.date), date)
    );
    return dayTransactions.reduce((sum, t) => sum + t.amount, 0);
  };

  // Lấy các giao dịch cho một ngày
  const getDailyTransactions = (date: Date) => {
    return transactions.filter((t) => isSameDay(new Date(t.date), date));
  };

  // Hàm định dạng tiền
  const formatCurrency = (amount: number) => {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Chuyển tháng trước/sau
  const changeMonth = (increment: number) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + increment);
    setCurrentMonth(newMonth);
  };

  // Header lịch
  const renderCalendarHeader = () => {
    const monthNames = ["Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", 
                         "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"];
    
    const monthName = monthNames[currentMonth.getMonth()];
    const year = currentMonth.getFullYear();

    return (
      <View style={styles.calendarHeader}>
        <View style={styles.monthYearContainer}>
          <Text style={[styles.monthTitle, { color: themeColors.text }]}>
            {monthName}
          </Text>
          <Text style={[styles.yearTitle, { color: themeColors.secondaryText }]}>
            {year}
          </Text>
        </View>

        <View style={styles.monthNavigationButtons}>
          <TouchableOpacity 
            style={[styles.navButton, { backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0' }]} 
            onPress={() => changeMonth(-1)}
          >
            <Ionicons name="chevron-back" size={20} color={themeColors.primary} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.navButton, { backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0' }]} 
            onPress={() => changeMonth(1)}
          >
            <Ionicons name="chevron-forward" size={20} color={themeColors.primary} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Thanh chuyển đổi chế độ xem
  const renderViewModeSelector = () => {
    return (
      <View style={styles.viewModeContainer}>
        <LinearGradient
          colors={isDark ? ['#1A1A1A', '#282828'] : ['#F6F6F6', '#EFEFEF']} 
          style={styles.viewModeSelectorBackground}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {(["month", "week"] as CalendarViewMode[]).map((mode) => (
            <TouchableOpacity
              key={mode}
              style={[
                styles.viewModeButton,
                viewMode === mode && {
                  backgroundColor: themeColors.primary,
                  shadowColor: themeColors.primary,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.3,
                  shadowRadius: 4,
                  elevation: 4,
                },
              ]}
              onPress={() => setViewMode(mode)}
            >
              <Text
                style={[
                  styles.viewModeText,
                  {
                    color:
                      viewMode === mode
                        ? '#FFFFFF'
                        : themeColors.text,
                  },
                ]}
              >
                {mode === "month" ? "Tháng" : "Tuần"}
              </Text>
            </TouchableOpacity>
          ))}
        </LinearGradient>
      </View>
    );
  };

  // Tên các ngày trong tuần
  const renderWeekDayNames = () => {
    const weekDays = ["CN", "T2", "T3", "T4", "T5", "T6", "T7"];
    return (
      <View style={styles.weekDayRow}>
        {weekDays.map((day, index) => (
          <View key={index} style={styles.weekDayCell}>
            <Text
              style={[
                styles.weekDayText, 
                { 
                  color: index === 0 ? '#FF5252' : themeColors.secondaryText,
                  fontWeight: '600'
                }
              ]}
            >
              {day}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  // Lịch chế độ tháng
  const renderMonthView = () => {
    const numRows = Math.ceil(daysInMonth.length / 7);
    const rows = [];

    for (let i = 0; i < numRows; i++) {
      const rowCells = [];
      for (let j = 0; j < 7; j++) {
        const dayIndex = i * 7 + j;
        if (dayIndex >= daysInMonth.length) {
          rowCells.push(<View key={`empty-${dayIndex}`} style={styles.dayCell} />);
          continue;
        }

        const day = daysInMonth[dayIndex];
        const isToday = isSameDay(day, new Date());
        const isSelected = isSameDay(day, selectedDate);
        const isCurrentMonth = day.getMonth() === currentMonth.getMonth();
        const total = getDailyTotal(day);
        const hasSpendings = total < 0;
        const hasIncome = total > 0;
        const isSunday = day.getDay() === 0;
        
        // Lịch âm
        const lunarDate = showLunarCalendar ? convertToLunarDate(day) : null;
        
        // Đánh dấu ngày đặc biệt (ngày lễ hoặc có sự kiện)
        const isSpecialDay = day.getDate() === 20; // Ví dụ: giả sử ngày 20 là ngày lễ

        rowCells.push(
          <TouchableOpacity
            key={dayIndex}
            style={[
              styles.dayCell,
              isToday && styles.todayCell,
              isSelected && styles.selectedCell,
              !isCurrentMonth && styles.otherMonthCell,
            ]}
            onPress={() => setSelectedDate(day)}
          >
            {/* Nền cho ngày được chọn */}
            {isSelected && (
              <View style={[
                styles.selectedDayBackground,
                { borderColor: '#007AFF', borderWidth: 1 } // Thêm viền cho rõ ràng hơn
              ]} />
            )}
            
            <View style={styles.dayCellContent}>
              {/* Đánh dấu ngày có giao dịch */}
              {total !== 0 && isCurrentMonth && (
                <View style={[
                  styles.transactionIndicator,
                  { backgroundColor: total > 0 ? '#4CAF50' : '#FF5252' }
                ]} />
              )}
              
              {/* Số ngày */}
              <Text
                style={[
                  styles.dayNumber,
                  {
                    color: !isCurrentMonth 
                      ? themeColors.disabledText
                      : isSelected 
                        ? isDark ? '#FFFFFF' : '#000000'
                        : isSunday && isCurrentMonth 
                          ? '#FF5252'
                          : themeColors.text,
                    fontWeight: isToday || isSelected ? "700" : "normal",
                  },
                ]}
              >
                {day.getDate()}
              </Text>
              
              {/* Hiển thị số tiền cho mỗi ngày nếu có giao dịch */}
              {total !== 0 && isCurrentMonth && (
                <Text
                  style={[
                    styles.dayAmount,
                    { color: total > 0 ? "#4CAF50" : "#FF5252" },
                  ]}
                  numberOfLines={1}
                >
                  {total > 0 ? "+" : "-"}{formatCurrency(Math.abs(total)/1000)}K
                </Text>
              )}
              
              {/* Ngày âm lịch */}
              {showLunarCalendar && lunarDate && isCurrentMonth && (
                <Animated.Text 
                  style={[
                    styles.lunarDayNumber,
                    {
                      opacity: toggleAnimation,
                      color: isSunday ? '#FF5252' : '#FF9800',
                      transform: [{
                        translateY: toggleAnimation.interpolate({
                          inputRange: [0, 1],
                          outputRange: [5, 0]
                        })
                      }]
                    }
                  ]}
                >
                  {lunarDate.day}
                </Animated.Text>
              )}
            </View>
            
            {/* Đánh dấu ngày đặc biệt */}
            {isSpecialDay && isCurrentMonth && (
              <View style={styles.specialDayMark} />
            )}
          </TouchableOpacity>
        );
      }

      rows.push(
        <View key={`row-${i}`} style={styles.calendarRow}>
          {rowCells}
        </View>
      );
    }

    return (
      <View style={styles.calendar}>
        {renderWeekDayNames()}
        {rows}
      </View>
    );
  };

  // Lịch chế độ tuần
  const renderWeekView = () => {
    return (
      <View style={styles.calendar}>
        {renderWeekDayNames()}
        <View style={styles.weekViewContainer}>
          {daysInWeek.map((day, index) => {
            const isToday = isSameDay(day, new Date());
            const isSelected = isSameDay(day, selectedDate);
            const total = getDailyTotal(day);
            const isSunday = day.getDay() === 0;
            
            // Lịch âm
            const lunarDate = showLunarCalendar ? convertToLunarDate(day) : null;

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.weekDayItem,
                  isToday && styles.todayWeekCell,
                  isSelected && styles.selectedWeekCell,
                ]}
                onPress={() => setSelectedDate(day)}
              >
                {isSelected && (
                  <View style={[
                    styles.selectedWeekDayBackground,
                    { borderColor: '#007AFF', borderWidth: 1 } // Thêm viền cho rõ ràng hơn
                  ]} />
                )}
                
                <Text style={[
                  styles.weekDayLabel, 
                  { 
                    color: isSunday ? '#FF5252' : themeColors.secondaryText,
                    fontWeight: '600'
                  }
                ]}>
                  {day.toLocaleDateString("vi", { weekday: "short" })}
                </Text>
                
                <Text
                  style={[
                    styles.weekDayNumber,
                    { 
                      color: isSelected 
                        ? isDark ? '#FFFFFF' : '#000000' 
                        : isSunday ? '#FF5252' : themeColors.text,
                      fontWeight: isToday || isSelected ? "700" : "normal",
                    },
                  ]}
                >
                  {day.getDate()}
                </Text>
                
                {/* Ngày âm lịch */}
                {showLunarCalendar && lunarDate && (
                  <Animated.Text 
                    style={[
                      styles.weekLunarDayNumber,
                      {
                        opacity: toggleAnimation,
                        color: isSunday ? '#FF5252' : '#FF9800',
                        transform: [{
                          translateY: toggleAnimation.interpolate({
                            inputRange: [0, 1],
                            outputRange: [5, 0]
                          })
                        }]
                      }
                    ]}
                  >
                    {lunarDate.day}/{lunarDate.month}
                  </Animated.Text>
                )}
                
                {/* Hiển thị số tiền trong ngày */}
                {total !== 0 && (
                  <Text
                  style={[
                    styles.weekDayAmount,
                    { color: total > 0 ? "#4CAF50" : "#FF5252" },
                  ]}
                  numberOfLines={1}
                >
                  {total > 0 ? "+" : "-"}{formatCurrency(Math.abs(total)/1000)}K
                </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // Chi tiết giao dịch theo ngày
  const renderTransactionDetails = () => {
    const dailyTransactions = getDailyTransactions(selectedDate);
    const dailyTotal = getDailyTotal(selectedDate);
    
    // Thông tin ngày
    const weekday = selectedDate.toLocaleDateString("vi", { weekday: "long" });
    const day = selectedDate.getDate();
    const month = selectedDate.toLocaleDateString("vi", { month: "long" });
    const year = selectedDate.getFullYear();
    
    // Lịch âm
    const lunarDate = showLunarCalendar ? convertToLunarDate(selectedDate) : null;
    const lunarDateString = lunarDate ? 
      `${lunarDate.day}/${lunarDate.month} âm lịch` : '';

    // Tính tổng thu nhập và chi tiêu
    const income = dailyTransactions
      .filter(t => t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);
      
    const expenses = dailyTransactions
      .filter(t => t.amount < 0)
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);

    if (dailyTransactions.length === 0) {
      return (
        <View style={styles.emptyTransactionsContainer}>
          <View style={styles.dateInfoContainer}>
            <View style={styles.dateInfo}>
              <Text style={[styles.weekdayText, { color: themeColors.secondaryText }]}>
                {weekday}
              </Text>
              <Text style={[styles.dateText, { color: themeColors.text }]}>
                {day} {month}, {year}
              </Text>
              {showLunarCalendar && lunarDate && (
                <Text style={[styles.lunarDateText, { color: '#FF9800' }]}>
                  {lunarDateString}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.emptyStateContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons 
                name="calendar-outline" 
                size={60} 
                color={isDark ? '#454545' : '#E0E0E0'} 
              />
            </View>
            <Text style={[styles.emptyStateText, { color: themeColors.secondaryText }]}>
              Không có giao dịch nào vào ngày này
            </Text>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.transactionsContainer}>
        <View style={styles.dateInfoContainer}>
          <View style={styles.dateInfo}>
            <Text style={[styles.weekdayText, { color: themeColors.secondaryText }]}>
              {weekday}
            </Text>
            <Text style={[styles.dateText, { color: themeColors.text }]}>
              {day} {month}, {year}
            </Text>
            {showLunarCalendar && lunarDate && (
              <Text style={[styles.lunarDateText, { color: '#FF9800' }]}>
                {lunarDateString}
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.summaryContainer}>
          <View style={[
            styles.summaryCard, 
            { backgroundColor: isDark ? '#2A2A2A' : '#F5F5F5' }
          ]}>
            <View style={styles.summaryRowContainer}>
              {/* Thu nhập */}
              <View style={styles.summaryColumnItem}>
                <View style={[styles.summaryIconContainer, { backgroundColor: 'rgba(76, 175, 80, 0.15)' }]}>
                  <Ionicons name="arrow-down" size={20} color="#4CAF50" />
                </View>
                <Text style={[styles.summaryLabel, { color: themeColors.secondaryText }]}>
                  Thu nhập
                </Text>
                <Text style={[styles.summaryAmount, { color: '#4CAF50' }]} numberOfLines={1} ellipsizeMode="tail">
                  +{formatCurrency(income)}đ
                </Text>
              </View>
              
              {/* Đường phân cách dọc */}
              <View style={styles.verticalDivider} />
              
              {/* Chi tiêu */}
              <View style={styles.summaryColumnItem}>
                <View style={[styles.summaryIconContainer, { backgroundColor: 'rgba(255, 82, 82, 0.15)' }]}>
                  <Ionicons name="arrow-up" size={20} color="#FF5252" />
                </View>
                <Text style={[styles.summaryLabel, { color: themeColors.secondaryText }]}>
                  Chi tiêu
                </Text>
                <Text style={[styles.summaryAmount, { color: '#FF5252' }]} numberOfLines={1} ellipsizeMode="tail">
                  -{formatCurrency(expenses)}đ
                </Text>
              </View>
              
              {/* Đường phân cách dọc */}
              <View style={styles.verticalDivider} />
              
              {/* Chênh lệch */}
              <View style={styles.summaryColumnItem}>
                <View style={[
                  styles.summaryIconContainer, 
                  { backgroundColor: dailyTotal >= 0 
                    ? 'rgba(76, 175, 80, 0.15)' 
                    : 'rgba(255, 82, 82, 0.15)' 
                  }
                ]}>
                  <Ionicons 
                    name="wallet-outline"
                    size={20} 
                    color={dailyTotal >= 0 ? "#4CAF50" : "#FF5252"} 
                  />
                </View>
                <Text style={[styles.summaryLabel, { color: themeColors.secondaryText }]}>
                  Chênh lệch
                </Text>
                <Text style={[
                  styles.summaryAmount, 
                  { color: dailyTotal >= 0 ? '#4CAF50' : '#FF5252' }
                ]} numberOfLines={1} ellipsizeMode="tail">
                  {dailyTotal >= 0 ? '+' : '-'}{formatCurrency(Math.abs(dailyTotal))}đ
                </Text>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.transactionListContainer}>
          <Text style={[styles.transactionListTitle, { color: themeColors.text }]}>
            Danh sách giao dịch
          </Text>
          
          {dailyTransactions.map((item) => {
            // Lấy tên danh mục từ danh mục liên kết
            const categoryName = item.category?.name || 'Danh mục';
            const translatedCategoryName = translateCategoryName(categoryName);
            // Sử dụng icon từ danh mục liên kết nếu có
            const categoryIcon = item.category?.icon || getCategoryIcon(item.type === 'income' ? 'salary' : 'default');
            // Sử dụng màu từ danh mục liên kết nếu có
            const categoryColor = item.category?.color || (item.amount > 0 ? '#4CAF50' : '#FF5252');
            
            return (
              <TouchableOpacity 
                key={item.id}
                style={[
                  styles.transactionItem,
                  { backgroundColor: isDark ? '#2A2A2A' : '#FFFFFF' }
                ]}
                onPress={() => router.push(`/transaction/${item.id}`)}
              >
                <View style={styles.transactionLeft}>
                  <View style={[
                    styles.categoryIconContainer,
                    { 
                      backgroundColor: `${categoryColor}30`,
                    }
                  ]}>
                    <Ionicons
                      name={categoryIcon as any}
                      size={24}
                      color={categoryColor}
                    />
                  </View>
                  
                  <View style={styles.transactionInfo}>
                    <Text style={[styles.transactionNote, { color: themeColors.text }]}>
                      {item.description || translatedCategoryName}
                    </Text>
                    <Text style={[styles.categoryName, { color: themeColors.secondaryText }]}>
                      {translatedCategoryName}
                    </Text>
                  </View>
                </View>
                
                <Text style={[
                  styles.transactionAmount,
                  { color: item.amount > 0 ? "#4CAF50" : "#FF5252" }
                ]}>
                  {item.amount > 0 ? "+" : "-"}{formatCurrency(Math.abs(item.amount))} đ
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: "",
          headerShown: true,
          headerTransparent: true,
          headerLeft: () => (
            <TouchableOpacity
              onPress={() => router.back()}
              style={[
                styles.headerBackButton,
                { backgroundColor: isDark ? 'rgba(42, 42, 42, 0.8)' : 'rgba(255, 255, 255, 0.8)' }
              ]}
            >
              <Ionicons
                name="arrow-back"
                size={22}
                color={themeColors.text}
              />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerRight}>
              <BlurView 
                intensity={80} 
                tint={isDark ? "dark" : "light"}
                style={styles.lunarToggleContainer}
              >
                <Text style={{
                  color: themeColors.secondaryText,
                  marginRight: 8,
                  fontSize: 14,
                  fontWeight: '500'
                }}>
                  Lịch âm
                </Text>
                <Switch
                  trackColor={{ false: isDark ? '#3A3A3A' : '#D1D1D1', true: isDark ? '#0A84FF50' : '#0A84FF50' }}
                  thumbColor={showLunarCalendar ? '#0A84FF' : isDark ? '#909090' : '#F4F3F4'}
                  ios_backgroundColor={isDark ? '#3A3A3A' : '#D1D1D1'}
                  onValueChange={toggleLunarCalendar}
                  value={showLunarCalendar}
                  style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
                />
              </BlurView>
            </View>
          ),
        }}
      />
      <View
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Phần lịch */}
          <View style={[
            styles.calendarContainer, 
            { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' }
          ]}>
            {renderCalendarHeader()}
            {renderViewModeSelector()}
            {viewMode === "month" && renderMonthView()}
            {viewMode === "week" && renderWeekView()}
          </View>

          {/* Chi tiết giao dịch */}
          {renderTransactionDetails()}
        </ScrollView>

        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color={themeColors.primary} />
          </View>
        )}
      </View>
    </>
  );
}

// Các hàm tiện ích
function isSameDay(date1: Date, date2: Date) {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
}

function generateCalendarDays(monthDate: Date) {
  const year = monthDate.getFullYear();
  const month = monthDate.getMonth();

  // Ngày đầu tiên của tháng
  const firstDay = new Date(year, month, 1);
  // Ngày cuối cùng của tháng
  const lastDay = new Date(year, month + 1, 0);

  const days = [];

  // Thêm các ngày từ tháng trước
  const firstDayOfWeek = firstDay.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ...
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const prevMonthDay = new Date(year, month, -i);
    days.push(prevMonthDay);
  }

  // Thêm các ngày trong tháng hiện tại
  for (let i = 1; i <= lastDay.getDate(); i++) {
    days.push(new Date(year, month, i));
  }

  // Thêm các ngày từ tháng tiếp theo
  const remainingDays = 7 - (days.length % 7 || 7);
  for (let i = 1; i <= remainingDays; i++) {
    days.push(new Date(year, month + 1, i));
  }

  return days;
}

function getCategoryIcon(category: string) {
  const icons: Record<string, string> = {
    food: "restaurant-outline",
    shopping: "bag-outline",
    transport: "car-outline",
    bills: "document-text-outline",
    salary: "cash-outline",
    default: "grid-outline",
  };

  return icons[category] || icons.default;
}

function getCategoryName(category: string) {
  const names: Record<string, string> = {
    food: "Ăn uống",
    shopping: "Mua sắm",
    transport: "Di chuyển",
    bills: "Hóa đơn",
    salary: "Lương",
    default: "Danh mục",
  };

  return names[category] || names.default;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 80,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  calendarContainer: {
    margin: 16,
    marginTop: 100,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  headerBackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
    marginTop: 4,
  },
  headerRight: {
    marginRight: 16,
    marginTop: 4,
  },
  lunarToggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 18,
    overflow: 'hidden',
  },
  calendarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  monthYearContainer: {
    flexDirection: "column",
  },
  monthTitle: {
    fontSize: 24,
    fontWeight: "700",
  },
  yearTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginTop: 4,
  },
  monthNavigationButtons: {
    flexDirection: "row",
  },
  navButton: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 8,
  },
  viewModeContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  viewModeSelectorBackground: {
    flexDirection: "row",
    borderRadius: 16,
    padding: 4,
    justifyContent: "space-between",
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  viewModeText: {
    fontSize: 14,
    fontWeight: "600",
  },
  calendar: {
    paddingHorizontal: 12,
    paddingBottom: 16,
  },
  weekDayRow: {
    flexDirection: "row",
    marginBottom: 12,
    paddingVertical: 8,
  },
  weekDayCell: {
    flex: 1,
    alignItems: "center",
  },
  weekDayText: {
    fontSize: 13,
  },
  calendarRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  dayCell: {
    width: DAY_CELL_SIZE,
    height: DAY_CELL_SIZE,
    justifyContent: "center",
    alignItems: "center",
    margin: 4,
    position: 'relative',
  },
  dayCellContent: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    zIndex: 2,
  },
  selectedDayBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 8, // Thay đổi từ hình tròn sang hình vuông bo tròn 4 góc
    backgroundColor: '#007AFF30',
  },
  todayCell: {
    borderWidth: 1.5,
    borderColor: "#007AFF",
    borderRadius: 8, // Bo tròn 4 góc giống selectedDayBackground
  },
  selectedCell: {},
  otherMonthCell: {
    opacity: 0.4,
  },
  dayNumber: {
    fontSize: 16,
    fontWeight: "500",
  },
  lunarDayNumber: {
    fontSize: 11,
    marginTop: 2,
  },
  dayAmount: {
    fontSize: 9,
    fontWeight: '600',
    marginTop: 4,
  },
  transactionIndicator: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  specialDayMark: {
    position: 'absolute',
    bottom: 4,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FF9800',
  },
  weekViewContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
  },
  weekDayItem: {
    width: DAY_CELL_SIZE + 6,
    height: 90,
    borderRadius: 16,
    padding: 8,
    alignItems: 'center',
    position: 'relative',
  },
  weekDayContent: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    zIndex: 2,
  },
  selectedWeekDayBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 8, // Thay đổi thành hình vuông bo tròn 4 góc
    backgroundColor: '#007AFF30',
  },
  todayWeekCell: {
    borderWidth: 1.5,
    borderColor: "#007AFF",
    borderRadius: 8, // Bo tròn 4 góc giống selectedWeekDayBackground
  },
  selectedWeekCell: {},
  weekDayLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  weekDayNumber: {
    fontSize: 16,
    fontWeight: "600",
  },
  weekLunarDayNumber: {
    fontSize: 11,
    marginTop: 4,
  },
  weekDayAmount: {
    fontSize: 9,
    fontWeight: '600',
    marginTop: 4,
  },
  dayViewPlaceholder: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  dayViewText: {
    fontSize: 18,
    fontWeight: "600",
  },
  transactionsContainer: {
    paddingHorizontal: 16,
  },
  dateInfoContainer: {
    marginBottom: 16,
  },
  dateInfo: {
    marginLeft: 4,
  },
  weekdayText: {
    fontSize: 14,
  },
  dateText: {
    fontSize: 26,
    fontWeight: "700",
    marginTop: 2,
  },
  lunarDateText: {
    fontSize: 14,
    marginTop: 4,
  },
  summaryContainer: {
    marginBottom: 24,
  },
  summaryCard: {
    borderRadius: 16,
    padding: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  summaryRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
  },
  summaryColumnItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  summaryIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 3,
    textAlign: 'center',
  },
  summaryAmount: {
    fontSize: 14,
    fontWeight: "700",
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  verticalDivider: {
    width: 1,
    alignSelf: 'stretch',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  summaryDivider: {
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginVertical: 8,
  },
  transactionListContainer: {
    marginBottom: 24,
  },
  transactionListTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 16,
    marginLeft: 4,
  },
  transactionListContent: {
    paddingBottom: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionNote: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 13,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "700",
  },
  emptyTransactionsContainer: {
    paddingHorizontal: 16,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 16,
    marginBottom: 24,
  },
  addTransactionButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 30,
    alignSelf: 'center',
  },
  addTransactionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});