// File: app/finance-calculator.tsx
// File này chứa trang máy tính tài chính và chuyển đổi tiền tệ
// File này liên quan đến: app/utils/calc-utils.tsx, app/utils/currency-api.tsx, app/(tabs)/more.tsx

import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  Keyboard,
  ActivityIndicator,
  Switch,
  Alert,
  RefreshControl,
} from "react-native";
import { Text, View } from "@/components/Themed";
import { useTheme } from "@/context/ThemeContext";
import { useLocalization } from "@/context/LocalizationContext";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { TextInput, ScrollView } from "react-native";
import {
  calculateCompoundInterest,
  calculateLoanEMI,
  calculateSimpleInterest,
  formatCurrency,
  calculateAmortizationSchedule,
} from "@/app/utils/calc-utils";
import {
  getRates,
  getExchangeRate,
  convertCurrency,
  popularCurrencies,
  popularCurrencyPairs,
} from "@/app/utils/currency-api";

// Định nghĩa các tab trong máy tính tài chính
type CalculatorTab = "savings" | "loan" | "currency";
type TimeUnit = "years" | "months";

export default function FinanceCalculatorScreen() {
  // Sử dụng theme và ngôn ngữ
  const { isDark, themeColors, getCardStyle } = useTheme();
  const { t } = useLocalization();

  // State cho tab đang hiển thị
  const [activeTab, setActiveTab] = useState<CalculatorTab>("currency");

  // State cho máy tính tiết kiệm
  const [savingsPrincipal, setSavingsPrincipal] = useState("");
  const [savingsRate, setSavingsRate] = useState("");
  const [savingsTime, setSavingsTime] = useState("");
  const [savingsTimeUnit, setSavingsTimeUnit] = useState<TimeUnit>("years");
  const [isCompound, setIsCompound] = useState(true);
  const [compoundFrequency, setCompoundFrequency] = useState("12"); // Tháng
  const [savingsResult, setSavingsResult] = useState<string>("");

  // State cho máy tính khoản vay
  const [loanAmount, setLoanAmount] = useState("");
  const [loanRate, setLoanRate] = useState("");
  const [loanTime, setLoanTime] = useState("");
  const [loanTimeUnit, setLoanTimeUnit] = useState<TimeUnit>("years");
  const [loanResult, setLoanResult] = useState<string>("");
  const [showSchedule, setShowSchedule] = useState(false);
  const [paymentSchedule, setPaymentSchedule] = useState<Array<any>>([]);

  // State cho chuyển đổi tiền tệ
  const [amount, setAmount] = useState("");
  const [fromCurrency, setFromCurrency] = useState("USD");
  const [toCurrency, setToCurrency] = useState("VND");
  const [conversionResult, setConversionResult] = useState<string>("");
  const [isLoadingRates, setIsLoadingRates] = useState(false);
  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({});
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(new Date());

  // Danh sách các tiền tệ phổ biến cho selector
  const currencyOptions = popularCurrencies;

  // Hàm tính toán cho tiết kiệm
  const calculateSavings = () => {
    Keyboard.dismiss();
    if (!savingsPrincipal || !savingsRate || !savingsTime) {
      Alert.alert("Thông báo", "Vui lòng nhập đầy đủ thông tin.");
      return;
    }

    const principal = parseFloat(savingsPrincipal.replace(/,/g, ""));
    const rate = parseFloat(savingsRate);
    const timeInput = parseFloat(savingsTime);
    const frequency = parseInt(compoundFrequency);

    if (isNaN(principal) || isNaN(rate) || isNaN(timeInput)) {
      Alert.alert("Thông báo", "Vui lòng nhập số hợp lệ.");
      return;
    }

    // Chuyển đổi thời gian về năm
    const timeInYears = savingsTimeUnit === "months" ? timeInput / 12 : timeInput;

    let result: number;
    let interest: number;

    if (isCompound) {
      result = calculateCompoundInterest(principal, rate, timeInYears, frequency);
      interest = result - principal;
    } else {
      interest = calculateSimpleInterest(principal, rate, timeInYears);
      result = principal + interest;
    }

    const timeLabel = savingsTimeUnit === "months" ? `${timeInput} tháng` : `${timeInput} năm`;
    setSavingsResult(
      `Thời gian: ${timeLabel}\nTiền gốc: ${formatCurrency(principal)}\nTiền lãi: ${formatCurrency(interest)}\nTổng tiền: ${formatCurrency(result)}`
    );
  };

  // Hàm tính toán cho khoản vay
  const calculateLoan = () => {
    Keyboard.dismiss();
    if (!loanAmount || !loanRate || !loanTime) {
      Alert.alert("Thông báo", "Vui lòng nhập đầy đủ thông tin.");
      return;
    }

    const principal = parseFloat(loanAmount.replace(/,/g, ""));
    const rate = parseFloat(loanRate);
    const timeInput = parseFloat(loanTime);

    if (isNaN(principal) || isNaN(rate) || isNaN(timeInput)) {
      Alert.alert("Thông báo", "Vui lòng nhập số hợp lệ.");
      return;
    }

    // Chuyển đổi thời gian về năm
    const timeInYears = loanTimeUnit === "months" ? timeInput / 12 : timeInput;
    const timeInMonths = loanTimeUnit === "months" ? timeInput : timeInput * 12;

    const monthlyPayment = calculateLoanEMI(principal, rate, timeInYears);
    const totalPayment = monthlyPayment * timeInMonths;
    const totalInterest = totalPayment - principal;

    const timeLabel = loanTimeUnit === "months" ? `${timeInput} tháng` : `${timeInput} năm`;
    setLoanResult(
      `Thời gian: ${timeLabel}\nSố tiền hàng tháng: ${formatCurrency(monthlyPayment)}\nTổng tiền lãi: ${formatCurrency(totalInterest)}\nTổng tiền phải trả: ${formatCurrency(totalPayment)}`
    );

    // Tính lịch trả nợ
    const schedule = calculateAmortizationSchedule(principal, rate, timeInYears);
    setPaymentSchedule(schedule);
  };

  // Hàm chuyển đổi tiền tệ
  const convertCurrencyHandler = async () => {
    Keyboard.dismiss();
    if (!amount) {
      Alert.alert("Thông báo", "Vui lòng nhập số tiền cần chuyển đổi.");
      return;
    }

    const numAmount = parseFloat(amount.replace(/,/g, ""));
    if (isNaN(numAmount)) {
      Alert.alert("Thông báo", "Vui lòng nhập số hợp lệ.");
      return;
    }

    setIsLoadingRates(true);
    try {
      // Chuyển đổi tiền tệ
      const result = await convertCurrency(numAmount, fromCurrency, toCurrency);
      
      // Định dạng kết quả đẹp hơn
      const formattedAmount = formatCurrency(numAmount, fromCurrency);
      const formattedResult = formatCurrency(result, toCurrency);
      
      setConversionResult(`${formattedAmount} = ${formattedResult}`);
      setLastUpdated(new Date());
    } catch (error) {
      console.error("Lỗi khi chuyển đổi tiền tệ:", error);
      Alert.alert(
        "Lỗi",
        `Không thể chuyển đổi từ ${fromCurrency} sang ${toCurrency}. Vui lòng thử lại sau.`
      );
    } finally {
      setIsLoadingRates(false);
    }
  };

  // Hàm làm mới tỷ giá hối đoái
  const refreshExchangeRates = async () => {
    setRefreshing(true);
    try {
      const rates = await getRates();
      setExchangeRates(rates);
      setLastUpdated(new Date());
      
      // Nếu đã có số tiền, tự động cập nhật kết quả chuyển đổi
      if (amount) {
        convertCurrencyHandler();
      }
    } catch (error) {
      console.error("Lỗi khi làm mới tỷ giá:", error);
      Alert.alert(
        "Lỗi",
        "Không thể cập nhật tỷ giá hối đoái. Vui lòng thử lại sau."
      );
    } finally {
      setRefreshing(false);
    }
  };

  // Hiển thị tỷ giá khi thay đổi tiền tệ
  useEffect(() => {
    if (activeTab === "currency" && fromCurrency && toCurrency) {
      // Chỉ hiển thị kết quả nếu người dùng đã nhập số tiền và đã tải tỷ giá
      if (amount && Object.keys(exchangeRates).length > 0) {
        convertCurrencyHandler();
      }
    }
  }, [fromCurrency, toCurrency, exchangeRates]);

  // Tải tỷ giá khi mở tab currency
  useEffect(() => {
    if (activeTab === "currency" && Object.keys(exchangeRates).length === 0) {
      refreshExchangeRates();
    }
  }, [activeTab]);

  // Render tab tiết kiệm
  const renderSavingsCalculator = () => (
    <View style={[styles.tabContent, { backgroundColor: "transparent" }]}>
      <View style={[getCardStyle("medium"), styles.card]}>
        <Text style={[styles.cardTitle, { color: themeColors.text }]}>
          Tính tiền gửi tiết kiệm
        </Text>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Số tiền gốc (VND)</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={savingsPrincipal}
            onChangeText={(text) => {
              // Định dạng số có dấu phẩy ngăn cách hàng nghìn
              const formattedText = text
                .replace(/,/g, "")
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              setSavingsPrincipal(formattedText);
            }}
            placeholder="Nhập số tiền gốc"
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Lãi suất (%/năm)</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={savingsRate}
            onChangeText={setSavingsRate}
            placeholder="Nhập lãi suất"
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Thời gian</Text>
          <View
            style={[
              styles.segmentedControl,
              { backgroundColor: themeColors.cardBackground, marginTop: 8, marginBottom: 8 },
            ]}
          >
            <TouchableOpacity
              style={[
                styles.segmentButton,
                savingsTimeUnit === "years" && {
                  backgroundColor: themeColors.primary,
                },
              ]}
              onPress={() => setSavingsTimeUnit("years")}
            >
              <Text
                style={{
                  color:
                    savingsTimeUnit === "years" ? "#fff" : themeColors.text,
                }}
              >
                Năm
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.segmentButton,
                savingsTimeUnit === "months" && {
                  backgroundColor: themeColors.primary,
                },
              ]}
              onPress={() => setSavingsTimeUnit("months")}
            >
              <Text
                style={{
                  color:
                    savingsTimeUnit === "months" ? "#fff" : themeColors.text,
                }}
              >
                Tháng
              </Text>
            </TouchableOpacity>
          </View>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={savingsTime}
            onChangeText={setSavingsTime}
            placeholder={`Nhập thời gian (${savingsTimeUnit === "years" ? "năm" : "tháng"})`}
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <View
          style={[
            styles.switchContainer,
            { backgroundColor: "transparent", marginBottom: 10 },
          ]}
        >
          <Text style={{ color: themeColors.text }}>Lãi kép</Text>
          <Switch
            value={isCompound}
            onValueChange={setIsCompound}
            trackColor={{ false: "#767577", true: themeColors.primary }}
            thumbColor={isDark ? "#f4f3f4" : "#f4f3f4"}
          />
        </View>

        {isCompound && (
          <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
            <Text style={{ color: themeColors.text }}>Tần suất gộp lãi</Text>
            <View
              style={[
                styles.segmentedControl,
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  compoundFrequency === "1" && {
                    backgroundColor: themeColors.primary,
                  },
                ]}
                onPress={() => setCompoundFrequency("1")}
              >
                <Text
                  style={{
                    color:
                      compoundFrequency === "1" ? "#fff" : themeColors.text,
                  }}
                >
                  Năm
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  compoundFrequency === "4" && {
                    backgroundColor: themeColors.primary,
                  },
                ]}
                onPress={() => setCompoundFrequency("4")}
              >
                <Text
                  style={{
                    color:
                      compoundFrequency === "4" ? "#fff" : themeColors.text,
                  }}
                >
                  Quý
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.segmentButton,
                  compoundFrequency === "12" && {
                    backgroundColor: themeColors.primary,
                  },
                ]}
                onPress={() => setCompoundFrequency("12")}
              >
                <Text
                  style={{
                    color:
                      compoundFrequency === "12" ? "#fff" : themeColors.text,
                  }}
                >
                  Tháng
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <TouchableOpacity
          style={[styles.button, { backgroundColor: themeColors.primary }]}
          onPress={calculateSavings}
        >
          <Text style={styles.buttonText}>Tính toán</Text>
        </TouchableOpacity>

        {savingsResult ? (
          <View
            style={[
              styles.resultContainer,
              {
                backgroundColor: themeColors.cardBackground,
                borderColor: themeColors.border,
              },
            ]}
          >
            <Text
              style={[styles.resultText, { color: themeColors.text }]}
            >
              {savingsResult}
            </Text>
          </View>
        ) : null}
      </View>
    </View>
  );

  // Render tab khoản vay
  const renderLoanCalculator = () => (
    <View style={[styles.tabContent, { backgroundColor: "transparent" }]}>
      <View style={[getCardStyle("medium"), styles.card]}>
        <Text style={[styles.cardTitle, { color: themeColors.text }]}>
          Tính khoản vay
        </Text>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Số tiền vay (VND)</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={loanAmount}
            onChangeText={(text) => {
              // Định dạng số có dấu phẩy ngăn cách hàng nghìn
              const formattedText = text
                .replace(/,/g, "")
                .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              setLoanAmount(formattedText);
            }}
            placeholder="Nhập số tiền vay"
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Lãi suất (%/năm)</Text>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={loanRate}
            onChangeText={setLoanRate}
            placeholder="Nhập lãi suất"
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
          <Text style={{ color: themeColors.text }}>Thời gian</Text>
          <View
            style={[
              styles.segmentedControl,
              { backgroundColor: themeColors.cardBackground, marginTop: 8, marginBottom: 8 },
            ]}
          >
            <TouchableOpacity
              style={[
                styles.segmentButton,
                loanTimeUnit === "years" && {
                  backgroundColor: themeColors.primary,
                },
              ]}
              onPress={() => setLoanTimeUnit("years")}
            >
              <Text
                style={{
                  color:
                    loanTimeUnit === "years" ? "#fff" : themeColors.text,
                }}
              >
                Năm
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.segmentButton,
                loanTimeUnit === "months" && {
                  backgroundColor: themeColors.primary,
                },
              ]}
              onPress={() => setLoanTimeUnit("months")}
            >
              <Text
                style={{
                  color:
                    loanTimeUnit === "months" ? "#fff" : themeColors.text,
                }}
              >
                Tháng
              </Text>
            </TouchableOpacity>
          </View>
          <TextInput
            style={[
              styles.input,
              {
                color: themeColors.text,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
              },
            ]}
            keyboardType="numeric"
            value={loanTime}
            onChangeText={setLoanTime}
            placeholder={`Nhập thời gian (${loanTimeUnit === "years" ? "năm" : "tháng"})`}
            placeholderTextColor={isDark ? "#666" : "#999"}
          />
        </View>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: themeColors.primary }]}
          onPress={calculateLoan}
        >
          <Text style={styles.buttonText}>Tính toán</Text>
        </TouchableOpacity>

        {loanResult ? (
          <View
            style={[
              styles.resultContainer,
              {
                backgroundColor: themeColors.cardBackground,
                borderColor: themeColors.border,
              },
            ]}
          >
            <Text
              style={[styles.resultText, { color: themeColors.text }]}
            >
              {loanResult}
            </Text>

            <TouchableOpacity
              style={[
                styles.toggleButton,
                { backgroundColor: themeColors.primary },
              ]}
              onPress={() => setShowSchedule(!showSchedule)}
            >
              <Text style={styles.buttonText}>
                {showSchedule ? "Ẩn lịch trả nợ" : "Xem lịch trả nợ"}
              </Text>
            </TouchableOpacity>
          </View>
        ) : null}

        {showSchedule && paymentSchedule.length > 0 && (
          <View
            style={[
              styles.scheduleContainer,
              {
                backgroundColor: themeColors.cardBackground,
                borderColor: themeColors.border,
              },
            ]}
          >
            <View
              style={[
                styles.scheduleHeader,
                { backgroundColor: themeColors.primary },
              ]}
            >
              <Text style={[styles.headerCell, { color: "#fff" }]}>Tháng</Text>
              <Text style={[styles.headerCell, { color: "#fff" }]}>
                Khoản trả
              </Text>
              <Text style={[styles.headerCell, { color: "#fff" }]}>
                Tiền gốc
              </Text>
              <Text style={[styles.headerCell, { color: "#fff" }]}>
                Tiền lãi
              </Text>
              <Text style={[styles.headerCell, { color: "#fff" }]}>
                Còn lại
              </Text>
            </View>
            <ScrollView style={{ maxHeight: 200 }}>
              {paymentSchedule.map((item, index) => (
                <View
                  key={index}
                  style={[
                    styles.scheduleRow,
                    {
                      backgroundColor:
                        index % 2 === 0
                          ? themeColors.cardBackground
                          : isDark
                          ? "#1A1A1A"
                          : "#F5F9FF",
                    },
                  ]}
                >
                  <Text style={[styles.cell, { color: themeColors.text }]}>
                    {item.month}
                  </Text>
                  <Text style={[styles.cell, { color: themeColors.text }]}>
                    {formatCurrency(item.payment).split(" ")[0]}
                  </Text>
                  <Text style={[styles.cell, { color: themeColors.text }]}>
                    {formatCurrency(item.principal).split(" ")[0]}
                  </Text>
                  <Text style={[styles.cell, { color: themeColors.text }]}>
                    {formatCurrency(item.interest).split(" ")[0]}
                  </Text>
                  <Text style={[styles.cell, { color: themeColors.text }]}>
                    {formatCurrency(item.balance).split(" ")[0]}
                  </Text>
                </View>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
    </View>
  );

  // Render tab chuyển đổi tiền tệ
  const renderCurrencyConverter = () => (
    <View style={[styles.tabContent, { backgroundColor: "transparent" }]}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refreshExchangeRates}
            colors={[themeColors.primary]}
            tintColor={themeColors.primary}
          />
        }
      >
        <View style={[getCardStyle("medium"), styles.card]}>
          <Text style={[styles.cardTitle, { color: themeColors.text }]}>
            Chuyển đổi tiền tệ
            {lastUpdated && (
              <Text
                style={[
                  styles.updateText,
                  { color: themeColors.secondaryText, fontSize: 12 },
                ]}
              >
                {" "}(cập nhật lúc: {lastUpdated.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })})
              </Text>
            )}
          </Text>

          <View style={[styles.inputContainer, { backgroundColor: "transparent" }]}>
            <Text style={{ color: themeColors.text }}>Số tiền</Text>
            <TextInput
              style={[
                styles.input,
                {
                  color: themeColors.text,
                  borderColor: themeColors.border,
                  backgroundColor: themeColors.cardBackground,
                },
              ]}
              keyboardType="numeric"
              value={amount}
              onChangeText={(text) => {
                // Định dạng số có dấu phẩy ngăn cách hàng nghìn
                const formattedText = text
                  .replace(/,/g, "")
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                setAmount(formattedText);
              }}
              placeholder="Nhập số tiền"
              placeholderTextColor={isDark ? "#666" : "#999"}
            />
          </View>

          <View style={[styles.currencySelectors, { backgroundColor: "transparent" }]}>
            <View
              style={[
                styles.currencySelector,
                { backgroundColor: "transparent" },
              ]}
            >
              <Text style={{ color: themeColors.text }}>Từ</Text>
              <View style={styles.currencyScroll}>
                {currencyOptions.map((currency) => (
                  <TouchableOpacity
                    key={currency.code}
                    style={[
                      styles.currencyOption,
                      {
                        backgroundColor:
                          fromCurrency === currency.code
                            ? themeColors.primary
                            : themeColors.cardBackground,
                        borderColor: themeColors.border,
                      },
                    ]}
                    onPress={() => setFromCurrency(currency.code)}
                  >
                    <Text
                      style={{
                        color:
                          fromCurrency === currency.code
                            ? "#fff"
                            : themeColors.text,
                      }}
                    >
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.swapButton,
                { backgroundColor: themeColors.primary },
              ]}
              onPress={() => {
                const temp = fromCurrency;
                setFromCurrency(toCurrency);
                setToCurrency(temp);
              }}
            >
              <Ionicons name="swap-vertical" size={24} color="#fff" />
            </TouchableOpacity>

            <View
              style={[
                styles.currencySelector,
                { backgroundColor: "transparent" },
              ]}
            >
              <Text style={{ color: themeColors.text }}>Đến</Text>
              <View style={styles.currencyScroll}>
                {currencyOptions.map((currency) => (
                  <TouchableOpacity
                    key={currency.code}
                    style={[
                      styles.currencyOption,
                      {
                        backgroundColor:
                          toCurrency === currency.code
                            ? themeColors.primary
                            : themeColors.cardBackground,
                        borderColor: themeColors.border,
                      },
                    ]}
                    onPress={() => setToCurrency(currency.code)}
                  >
                    <Text
                      style={{
                        color:
                          toCurrency === currency.code
                            ? "#fff"
                            : themeColors.text,
                      }}
                    >
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.button,
              { backgroundColor: themeColors.primary },
              isLoadingRates && { opacity: 0.7 },
            ]}
            onPress={convertCurrencyHandler}
            disabled={isLoadingRates}
          >
            {isLoadingRates ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Chuyển đổi</Text>
            )}
          </TouchableOpacity>

          {conversionResult ? (
            <View
              style={[
                styles.resultContainer,
                {
                  backgroundColor: themeColors.cardBackground,
                  borderColor: themeColors.border,
                },
              ]}
            >
              <Text
                style={[
                  styles.resultText,
                  {
                    color: themeColors.text,
                    fontSize: 18,
                    textAlign: "center",
                  },
                ]}
              >
                {conversionResult}
              </Text>
            </View>
          ) : null}
        </View>

        {/* Hiển thị tỷ giá phổ biến */}
        <View style={[getCardStyle("medium"), styles.card, { marginTop: 16 }]}>
          <Text style={[styles.cardTitle, { color: themeColors.text }]}>
            Tỷ giá phổ biến
          </Text>
          {isLoadingRates ? (
            <ActivityIndicator color={themeColors.primary} />
          ) : (
            popularCurrencyPairs.map((pair, index) => {
              return (
                <View
                  key={index}
                  style={[
                    styles.rateItem,
                    {
                      backgroundColor:
                        index % 2 === 0
                          ? themeColors.cardBackground
                          : isDark
                          ? "#1A1A1A"
                          : "#F5F9FF",
                      borderColor: themeColors.border,
                    },
                  ]}
                >
                  <Text style={{ color: themeColors.text }}>{pair.name}</Text>
                  <TouchableOpacity
                    onPress={() => {
                      setFromCurrency(pair.from);
                      setToCurrency(pair.to);
                    }}
                  >
                    <Text
                      style={{
                        color: themeColors.primary,
                        fontWeight: "bold",
                      }}
                    >
                      {exchangeRates && exchangeRates[pair.to] && exchangeRates[pair.from]
                        ? (exchangeRates[pair.to] / exchangeRates[pair.from]).toFixed(4)
                        : "---"}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })
          )}
        </View>
      </ScrollView>
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      <View style={[styles.header, { backgroundColor: "transparent" }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Máy tính tài chính
        </Text>
        <View style={{ width: 24 }} />
      </View>

      <View
        style={[
          styles.tabBar,
          {
            backgroundColor: isDark
              ? themeColors.cardBackground
              : themeColors.background,
            borderColor: themeColors.border,
          },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "currency" && {
              borderBottomColor: themeColors.primary,
              borderBottomWidth: 2,
            },
          ]}
          onPress={() => setActiveTab("currency")}
        >
          <Ionicons
            name="swap-horizontal-outline"
            size={24}
            color={
              activeTab === "currency" ? themeColors.primary : themeColors.text
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === "currency"
                    ? themeColors.primary
                    : themeColors.text,
              },
            ]}
          >
            Tiền tệ
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "savings" && {
              borderBottomColor: themeColors.primary,
              borderBottomWidth: 2,
            },
          ]}
          onPress={() => setActiveTab("savings")}
        >
          <Ionicons
            name="calculator-outline"
            size={24}
            color={
              activeTab === "savings" ? themeColors.primary : themeColors.text
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === "savings"
                    ? themeColors.primary
                    : themeColors.text,
              },
            ]}
          >
            Tiết kiệm
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "loan" && {
              borderBottomColor: themeColors.primary,
              borderBottomWidth: 2,
            },
          ]}
          onPress={() => setActiveTab("loan")}
        >
          <Ionicons
            name="cash-outline"
            size={24}
            color={
              activeTab === "loan" ? themeColors.primary : themeColors.text
            }
          />
          <Text
            style={[
              styles.tabText,
              {
                color:
                  activeTab === "loan" ? themeColors.primary : themeColors.text,
              },
            ]}
          >
            Khoản vay
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        {activeTab === "savings" && renderSavingsCalculator()}
        {activeTab === "loan" && renderLoanCalculator()}
        {activeTab === "currency" && renderCurrencyConverter()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
  },
  tabBar: {
    flexDirection: "row",
    justifyContent: "space-around",
    borderBottomWidth: 1,
    marginHorizontal: 16,
    marginBottom: 10,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  tabText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: "500",
  },
  tabContent: {
    padding: 16,
  },
  card: {
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  resultContainer: {
    marginTop: 16,
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
  },
  resultText: {
    fontSize: 16,
    lineHeight: 24,
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  segmentedControl: {
    flexDirection: "row",
    borderRadius: 8,
    marginTop: 8,
    overflow: "hidden",
  },
  segmentButton: {
    flex: 1,
    padding: 10,
    alignItems: "center",
  },
  currencySelectors: {
    marginBottom: 16,
  },
  currencySelector: {
    marginBottom: 8,
  },
  currencyScroll: {
    flexDirection: "row",
    marginTop: 8,
    flexWrap: "wrap",
  },
  currencyOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  swapButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    marginVertical: 8,
  },
  updateText: {
    fontSize: 12,
    marginBottom: 16,
    textAlign: "right",
  },
  rateItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  toggleButton: {
    height: 40,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
  },
  scheduleContainer: {
    marginTop: 16,
    borderWidth: 1,
    borderRadius: 8,
  },
  scheduleHeader: {
    flexDirection: "row",
    paddingVertical: 8,
  },
  headerCell: {
    flex: 1,
    fontWeight: "bold",
    textAlign: "center",
    fontSize: 12,
  },
  scheduleRow: {
    flexDirection: "row",
    paddingVertical: 8,
  },
  cell: {
    flex: 1,
    textAlign: "center",
    fontSize: 12,
  },
});