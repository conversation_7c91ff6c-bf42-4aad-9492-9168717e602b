// File: app/(more)/upgrade-premium.tsx
// File này liên quan đến: app/(tabs)/settings.tsx, context/ThemeContext.tsx, lib/services/iap-service.ts

import { useTheme } from "@/context/ThemeContext";
import { affiliateService } from "@/lib/services/affiliate-service";
import {
  getSubscriptions,
  initIAP,
  requestSubscription,
} from "@/lib/services/iap-service";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const { width, height } = Dimensions.get("window");

// Danh sách tính năng Premium
const PREMIUM_FEATURES = [
  {
    icon: "remove-circle-outline",
    title: "Không quảng cáo",
    description: "Trải nghiệm hoàn toàn không bị gián đoạn",
    color: "#FF6B6B",
  },
  {
    icon: "analytics-outline",
    title: "Báo cáo chi tiết",
    description: "Phân tích chi tiêu và thu nhập nâng cao",
    color: "#4ECDC4",
  },
  {
    icon: "cloud-upload-outline",
    title: "Sao lưu đám mây",
    description: "Đồng bộ dữ liệu trên tất cả thiết bị",
    color: "#45B7D1",
  },
  {
    icon: "notifications-outline",
    title: "Thông báo thông minh",
    description: "Nhắc nhở chi tiêu và mục tiêu tiết kiệm",
    color: "#96CEB4",
  },
  {
    icon: "shield-checkmark-outline",
    title: "Bảo mật nâng cao",
    description: "Mã hóa dữ liệu và xác thực sinh trắc học",
    color: "#FFEAA7",
  },
  {
    icon: "trending-up-outline",
    title: "Dự đoán xu hướng",
    description: "AI phân tích và dự báo chi tiêu tương lai",
    color: "#DDA0DD",
  },
];

export default function UpgradePremiumScreen() {
  const { themeColors, isDark, getCardStyle, getShadowStyle } = useTheme();
  const { setPremiumStatus } = usePremium();
  const [products, setProducts] = useState<SubscriptionProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<string>("premium_yearly");
  const [purchasing, setPurchasing] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeIAP();
    loadReferralCode();
    return () => {
      // RNIap.endConnection() sẽ được gọi khi component unmount
    };
  }, []);

  const loadReferralCode = async () => {
    try {
      // Kiểm tra xem có referral code được lưu từ deep link không
      const savedReferralCode = await AsyncStorage.getItem(
        "pending_referral_code"
      );
      if (savedReferralCode) {
        setReferralCode(savedReferralCode);
        console.log("Found referral code:", savedReferralCode);
      }
    } catch (error) {
      console.error("Error loading referral code:", error);
    }
  };

  const initializeIAP = async () => {
    try {
      setError(null);
      console.log("🚀 Starting IAP initialization...");

      const initResult = await initIAP();
      if (!initResult) {
        throw new Error("Failed to initialize IAP connection");
      }

      console.log("📦 Fetching subscription products...");
      const subs = await getSubscriptions();
      console.log("✅ Products fetched:", subs);

      setProducts(subs);

      if (subs.length === 0) {
        setError(
          "Không thể tải thông tin gói Premium. Vui lòng kiểm tra kết nối mạng và thử lại."
        );
      }
    } catch (error: any) {
      console.error("❌ Error initializing IAP:", error);
      const errorMessage =
        error.message || "Không thể tải thông tin gói Premium";
      setError(errorMessage);

      Alert.alert(
        "Lỗi",
        "Không thể tải thông tin gói Premium. Vui lòng thử lại sau.",
        [{ text: "Thử lại", onPress: initializeIAP }, { text: "OK" }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async (sku: string) => {
    if (purchasing) return;

    try {
      setPurchasing(true);

      // Gọi IAP service để mua subscription
      await requestSubscription(sku);

      // Lấy thông tin sản phẩm để tracking
      const product = products.find((p) => p.productId === sku);
      const purchaseAmount = parseFloat(product?.price || "0");

      // Set premium status
      await setPremiumStatus(true);
      console.log("✅ Premium status updated");

      // Track conversion nếu có referral code
      if (referralCode) {
        try {
          // Use trackPremiumPurchase instead of trackConversion
          await affiliateService.trackPremiumPurchase({
            userId: "current_user_id", // You should get this from auth context
            affiliateCode: referralCode,
            packageType: sku,
            amount: purchaseAmount,
            purchaseId: `${sku}_${Date.now()}`,
          });

          // Xóa referral code sau khi đã track thành công
          await AsyncStorage.removeItem("pending_referral_code");
          console.log("Affiliate conversion tracked successfully");
        } catch (affiliateError) {
          console.error("Error tracking affiliate conversion:", affiliateError);
          // Không làm gián đoạn quá trình mua hàng nếu tracking lỗi
        }
      }

      setShowSuccessModal(true);
    } catch (error: any) {
      console.error("Purchase error:", error);
      if (error.code !== "E_USER_CANCELLED") {
        Alert.alert(
          "Lỗi thanh toán",
          "Không thể hoàn tất thanh toán. Vui lòng thử lại sau.",
          [{ text: "OK" }]
        );
      }
    } finally {
      setPurchasing(false);
    }
  };

  const formatPrice = (price: string) => {
    return price || "Đang tải...";
  };

  const getMonthlyPrice = (yearlyPrice: string, yearlyProduct: any) => {
    if (!yearlyPrice || !yearlyProduct) return "Đang tải...";

    // Tính giá theo tháng từ gói năm
    const numericPrice = parseFloat(yearlyPrice.replace(/[^0-9.]/g, ""));
    const monthlyPrice = (numericPrice / 12).toFixed(0);
    return `${monthlyPrice}.000đ`;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        onPress={() => router.back()}
        style={[
          styles.backButton,
          {
            backgroundColor: themeColors.iconBackground,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="arrow-back" size={24} color={themeColors.text} />
      </TouchableOpacity>
    </View>
  );

  const renderHeroSection = () => (
    <View style={styles.heroSection}>
      <LinearGradient
        colors={
          isDark ? ["#1a1a2e", "#16213e", "#0f3460"] : ["#667eea", "#764ba2"]
        }
        style={styles.heroGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.heroOverlay}>
          <View style={styles.heroContent}>
            <View style={styles.premiumBadge}>
              <Ionicons name="diamond" size={24} color="#FFD700" />
              <Text style={styles.badgeText}>PREMIUM</Text>
            </View>
            <Text style={styles.heroTitle}>AI Money Premium</Text>
            <Text style={styles.heroSubtitle}>
              Trải nghiệm không giới hạn với tất cả tính năng cao cấp
            </Text>
            <View style={styles.featuresHighlight}>
              <View style={styles.highlightItem}>
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                <Text style={styles.highlightText}>Không quảng cáo</Text>
              </View>
              <View style={styles.highlightItem}>
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                <Text style={styles.highlightText}>Báo cáo chi tiết</Text>
              </View>
              <View style={styles.highlightItem}>
                <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
                <Text style={styles.highlightText}>Sao lưu đám mây</Text>
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>
    </View>
  );

  const renderFeatureItem = ({ item }: { item: any }) => (
    <View
      style={[
        styles.featureItem,
        getCardStyle("low"),
        { backgroundColor: themeColors.cardBackground },
      ]}
    >
      <View
        style={[
          styles.featureIcon,
          {
            backgroundColor: `${item.color}20`,
          },
        ]}
      >
        <Ionicons name={item.icon} size={28} color={item.color} />
      </View>
      <View style={styles.featureContent}>
        <Text style={[styles.featureTitle, { color: themeColors.text }]}>
          {item.title}
        </Text>
        <Text
          style={[
            styles.featureDescription,
            { color: themeColors.secondaryText },
          ]}
        >
          {item.description}
        </Text>
      </View>
    </View>
  );

  const renderPricingPlans = () => {
    const yearlyProduct = products.find(
      (p) => p.productId === "premium_yearly"
    );
    const monthlyProduct = products.find(
      (p) => p.productId === "premium_monthly"
    );

    return (
      <View style={styles.pricingSection}>
        <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
          Chọn gói phù hợp
        </Text>

        {/* Yearly Plan */}
        <TouchableOpacity
          style={[
            styles.pricingCard,
            getCardStyle("medium"),
            {
              backgroundColor: themeColors.cardBackground,
              borderWidth: selectedPlan === "premium_yearly" ? 2 : 1,
              borderColor:
                selectedPlan === "premium_yearly"
                  ? "#FFD700"
                  : themeColors.border,
            },
          ]}
          onPress={() => setSelectedPlan("premium_yearly")}
        >
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>PHỔ BIẾN NHẤT</Text>
          </View>

          <View style={styles.pricingHeader}>
            <Text style={[styles.planTitle, { color: themeColors.text }]}>
              Premium Năm
            </Text>
            <View style={styles.priceContainer}>
              <Text style={[styles.price, { color: themeColors.primary }]}>
                {yearlyProduct
                  ? formatPrice(yearlyProduct.localizedPrice)
                  : "Đang tải..."}
              </Text>
              <Text
                style={[
                  styles.priceSubtext,
                  { color: themeColors.secondaryText },
                ]}
              >
                {yearlyProduct
                  ? `~${getMonthlyPrice(yearlyProduct.localizedPrice, yearlyProduct)}/tháng`
                  : ""}
              </Text>
            </View>
          </View>

          <View style={styles.savingsBadge}>
            <Text style={styles.savingsText}>Tiết kiệm 60%</Text>
          </View>

          <View style={styles.planFeatures}>
            <Text
              style={[
                styles.planFeatureText,
                { color: themeColors.secondaryText },
              ]}
            >
              • Tất cả tính năng Premium
            </Text>
            <Text
              style={[
                styles.planFeatureText,
                { color: themeColors.secondaryText },
              ]}
            >
              • Hỗ trợ ưu tiên 24/7
            </Text>
            <Text
              style={[
                styles.planFeatureText,
                { color: themeColors.secondaryText },
              ]}
            >
              • Cập nhật tính năng mới đầu tiên
            </Text>
          </View>

          {selectedPlan === "premium_yearly" && (
            <View style={styles.selectedIndicator}>
              <Ionicons name="checkmark-circle" size={24} color="#FFD700" />
            </View>
          )}
        </TouchableOpacity>

        {/* Monthly Plan */}
        <TouchableOpacity
          style={[
            styles.pricingCard,
            getCardStyle("medium"),
            {
              backgroundColor: themeColors.cardBackground,
              borderWidth: selectedPlan === "premium_monthly" ? 2 : 1,
              borderColor:
                selectedPlan === "premium_monthly"
                  ? themeColors.primary
                  : themeColors.border,
              marginTop: 16,
            },
          ]}
          onPress={() => setSelectedPlan("premium_monthly")}
        >
          <View style={styles.pricingHeader}>
            <Text style={[styles.planTitle, { color: themeColors.text }]}>
              Premium Tháng
            </Text>
            <View style={styles.priceContainer}>
              <Text style={[styles.price, { color: themeColors.primary }]}>
                {monthlyProduct
                  ? formatPrice(monthlyProduct.localizedPrice)
                  : "Đang tải..."}
              </Text>
              <Text
                style={[
                  styles.priceSubtext,
                  { color: themeColors.secondaryText },
                ]}
              >
                /tháng
              </Text>
            </View>
          </View>

          <View style={styles.planFeatures}>
            <Text
              style={[
                styles.planFeatureText,
                { color: themeColors.secondaryText },
              ]}
            >
              • Tất cả tính năng Premium
            </Text>
            <Text
              style={[
                styles.planFeatureText,
                { color: themeColors.secondaryText },
              ]}
            >
              • Linh hoạt hủy bất kỳ lúc nào
            </Text>
          </View>

          {selectedPlan === "premium_monthly" && (
            <View style={styles.selectedIndicator}>
              <Ionicons
                name="checkmark-circle"
                size={24}
                color={themeColors.primary}
              />
            </View>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  const renderPurchaseButton = () => (
    <View style={styles.purchaseSection}>
      <TouchableOpacity
        style={[
          styles.purchaseButton,
          {
            backgroundColor:
              selectedPlan === "premium_yearly"
                ? "#FFD700"
                : themeColors.primary,
            ...getShadowStyle("medium"),
          },
        ]}
        onPress={() => handlePurchase(selectedPlan)}
        disabled={purchasing || loading}
      >
        {purchasing ? (
          <ActivityIndicator size="small" color="#000" />
        ) : (
          <>
            <Ionicons name="diamond" size={20} color="#000" />
            <Text style={styles.purchaseButtonText}>
              Nâng cấp ngay -{" "}
              {products.find((p) => p.productId === selectedPlan)
                ?.localizedPrice || "Đang tải..."}
            </Text>
          </>
        )}
      </TouchableOpacity>

      <Text
        style={[styles.disclaimerText, { color: themeColors.secondaryText }]}
      >
        Tự động gia hạn. Hủy bất kỳ lúc nào trong cài đặt App Store.
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải thông tin Premium...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      {renderHeader()}

      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {renderHeroSection()}

        <View style={styles.content}>
          {/* Hiển thị thông báo referral nếu có */}
          {referralCode && (
            <View
              style={[
                styles.referralNotice,
                getCardStyle("low"),
                { backgroundColor: themeColors.cardBackground },
              ]}
            >
              <View style={styles.referralIcon}>
                <Ionicons name="people" size={20} color="#4CAF50" />
              </View>
              <View style={styles.referralText}>
                <Text
                  style={[styles.referralTitle, { color: themeColors.text }]}
                >
                  Bạn được giới thiệu bởi: {referralCode}
                </Text>
                <Text
                  style={[
                    styles.referralDescription,
                    { color: themeColors.secondaryText },
                  ]}
                >
                  Cảm ơn bạn đã sử dụng link giới thiệu! 🎉
                </Text>
              </View>
            </View>
          )}

          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Tính năng Premium
          </Text>

          <FlatList
            data={PREMIUM_FEATURES}
            renderItem={renderFeatureItem}
            keyExtractor={(item) => item.title}
            scrollEnabled={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />

          {renderPricingPlans()}
        </View>
      </ScrollView>

      {renderPurchaseButton()}

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSuccessModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.successModal, getCardStyle("high")]}>
            <View style={styles.successIcon}>
              <Ionicons name="checkmark-circle" size={60} color="#4CAF50" />
            </View>
            <Text style={[styles.successTitle, { color: themeColors.text }]}>
              Chúc mừng!
            </Text>
            <Text
              style={[
                styles.successMessage,
                { color: themeColors.secondaryText },
              ]}
            >
              Bạn đã nâng cấp thành công lên AI Money Premium.{"\n"}
              Tận hưởng tất cả tính năng cao cấp ngay bây giờ!
            </Text>
            <TouchableOpacity
              style={[
                styles.successButton,
                {
                  backgroundColor: themeColors.primary,
                  ...getShadowStyle("low"),
                },
              ]}
              onPress={() => {
                setShowSuccessModal(false);
                router.back();
              }}
            >
              <Text style={styles.successButtonText}>Tuyệt vời!</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 60,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  heroSection: {
    height: height * 0.45,
  },
  heroGradient: {
    flex: 1,
    position: "relative",
  },
  heroOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.1)",
  },
  heroContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 80,
  },
  premiumBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255,255,255,0.2)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 20,
  },
  badgeText: {
    color: "#FFD700",
    fontSize: 14,
    fontWeight: "bold",
    marginLeft: 6,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 12,
    textAlign: "center",
  },
  heroSubtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.9)",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 24,
  },
  featuresHighlight: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: 12,
  },
  highlightItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255,255,255,0.15)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  highlightText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
    marginLeft: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    marginTop: -40,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  featureItem: {
    flexDirection: "row",
    padding: 16,
    marginBottom: 12,
    borderRadius: 16,
    alignItems: "center",
  },
  featureIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  pricingSection: {
    marginTop: 30,
  },
  pricingCard: {
    padding: 20,
    borderRadius: 20,
    position: "relative",
  },
  popularBadge: {
    position: "absolute",
    top: -8,
    left: 20,
    backgroundColor: "#FFD700",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#000",
  },
  pricingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  planTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  priceContainer: {
    alignItems: "flex-end",
  },
  price: {
    fontSize: 24,
    fontWeight: "bold",
  },
  priceSubtext: {
    fontSize: 12,
    marginTop: 2,
  },
  savingsBadge: {
    alignSelf: "flex-start",
    backgroundColor: "#4CAF50",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginBottom: 12,
  },
  savingsText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  planFeatures: {
    marginTop: 8,
  },
  planFeatureText: {
    fontSize: 14,
    marginBottom: 4,
    lineHeight: 20,
  },
  selectedIndicator: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  purchaseSection: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 16,
    paddingVertical: 20,
    paddingBottom: 34,
  },
  purchaseButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderRadius: 16,
    marginBottom: 12,
  },
  purchaseButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  disclaimerText: {
    fontSize: 12,
    textAlign: "center",
    lineHeight: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  successModal: {
    width: "85%",
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 30,
    alignItems: "center",
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 12,
  },
  successMessage: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 24,
  },
  successButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 12,
  },
  successButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  referralNotice: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: "#4CAF50",
  },
  referralIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#E8F5E8",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  referralText: {
    flex: 1,
  },
  referralTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 2,
  },
  referralDescription: {
    fontSize: 12,
  },
});
