// File: app/(more)/affiliate-dashboard.tsx
// File này liên quan đến: app/(tabs)/settings.tsx, lib/services/affiliate-service.ts, lib/services/referral-code-service.ts

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  Share,
  Alert,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useTheme } from "@/context/ThemeContext";
import { LinearGradient } from "expo-linear-gradient";
import * as Clipboard from "expo-clipboard";
import ReferralCodeInput from "@/components/ReferralCodeInput";
import { referralCodeService } from "@/lib/services/referral-code-service";

const { width } = Dimensions.get("window");

interface AffiliateStats {
  totalEarnings: number;
  pendingEarnings: number;
  totalReferrals: number;
  activeReferrals: number;
  clickCount: number;
  conversionRate: number;
  affiliateCode: string;
  tier: string;
  commissionRate: number;
}

export default function AffiliateDashboardScreen() {
  const { themeColors, isDark, getCardStyle, getShadowStyle } = useTheme();
  const [loading, setLoading] = useState(true);
  const [userCode, setUserCode] = useState("");
  const [loadingCode, setLoadingCode] = useState(true);
  
  const userId = "current_user_123";

  const [stats, setStats] = useState<AffiliateStats>({
    totalEarnings: 0,
    pendingEarnings: 0,
    totalReferrals: 0,
    activeReferrals: 0,
    clickCount: 0,
    conversionRate: 0,
    affiliateCode: "",
    tier: "Basic",
    commissionRate: 0.5,
  });

  useEffect(() => {
    loadAffiliateData();
    loadUserCode();
  }, []);

  const loadUserCode = async () => {
    try {
      setLoadingCode(true);
      const code = await referralCodeService.getUserCode(userId);
      setUserCode(code);
    } catch (error) {
      console.error("Error loading user code:", error);
    } finally {
      setLoadingCode(false);
    }
  };

  const loadAffiliateData = async () => {
    try {
      setTimeout(() => {
        setStats({
          totalEarnings: 1250000,
          pendingEarnings: 450000,
          totalReferrals: 25,
          activeReferrals: 18,
          clickCount: 156,
          conversionRate: 16.02,
          affiliateCode: "001234",
          tier: "Silver",
          commissionRate: 0.52,
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Error loading affiliate data:", error);
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const formatDisplayCode = (code: string) => {
    return code.replace(/(\d{3})(\d{3})/, "$1-$2");
  };

  const copyUserCode = async () => {
    try {
      await Clipboard.setStringAsync(userCode);
      Alert.alert("Thành công", "Đã sao chép mã giới thiệu của bạn!");
    } catch (error) {
      Alert.alert("Lỗi", "Không thể sao chép mã giới thiệu");
    }
  };

  const shareUserCode = async () => {
    try {
      await Share.share({
        message: `Tham gia AI Money Premium với mã giới thiệu của tôi: ${userCode}\n\nTải app tại: https://apps.apple.com/us/app/moneyup-chi-ti%C3%AAu-th%C3%B4ng-minh/id6745965337`,
        title: "Chia sẻ mã giới thiệu AI Money",
      });
    } catch (error) {
      console.error("Error sharing code:", error);
    }
  };

  const handleReferralCodeUsed = (referrerName: string) => {
    Alert.alert(
      "Chào mừng!",
      `Cảm ơn bạn đã sử dụng mã giới thiệu của ${referrerName}. Bạn sẽ nhận được ưu đãi đặc biệt khi nâng cấp Premium!`
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        onPress={() => router.back()}
        style={[
          styles.backButton,
          {
            backgroundColor: themeColors.iconBackground,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="arrow-back" size={24} color={themeColors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: themeColors.text }]}>
        Affiliate Dashboard
      </Text>
      <TouchableOpacity
        onPress={() => router.push("/(more)/affiliate-earnings")}
        style={[
          styles.earningsButton,
          {
            backgroundColor: themeColors.primary,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="wallet" size={20} color="#fff" />
      </TouchableOpacity>
    </View>
  );

  const renderWelcomeSection = () => (
    <View style={styles.welcomeSection}>
      <LinearGradient
        colors={
          isDark
            ? ["#1e3c72", "#2a5298"]
            : ["#667eea", "#764ba2"]
        }
        style={styles.welcomeGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.welcomeContent}>
          <View style={styles.welcomeIcon}>
            <Ionicons name="people" size={32} color="#FFD700" />
          </View>
          <Text style={styles.welcomeTitle}>Chương trình Affiliate</Text>
          
        </View>
      </LinearGradient>
    </View>
  );

  const renderBenefitsSection = () => (
    <View style={styles.benefitsSection}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Ưu đãi chương trình
      </Text>
      
      <View
        style={[
          styles.benefitsCard,
          getCardStyle("medium"),
          { backgroundColor: themeColors.cardBackground },
        ]}
      >
        <View style={styles.benefitItem}>
          <View style={[styles.benefitIcon, { backgroundColor: "#4CAF5015" }]}>
            <Ionicons name="people" size={20} color="#4CAF50" />
          </View>
          <View style={styles.benefitContent}>
            <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
              Người giới thiệu
            </Text>
            <Text style={[styles.benefitDescription, { color: themeColors.secondaryText }]}>
              Nhận <Text style={{ color: "#4CAF50", fontWeight: "600" }}>50% hoa hồng</Text> khi người được giới thiệu mua gói Premium
            </Text>
          </View>
        </View>

        <View style={[styles.benefitDivider, { backgroundColor: themeColors.border }]} />

        <View style={styles.benefitItem}>
          <View style={[styles.benefitIcon, { backgroundColor: "#2196F315" }]}>
            <Ionicons name="gift" size={20} color="#2196F3" />
          </View>
          <View style={styles.benefitContent}>
            <Text style={[styles.benefitTitle, { color: themeColors.text }]}>
              Người được giới thiệu
            </Text>
            <Text style={[styles.benefitDescription, { color: themeColors.secondaryText }]}>
              Nhận <Text style={{ color: "#2196F3", fontWeight: "600" }}>1 tháng Premium miễn phí</Text> và <Text style={{ color: "#FF9800", fontWeight: "600" }}>5.000 VND</Text> khi nhập mã thành công
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.noteContainer}>
        <View style={styles.noteIcon}>
          <Ionicons name="information-circle" size={16} color="#FF9800" />
        </View>
        <Text style={[styles.noteText, { color: themeColors.secondaryText }]}>
          Mỗi người chỉ được nhập mã giới thiệu một lần duy nhất
        </Text>
      </View>
    </View>
  );

  const renderReferralCodeInput = () => (
    <View style={styles.referralInputSection}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Nhập mã giới thiệu
      </Text>
      <ReferralCodeInput 
        userId={userId} 
        onCodeUsed={handleReferralCodeUsed}
      />
    </View>
  );

  const renderUserReferralCode = () => (
    <View style={styles.userCodeSection}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Mã giới thiệu của bạn
      </Text>
      
      <View
        style={[
          styles.userCodeCard,
          getCardStyle("medium"),
          { backgroundColor: themeColors.cardBackground },
        ]}
      >
        <View style={styles.userCodeHeader}>
          <View style={[styles.userCodeIcon, { backgroundColor: "#4CAF5020" }]}>
            <Ionicons name="gift" size={24} color="#4CAF50" />
          </View>
          <View style={styles.userCodeInfo}>
            <Text style={[styles.userCodeLabel, { color: themeColors.secondaryText }]}>
              Mã của bạn
            </Text>
            <Text style={[styles.userCodeText, { color: themeColors.primary }]}>
              {loadingCode ? "Đang tải..." : formatDisplayCode(userCode)}
            </Text>
          </View>
          <View style={styles.userCodeActions}>
            <TouchableOpacity
              style={[styles.userCodeActionBtn, { backgroundColor: themeColors.iconBackground }]}
              onPress={copyUserCode}
              disabled={loadingCode || !userCode}
            >
              <Ionicons name="copy" size={16} color={themeColors.primary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.userCodeActionBtn, { backgroundColor: themeColors.primary, marginLeft: 8 }]}
              onPress={shareUserCode}
              disabled={loadingCode || !userCode}
            >
              <Ionicons name="share" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.userCodeStats}>
          <View style={styles.userCodeStat}>
            <Text style={[styles.statNumber, { color: "#4CAF50" }]}>
              {stats.totalReferrals}
            </Text>
            <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
              Người đã dùng
            </Text>
          </View>
          <View style={styles.userCodeStat}>
            <Text style={[styles.statNumber, { color: "#2196F3" }]}>
              {stats.tier}
            </Text>
            <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
              Hạng hiện tại
            </Text>
          </View>
          <View style={styles.userCodeStat}>
            <Text style={[styles.statNumber, { color: "#FF9800" }]}>
              {(stats.commissionRate * 100).toFixed(0)}%
            </Text>
            <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
              Hoa hồng
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderStatsCards = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statsRow}>
        <View
          style={[
            styles.statCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.statIconContainer}>
            <Ionicons name="wallet" size={24} color="#4CAF50" />
          </View>
          <Text style={[styles.statValue, { color: "#4CAF50" }]}>
            {formatCurrency(stats.totalEarnings)}
          </Text>
          <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
            Tổng thu nhập
          </Text>
        </View>

        <View
          style={[
            styles.statCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.statIconContainer}>
            <Ionicons name="hourglass" size={24} color="#FF9800" />
          </View>
          <Text style={[styles.statValue, { color: "#FF9800" }]}>
            {formatCurrency(stats.pendingEarnings)}
          </Text>
          <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
            Chờ thanh toán
          </Text>
        </View>
      </View>

      <View style={styles.statsRow}>
        <View
          style={[
            styles.statCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.statIconContainer}>
            <Ionicons name="people" size={24} color="#2196F3" />
          </View>
          <Text style={[styles.statValue, { color: "#2196F3" }]}>
            {stats.totalReferrals}
          </Text>
          <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
            Tổng giới thiệu
          </Text>
        </View>

        <View
          style={[
            styles.statCard,
            getCardStyle("medium"),
            { backgroundColor: themeColors.cardBackground },
          ]}
        >
          <View style={styles.statIconContainer}>
            <Ionicons name="trending-up" size={24} color="#9C27B0" />
          </View>
          <Text style={[styles.statValue, { color: "#9C27B0" }]}>
            {stats.conversionRate}%
          </Text>
          <Text style={[styles.statLabel, { color: themeColors.secondaryText }]}>
            Tỷ lệ chuyển đổi
          </Text>
        </View>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsSection}>
      <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
        Thao tác nhanh
      </Text>

      <View style={styles.actionsGrid}>
        <TouchableOpacity
          style={[
            styles.actionCard,
            getCardStyle("low"),
            { backgroundColor: themeColors.cardBackground },
          ]}
          onPress={() => router.push("/(more)/affiliate-share")}
        >
          <View style={[styles.actionIcon, { backgroundColor: "#E3F2FD" }]}>
            <Ionicons name="share-social" size={24} color="#2196F3" />
          </View>
          <Text style={[styles.actionTitle, { color: themeColors.text }]}>
            Chia sẻ
          </Text>
          <Text style={[styles.actionDescription, { color: themeColors.secondaryText }]}>
            Tạo nội dung chia sẻ
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionCard,
            getCardStyle("low"),
            { backgroundColor: themeColors.cardBackground },
          ]}
          onPress={() => router.push("/(more)/affiliate-earnings")}
        >
          <View style={[styles.actionIcon, { backgroundColor: "#E8F5E8" }]}>
            <Ionicons name="analytics" size={24} color="#4CAF50" />
          </View>
          <Text style={[styles.actionTitle, { color: themeColors.text }]}>
            Thống kê
          </Text>
          <Text style={[styles.actionDescription, { color: themeColors.secondaryText }]}>
            Xem báo cáo chi tiết
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={[styles.loadingText, { color: themeColors.text }]}>
            Đang tải dữ liệu affiliate...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      {renderHeader()}
      
      <ScrollView 
        style={{ flex: 1 }} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 30 }}
      >
        {renderWelcomeSection()}
        {renderBenefitsSection()}
        {renderReferralCodeInput()}
        {renderUserReferralCode()}
        {renderStatsCards()}
        {renderQuickActions()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 1,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  earningsButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  welcomeSection: {
    marginHorizontal: 16,
    marginVertical: 20,
    borderRadius: 20,
    overflow: "hidden",
  },
  welcomeGradient: {
    padding: 24,
  },
  welcomeContent: {
    alignItems: "center",
  },
  welcomeIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "rgba(255,255,255,0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: "rgba(255,255,255,0.9)",
    textAlign: "center",
    lineHeight: 20,
  },
  benefitsSection: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  benefitsCard: {
    padding: 20,
    borderRadius: 16,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingVertical: 8,
  },
  benefitIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    marginTop: 2,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  benefitDivider: {
    height: 1,
    marginVertical: 12,
  },
  noteContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    paddingHorizontal: 4,
  },
  noteIcon: {
    marginRight: 8,
  },
  noteText: {
    fontSize: 12,
    flex: 1,
    lineHeight: 16,
  },
  referralInputSection: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  userCodeSection: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  userCodeCard: {
    padding: 20,
    borderRadius: 16,
  },
  userCodeHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  userCodeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  userCodeInfo: {
    flex: 1,
  },
  userCodeLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  userCodeText: {
    fontSize: 20,
    fontWeight: "bold",
    letterSpacing: 2,
  },
  userCodeActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  userCodeActionBtn: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  userCodeStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#E0E0E0",
  },
  userCodeStat: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 2,
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: "row",
    marginBottom: 12,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    marginHorizontal: 6,
  },
  statIconContainer: {
    marginBottom: 8,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  actionsSection: {
    paddingHorizontal: 16,
  },
  actionsGrid: {
    flexDirection: "row",
  },
  actionCard: {
    flex: 1,
    padding: 16,
    borderRadius: 16,
    alignItems: "center",
    marginHorizontal: 6,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    textAlign: "center",
  },
});