// File: app/(more)/debt.tsx
// File này hiển thị danh sách sổ nợ của người dùng
// File này liên quan đến: context/DebtContext.tsx, lib/models/debt.ts, app/(more)/debt/add.tsx

import { Text, View } from "@/components/Themed";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { useCurrency } from "@/context/CurrencyContext";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import React, { useState, useEffect, useCallback } from "react";
import {
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Alert,
  TextInput,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { DebtModel } from "@/lib/models/debt";

// <PERSON><PERSON>nh nghĩa kiểu dữ liệu khoản nợ
interface Debt {
  id: string;
  name: string;
  amount: number;
  remaining: number;
  type: 'payable' | 'receivable'; // phải trả | cho vay
  description?: string;
  due_date?: string;
  contact_info?: string;
  reminder: number;
  reminder_date?: string;
  status: 'active' | 'paid' | 'canceled';
  created_at: string;
}

export default function DebtScreen() {
  const { isDark, themeColors, getCardStyle, getIconContainerStyle } = useTheme();
  const { t } = useLocalization();
  const { formatCurrency } = useCurrency();

  // States
  const [debts, setDebts] = useState<Debt[]>([]);
  const [filteredDebts, setFilteredDebts] = useState<Debt[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'payable' | 'receivable'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'paid' | 'canceled'>('active');

  // Load data
  const loadDebts = useCallback(async () => {
    try {
      setLoading(true);
      const data = await DebtModel.getAll();
      setDebts(data || []);
    } catch (error) {
      console.error('Lỗi khi tải sổ nợ:', error);
      Alert.alert('Lỗi', 'Không thể tải danh sách sổ nợ');
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto reload when screen focused
  useFocusEffect(
    useCallback(() => {
      loadDebts();
    }, [loadDebts])
  );

  // Filter debts when search or filter changes
  useEffect(() => {
    filterDebts();
  }, [debts, searchText, filterType, filterStatus]);

  const filterDebts = () => {
    let filtered = debts;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(debt => debt.type === filterType);
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(debt => debt.status === filterStatus);
    }

    // Filter by search text
    if (searchText) {
      filtered = filtered.filter(debt =>
        debt.name.toLowerCase().includes(searchText.toLowerCase()) ||
        debt.description?.toLowerCase().includes(searchText.toLowerCase()) ||
        debt.contact_info?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredDebts(filtered);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDebts();
    setRefreshing(false);
  };

  const handleDeleteDebt = async (debt: Debt) => {
    Alert.alert(
      "Xác nhận xóa",
      `Bạn có chắc muốn xóa khoản nợ "${debt.name}"?`,
      [
        { text: "Hủy", style: "cancel" },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              await DebtModel.delete(debt.id);
              await loadDebts();
              Alert.alert("Thành công", "Đã xóa khoản nợ");
            } catch (error) {
              console.error('Lỗi khi xóa nợ:', error);
              Alert.alert("Lỗi", "Không thể xóa khoản nợ");
            }
          },
        },
      ]
    );
  };

  const handleMarkAsPaid = async (debt: Debt) => {
    try {
      await DebtModel.updateStatus(debt.id, 'paid');
      await loadDebts();
      Alert.alert("Thành công", "Đã đánh dấu hoàn thành");
    } catch (error) {
      console.error('Lỗi khi cập nhật trạng thái:', error);
      Alert.alert("Lỗi", "Không thể cập nhật trạng thái");
    }
  };

  // Calculate totals
  const totalPayable = debts
    .filter(d => d.type === 'payable' && d.status === 'active')
    .reduce((sum, d) => sum + d.remaining, 0);

  const totalReceivable = debts
    .filter(d => d.type === 'receivable' && d.status === 'active')
    .reduce((sum, d) => sum + d.remaining, 0);

  // Check overdue debts
  const overdueDebts = debts.filter(debt => {
    if (!debt.due_date || debt.status !== 'active') return false;
    return new Date(debt.due_date) < new Date();
  });

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: "transparent" }]}>
      <View style={[styles.titleContainer, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons
            name="chevron-back"
            size={24}
            color={themeColors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: themeColors.text }]}>
          Sổ Nợ
        </Text>
      </View>

      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push("/debt/add")}
      >
        <Ionicons
          name="add"
          size={24}
          color={themeColors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderSummary = () => (
    <View style={[styles.summaryContainer, { backgroundColor: "transparent" }]}>
      {/* Overdue alert */}
      {overdueDebts.length > 0 && (
        <View style={[styles.alertCard, getCardStyle("medium"), {
          borderLeftWidth: 4,
          borderLeftColor: themeColors.danger
        }]}>
          <View style={[styles.alertIcon, {
            backgroundColor: themeColors.dangerBackground,
            borderColor: themeColors.dangerBorder
          }]}>
            <Ionicons
              name="warning"
              size={20}
              color={themeColors.danger}
            />
          </View>
          <View style={[styles.alertContent, { backgroundColor: "transparent" }]}>
            <Text style={[styles.alertTitle, { color: themeColors.danger }]}>
              Quá hạn
            </Text>
            <Text style={[styles.alertText, { color: themeColors.text }]}>
              {overdueDebts.length} khoản nợ đã quá hạn
            </Text>
          </View>
        </View>
      )}

      {/* Summary cards */}
      <View style={[styles.summaryCards, { backgroundColor: "transparent" }]}>
        <View style={[styles.summaryCard, getCardStyle("medium")]}>
          <View style={[styles.summaryIcon, getIconContainerStyle(), {
            backgroundColor: '#FFEBEE'
          }]}>
            <Ionicons
              name="arrow-up"
              size={20}
              color="#F44336"
            />
          </View>
          <View style={[styles.summaryContent, { backgroundColor: "transparent" }]}>
            <Text style={[styles.summaryLabel, { color: themeColors.secondaryText }]}>
              Phải trả
            </Text>
            <Text style={[styles.summaryAmount, { color: "#F44336" }]}>
              {formatCurrency(totalPayable)}
            </Text>
          </View>
        </View>

        <View style={[styles.summaryCard, getCardStyle("medium")]}>
          <View style={[styles.summaryIcon, getIconContainerStyle(), {
            backgroundColor: '#E8F5E8'
          }]}>
            <Ionicons
              name="arrow-down"
              size={20}
              color="#4CAF50"
            />
          </View>
          <View style={[styles.summaryContent, { backgroundColor: "transparent" }]}>
            <Text style={[styles.summaryLabel, { color: themeColors.secondaryText }]}>
              Cho vay
            </Text>
            <Text style={[styles.summaryAmount, { color: "#4CAF50" }]}>
              {formatCurrency(totalReceivable)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  const renderFilters = () => (
    <View style={[styles.filtersContainer, { backgroundColor: "transparent" }]}>
      {/* Search */}
      <View style={[styles.searchContainer, getCardStyle("low")]}>
        <Ionicons
          name="search"
          size={20}
          color={themeColors.secondaryText}
          style={styles.searchIcon}
        />
        <TextInput
          style={[styles.searchInput, { color: themeColors.text }]}
          placeholder="Tìm kiếm..."
          placeholderTextColor={themeColors.secondaryText}
          value={searchText}
          onChangeText={setSearchText}
        />
        {searchText ? (
          <TouchableOpacity
            onPress={() => setSearchText('')}
            style={styles.clearSearch}
          >
            <Ionicons
              name="close"
              size={16}
              color={themeColors.secondaryText}
            />
          </TouchableOpacity>
        ) : null}
      </View>

      {/* Type filters */}
      <View style={[styles.filterRow, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterType === 'all' && { backgroundColor: themeColors.primary }
          ]}
          onPress={() => setFilterType('all')}
        >
          <Text style={[
            styles.filterText,
            { color: filterType === 'all' ? 'white' : themeColors.text }
          ]}>
            Tất cả
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterType === 'payable' && { backgroundColor: '#F44336' }
          ]}
          onPress={() => setFilterType('payable')}
        >
          <Text style={[
            styles.filterText,
            { color: filterType === 'payable' ? 'white' : themeColors.text }
          ]}>
            Phải trả
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterType === 'receivable' && { backgroundColor: '#4CAF50' }
          ]}
          onPress={() => setFilterType('receivable')}
        >
          <Text style={[
            styles.filterText,
            { color: filterType === 'receivable' ? 'white' : themeColors.text }
          ]}>
            Cho vay
          </Text>
        </TouchableOpacity>
      </View>

      {/* Status filters */}
      <View style={[styles.filterRow, { backgroundColor: "transparent" }]}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterStatus === 'active' && { backgroundColor: themeColors.primary }
          ]}
          onPress={() => setFilterStatus('active')}
        >
          <Text style={[
            styles.filterText,
            { color: filterStatus === 'active' ? 'white' : themeColors.text }
          ]}>
            Đang nợ
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterStatus === 'paid' && { backgroundColor: '#4CAF50' }
          ]}
          onPress={() => setFilterStatus('paid')}
        >
          <Text style={[
            styles.filterText,
            { color: filterStatus === 'paid' ? 'white' : themeColors.text }
          ]}>
            Đã trả
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            getCardStyle("low"),
            filterStatus === 'all' && { backgroundColor: themeColors.primary }
          ]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[
            styles.filterText,
            { color: filterStatus === 'all' ? 'white' : themeColors.text }
          ]}>
            Tất cả
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderDebtItem = (debt: Debt) => {
    const isOverdue = debt.due_date && new Date(debt.due_date) < new Date() && debt.status === 'active';
    const typeColor = debt.type === 'payable' ? '#F44336' : '#4CAF50';
    const typeIcon = debt.type === 'payable' ? 'arrow-up' : 'arrow-down';
    
    return (
      <TouchableOpacity
        key={debt.id}
        style={[
          styles.debtCard,
          getCardStyle("medium"),
          isOverdue && { borderLeftWidth: 4, borderLeftColor: themeColors.danger }
        ]}
        onPress={() => router.push(`/debt/${debt.id}`)}
      >
        <View style={[styles.debtHeader, { backgroundColor: "transparent" }]}>
          <View style={[styles.debtIcon, getIconContainerStyle(), {
            backgroundColor: debt.type === 'payable' ? '#FFEBEE' : '#E8F5E8'
          }]}>
            <Ionicons
              name={typeIcon}
              size={20}
              color={typeColor}
            />
          </View>
          
          <View style={[styles.debtInfo, { backgroundColor: "transparent" }]}>
            <Text style={[styles.debtName, { color: themeColors.text }]}>
              {debt.name}
            </Text>
            {debt.contact_info && (
              <Text style={[styles.debtContact, { color: themeColors.secondaryText }]}>
                {debt.contact_info}
              </Text>
            )}
          </View>

          <View style={[styles.debtActions, { backgroundColor: "transparent" }]}>
            {debt.status === 'active' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleMarkAsPaid(debt)}
              >
                <Ionicons
                  name="checkmark-circle"
                  size={20}
                  color="#4CAF50"
                />
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => router.push(`/debt/edit/${debt.id}`)}
            >
              <Ionicons
                name="create"
                size={18}
                color={themeColors.primary}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteDebt(debt)}
            >
              <Ionicons
                name="trash"
                size={18}
                color={themeColors.danger}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={[styles.debtDetails, { backgroundColor: "transparent" }]}>
          <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
            <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
              Tổng tiền:
            </Text>
            <Text style={[styles.amountValue, { color: themeColors.text }]}>
              {formatCurrency(debt.amount)}
            </Text>
          </View>

          <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
            <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
              Còn lại:
            </Text>
            <Text style={[styles.remainingAmount, { color: typeColor }]}>
              {formatCurrency(debt.remaining)}
            </Text>
          </View>

          {debt.due_date && (
            <View style={[styles.amountRow, { backgroundColor: "transparent" }]}>
              <Text style={[styles.amountLabel, { color: themeColors.secondaryText }]}>
                Hạn trả:
              </Text>
              <Text style={[
                styles.dueDateText,
                { color: isOverdue ? themeColors.danger : themeColors.text }
              ]}>
                {new Date(debt.due_date).toLocaleDateString('vi-VN')}
                {isOverdue && ' (Quá hạn)'}
              </Text>
            </View>
          )}

          {debt.description && (
            <Text style={[styles.debtDescription, { color: themeColors.secondaryText }]}>
              {debt.description}
            </Text>
          )}

          <View style={[styles.statusRow, { backgroundColor: "transparent" }]}>
            <View style={[
              styles.statusBadge,
              {
                backgroundColor: debt.status === 'active' ? themeColors.primary :
                                debt.status === 'paid' ? '#4CAF50' : '#757575'
              }
            ]}>
              <Text style={styles.statusText}>
                {debt.status === 'active' ? 'Đang nợ' :
                 debt.status === 'paid' ? 'Đã trả' : 'Đã hủy'}
              </Text>
            </View>

            <Text style={[styles.dateText, { color: themeColors.secondaryText }]}>
              {new Date(debt.created_at).toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={[styles.emptyState, { backgroundColor: "transparent" }]}>
      <View style={[styles.emptyIcon, getIconContainerStyle()]}>
        <Ionicons
          name="document-text-outline"
          size={48}
          color={themeColors.secondaryText}
        />
      </View>
      <Text style={[styles.emptyTitle, { color: themeColors.text }]}>
        Chưa có khoản nợ nào
      </Text>
      <Text style={[styles.emptyDescription, { color: themeColors.secondaryText }]}>
        Nhấn nút + để thêm khoản nợ mới
      </Text>
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: themeColors.background, flexGrow: 1 },
      ]}
      edges={["top", "left", "right"]}
    >
      {renderHeader()}

      <ScrollView
        style={{ flex: 1, backgroundColor: "transparent" }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[themeColors.primary]}
            tintColor={themeColors.primary}
          />
        }
        contentContainerStyle={{ paddingBottom: 120 }}
      >
        {renderSummary()}
        {renderFilters()}

        <View style={[styles.debtsList, { backgroundColor: "transparent" }]}>
          {loading ? (
            <View style={[styles.loadingState, { backgroundColor: "transparent" }]}>
              <Text style={[styles.loadingText, { color: themeColors.secondaryText }]}>
                Đang tải...
              </Text>
            </View>
          ) : filteredDebts.length === 0 ? (
            renderEmptyState()
          ) : (
            filteredDebts.map(renderDebtItem)
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    padding: 8,
  },
  summaryContainer: {
    padding: 16,
  },
  alertCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
  },
  alertIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  alertText: {
    fontSize: 14,
  },
  summaryCards: {
    flexDirection: 'row',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  summaryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  summaryContent: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  summaryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  filtersContainer: {
    padding: 16,
    paddingTop: 0,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    padding: 0,
  },
  clearSearch: {
    padding: 4,
  },
  filterRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  debtsList: {
    padding: 16,
    paddingTop: 0,
  },
  debtCard: {
    padding: 16,
    marginBottom: 12,
  },
  debtHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  debtIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  debtInfo: {
    flex: 1,
  },
  debtName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  debtContact: {
    fontSize: 14,
  },
  debtActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  debtDetails: {
    gap: 8,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
  },
  amountValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  remainingAmount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  dueDateText: {
    fontSize: 14,
    fontWeight: '500',
  },
  debtDescription: {
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 4,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  dateText: {
    fontSize: 12,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
  },
  loadingState: {
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
  },
});