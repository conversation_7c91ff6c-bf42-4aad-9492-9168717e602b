// File: app/(more)/affiliate-share.tsx
// File này liên quan đến: app/(more)/affiliate-dashboard.tsx, lib/services/affiliate-service.ts

import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Share,
  Alert,
  TextInput,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useTheme } from "@/context/ThemeContext";
import * as Clipboard from "expo-clipboard";

interface ShareTemplate {
  id: string;
  title: string;
  content: string;
  platform: string;
  icon: string;
  color: string;
}

const SHARE_TEMPLATES: ShareTemplate[] = [
  {
    id: "facebook",
    title: "Facebook Post",
    content: "🔥 Khám phá AI Money Premium - Ứng dụng quản lý tài chính thông minh nhất! \n\n✅ Không quảng cáo\n✅ Báo cáo chi tiết\n✅ Sao lưu đám mây\n✅ AI phân tích thông minh\n\nTải ngay và nhận ưu đãi đặc biệt: [LINK]",
    platform: "Facebook",
    icon: "logo-facebook",
    color: "#1877F2",
  },
  {
    id: "instagram",
    title: "Instagram Story",
    content: "💰 AI Money Premium - Quản lý tài chính thông minh!\n\n🚀 Tính năng cao cấp\n📊 Báo cáo chi tiết\n☁️ Sao lưu tự động\n\nSwipe up để tải ngay! 👆\n[LINK]",
    platform: "Instagram",
    icon: "logo-instagram",
    color: "#E4405F",
  },
  {
    id: "whatsapp",
    title: "WhatsApp Message",
    content: "Chào bạn! 👋\n\nMình đang dùng AI Money Premium và thấy rất hữu ích cho việc quản lý tài chính. Ứng dụng có nhiều tính năng hay như:\n\n• Không quảng cáo\n• Báo cáo chi tiết\n• Sao lưu đám mây\n• AI phân tích chi tiêu\n\nBạn thử tải về xem nhé: [LINK]",
    platform: "WhatsApp",
    icon: "logo-whatsapp",
    color: "#25D366",
  },
  {
    id: "telegram",
    title: "Telegram Message",
    content: "🤖 AI Money Premium - Ứng dụng quản lý tài chính với AI thông minh!\n\n🎯 Tính năng nổi bật:\n• Phân tích chi tiêu tự động\n• Báo cáo chi tiết\n• Không quảng cáo\n• Sao lưu đám mây\n\nTải ngay: [LINK]",
    platform: "Telegram",
    icon: "paper-plane",
    color: "#0088CC",
  },
];

export default function AffiliateShareScreen() {
  const { themeColors, isDark, getCardStyle, getShadowStyle } = useTheme();
  const [selectedTemplate, setSelectedTemplate] = useState<ShareTemplate | null>(null);
  const [customContent, setCustomContent] = useState("");
  const [showCustomEditor, setShowCustomEditor] = useState(false);

  const referralLink = "https://aimoney.app/ref/AMU2024001"; // Lấy từ context/API

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        onPress={() => router.back()}
        style={[
          styles.backButton,
          {
            backgroundColor: themeColors.iconBackground,
            ...getShadowStyle("low"),
          },
        ]}
      >
        <Ionicons name="arrow-back" size={24} color={themeColors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: themeColors.text }]}>
        Chia sẻ Affiliate
      </Text>
      <View style={{ width: 44 }} />
    </View>
  );

  const replaceLink = (content: string) => {
    return content.replace("[LINK]", referralLink);
  };

  const shareContent = async (content: string, platform: string) => {
    const finalContent = replaceLink(content);
    
    try {
      await Share.share({
        message: finalContent,
        url: referralLink,
        title: `AI Money Premium - ${platform}`,
      });
    } catch (error) {
      console.error("Error sharing:", error);
      Alert.alert("Lỗi", "Không thể chia sẻ. Vui lòng thử lại.");
    }
  };

  const copyContent = async (content: string) => {
    const finalContent = replaceLink(content);
    
    try {
      await Clipboard.setStringAsync(finalContent);
      Alert.alert("Thành công", "Đã sao chép nội dung!");
    } catch (error) {
      Alert.alert("Lỗi", "Không thể sao chép. Vui lòng thử lại.");
    }
  };

  const renderTemplateCard = (template: ShareTemplate) => (
    <TouchableOpacity
      key={template.id}
      style={[
        styles.templateCard,
        getCardStyle("medium"),
        {
          backgroundColor: themeColors.cardBackground,
          borderWidth: selectedTemplate?.id === template.id ? 2 : 0,
          borderColor: selectedTemplate?.id === template.id ? template.color : "transparent",
        },
      ]}
      onPress={() => setSelectedTemplate(template)}
    >
      <View style={styles.templateHeader}>
        <View style={[styles.platformIcon, { backgroundColor: `${template.color}20` }]}>
          <Ionicons name={template.icon as any} size={24} color={template.color} />
        </View>
        <View style={styles.templateInfo}>
          <Text style={[styles.templateTitle, { color: themeColors.text }]}>
            {template.title}
          </Text>
          <Text style={[styles.platformName, { color: themeColors.secondaryText }]}>
            {template.platform}
          </Text>
        </View>
        {selectedTemplate?.id === template.id && (
          <Ionicons name="checkmark-circle" size={24} color={template.color} />
        )}
      </View>
      
      <Text 
        style={[styles.templatePreview, { color: themeColors.secondaryText }]}
        numberOfLines={3}
      >
        {template.content.substring(0, 100)}...
      </Text>
      
      <View style={styles.templateActions}>
        <TouchableOpacity
          style={[styles.actionBtn, { backgroundColor: themeColors.iconBackground }]}
          onPress={() => copyContent(template.content)}
        >
          <Ionicons name="copy" size={16} color={themeColors.primary} />
          <Text style={[styles.actionText, { color: themeColors.primary }]}>
            Sao chép
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionBtn, { backgroundColor: template.color }]}
          onPress={() => shareContent(template.content, template.platform)}
        >
          <Ionicons name="share" size={16} color="#fff" />
          <Text style={[styles.actionText, { color: "#fff" }]}>
            Chia sẻ
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderContentPreview = () => {
    if (!selectedTemplate) return null;

    return (
      <View
        style={[
          styles.previewSection,
          getCardStyle("medium"),
          { backgroundColor: themeColors.cardBackground },
        ]}
      >
        <Text style={[styles.previewTitle, { color: themeColors.text }]}>
          Xem trước nội dung
        </Text>
        
        <View style={styles.previewContent}>
          <Text style={[styles.previewText, { color: themeColors.text }]}>
            {replaceLink(selectedTemplate.content)}
          </Text>
        </View>
        
        <View style={styles.previewActions}>
          <TouchableOpacity
            style={[
              styles.previewBtn,
              { 
                backgroundColor: themeColors.iconBackground,
                borderColor: themeColors.border,
              }
            ]}
            onPress={() => copyContent(selectedTemplate.content)}
          >
            <Ionicons name="copy" size={18} color={themeColors.primary} />
            <Text style={[styles.previewBtnText, { color: themeColors.primary }]}>
              Sao chép
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.previewBtn,
              { backgroundColor: selectedTemplate.color }
            ]}
            onPress={() => shareContent(selectedTemplate.content, selectedTemplate.platform)}
          >
            <Ionicons name="share" size={18} color="#fff" />
            <Text style={[styles.previewBtnText, { color: "#fff" }]}>
              Chia sẻ ngay
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderCustomEditor = () => (
    <View
      style={[
        styles.customSection,
        getCardStyle("medium"),
        { backgroundColor: themeColors.cardBackground },
      ]}
    >
      <View style={styles.customHeader}>
        <Text style={[styles.customTitle, { color: themeColors.text }]}>
          Tạo nội dung tùy chỉnh
        </Text>
        <TouchableOpacity
          onPress={() => setShowCustomEditor(!showCustomEditor)}
        >
          <Ionicons 
            name={showCustomEditor ? "chevron-up" : "chevron-down"} 
            size={24} 
            color={themeColors.primary} 
          />
        </TouchableOpacity>
      </View>
      
      {showCustomEditor && (
        <View style={styles.customEditor}>
          <TextInput
            style={[
              styles.customInput,
              {
                backgroundColor: themeColors.iconBackground,
                color: themeColors.text,
                borderColor: themeColors.border,
              },
            ]}
            value={customContent}
            onChangeText={setCustomContent}
            placeholder="Viết nội dung chia sẻ của bạn... (dùng [LINK] để chèn link giới thiệu)"
            placeholderTextColor={themeColors.secondaryText}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
          />
          
          <View style={styles.customActions}>
            <TouchableOpacity
              style={[
                styles.customBtn,
                { 
                  backgroundColor: themeColors.iconBackground,
                  borderColor: themeColors.border,
                }
              ]}
              onPress={() => copyContent(customContent)}
              disabled={!customContent.trim()}
            >
              <Ionicons name="copy" size={18} color={themeColors.primary} />
              <Text style={[styles.customBtnText, { color: themeColors.primary }]}>
                Sao chép
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.customBtn,
                { backgroundColor: themeColors.primary }
              ]}
              onPress={() => shareContent(customContent, "Tùy chỉnh")}
              disabled={!customContent.trim()}
            >
              <Ionicons name="share" size={18} color="#fff" />
              <Text style={[styles.customBtnText, { color: "#fff" }]}>
                Chia sẻ
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderQuickShare = () => (
    <View
      style={[
        styles.quickShareSection,
        getCardStyle("low"),
        { backgroundColor: themeColors.cardBackground },
      ]}
    >
      <Text style={[styles.quickShareTitle, { color: themeColors.text }]}>
        Chia sẻ nhanh
      </Text>
      
      <View style={styles.quickShareGrid}>
        <TouchableOpacity
          style={[styles.quickShareItem, { backgroundColor: themeColors.iconBackground }]}
          onPress={() => copyContent(referralLink)}
        >
          <Ionicons name="link" size={24} color={themeColors.primary} />
          <Text style={[styles.quickShareText, { color: themeColors.text }]}>
            Copy Link
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.quickShareItem, { backgroundColor: "#25D366" }]}
          onPress={() => shareContent(`Tham gia AI Money Premium: ${referralLink}`, "WhatsApp")}
        >
          <Ionicons name="logo-whatsapp" size={24} color="#fff" />
          <Text style={[styles.quickShareText, { color: "#fff" }]}>
            WhatsApp
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.quickShareItem, { backgroundColor: "#0088CC" }]}
          onPress={() => shareContent(`Khám phá AI Money Premium: ${referralLink}`, "Telegram")}
        >
          <Ionicons name="paper-plane" size={24} color="#fff" />
          <Text style={[styles.quickShareText, { color: "#fff" }]}>
            Telegram
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.quickShareItem, { backgroundColor: "#1877F2" }]}
          onPress={() => shareContent(`AI Money Premium - Quản lý tài chính thông minh: ${referralLink}`, "Facebook")}
        >
          <Ionicons name="logo-facebook" size={24} color="#fff" />
          <Text style={[styles.quickShareText, { color: "#fff" }]}>
            Facebook
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      {renderHeader()}
      
      <ScrollView 
        style={{ flex: 1 }} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 30 }}
      >
        <View style={styles.content}>
          <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
            Mẫu nội dung chia sẻ
          </Text>
          
          {SHARE_TEMPLATES.map(renderTemplateCard)}
          
          {renderContentPreview()}
          {renderCustomEditor()}
          {renderQuickShare()}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 60,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
  },
  templateCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 16,
  },
  templateHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  platformIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  templateInfo: {
    flex: 1,
  },
  templateTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  platformName: {
    fontSize: 14,
  },
  templatePreview: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  templateActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionBtn: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    flex: 0.48,
    justifyContent: "center",
  },
  actionText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  previewSection: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
  },
  previewContent: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  previewText: {
    fontSize: 14,
    lineHeight: 20,
  },
  previewActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  previewBtn: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    flex: 0.48,
    justifyContent: "center",
    borderWidth: 1,
  },
  previewBtnText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  customSection: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 20,
  },
  customHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  customTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  customEditor: {
    marginTop: 16,
  },
  customInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    minHeight: 120,
    marginBottom: 16,
  },
  customActions: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  customBtn: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    flex: 0.48,
    justifyContent: "center",
    borderWidth: 1,
  },
  customBtnText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  quickShareSection: {
    padding: 16,
    borderRadius: 16,
  },
  quickShareTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  quickShareGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  quickShareItem: {
    width: "48%",
    alignItems: "center",
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  quickShareText: {
    fontSize: 12,
    fontWeight: "500",
    marginTop: 6,
  },
});