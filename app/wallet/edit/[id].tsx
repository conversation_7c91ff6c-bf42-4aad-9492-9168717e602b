// File: app/wallet/edit/[id].tsx
// File này liên quan đến: context/ThemeContext.tsx, constants/WalletData.ts
// Đ<PERSON> cập nhật với tính năng chọn logo ngân hàng/ví điện tử và sử dụng ThemeContext

import { Text, View } from "@/components/Themed";
import {
  BANK_TEMPLATES,
  BankLogos,
  WALLET_COLORS,
  WALLET_TYPES,
  getFilteredTemplates,
  getWalletTypeName,
  isImageIcon
} from "@/constants/WalletData";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function EditWalletScreen() {
  const { id } = useLocalSearchParams();
  const walletId = typeof id === "string" ? id : "";
  const { t } = useLocalization();

  // Sử dụng đầy đủ các thuộc tính từ useTheme
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();

  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [name, setName] = useState("");
  const [balance, setBalance] = useState("");
  const [selectedType, setSelectedType] = useState(WALLET_TYPES[0].id);
  const [selectedColor, setSelectedColor] = useState(WALLET_COLORS[0]);
  const [selectedIcon, setSelectedIcon] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isNegative, setIsNegative] = useState(false); // Trạng thái số âm

  // State cho việc chọn icon/logo
  const [showLogoModal, setShowLogoModal] = useState(false);
  const [templateType, setTemplateType] = useState('bank');
  const [searchQuery, setSearchQuery] = useState('');

  // Tải thông tin ví từ Supabase
  useEffect(() => {
    const fetchWallet = async () => {
      if (!walletId) return;

      try {
        setIsLoading(true);
        const walletData = await WalletModel.getById(walletId);

        if (walletData) {
          setWallet(walletData);
          setName(walletData.name);
          
          // Kiểm tra nếu số dư là số âm
          if (walletData.balance < 0) {
            setIsNegative(true);
            setBalance(Math.abs(walletData.balance).toString()); // Lưu giá trị tuyệt đối
          } else {
            setIsNegative(false);
            setBalance(walletData.balance.toString());
          }
          
          setSelectedType(walletData.type);
          setSelectedColor(walletData.color);
          setSelectedIcon(walletData.icon || "");
          
          // Nếu ví thuộc loại bank hoặc ewallet, đặt templateType tương ứng
          if (walletData.type === 'bank' || walletData.type === 'ewallet') {
            setTemplateType('bank');
          } else {
            setTemplateType('cash');
          }
        }
      } catch (error) {
        console.error("Lỗi khi tải thông tin ví:", error);
        Alert.alert(t("common.error"), t("wallets.loadError"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchWallet();
  }, [walletId]);

  // Xử lý chuyển đổi trạng thái âm/dương
  const toggleNegative = () => {
    setIsNegative(!isNegative);
  };

  // Định dạng số dư hiển thị ở phần xem trước với dấu âm nếu cần
  const getFormattedBalance = () => {
    if (!balance) return "0đ";
    const numBalance = parseFloat(balance);
    return `${isNegative ? "-" : ""}${numBalance.toLocaleString()}đ`;
  };

  // Xử lý việc chọn icon từ modal
  const handleSelectIcon = (template) => {
    // Nếu đã chọn template là ví tiền mặt
    if (templateType === 'cash') {
      setSelectedIcon(template.icon);
      setSelectedType('cash');
    } 
    // Nếu đã chọn template là ngân hàng hoặc ví điện tử
    else {
      setSelectedIcon(template.icon);
      setSelectedType(template.type);
      // Cập nhật màu sắc nếu có màu từ template
      if (template.color) {
        setSelectedColor(template.color);
      }
    }
    
    setShowLogoModal(false);
  };

  // Show loading screen while fetching wallet data
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.editWallet")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // If wallet not found, show error screen
  if (!wallet) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.editWallet")}
          </Text>
          <View style={{ width: 40 }} />
        </View>
        <View
          style={[styles.emptyContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={themeColors.primary}
          />
          <Text
            style={{
              color: themeColors.secondaryText,
              fontSize: 16,
              marginTop: 12,
            }}
          >
            {t("wallets.walletNotFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleUpdate = async () => {
    // Validate inputs
    if (!name.trim()) {
      Alert.alert(t("common.error"), t("wallets.nameRequired"));
      return;
    }

    if (!balance.trim() || isNaN(Number(balance))) {
      Alert.alert(t("common.error"), t("wallets.balanceRequired"));
      return;
    }

    try {
      setIsSaving(true);

      // Tạo đối tượng ví cập nhật với số dư âm nếu cần
      const balanceValue = parseFloat(balance);
      const finalBalance = isNegative ? -balanceValue : balanceValue;
      
      const walletData = {
        name: name.trim(),
        balance: finalBalance,
        type: selectedType as "cash" | "bank" | "ewallet",
        color: selectedColor,
        icon: selectedIcon || "wallet-outline",
      };

      console.log("Updating wallet with data:", walletData);

      // Cập nhật ví trong database
      await WalletModel.update(walletId, walletData);

      // Hiển thị thông báo thành công và quay lại
      Alert.alert(t("common.success"), t("wallets.walletUpdated"), [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Lỗi khi cập nhật ví:", error);
      Alert.alert(t("common.error"), t("wallets.updateError"));
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      t("common.delete"),
      t("wallets.deleteConfirm"),
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              setIsLoading(true);
              await WalletModel.delete(walletId);
              Alert.alert(t("common.success"), t("wallets.walletDeleted"));
              // Navigate back to the wallet list
              router.push("/wallet");
            } catch (error) {
              console.error("Lỗi khi xóa ví:", error);
              Alert.alert(t("common.error"), t("wallets.deleteError"));
              setIsLoading(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Render từng item trong danh sách logo
  const renderTemplateItem = ({ item }) => {
    const isBankTemplate = typeof item.icon === 'string' && item.icon.endsWith('.png');
    const isSelected = selectedIcon === item.icon;
    
    return (
      <TouchableOpacity
        style={[
          styles.templateItem,
          {
            backgroundColor: themeColors.cardBackground,
            borderColor: isSelected ? item.color : themeColors.border,
            ...getShadowStyle('medium'),
            shadowColor: isSelected ? item.color : themeColors.shadowColor,
          }
        ]}
        onPress={() => handleSelectIcon(item)}
      >
        <View 
          style={[
            styles.templateIcon, 
            { 
              backgroundColor: item.color, 
              shadowColor: item.color,
              ...getShadowStyle('medium')
            }
          ]}
        >
          {isBankTemplate ? (
            <Image
              source={BankLogos[item.icon]}
              style={{ width: 30, height: 30 }}
              contentFit="contain"
            />
          ) : (
            <Ionicons name={item.icon} size={24} color="white" />
          )}
        </View>
        <Text style={[styles.templateName, { color: themeColors.text }]}>
          {item.name}
        </Text>
        {isSelected && (
          <View style={styles.selectedCheck}>
            <Ionicons name="checkmark-circle" size={22} color={item.color} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.editWallet")}
          </Text>
          <View style={[styles.headerButtonsContainer, { backgroundColor: 'transparent' }]}>
            <TouchableOpacity
              onPress={handleDelete}
              style={[
                styles.deleteHeaderButton,
                {
                  backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                  ...getShadowStyle('low'),
                  shadowColor: '#B71C1C',
                }
              ]}
            >
              <Ionicons name="trash-outline" size={20} color={themeColors.danger} />
            </TouchableOpacity>
            <TouchableOpacity 
              onPress={handleUpdate} 
              disabled={isSaving}
              style={[
                styles.saveButton,
                {
                  backgroundColor: isSaving ? (isDark ? "#375980" : "#BBDEFB") : themeColors.primary,
                  ...getShadowStyle('low')
                }
              ]}
            >
              <Text
                style={{
                  color: "white",
                  fontSize: 16,
                  fontWeight: "500",
                }}
              >
                {isSaving ? t("common.loading") : t("common.save")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Preview */}
          <View 
            style={[
              styles.previewCard, 
              getCardStyle('high')
            ]}
          >
            <View
              style={[
                styles.walletIcon, 
                { 
                  backgroundColor: selectedColor,
                  ...getShadowStyle('medium')
                }
              ]}
            >
              {isImageIcon(selectedIcon) ? (
                // Hiển thị logo ngân hàng/ví điện tử
                <Image
                  source={BankLogos[selectedIcon]}
                  style={{ width: 36, height: 36 }}
                  contentFit="contain"
                />
              ) : (
                // Hiển thị icon Ionicons cho các loại ví khác
                <Ionicons
                  name={
                    selectedIcon || (WALLET_TYPES.find((type) => type.id === selectedType)?.icon as any)
                  }
                  size={30}
                  color="white"
                />
              )}
            </View>
            <Text style={[styles.previewTitle, { color: themeColors.text }]}>
              {name || wallet.name}
            </Text>
            <Text 
              style={[
                styles.previewBalance, 
                { 
                  color: isNegative ? themeColors.danger : themeColors.secondaryText
                }
              ]}
            >
              {getFormattedBalance()}
            </Text>
          </View>

          {/* Form Fields */}
          <View 
            style={[
              styles.formSection, 
              getCardStyle('medium')
            ]}
          >
            <Text style={[styles.label, { color: themeColors.text }]}>
              {t("wallets.walletName")}
            </Text>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: themeColors.iconBackground,
                  color: themeColors.text,
                  borderColor: themeColors.border,
                  ...getShadowStyle('low')
                },
              ]}
              placeholder={t("wallets.enterWalletName")}
              placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
              value={name}
              onChangeText={setName}
            />

            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("wallets.balance")}
            </Text>
            
            {/* Input balance với nút chuyển đổi âm/dương - đã bỏ nền */}
            <View style={[styles.balanceInputContainer, { backgroundColor: 'transparent' }]}>
              <TouchableOpacity 
                style={[
                  styles.negativeToggle, 
                  { 
                    backgroundColor: isNegative 
                      ? themeColors.danger 
                      : themeColors.border,
                    borderColor: isNegative 
                      ? themeColors.dangerBorder 
                      : themeColors.border,
                    ...getShadowStyle('low')
                  }
                ]} 
                onPress={toggleNegative}
              >
                <Text style={{ 
                  color: isNegative 
                    ? "white" 
                    : themeColors.text, 
                  fontWeight: "bold",
                  fontSize: 18
                }}>
                  {isNegative ? "-" : "+"}
                </Text>
              </TouchableOpacity>
              
              <TextInput
                style={[
                  styles.balanceInput,
                  {
                    backgroundColor: themeColors.iconBackground,
                    color: themeColors.text,
                    borderColor: themeColors.border,
                    ...getShadowStyle('low')
                  },
                ]}
                placeholder={t("wallets.enterBalance")}
                placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                value={balance}
                onChangeText={setBalance}
                keyboardType="numeric"
              />
            </View>

            {/* Thêm phần chọn icon/logo */}
            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("wallets.icon")}
            </Text>

            {/* Hiển thị icon đã chọn và nút chọn logo mới */}
            <View style={[styles.iconSelector, { backgroundColor: 'transparent' }]}>
              <View style={[
                styles.selectedIconContainer, 
                { 
                  backgroundColor: themeColors.iconBackground, 
                  borderColor: themeColors.border,
                  ...getShadowStyle('low')
                }
              ]}>
                <View style={[
                  styles.iconPreview, 
                  { 
                    backgroundColor: selectedColor,
                    ...getShadowStyle('low')
                  }
                ]}>
                  {isImageIcon(selectedIcon) ? (
                    <Image
                      source={BankLogos[selectedIcon]}
                      style={{ width: 24, height: 24 }}
                      contentFit="contain"
                    />
                  ) : (
                    <Ionicons
                      name={
                        selectedIcon || (WALLET_TYPES.find((type) => type.id === selectedType)?.icon as any)
                      }
                      size={24}
                      color="white"
                    />
                  )}
                </View>
                <Text style={{ 
                  marginLeft: 10, 
                  color: themeColors.text,
                  flex: 1 
                }}>
                  {isImageIcon(selectedIcon) 
                    ? BANK_TEMPLATES.find(t => t.icon === selectedIcon)?.name || t("wallets.customIcon")
                    : t("wallets.defaultIcon")}
                </Text>
              </View>

              <TouchableOpacity
                style={[
                  styles.chooseIconButton, 
                  { 
                    backgroundColor: themeColors.primary,
                    ...getShadowStyle('medium')
                  }
                ]}
                onPress={() => setShowLogoModal(true)}
              >
                <Ionicons name="images-outline" size={20} color="white" style={{ marginRight: 6 }} />
                <Text style={{ color: "white", fontWeight: "600" }}>
                  {t("wallets.chooseIcon")}
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("wallets.walletType")}
            </Text>
            {/* Phần loại ví - đã xóa nền */}
            <View style={[styles.typeContainer, { backgroundColor: 'transparent' }]}>
              {WALLET_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.id}
                  style={[
                    styles.typeOption,
                    {
                      backgroundColor:
                        selectedType === type.id
                          ? isDark
                            ? `${selectedColor}30`
                            : `${selectedColor}20`
                          : themeColors.iconBackground,
                      borderColor:
                        selectedType === type.id
                          ? selectedColor
                          : themeColors.border,
                      ...getShadowStyle(selectedType === type.id ? 'medium' : 'low')
                    },
                  ]}
                  onPress={() => setSelectedType(type.id)}
                >
                  <Ionicons
                    name={type.icon as any}
                    size={24}
                    color={
                      selectedType === type.id
                        ? selectedColor
                        : themeColors.primary
                    }
                  />
                  <Text
                    style={[
                      styles.typeText,
                      {
                        color:
                          selectedType === type.id ? selectedColor : themeColors.text,
                        fontWeight: selectedType === type.id ? "600" : "normal",
                      },
                    ]}
                  >
                    {getWalletTypeName(type.id, t)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={[styles.label, { color: themeColors.text, marginTop: 16 }]}>
              {t("categories.color")}
            </Text>
            {/* Đã xóa phần nền background panel ở đây, để trực tiếp hiển thị các màu tròn trên nền chính của ứng dụng */}
            <View style={[styles.colorContainer, { backgroundColor: 'transparent' }]}>
              {WALLET_COLORS.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    selectedColor === color && styles.selectedColorOption,
                    { 
                      shadowColor: color,
                      shadowOpacity: selectedColor === color ? 0.6 : 0.3,
                      shadowOffset: { width: 0, height: 2 },
                      shadowRadius: 3,
                      elevation: selectedColor === color ? 5 : 4,
                    }
                  ]}
                  onPress={() => setSelectedColor(color)}
                >
                  {selectedColor === color && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Modal chọn logo */}
      <Modal
        visible={showLogoModal}
        transparent={false}
        animationType="slide"
        onRequestClose={() => setShowLogoModal(false)}
        statusBarTranslucent={true}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: themeColors.background }}>
          <View style={[
            styles.modalHeader,
            { 
              borderBottomColor: themeColors.border,
              paddingTop: 20,
              backgroundColor: 'transparent'
            }
          ]}>
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Chọn biểu tượng ví
            </Text>
            <TouchableOpacity
              onPress={() => setShowLogoModal(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>
          
          {/* Tab chọn loại biểu tượng */}
          <View style={[styles.templateTypeTabs, { 
            borderBottomColor: themeColors.border,
            backgroundColor: 'transparent'
          }]}>
            <TouchableOpacity
              style={[
                styles.templateTypeTab,
                templateType === 'bank' && {
                  borderBottomWidth: 3,
                  borderBottomColor: themeColors.primary
                }
              ]}
              onPress={() => setTemplateType('bank')}
            >
              <Text style={{ 
                color: templateType === 'bank' ? themeColors.primary : themeColors.primary,
                fontWeight: templateType === 'bank' ? '600' : 'normal'
              }}>
                Ngân hàng / Ví điện tử
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.templateTypeTab,
                templateType === 'cash' && {
                  borderBottomWidth: 3,
                  borderBottomColor: themeColors.primary
                }
              ]}
              onPress={() => setTemplateType('cash')}
            >
              <Text style={{ 
                color: templateType === 'cash' ? themeColors.primary : themeColors.primary,
                fontWeight: templateType === 'cash' ? '600' : 'normal'
              }}>
                Tiền mặt
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Khung tìm kiếm biểu tượng */}
          <View style={[
            styles.searchContainer,
            { 
              backgroundColor: themeColors.iconBackground,
              borderColor: themeColors.border
            }
          ]}>
            <Ionicons 
              name="search-outline" 
              size={20} 
              color={themeColors.primary} 
              style={{ marginRight: 8 }}
            />
            <TextInput
              style={{ 
                flex: 1, 
                color: themeColors.text,
                height: 40,
              }}
              placeholder="Tìm kiếm biểu tượng..."
              placeholderTextColor={isDark ? '#6D8AC3' : '#90CAF9'}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons 
                  name="close-circle" 
                  size={20} 
                  color={themeColors.primary} 
                />
              </TouchableOpacity>
            )}
          </View>
          
          <ScrollView style={[styles.modalBody, { backgroundColor: 'transparent' }]} keyboardShouldPersistTaps="handled">
            {/* Danh sách biểu tượng */}
            <View style={{ paddingBottom: 30, backgroundColor: 'transparent' }}>
              <FlatList
                data={getFilteredTemplates(templateType, searchQuery)}
                renderItem={renderTemplateItem}
                keyExtractor={item => item.id}
                numColumns={2}
                columnWrapperStyle={[styles.templateGrid, { backgroundColor: 'transparent' }]}
                scrollEnabled={false}
                ListEmptyComponent={
                  <View style={[
                    styles.emptySearchResults,
                    getCardStyle('medium')
                  ]}>
                    <Ionicons 
                      name="search-outline" 
                      size={24} 
                      color={themeColors.primary} 
                    />
                    <Text style={{ 
                      color: themeColors.secondaryText, 
                      marginTop: 10,
                      textAlign: 'center' 
                    }}>
                      Không tìm thấy biểu tượng phù hợp
                    </Text>
                  </View>
                }
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  previewCard: {
    padding: 24,
    borderRadius: 16,
    alignItems: "center",
    marginBottom: 20,
  },
  walletIcon: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  previewTitle: {
    fontSize: 22,
    fontWeight: "600",
    marginBottom: 8,
  },
  previewBalance: {
    fontSize: 26,
    fontWeight: "bold",
  },
  formSection: {
    marginVertical: 16,
    borderRadius: 16,
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  input: {
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  // Styles cho trường nhập balance
  balanceInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "transparent", // Đảm bảo nền trong suốt
  },
  negativeToggle: {
    width: 56,
    height: 54,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    marginRight: 8,
    borderWidth: 1,
  },
  balanceInput: {
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  typeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 12,
    backgroundColor: "transparent", // Đảm bảo nền trong suốt
  },
  typeOption: {
    flex: 1,
    alignItems: "center",
    padding: 16,
    borderRadius: 14,
    borderWidth: 2,
    marginHorizontal: 6,
  },
  typeText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: "500",
  },
  colorContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 16,
    justifyContent: "center",
    backgroundColor: "transparent", // Đảm bảo nền trong suốt
  },
  colorOption: {
    width: 44,
    height: 44,
    borderRadius: 22,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  selectedColorOption: {
    borderWidth: 3,
    borderColor: "white",
    transform: [{ scale: 1.1 }], // Hiệu ứng phóng to khi được chọn
  },
  headerButtonsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  deleteHeaderButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  // Styles cho việc chọn icon
  iconSelector: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  selectedIconContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    height: 54,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  iconPreview: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  chooseIconButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    height: 54,
    borderRadius: 12,
    justifyContent: "center",
  },
  // Modal styles
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    backgroundColor: 'transparent',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  modalBody: {
    padding: 16,
    flex: 1,
    backgroundColor: 'transparent',
  },
  templateTypeTabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    backgroundColor: 'transparent',
  },
  templateTypeTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 1,
    height: 44,
  },
  templateGrid: {
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  templateItem: {
    width: '48%',
    marginBottom: 16,
    padding: 12,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 2,
    position: 'relative',
  },
  templateIcon: {
    width: 50,
    height: 50,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  templateName: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedCheck: {
    position: 'absolute',
    top: 5,
    right: 5,
  },
  emptySearchResults: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 20,
    borderRadius: 16,
  },
});