// File: app/wallet/[id].tsx
// File này liên quan đến: context/ThemeContext.tsx, constants/WalletData.ts
// Màn hình chi tiết ví - đ<PERSON> cập nhật để sử dụng ThemeContext và WalletData.ts

import { Text, View } from "@/components/Themed";
import { BankLogos, isImageIcon } from "@/constants/WalletData";
import { useCurrency } from "@/context/CurrencyContext";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { TransactionModel, type Transaction } from "@/lib/models/transaction";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { useCategoryTranslation } from "@/utils/categoryTranslation";
import { formatDate } from "@/utils/dateFormatter";
import { getIconForWalletType } from "@/utils/iconHelper";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function WalletDetailScreen() {
  const { id } = useLocalSearchParams();
  const walletId = typeof id === "string" ? id : "";
  const translateCategoryName = useCategoryTranslation();
  
  // Sử dụng đầy đủ các thuộc tính từ useTheme
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();
  const { formatCurrency } = useCurrency();

  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hideBalance, setHideBalance] = useState(false);

  // Thêm state cho Toast
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const toastAnimation = useRef(new Animated.Value(0)).current;

  // Toggle hide balance
  const toggleHideBalance = () => {
    setHideBalance((prev) => !prev);
  };

  // Format số tiền
  const formatAmount = (amount: number) => {
    if (hideBalance) {
      return "•••••••";
    }
    return formatCurrency(amount);
  };

  // Hiển thị thông báo toast
  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);

    // Animation hiển thị toast
    Animated.sequence([
      // Hiện lên
      Animated.timing(toastAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      // Giữ 2 giây
      Animated.delay(2000),
      // Ẩn đi
      Animated.timing(toastAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowToast(false);
    });
  };

  // Xử lý thiết lập ví mặc định
  const handleSetDefaultWallet = async () => {
    if (!wallet) return;

    // Nếu ví này đã là mặc định, không cần thực hiện thêm
    if (wallet.is_default) {
      showToastMessage(t("wallets.alreadyDefault"));
      return;
    }

    try {
      setIsLoading(true);

      // Cập nhật ví mặc định trong cơ sở dữ liệu
      await WalletModel.update(wallet.id, { is_default: true });

      // Cập nhật state
      setWallet({
        ...wallet,
        is_default: true,
      });

      // Hiển thị thông báo thành công
      showToastMessage(t("wallets.defaultWalletSet"));
    } catch (error) {
      console.error("Lỗi khi đặt ví mặc định:", error);
      Alert.alert(t("common.error"), t("wallets.defaultWalletError"));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch wallet and transactions
  useEffect(() => {
    const fetchData = async () => {
      if (!walletId) {
        console.log("No walletId provided");
        return;
      }

      console.log("Loading data for wallet with ID:", walletId);

      try {
        setIsLoading(true);

        // Fetch wallet details
        const walletData = await WalletModel.getById(walletId);
        console.log("Result from getById:", walletData);
        setWallet(walletData);

        if (walletData) {
          // Fetch transactions for this wallet
          const transactionsData = await TransactionModel.getByWallet(walletId);
          console.log("Number of transactions:", transactionsData.length);
          setTransactions(transactionsData);
        } else {
          console.log("Wallet data not found");
        }
      } catch (error) {
        console.error("Error fetching wallet data:", error);
        Alert.alert(t("common.error"), t("wallets.loadError"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [walletId, t]);

  // Helper function to get icon based on wallet type
  const getWalletIcon = (type: string): string => {
    return getIconForWalletType(type);
  };

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        {/* Header - cập nhật theo style mới */}
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.walletDetails")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Wallet not found
  if (!wallet) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        {/* Header - cập nhật theo style mới */}
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.walletDetails")}
          </Text>
          <View style={{ width: 30 }} />
        </View>
        <View
          style={[styles.emptyContainer, { backgroundColor: "transparent" }]}
        >
          <Ionicons
            name="alert-circle-outline"
            size={60}
            color={themeColors.primary}
          />
          <Text
            style={{
              color: themeColors.primary,
              fontSize: 16,
              marginTop: 12,
            }}
          >
            {t("wallets.walletNotFound")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      {/* Toast Notification */}
      {showToast && (
        <Animated.View
          style={[
            styles.toastContainer,
            {
              backgroundColor: "#4CAF50", // Giữ lại màu xanh lá cây cho toast thông báo thành công
              transform: [
                {
                  translateY: toastAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-100, 0],
                  }),
                },
              ],
              opacity: toastAnimation,
              ...getShadowStyle('medium')
            },
          ]}
        >
          <Ionicons name="checkmark-circle" size={24} color="white" />
          <Text style={styles.toastMessage}>{toastMessage}</Text>
        </Animated.View>
      )}

      {/* Header - cập nhật theo style mới */}
      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("wallets.walletDetails")}
        </Text>
        <View style={{ width: 30 }} />
      </View>

      {/* ScrollView chứa toàn bộ nội dung */}
      <ScrollView
        style={{ backgroundColor: themeColors.background }}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Wallet Card */}
        <View
          style={[
            styles.walletCard,
            {
              backgroundColor: themeColors.cardBackground,
              borderWidth: 1,
              borderColor: themeColors.border,
              borderLeftWidth: 3,
              borderLeftColor: wallet.color,
              ...getShadowStyle('high')
            },
          ]}
        >
          {/* Biểu tượng ví mặc định */}
          {wallet.is_default && (
            <View style={styles.defaultBadge}>
              <Ionicons name="star" size={14} color="white" />
            </View>
          )}

          {/* Header ví đã được cải thiện */}
          <View
            style={[styles.walletHeader, { backgroundColor: "transparent" }]}
          >
            <View
              style={[
                styles.walletIconContainer,
                {
                  backgroundColor: wallet.color,
                  shadowColor: wallet.color,
                  ...getShadowStyle('medium')
                },
              ]}
            >
              {isImageIcon(wallet.icon) ? (
                // Hiển thị logo ngân hàng/ví điện tử
                <Image
                  source={BankLogos[wallet.icon]}
                  style={{ width: 30, height: 30 }}
                  contentFit="contain"
                />
              ) : (
                // Hiển thị icon Ionicons cho các loại ví khác
                <Ionicons
                  name={getWalletIcon(wallet.type) as any}
                  size={28}
                  color="white"
                />
              )}
            </View>
            <View style={{ flex: 1, backgroundColor: "transparent" }}>
              {/* Tên ví được cải tiến */}
              <View
                style={[
                  styles.walletNameContainer,
                  {
                    backgroundColor: "transparent",
                    borderBottomWidth: 1,
                    borderBottomColor: isDark
                      ? "rgba(255,255,255,0.1)"
                      : "rgba(25,118,210,0.1)",
                    paddingBottom: 6,
                    marginBottom: 6,
                  },
                ]}
              >
                <Text
                  style={[
                    styles.walletName,
                    { color: themeColors.text, fontSize: 22 },
                  ]}
                >
                  {wallet.name}
                </Text>
                <View
                  style={{
                    flexDirection: "row",
                    backgroundColor: "transparent",
                  }}
                >
                  <TouchableOpacity
                    style={[
                      styles.walletActionButton,
                      getIconContainerStyle(),
                      {
                        marginRight: 8,
                        ...getShadowStyle('low')
                      }
                    ]}
                    onPress={() => router.push(`/wallet/edit/${wallet.id}`)}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name="create-outline"
                      size={20}
                      color={themeColors.primary}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.walletActionButton,
                      {
                        backgroundColor: isDark ? themeColors.dangerBackground : "#FFEBEE",
                        borderWidth: 1,
                        borderColor: isDark ? themeColors.dangerBorder : "#FFCDD2",
                        ...getShadowStyle('low'),
                        shadowColor: isDark ? "#1A0A0A" : "#D32F2F",
                      },
                    ]}
                    onPress={() => {
                      Alert.alert(
                        t("wallets.deleteWallet"),
                        t("wallets.deleteWalletQuestion"),
                        [
                          {
                            text: t("common.cancel"),
                            style: "cancel",
                          },
                          {
                            text: t("common.delete"),
                            style: "destructive",
                            onPress: async () => {
                              try {
                                await WalletModel.delete(wallet.id);
                                router.back();
                              } catch (error) {
                                console.error("Lỗi khi xóa ví:", error);
                                Alert.alert(
                                  t("common.error"),
                                  t("wallets.deleteError")
                                );
                              }
                            },
                          },
                        ]
                      );
                    }}
                    activeOpacity={0.7}
                  >
                    <Ionicons
                      name="trash-outline"
                      size={20}
                      color={themeColors.danger}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              {/* Loại ví được cải tiến */}
              <View
                style={[
                  styles.walletTypeContainer,
                  { backgroundColor: "transparent" },
                ]}
              >
                <Text
                  style={[
                    styles.walletType,
                    {
                      color: themeColors.secondaryText,
                      backgroundColor: themeColors.iconBackground,
                      borderRadius: 12,
                      paddingHorizontal: 10,
                      paddingVertical: 3,
                      fontSize: 14,
                      overflow: "hidden",
                      borderWidth: 1,
                      borderColor: themeColors.border,
                    },
                  ]}
                >
                  {wallet.type === "cash"
                    ? t("wallets.cashType")
                    : wallet.type === "bank"
                    ? t("wallets.bankType")
                    : wallet.type === "ewallet"
                    ? t("wallets.ewalletType")
                    : wallet.type}
                </Text>
              </View>
            </View>
          </View>

          {/* Phần số dư (đã làm trong suốt) */}
          <View
            style={[styles.balanceSection, { backgroundColor: "transparent" }]}
          >
            <View
              style={[
                styles.balanceLabelContainer,
                { backgroundColor: "transparent" },
              ]}
            >
              <Text
                style={[
                  styles.balanceLabel,
                  { color: themeColors.primary },
                ]}
              >
                {t("wallets.balance")}
              </Text>
              <TouchableOpacity
                onPress={toggleHideBalance}
                style={[styles.eyeButton, { backgroundColor: "transparent" }]}
              >
                <Ionicons
                  name={hideBalance ? "eye-off-outline" : "eye-outline"}
                  size={18}
                  color={themeColors.primary}
                />
              </TouchableOpacity>
            </View>
            <Text
              style={[
                styles.balanceText,
                {
                  color: isDark ? themeColors.primary : wallet.color,
                  backgroundColor: "transparent",
                },
              ]}
            >
              {formatAmount(wallet.balance)}
            </Text>
          </View>

          {/* Thêm nút đặt ví mặc định */}
          <View
            style={[styles.walletFooter, { backgroundColor: "transparent" }]}
          >
            <TouchableOpacity
              style={[
                styles.defaultWalletButton,
                {
                  backgroundColor: wallet.is_default
                    ? "transparent"
                    : themeColors.iconBackground,
                  borderWidth: wallet.is_default ? 1 : 1,
                  borderColor: wallet.is_default ? "#FFB700" : themeColors.border,
                  opacity: wallet.is_default ? 0.7 : 1,
                  ...getShadowStyle('low')
                },
              ]}
              onPress={handleSetDefaultWallet}
              disabled={wallet.is_default}
            >
              <Ionicons
                name="star"
                size={16}
                color={wallet.is_default ? "#FFB700" : themeColors.primary}
                style={{ marginRight: 6 }}
              />
              <Text
                style={{
                  color: wallet.is_default ? "#FFB700" : themeColors.primary,
                  fontWeight: "500",
                  fontSize: 14,
                }}
              >
                {wallet.is_default
                  ? t("wallets.isDefaultWallet")
                  : t("wallets.setAsDefault")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Transactions List */}
        <View
          style={[styles.sectionHeader, { backgroundColor: "transparent" }]}
        >
          <View
            style={[
              styles.sectionTitleContainer,
              { backgroundColor: "transparent" },
            ]}
          >
            <Ionicons
              name="receipt-outline"
              size={20}
              color={themeColors.text}
              style={{ marginRight: 8 }}
            />
            <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
              {t("home.recentTransactions")}
            </Text>
          </View>

          {transactions.length > 0 && (
            <TouchableOpacity
              style={[styles.viewAllButton, { backgroundColor: "transparent" }]}
              onPress={() => router.push(`/transaction/list`)}
            >
              <Text style={{ color: themeColors.primary, fontSize: 14 }}>
                {t("home.viewAll")}
              </Text>
              <Ionicons name="chevron-forward" size={14} color={themeColors.primary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Transaction Card */}
        <View
          style={[
            styles.transactionsCard,
            getCardStyle('high')
          ]}
        >
          {transactions.length > 0 ? (
            transactions.slice(0, 5).map((transaction) => (
              <TouchableOpacity
                key={transaction.id}
                style={[
                  styles.transactionItem,
                  {
                    backgroundColor: themeColors.iconBackground,
                    borderWidth: 1,
                    borderColor: themeColors.border,
                    borderLeftWidth: 3,
                    borderLeftColor:
                      transaction.amount > 0 ? "#4CAF50" : themeColors.danger,
                    ...getShadowStyle('low')
                  },
                ]}
                onPress={() =>
                  router.push(`/transaction/${transaction.id}` as any)
                }
              >
                <View
                  style={[
                    styles.transactionLeft,
                    { backgroundColor: "transparent" },
                  ]}
                >
                  <View
                    style={[
                      styles.categoryIcon,
                      {
                        backgroundColor:
                          transaction.category?.color ||
                          (transaction.amount > 0 ? "#4CAF50" : "#FF5722"),
                        shadowColor:
                          transaction.category?.color ||
                          (transaction.amount > 0 ? "#4CAF50" : "#FF5722"),
                        ...getShadowStyle('medium')
                      },
                    ]}
                  >
                    <Ionicons
                      name={
                        (transaction.category?.icon as any) ||
                        (transaction.amount > 0
                          ? "arrow-up-circle"
                          : "arrow-down-circle")
                      }
                      size={16}
                      color="white"
                    />
                  </View>
                  <View style={{ flex: 1, backgroundColor: "transparent" }}>
                    <Text
                      style={[styles.transactionTitle, { color: themeColors.text }]}
                      numberOfLines={1}
                    >
                      {transaction.description ||
                        translateCategoryName(
                          String(transaction.category?.name)
                        )}
                    </Text>
                    <Text
                      style={[
                        styles.transactionDetails,
                        { color: themeColors.primary },
                      ]}
                    >
                      {formatDate(new Date(transaction.date))}
                    </Text>
                  </View>
                </View>
                <Text
                  style={[
                    styles.transactionAmount,
                    {
                      color:
                        transaction.amount < 0
                          ? themeColors.danger
                          : "#4CAF50",
                      backgroundColor: "transparent",
                    },
                  ]}
                >
                  {hideBalance ? "•••••••" : formatCurrency(transaction.amount)}
                </Text>
              </TouchableOpacity>
            ))
          ) : (
            <View
              style={[
                styles.emptyListContainer,
                {
                  backgroundColor: "transparent",
                },
              ]}
            >
              <View
                style={[
                  styles.emptyIconContainer,
                  {
                    backgroundColor: themeColors.iconBackground,
                    borderWidth: 1,
                    borderColor: themeColors.border,
                    ...getShadowStyle('low')
                  },
                ]}
              >
                <Ionicons
                  name="receipt-outline"
                  size={40}
                  color={themeColors.primary}
                />
              </View>
              <Text
                style={{
                  color: themeColors.primary,
                  fontSize: 16,
                  marginTop: 16,
                  textAlign: "center",
                  fontWeight: "500",
                }}
              >
                {t("wallets.noTransactions")}
              </Text>
              <Text
                style={{
                  color: themeColors.primary,
                  fontSize: 14,
                  marginTop: 8,
                  marginBottom: 16,
                  textAlign: "center",
                  paddingHorizontal: 20,
                }}
              >
                {t("wallets.addTransactionHint")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.addTransactionButton,
                  {
                    backgroundColor: themeColors.primary,
                    flexDirection: "row",
                    alignItems: "center",
                    ...getShadowStyle('medium')
                  },
                ]}
                onPress={() => router.push("/(tabs)/add")}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={18}
                  color="#FFF"
                  style={{ marginRight: 6 }}
                />
                <Text style={{ color: "#FFF", fontWeight: "600" }}>
                  {t("wallets.addTransaction")}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  // Header theo style mới (đồng nhất với các màn hình khác)
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  walletCard: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    padding: 16,
    borderRadius: 16,
    position: "relative", // Cho phép định vị biểu tượng mặc định
  },
  // Biểu tượng ví mặc định
  defaultBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#FFB700",
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderBottomLeftRadius: 8,
    borderTopRightRadius: 16,
    zIndex: 1,
  },
  walletHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  walletIconContainer: {
    width: 46,
    height: 46,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  // Container tên ví
  walletNameContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingRight: 4,
  },
  walletName: {
    fontWeight: "700",
  },
  // Loại ví
  walletTypeContainer: {
    alignItems: "flex-start",
  },
  walletType: {
    fontWeight: "500",
  },
  // Nút hành động (sửa/xóa)
  walletActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  balanceSection: {
    marginBottom: 16,
  },
  balanceLabelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 2,
  },
  balanceLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginRight: 6,
  },
  eyeButton: {
    padding: 2,
  },
  balanceText: {
    fontSize: 24,
    fontWeight: "700",
  },
  walletFooter: {
    marginTop: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  // Nút đặt ví mặc định
  defaultWalletButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
    justifyContent: "center",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 4,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  viewAllButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  // Card chứa danh sách giao dịch
  transactionsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 12,
  },
  transactionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  transactionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  transactionTitle: {
    fontSize: 15,
    fontWeight: "600",
  },
  transactionDetails: {
    fontSize: 13,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "700",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 60,
  },
  emptyListContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 30,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  addTransactionButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 12,
  },
  // Toast styles
  toastContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 999,
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    paddingTop: 30, // Để hiển thị dưới status bar
    paddingBottom: 16,
  },
  toastMessage: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
});