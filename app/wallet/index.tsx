// File: app/(tabs)/wallet/index.tsx
// File này liên quan đến: context/ThemeContext.tsx, constants/WalletData.ts, lib/models/wallet.ts, lib/models/wallet_position.ts
// File này đã được cập nhật để cải thiện tính năng sắp xếp ví

import { Text, View } from "@/components/Themed";
import {
  BankLogos,
  getWalletTypeName,
  isImageIcon
} from "@/constants/WalletData";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { TransactionModel } from "@/lib/models/transaction";
import { WalletModel, type Wallet } from "@/lib/models/wallet";
import { WalletPositionModel } from "@/lib/models/wallet_position";
import { getIconForWalletType } from "@/utils/iconHelper";
import { Ionicons } from "@expo/vector-icons";
import { useFocusEffect } from "@react-navigation/native";
import { Image } from "expo-image";
import { router } from "expo-router";
import * as React from "react";
import {
  ActivityIndicator,
  Alert,
  Animated,
  FlatList,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

// Biến toàn cục để lưu trữ tùy chọn người dùng
const UserPreferences = {
  walletViewMode: "grid" as "list" | "grid",
};

export default function WalletListScreen() {
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  const isMounted = React.useRef(true);

  const [wallets, setWallets] = React.useState<Wallet[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [selectedWalletId, setSelectedWalletId] = React.useState<string | null>(null);
  const [hideBalance, setHideBalance] = React.useState(false);
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [viewMode, setViewMode] = React.useState<"list" | "grid">(UserPreferences.walletViewMode);

  const [defaultWalletId, setDefaultWalletId] = React.useState<string | null>(null);

  // Thêm state để lưu view mode gốc khi vào chế độ sắp xếp
  const [originalViewMode, setOriginalViewMode] = React.useState<"list" | "grid">(UserPreferences.walletViewMode);
  const [isSortMode, setIsSortMode] = React.useState(false);
  const [walletPositionsChanged, setWalletPositionsChanged] = React.useState(false);

  const [showToast, setShowToast] = React.useState(false);
  const [toastMessage, setToastMessage] = React.useState("");
  const toastAnimation = React.useRef(new Animated.Value(0)).current;

  const [summary, setSummary] = React.useState({
    moneyIn: 0,
    moneyOut: 0,
    currentMonth: new Date().getMonth() + 1,
    currentYear: new Date().getFullYear(),
  });

  const containerRef = React.useRef<View>(null);
  const scrollViewRef = React.useRef<ScrollView>(null);
  
  const sortColor = "#FF9800";

  React.useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Tạo các mảng riêng cho từng loại ví - chỉ sử dụng khi KHÔNG ở chế độ sắp xếp
  const bankWallets = React.useMemo(() => {
    if (isSortMode) return [];
    return wallets.filter(wallet => wallet.type === "bank");
  }, [wallets, isSortMode]);
  
  const ewalletWallets = React.useMemo(() => {
    if (isSortMode) return [];
    return wallets.filter(wallet => wallet.type === "ewallet");
  }, [wallets, isSortMode]);
  
  const cashWallets = React.useMemo(() => {
    if (isSortMode) return [];
    return wallets.filter(wallet => wallet.type === "cash");
  }, [wallets, isSortMode]);

  const totalBalance = React.useMemo(() => {
    return wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
  }, [wallets]);

  const formatCurrency = (amount: number) => {
    if (hideBalance) {
      return "•••••••";
    }
    return amount.toLocaleString() + "đ";
  };

  const toggleHideBalance = () => {
    setHideBalance((prev) => !prev);
  };

  const handleOutsideTouch = React.useCallback(() => {
    if (selectedWalletId) {
      setSelectedWalletId(null);
    }
  }, [selectedWalletId]);

  const toggleViewMode = React.useCallback(() => {
    // Không cho phép thay đổi view mode khi đang sắp xếp
    if (isSortMode) return;
    
    const newMode = viewMode === "list" ? "grid" : "list";
    setViewMode(newMode);
    UserPreferences.walletViewMode = newMode;
  }, [viewMode, isSortMode]);

  const handleSaveSortOrder = React.useCallback(async () => {
    try {
      console.log("Bắt đầu lưu thứ tự ví mới vào bảng riêng");
      setIsLoading(true);

      const walletIds = wallets.map((wallet) => wallet.id);
      await WalletPositionModel.savePositions(walletIds);

      showToastMessage(t("wallets.sortOrderSaved"));
      setWalletPositionsChanged(false);
      setIsSortMode(false);
      
      // Khôi phục view mode gốc
      setViewMode(originalViewMode);
      UserPreferences.walletViewMode = originalViewMode;

      console.log("Lưu thứ tự ví mới thành công và khôi phục view mode:", originalViewMode);
    } catch (error) {
      console.error("Lỗi khi lưu thứ tự ví mới:", error);
      Alert.alert(t("common.error"), t("wallets.updateError"));
      setIsSortMode(false);
      // Khôi phục view mode ngay cả khi có lỗi
      setViewMode(originalViewMode);
      UserPreferences.walletViewMode = originalViewMode;
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [wallets, t, originalViewMode]);

  const showToastMessage = React.useCallback((message: string) => {
    setToastMessage(message);
    setShowToast(true);

    Animated.sequence([
      Animated.timing(toastAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.delay(2000),
      Animated.timing(toastAnimation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (isMounted.current) {
        setShowToast(false);
      }
    });
  }, [toastAnimation]);

  const toggleEditMode = React.useCallback(() => {
    if (isSortMode) {
      handleSaveSortOrder();
      return;
    }

    setIsEditMode(prev => !prev);
    if (isEditMode) {
      setSelectedWalletId(null);
    }
  }, [isSortMode, isEditMode, handleSaveSortOrder]);

  const toggleSortMode = React.useCallback(() => {
    if (isEditMode) {
      setIsEditMode(false);
    }

    if (selectedWalletId) {
      setSelectedWalletId(null);
    }

    if (isSortMode && walletPositionsChanged) {
      handleSaveSortOrder();
      return;
    }

    if (!isSortMode) {
      // Bắt đầu chế độ sắp xếp: lưu view mode hiện tại và chuyển sang list
      setOriginalViewMode(viewMode);
      setViewMode("list");
      console.log("Bắt đầu sắp xếp: lưu view mode", viewMode, "và chuyển sang list");
    } else {
      // Kết thúc chế độ sắp xếp: khôi phục view mode gốc
      setViewMode(originalViewMode);
      UserPreferences.walletViewMode = originalViewMode;
      console.log("Kết thúc sắp xếp: khôi phục view mode", originalViewMode);
    }

    setIsSortMode(prev => !prev);

    if (!isSortMode) {
      setWalletPositionsChanged(false);
    }
  }, [isEditMode, selectedWalletId, isSortMode, walletPositionsChanged, handleSaveSortOrder, viewMode, originalViewMode]);

  // Hàm di chuyển ví - được cập nhật để hoạt động với toàn bộ danh sách khi sắp xếp
  const moveWalletUp = React.useCallback((index: number) => {
    try {
      if (index <= 0) {
        console.log("Không thể di chuyển lên khi đã ở vị trí đầu tiên");
        showToastMessage(t("wallets.cannotMoveUp"));
        return;
      }

      const newWallets = [...wallets];
      const temp = newWallets[index];
      newWallets[index] = newWallets[index - 1];
      newWallets[index - 1] = temp;

      setWallets(newWallets);
      setWalletPositionsChanged(true);
      
      console.log("Di chuyển ví lên thành công, index:", index);
    } catch (error) {
      console.error("Lỗi khi di chuyển ví lên:", error);
    }
  }, [wallets, showToastMessage, t]);

  const moveWalletDown = React.useCallback((index: number) => {
    try {
      if (index >= wallets.length - 1) {
        console.log("Không thể di chuyển xuống khi đã ở vị trí cuối cùng");
        showToastMessage(t("wallets.cannotMoveDown"));
        return;
      }

      const newWallets = [...wallets];
      const temp = newWallets[index];
      newWallets[index] = newWallets[index + 1];
      newWallets[index + 1] = temp;

      setWallets(newWallets);
      setWalletPositionsChanged(true);
      
      console.log("Di chuyển ví xuống thành công, index:", index);
    } catch (error) {
      console.error("Lỗi khi di chuyển ví xuống:", error);
    }
  }, [wallets, showToastMessage, t]);

  const fetchMonthlyStats = React.useCallback(async () => {
    try {
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentYear = now.getFullYear();

      const stats = await TransactionModel.getMonthlyStats(currentMonth, currentYear);

      if (isMounted.current) {
        setSummary({
          moneyIn: stats.income,
          moneyOut: stats.expense,
          currentMonth,
          currentYear,
        });
      }
    } catch (error) {
      console.error("Lỗi khi lấy thống kê thu chi:", error);
    }
  }, []);

  const fetchWallets = React.useCallback(async () => {
    try {
      setIsLoading(true);

      const walletsData = await WalletModel.getAll();

      let walletPositions = [];
      try {
        walletPositions = await WalletPositionModel.getAll();
      } catch (error) {
        console.log("Không có dữ liệu vị trí hoặc lỗi:", error);
      }

      const positionMap = {};
      walletPositions.forEach((pos) => {
        positionMap[pos.wallet_id] = pos.position;
      });

      const sortedWallets = [...walletsData].sort((a, b) => {
        const posA = positionMap[a.id] !== undefined ? positionMap[a.id] : 999;
        const posB = positionMap[b.id] !== undefined ? positionMap[b.id] : 999;
        if (posA !== 999 || posB !== 999) {
          return posA - posB;
        }
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      });

      if (isMounted.current) {
        setWallets(sortedWallets);

        const defaultWallet = sortedWallets.find((wallet) => wallet.is_default);
        if (defaultWallet) {
          setDefaultWalletId(defaultWallet.id);
        } else {
          setDefaultWalletId(null);
        }

        await fetchMonthlyStats();
      }
    } catch (error) {
      console.error("Lỗi khi tải danh sách ví:", error);
      if (isMounted.current) {
        Alert.alert(t("common.error"), t("wallets.loadError"));
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [t, fetchMonthlyStats]);

  const handleDeleteWallet = React.useCallback(async (walletId: string) => {
    const isDefault = walletId === defaultWalletId;

    const message = isDefault
      ? t("wallets.deleteDefaultWalletConfirm")
      : t("wallets.deleteConfirm");

    Alert.alert(
      t("common.delete"),
      message,
      [
        {
          text: t("common.cancel"),
          style: "cancel",
        },
        {
          text: t("common.delete"),
          style: "destructive",
          onPress: async () => {
            try {
              setIsLoading(true);
              await WalletModel.delete(walletId);
              
              if (isMounted.current) {
                const updatedWallets = wallets.filter((w) => w.id !== walletId);
                setWallets(updatedWallets);

                if (isDefault) {
                  setDefaultWalletId(null);
                }

                showToastMessage(t("wallets.walletDeleted"));
              }
            } catch (error) {
              console.error("Lỗi khi xóa ví:", error);
              if (isMounted.current) {
                Alert.alert(t("common.error"), t("wallets.deleteError"));
              }
            } finally {
              if (isMounted.current) {
                setIsLoading(false);
              }
            }
          },
        },
      ],
      { cancelable: true }
    );
  }, [defaultWalletId, t, wallets, showToastMessage]);

  const handleSetDefaultWallet = React.useCallback(async (walletId: string) => {
    try {
      setIsLoading(true);

      if (walletId === defaultWalletId) {
        showToastMessage(t("wallets.alreadyDefault"));
        if (isMounted.current) {
          setIsLoading(false);
        }
        return;
      }

      await WalletModel.update(walletId, { is_default: true });

      if (isMounted.current) {
        setDefaultWalletId(walletId);

        const updatedWallets = wallets.map((wallet) => ({
          ...wallet,
          is_default: wallet.id === walletId,
        }));

        setWallets(updatedWallets);
        showToastMessage(t("wallets.defaultWalletSet"));
      }
    } catch (error) {
      console.error("Lỗi khi đặt ví mặc định:", error);
      if (isMounted.current) {
        Alert.alert(t("common.error"), t("wallets.defaultWalletError"));
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [defaultWalletId, t, wallets, showToastMessage]);

  const getBalanceColor = React.useCallback((balance: number) => {
    if (balance < 0) {
      return themeColors.danger;
    }
    return "#4CAF50";
  }, [themeColors.danger]);

  const handlePress = React.useCallback((walletId: string) => {
    if (isEditMode || isSortMode) {
      return;
    }
    router.push(`/wallet/${walletId}`);
  }, [isEditMode, isSortMode]);

  const handleEdit = React.useCallback((walletId: string) => {
    router.push(`/wallet/edit/${walletId}`);
  }, []);

  const handleDelete = React.useCallback((walletId: string) => {
    handleDeleteWallet(walletId);
  }, [handleDeleteWallet]);

  const handleSetDefault = React.useCallback((walletId: string) => {
    handleSetDefaultWallet(walletId);
  }, [handleSetDefaultWallet]);

  // Cập nhật renderWalletItem để hiển thị loại ví khi ở chế độ sắp xếp
  const renderWalletItem = React.useCallback(({
    item,
    index,
    walletType,
  }: {
    item: Wallet;
    index: number;
    walletType?: string;
  }) => {
    try {
      const isDefault = item.is_default || item.id === defaultWalletId;
      
      // Khi ở chế độ sắp xếp, kiểm tra vị trí trong toàn bộ danh sách
      const isFirst = isSortMode ? index === 0 : false;
      const isLast = isSortMode ? index === wallets.length - 1 : false;

      // Khi không ở chế độ sắp xếp, kiểm tra theo nhóm
      if (!isSortMode && walletType) {
        if (walletType === "bank") {
          const isFirstInGroup = index === 0;
          const isLastInGroup = index === bankWallets.length - 1;
        } else if (walletType === "ewallet") {
          const isFirstInGroup = index === 0;
          const isLastInGroup = index === ewalletWallets.length - 1;
        } else if (walletType === "cash") {
          const isFirstInGroup = index === 0;
          const isLastInGroup = index === cashWallets.length - 1;
        }
      }

      const hasBankLogo = isImageIcon(item.icon);
      const balanceColor = getBalanceColor(item.balance);

      const renderWalletIcon = () => {
        if (hasBankLogo && BankLogos[item.icon]) {
          return (
            <Image
              source={BankLogos[item.icon]}
              style={{ width: 24, height: 24 }}
              contentFit="contain"
            />
          );
        } else {
          return (
            <Ionicons
              name={hasBankLogo ? "image" : (getIconForWalletType(item.type) as any)}
              size={22}
              color="white"
            />
          );
        }
      };

      // Khi ở chế độ sắp xếp, luôn hiển thị dạng list
      const currentViewMode = isSortMode ? "list" : viewMode;

      if (currentViewMode === "grid") {
        return (
          <Pressable
            style={({ pressed }) => [
              styles.walletItemGrid,
              {
                backgroundColor: themeColors.cardBackground,
                marginLeft: index % 2 === 0 ? 0 : 6,
                marginRight: index % 2 === 0 ? 6 : 0,
                borderWidth: 1,
                borderColor: themeColors.border,
                borderLeftWidth: 3,
                borderLeftColor: item.color,
                transform: [{ scale: pressed ? 0.98 : 1 }],
                ...getShadowStyle('medium')
              },
            ]}
            onPress={() => handlePress(item.id)}
          >
            <View style={[styles.walletHeader, { backgroundColor: "transparent" }]}>
              <View
                style={[
                  styles.walletIconContainer,
                  {
                    backgroundColor: item.color,
                    shadowColor: item.color,
                    ...getShadowStyle('medium')
                  },
                ]}
              >
                {renderWalletIcon()}
              </View>
              <View style={{ backgroundColor: "transparent", flex: 1 }}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    backgroundColor: "transparent",
                  }}
                >
                  <Text
                    style={[
                      styles.walletName,
                      {
                        color: themeColors.text,
                        fontWeight: "700",
                      },
                    ]}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {item.name}
                  </Text>

                  {isDefault && (
                    <View style={styles.defaultBadgeSmall}>
                      <Ionicons name="star" size={12} color="white" />
                    </View>
                  )}
                </View>
                <Text
                  style={{ 
                    color: hideBalance ? (isDark ? item.color : item.color) : balanceColor,
                    fontSize: 13,
                    fontWeight: "600"
                  }}
                  numberOfLines={1}
                >
                  {formatCurrency(item.balance)}
                </Text>
              </View>
            </View>

            {isEditMode && (
              <View
                style={[
                  styles.swipeActions,
                  {
                    backgroundColor: themeColors.iconBackground,
                    position: "absolute",
                    bottom: 4,
                    left: 4,
                    right: 4,
                    borderRadius: 8,
                    padding: 4,
                    zIndex: 10,
                    flexDirection: "row",
                    justifyContent: "space-evenly",
                    borderWidth: 1,
                    borderColor: themeColors.border,
                    alignItems: "center",
                  },
                ]}
              >
                <Pressable
                  style={({ pressed }) => [
                    styles.iconButton,
                    { 
                      opacity: isDefault ? 0.5 : pressed ? 0.7 : 1,
                      width: 32,
                      height: 32,
                      justifyContent: "center",
                      alignItems: "center",
                    },
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handleSetDefault(item.id);
                  }}
                  disabled={isDefault}
                >
                  <Ionicons name="star" size={18} color={themeColors.primary} />
                </Pressable>

                <Pressable
                  style={({ pressed }) => [
                    styles.iconButton,
                    {
                      opacity: pressed ? 0.7 : 1,
                      width: 32,
                      height: 32,
                      justifyContent: "center",
                      alignItems: "center",
                    },
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handleEdit(item.id);
                  }}
                >
                  <Ionicons
                    name="create-outline"
                    size={18}
                    color={themeColors.primary}
                  />
                </Pressable>

                <Pressable
                  style={({ pressed }) => [
                    styles.iconButton,
                    {
                      opacity: pressed ? 0.7 : 1,
                      width: 32,
                      height: 32,
                      justifyContent: "center",
                      alignItems: "center",
                    },
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handleDelete(item.id);
                  }}
                >
                  <Ionicons name="trash-outline" size={18} color={themeColors.danger} />
                </Pressable>
              </View>
            )}
          </Pressable>
        );
      } else {
        // List view - thêm hiển thị loại ví khi ở chế độ sắp xếp
        return (
          <TouchableOpacity
            style={[
              styles.walletItemList,
              {
                backgroundColor: themeColors.cardBackground,
                borderWidth: 1,
                borderColor: isSortMode
                  ? "rgba(255,150,0,0.3)"
                  : themeColors.border,
                borderLeftWidth: 3,
                borderLeftColor: item.color,
                ...getShadowStyle('medium')
              },
            ]}
            onPress={() => handlePress(item.id)}
            activeOpacity={0.7}
            key={item.id}
          >
            {isDefault && (
              <View style={styles.defaultBadge}>
                <Ionicons name="star" size={14} color="white" />
              </View>
            )}

            {isSortMode && (
              <View style={styles.sortButtonsList}>
                <TouchableOpacity
                  style={[styles.sortButton, isFirst && styles.disabledButton]}
                  onPress={() => moveWalletUp(index)}
                  disabled={isFirst}
                >
                  <Ionicons
                    name="chevron-up"
                    size={24}
                    color={
                      isFirst
                        ? isDark
                          ? "#555"
                          : "#ccc"
                        : isDark
                        ? "#fff"
                        : "#555"
                    }
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.sortButton, isLast && styles.disabledButton]}
                  onPress={() => moveWalletDown(index)}
                  disabled={isLast}
                >
                  <Ionicons
                    name="chevron-down"
                    size={24}
                    color={
                      isLast
                        ? isDark
                          ? "#555"
                          : "#ccc"
                        : isDark
                        ? "#fff"
                        : "#555"
                    }
                  />
                </TouchableOpacity>
              </View>
            )}

            <View
              style={[
                styles.walletIconContainer,
                {
                  backgroundColor: item.color,
                  shadowColor: item.color,
                  ...getShadowStyle('medium'),
                  marginLeft: isSortMode ? 10 : undefined,
                },
              ]}
            >
              {renderWalletIcon()}
            </View>

            <View
              style={{
                flex: 1,
                flexDirection: "row",
                justifyContent: "space-between",
                alignItems: "center",
                backgroundColor: "transparent",
                marginLeft: 10,
              }}
            >
              <View style={{ backgroundColor: "transparent" }}>
                <View style={{ 
                  flexDirection: "row", 
                  alignItems: "center", 
                  backgroundColor: "transparent" 
                }}>
                  <Text
                    style={[
                      styles.walletName,
                      {
                        color: themeColors.text,
                        fontWeight: "600",
                      },
                    ]}
                  >
                    {item.name}
                  </Text>
                  
                  {/* Hiển thị loại ví khi ở chế độ sắp xếp */}
                  {isSortMode && (
                    <View style={[
                      styles.walletTypeBadge,
                      { backgroundColor: item.color }
                    ]}>
                      <Text style={styles.walletTypeText}>
                        {getWalletTypeName(item.type)}
                      </Text>
                    </View>
                  )}
                </View>
                
                <Text
                  style={{ 
                    color: hideBalance ? (isDark ? item.color : item.color) : balanceColor,
                    fontSize: 13,
                    fontWeight: "600"
                  }}
                >
                  {formatCurrency(item.balance)}
                </Text>
              </View>
            </View>

            {isEditMode && (
              <View
                style={{
                  backgroundColor: "transparent",
                  marginLeft: 8,
                  alignItems: "center",
                }}
              >
                <TouchableOpacity
                  style={{ 
                    padding: 8, 
                    opacity: isDefault ? 0.5 : 1,
                    alignItems: "center",
                  }}
                  onPress={(e) => {
                    e.stopPropagation();
                    handleSetDefault(item.id);
                  }}
                  disabled={isDefault}
                >
                  <Ionicons name="star" size={22} color={themeColors.primary} />
                </TouchableOpacity>

                <View
                  style={{
                    flexDirection: "row",
                    backgroundColor: "transparent",
                  }}
                >
                  <TouchableOpacity 
                    style={{ padding: 8 }} 
                    onPress={(e) => {
                      e.stopPropagation();
                      handleEdit(item.id);
                    }}
                  >
                    <Ionicons
                      name="create-outline"
                      size={22}
                      color={themeColors.primary}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={{ padding: 8 }} 
                    onPress={(e) => {
                      e.stopPropagation();
                      handleDelete(item.id);
                    }}
                  >
                    <Ionicons name="trash-outline" size={22} color={themeColors.danger} />
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </TouchableOpacity>
        );
      }
    } catch (error) {
      console.error("Lỗi khi render wallet item:", error);
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: themeColors.danger,
            margin: 8,
            borderRadius: 8,
          }}
        >
          <Text style={{ color: "white" }}>Không thể hiển thị ví</Text>
        </View>
      );
    }
  }, [
    defaultWalletId, 
    isSortMode, 
    wallets.length, 
    viewMode, 
    isEditMode, 
    getBalanceColor, 
    handlePress, 
    handleEdit, 
    handleDelete, 
    handleSetDefault, 
    themeColors, 
    isDark, 
    getShadowStyle,
    hideBalance,
    moveWalletUp,
    moveWalletDown,
    bankWallets.length,
    ewalletWallets.length,
    cashWallets.length
  ]);

  const renderWalletList = React.useCallback((wallets: Wallet[], type: string, title: string, icon: string) => {
    if (wallets.length === 0 && !isEditMode) {
      return null;
    }

    return (
      <View style={{ marginBottom: 20, backgroundColor: "transparent" }} key={`section-${type}`}>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            paddingHorizontal: 16,
            marginBottom: 12,
            backgroundColor: "transparent",
          }}
        >
          <View
            style={{
              width: 4,
              height: 16,
              backgroundColor: themeColors.primary,
              marginRight: 8,
              borderRadius: 2,
            }}
          />
          <Ionicons name={icon as any} size={20} color={themeColors.primary} style={{ marginRight: 8 }} />
          <Text
            style={{
              color: themeColors.primary,
              fontSize: 18,
              fontWeight: "bold",
            }}
          >
            {title} ({wallets.length})
          </Text>
        </View>

        <FlatList
          data={wallets}
          keyExtractor={(item) => item.id}
          renderItem={({item, index}) => renderWalletItem({item, index, walletType: type})}
          contentContainerStyle={{
            paddingHorizontal: 16,
            paddingBottom: 16,
            paddingTop: 4,
            backgroundColor: "transparent",
          }}
          showsVerticalScrollIndicator={false}
          numColumns={viewMode === "grid" ? 2 : 1}
          key={`${type}-${viewMode}`}
          scrollEnabled={false}
          columnWrapperStyle={
            viewMode === "grid" ? styles.gridColumnWrapper : undefined
          }
          extraData={[
            viewMode,
            isEditMode,
            hideBalance,
            defaultWalletId,
            isSortMode,
          ]}
          ListEmptyComponent={isEditMode ? (
            <View
              style={[
                styles.emptyContainer,
                { backgroundColor: "transparent", paddingVertical: 16 },
              ]}
            >
              <Ionicons name={icon as any} size={30} color={themeColors.primary} />
              <Text
                style={{
                  color: themeColors.secondaryText,
                  fontSize: 14,
                  marginTop: 8,
                  textAlign: "center",
                }}
              >
                {`Bạn chưa có ${title.toLowerCase()}.`}
              </Text>
              <TouchableOpacity
                style={[
                  styles.emptyButton,
                  { backgroundColor: themeColors.primary, marginTop: 12, borderRadius: 12 },
                ]}
                onPress={() => router.push("/wallet/add")}
              >
                <Text style={{ color: "#FFF", fontWeight: "600" }}>
                  Thêm mới
                </Text>
              </TouchableOpacity>
            </View>
          ) : null}
        />
      </View>
    );
  }, [isEditMode, viewMode, hideBalance, defaultWalletId, isSortMode, renderWalletItem, themeColors]);

  // Render danh sách ví trong chế độ sắp xếp - tất cả ví trong một list
  const renderSortModeWalletList = React.useCallback(() => {
    return (
      <View style={{ marginBottom: 20, backgroundColor: "transparent" }}>
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            paddingHorizontal: 16,
            marginBottom: 12,
            backgroundColor: "transparent",
          }}
        >
          <View
            style={{
              width: 4,
              height: 16,
              backgroundColor: sortColor,
              marginRight: 8,
              borderRadius: 2,
            }}
          />
          <Ionicons name="reorder-two-outline" size={20} color={sortColor} style={{ marginRight: 8 }} />
          <Text
            style={{
              color: sortColor,
              fontSize: 18,
              fontWeight: "bold",
            }}
          >
            {t("wallets.sortAllWallets")} ({wallets.length})
          </Text>
        </View>

        <FlatList
          data={wallets}
          keyExtractor={(item) => item.id}
          renderItem={({item, index}) => renderWalletItem({item, index})}
          contentContainerStyle={{
            paddingHorizontal: 16,
            paddingBottom: 16,
            paddingTop: 4,
            backgroundColor: "transparent",
          }}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          extraData={[
            isEditMode,
            hideBalance,
            defaultWalletId,
            isSortMode,
          ]}
        />
      </View>
    );
  }, [wallets, renderWalletItem, isEditMode, hideBalance, defaultWalletId, isSortMode, sortColor, t]);

  useFocusEffect(
    React.useCallback(() => {
      console.log("Wallet screen in focus, refreshing data");
      isMounted.current = true;
      fetchWallets();
      setSelectedWalletId(null);
      setIsEditMode(false);
      setIsSortMode(false);
      setWalletPositionsChanged(false);
      return () => {
        isMounted.current = false;
      };
    }, [fetchWallets])
  );

  React.useEffect(() => {
    fetchWallets();
  }, [fetchWallets]);

  const walletSections = React.useMemo(() => [
    {
      type: "bank",
      data: bankWallets,
      title: "Tài khoản ngân hàng",
      icon: "business-outline"
    },
    {
      type: "ewallet",
      data: ewalletWallets,
      title: "Ví điện tử",
      icon: "phone-portrait-outline"
    },
    {
      type: "cash",
      data: cashWallets,
      title: "Tiền mặt",
      icon: "wallet-outline"
    }
  ], [bankWallets, ewalletWallets, cashWallets]);

  if (isLoading) {
    return (
      <SafeAreaView
        edges={["top"]}
        style={[styles.container, { backgroundColor: themeColors.background }]}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.title")}
          </Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={themeColors.primary} />
          <Text style={{ marginTop: 12, color: themeColors.text }}>
            {t("common.loading")}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      {showToast && (
        <Animated.View
          style={[
            styles.toastContainer,
            {
              backgroundColor: "#4CAF50",
              top: 56,
              transform: [
                {
                  translateY: toastAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-100, 0],
                  }),
                },
              ],
              opacity: toastAnimation,
            },
          ]}
        >
          <Ionicons name="checkmark-circle" size={24} color="white" />
          <Text style={styles.toastMessage}>{toastMessage}</Text>
        </Animated.View>
      )}

      {isSortMode && (
        <View
          style={[
            styles.sortModeBar,
            {
              backgroundColor: sortColor,
              top: 56,
            },
          ]}
        >
          <Ionicons
            name="information-circle-outline"
            size={24}
            color="#FFF"
          />
          <Text style={styles.sortModeText}>
            {t("wallets.useSortArrows")}
          </Text>
        </View>
      )}

      <View style={[styles.header, { backgroundColor: themeColors.background }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={themeColors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: themeColors.text }]}>
          {t("wallets.title")}
        </Text>
        <View style={styles.headerRightButtons}>
          <TouchableOpacity 
            onPress={toggleViewMode}
            style={{ opacity: isSortMode ? 0.5 : 1 }}
            disabled={isSortMode}
          >
            <Ionicons
              name={viewMode === "list" ? "grid-outline" : "list-outline"}
              size={28}
              color={isSortMode ? themeColors.secondaryText : themeColors.primary}
            />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        ref={scrollViewRef}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ 
          flexGrow: 1,
          paddingTop: isSortMode ? 48 : 0,
        }}
        scrollEnabled={true}
      >
        <View
          style={{
            paddingHorizontal: 16,
            marginBottom: 16,
            backgroundColor: "transparent",
          }}
        >
          <View
            style={[
              {
                backgroundColor: themeColors.cardBackground,
                borderRadius: 16,
                padding: 16,
                paddingTop: 16,
                paddingBottom: 18,
                marginBottom: 12,
                alignItems: "center",
                borderWidth: 1,
                borderColor: themeColors.border,
              },
              getShadowStyle('medium')
            ]}
          >
            <Text
              style={{
                color: themeColors.text,
                fontSize: 18,
                fontWeight: "bold",
                marginBottom: 6,
              }}
            >
              {t("wallets.yourBalance")}
              <TouchableOpacity
                onPress={toggleHideBalance}
                style={{ paddingLeft: 10 }}
              >
                <Ionicons
                  name={hideBalance ? "eye-off-outline" : "eye-outline"}
                  size={20}
                  color={themeColors.secondaryText}
                />
              </TouchableOpacity>
            </Text>

            <Text
              style={{
                color: totalBalance >= 0 ? "#4CAF50" : themeColors.danger,
                fontSize: 26,
                fontWeight: "bold",
                lineHeight: 32,
              }}
            >
              {hideBalance ? "•••••••" : totalBalance.toLocaleString() + " đ"}
            </Text>
          </View>

          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              backgroundColor: "transparent",
            }}
          >
            <View
              style={[
                {
                  flex: 1,
                  backgroundColor: themeColors.cardBackground,
                  borderRadius: 12,
                  padding: 12,
                  marginRight: 4,
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: themeColors.border,
                },
                getShadowStyle('low')
              ]}
            >
              <Text
                style={{
                  color: themeColors.text,
                  fontSize: 13,
                  fontWeight: "500",
                  marginBottom: 4,
                }}
              >
                {t("wallets.thisMonthIncome")}
              </Text>
              <Text
                style={{ color: "#4CAF50", fontSize: 16, fontWeight: "bold" }}
              >
                {hideBalance
                  ? "•••••••"
                  : summary.moneyIn.toLocaleString() + "đ"}
              </Text>
            </View>

            <View
              style={[
                {
                  flex: 1,
                  backgroundColor: themeColors.cardBackground,
                  borderRadius: 12,
                  padding: 12,
                  marginLeft: 4,
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: themeColors.border,
                },
                getShadowStyle('low')
              ]}
            >
              <Text
                style={{
                  color: themeColors.text,
                  fontSize: 13,
                  fontWeight: "500",
                  marginBottom: 4,
                }}
              >
                {t("wallets.thisMonthExpense")}
              </Text>
              <Text
                style={{ color: themeColors.danger, fontSize: 16, fontWeight: "bold" }}
              >
                {hideBalance
                  ? "•••••••"
                  : summary.moneyOut.toLocaleString() + "đ"}
              </Text>
            </View>
          </View>
        </View>

        <View
          style={{
            flexDirection: "row",
            paddingHorizontal: 16,
            marginBottom: 16,
            backgroundColor: "transparent",
          }}
        >
          <TouchableOpacity
            style={[
              {
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                paddingVertical: 10,
                paddingHorizontal: 14,
                borderRadius: 12,
                borderWidth: 1,
                borderColor: themeColors.border,
                backgroundColor: themeColors.cardBackground,
                flex: 1,
                marginRight: 6,
                opacity: isSortMode ? 0.5 : 1,
              },
              getShadowStyle('low')
            ]}
            onPress={() => router.push("/wallet/add")}
            disabled={isSortMode}
          >
            <Ionicons name="add-outline" size={18} color={themeColors.primary} />
            <Text
              style={{
                color: themeColors.text,
                marginLeft: 6,
                fontSize: 13,
              }}
            >
              {t("wallets.add")}
            </Text>
          </TouchableOpacity>

          {isSortMode ? (
            <TouchableOpacity
              style={[
                {
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: 10,
                  paddingHorizontal: 14,
                  borderRadius: 12,
                  borderWidth: 0,
                  backgroundColor: sortColor,
                  flex: 1,
                  marginHorizontal: 6,
                },
                getShadowStyle('low')
              ]}
              onPress={toggleSortMode}
            >
              <Ionicons name="checkmark" size={18} color="#FFF" />
              <Text
                style={{
                  color: "#FFF",
                  marginLeft: 6,
                  fontSize: 13,
                  fontWeight: "bold",
                }}
              >
                {t("wallets.done")}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                {
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: 10,
                  paddingHorizontal: 14,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: themeColors.border,
                  backgroundColor: themeColors.cardBackground,
                  flex: 1,
                  marginHorizontal: 6,
                  opacity: isEditMode ? 0.5 : 1,
                },
                getShadowStyle('low')
              ]}
              onPress={toggleSortMode}
              disabled={isEditMode}
            >
              <Ionicons
                name="reorder-two-outline"
                size={18}
                color={themeColors.primary}
              />
              <Text
                style={{
                  color: themeColors.text,
                  marginLeft: 6,
                  fontSize: 13,
                }}
              >
                {t("wallets.sort")}
              </Text>
            </TouchableOpacity>
          )}

          {isEditMode ? (
            <TouchableOpacity
              style={[
                {
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: 10,
                  paddingHorizontal: 14,
                  borderRadius: 12,
                  borderWidth: 0,
                  backgroundColor: themeColors.primary,
                  flex: 1,
                  marginLeft: 6,
                },
                getShadowStyle('low')
              ]}
              onPress={toggleEditMode}
            >
              <Ionicons name="checkmark" size={18} color="#FFF" />
              <Text
                style={{
                  color: "white",
                  marginLeft: 6,
                  fontSize: 13,
                  fontWeight: "bold",
                }}
              >
                {t("wallets.done")}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                {
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingVertical: 10,
                  paddingHorizontal: 14,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: themeColors.border,
                  backgroundColor: themeColors.cardBackground,
                  flex: 1,
                  marginLeft: 6,
                  opacity: isSortMode ? 0.5 : 1,
                },
                getShadowStyle('low')
              ]}
              onPress={toggleEditMode}
              disabled={isSortMode}
            >
              <Ionicons name="create-outline" size={18} color={themeColors.primary} />
              <Text
                style={{
                  color: themeColors.text,
                  marginLeft: 6,
                  fontSize: 13,
                }}
              >
                {t("wallets.edit")}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Hiển thị danh sách ví */}
        {wallets.length === 0 ? (
          <View
            style={[
              styles.emptyContainer,
              { backgroundColor: "transparent", paddingVertical: 60 },
            ]}
          >
            <Ionicons name="wallet-outline" size={40} color={themeColors.primary} />
            <Text
                style={{
                  color: themeColors.secondaryText,
                  fontSize: 14,
                  marginTop: 8,
                  textAlign: "center",
                }}
              >
                {t("wallets.noWallets")}
              </Text>
              <TouchableOpacity
                style={[
                  styles.emptyButton,
                  { backgroundColor: themeColors.primary, marginTop: 16, borderRadius: 12 },
                ]}
                onPress={() => router.push("/wallet/add")}
              >
                <Text style={{ color: "#FFF", fontWeight: "600" }}>
                  {t("wallets.addWallet")}
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            // Hiển thị theo chế độ: sắp xếp (tất cả ví) hoặc phân nhóm
            isSortMode ? (
              renderSortModeWalletList()
            ) : (
              walletSections.map((section) => 
                renderWalletList(
                  section.data, 
                  section.type, 
                  section.title, 
                  section.icon
                )
              )
            )
          )}
       </ScrollView>
     </SafeAreaView>
   );
}

const styles = StyleSheet.create({
 container: {
   flex: 1,
 },
 header: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "space-between",
   padding: 16,
   paddingTop: 8,
   paddingBottom: 12,
 },
 headerTitle: {
   fontSize: 20,
   fontWeight: "bold",
 },
 headerRightButtons: {
   flexDirection: "row",
   alignItems: "center",
 },
 backButton: {
   padding: 4,
 },
 loadingContainer: {
   flex: 1,
   justifyContent: "center",
   alignItems: "center",
 },
 gridColumnWrapper: {
   justifyContent: "space-between",
 },
 walletItemGrid: {
   flex: 1,
   borderRadius: 16,
   padding: 12,
   marginBottom: 12,
   maxWidth: "48.5%",
   position: "relative",
   height: 84,
   overflow: "visible",
   paddingBottom: 44,
 },
 walletItemList: {
   flexDirection: "row",
   alignItems: "center",
   paddingVertical: 12,
   paddingHorizontal: 12,
   marginBottom: 8,
   borderRadius: 12,
   position: "relative",
   overflow: "hidden",
 },
 walletHeader: {
   flexDirection: "row",
   alignItems: "center",
   marginBottom: 0,
 },
 walletIconContainer: {
   width: 38,
   height: 38,
   borderRadius: 10,
   justifyContent: "center",
   alignItems: "center",
   marginRight: 10,
 },
 walletName: {
   fontSize: 15,
   fontWeight: "600",
   marginBottom: 2,
 },
 // Thêm style cho badge loại ví
 walletTypeBadge: {
   paddingHorizontal: 6,
   paddingVertical: 2,
   borderRadius: 8,
   marginLeft: 8,
 },
 walletTypeText: {
   color: "white",
   fontSize: 10,
   fontWeight: "600",
 },
 swipeActions: {
   justifyContent: "space-around",
   alignItems: "center",
 },
 editButton: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 6,
   paddingHorizontal: 12,
 },
 deleteButton: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 6,
   paddingHorizontal: 12,
 },
 defaultButton: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 6,
   paddingHorizontal: 12,
 },
 iconButton: {
   padding: 4,
   alignItems: "center",
   justifyContent: "center",
   marginHorizontal: 2,
 },
 actionText: {
   fontWeight: "500",
   fontSize: 13,
   marginLeft: 4,
 },
 balanceText: {
   fontSize: 17,
   fontWeight: "bold",
 },
 emptyContainer: {
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 60,
   backgroundColor: "transparent",
 },
 emptyButton: {
   paddingHorizontal: 20,
   paddingVertical: 10,
   borderRadius: 20,
 },
 defaultBadge: {
   position: "absolute",
   top: 0,
   left: 0,
   backgroundColor: "#FFB700",
   paddingHorizontal: 6,
   paddingVertical: 3,
   borderBottomRightRadius: 8,
   borderTopLeftRadius: 10,
   zIndex: 1,
 },
 defaultBadgeSmall: {
   backgroundColor: "#FFB700",
   paddingHorizontal: 4,
   paddingVertical: 2,
   borderRadius: 4,
   marginLeft: 5,
 },
 toastContainer: {
   position: "absolute",
   left: 0,
   right: 0,
   zIndex: 999,
   flexDirection: "row",
   alignItems: "center",
   padding: 12,
   paddingTop: 16,
   paddingBottom: 16,
   shadowColor: "#000",
   shadowOffset: { width: 0, height: 2 },
   shadowOpacity: 0.2,
   shadowRadius: 3,
   elevation: 3,
 },
 toastMessage: {
   color: "white",
   fontSize: 16,
   fontWeight: "500",
   marginLeft: 8,
 },
 sortButtonsGrid: {
   position: "absolute",
   top: 10,
   right: 10,
   zIndex: 10,
   backgroundColor: "rgba(0,0,0,0.1)",
   borderRadius: 8,
   flexDirection: "column",
 },
 sortButtonsList: {
   backgroundColor: "rgba(0,0,0,0.1)",
   borderRadius: 8,
   flexDirection: "column",
   marginRight: 10,
 },
 sortButton: {
   padding: 6,
   alignItems: "center",
   justifyContent: "center",
 },
 disabledButton: {
   opacity: 0.5,
 },
 sortModeBar: {
   flexDirection: "row",
   alignItems: "center",
   justifyContent: "center",
   paddingVertical: 12,
   paddingHorizontal: 16,
   position: "absolute",
   left: 0,
   right: 0,
   zIndex: 99,
   shadowColor: "#000",
   shadowOffset: { width: 0, height: 2 },
   shadowOpacity: 0.2,
   shadowRadius: 3,
   elevation: 3,
 },
 sortModeText: {
   color: "#FFF",
   fontWeight: "600",
   fontSize: 16,
   marginLeft: 8,
 },
});