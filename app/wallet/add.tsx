// File: app/wallet/add.tsx
// File này liên quan đến: context/ThemeContext.tsx, constants/WalletData.ts
// M<PERSON>n hình thêm ví mới với tính năng chọn ví sẵn có - Đã tối ưu giao diện và sử dụng WalletData.ts và ThemeContext

import { Text, View } from "@/components/Themed";
import {
  BankLogos,
  BANK_TEMPLATES,
  CASH_TEMPLATES,
  WALLET_COLORS,
  WALLET_TYPES,
  formatMoney,
  getFilteredTemplates,
  getWalletTypeName,
  isImageIcon,
  parseMoney
} from "@/constants/WalletData";
import { useLocalization } from "@/context/LocalizationContext";
import { useTheme } from "@/context/ThemeContext";
import { WalletModel } from "@/lib/models/wallet";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

export default function AddWalletScreen() {
  // Sử dụng đầy đủ các thuộc tính từ useTheme
  const { isDark, themeColors, getCardStyle, getIconContainerStyle, getShadowStyle } = useTheme();
  const { t } = useLocalization();

  // Đọc tham số query
  const { fromSetup } = useLocalSearchParams();
  const isFromSetup = fromSetup === "true";

  // State cho mode thêm ví (mẫu hoặc tùy chỉnh)
  const [addMode, setAddMode] = useState("template");

  // State cho thông tin ví tùy chỉnh
  const [name, setName] = useState("");
  const [balance, setBalance] = useState("");
  const [selectedType, setSelectedType] = useState(WALLET_TYPES[0].id);
  const [selectedColor, setSelectedColor] = useState(WALLET_COLORS[0]);
  const [isLoading, setIsLoading] = useState(false);

  // State cho chọn ví mẫu
  const [selectedTemplates, setSelectedTemplates] = useState([]);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [templateType, setTemplateType] = useState("bank");
  const [templateBalances, setTemplateBalances] = useState({});
  const [templateNames, setTemplateNames] = useState({});
  const [selectedTemplateIds, setSelectedTemplateIds] = useState([]);
  // State cho tìm kiếm ví mẫu
  const [searchQuery, setSearchQuery] = useState("");

  // Màu sắc cho chế độ sắp xếp (giữ nguyên)
  const sortColor = "#FF9800"; // Màu cam cho chế độ sắp xếp

  // Reset state khi mở modal
  useEffect(() => {
    if (showTemplateModal) {
      setSelectedTemplateIds([]);
      setTemplateBalances({});
      setTemplateNames({});
      setSearchQuery("");
    }
  }, [showTemplateModal]);

  // Xử lý lưu ví tùy chỉnh
  const handleSaveCustom = async () => {
    // Validate inputs
    if (!name.trim()) {
      Alert.alert(t("common.error"), t("wallets.nameRequired"));
      return;
    }

    if (!balance.trim() || isNaN(Number(balance))) {
      Alert.alert(t("common.error"), t("wallets.balanceRequired"));
      return;
    }

    try {
      setIsLoading(true);

      // Tạo đối tượng ví mới
      const walletData = {
        name: name.trim(),
        balance: parseFloat(balance),
        type: selectedType,
        color: selectedColor,
        icon:
          WALLET_TYPES.find((type) => type.id === selectedType)?.icon ||
          "wallet-outline",
      };

      // Lưu ví mới vào database
      await WalletModel.create(walletData);

      // Hiển thị thông báo thành công và quay lại
      Alert.alert(t("common.success"), t("wallets.walletAdded"), [
        {
          text: t("common.confirm"),
          onPress: () => {
            // Kiểm tra để quyết định điều hướng
            if (isFromSetup) {
              router.push("/(setup)/create-categories");
            } else {
              router.back(); // Hành vi mặc định là quay lại
            }
          },
        },
      ]);
    } catch (error) {
      console.error("Lỗi khi tạo ví mới:", error);
      Alert.alert(t("common.error"), t("wallets.addError"));
    } finally {
      setIsLoading(false);
    }
  };

  // Xử lý chọn/bỏ chọn template
  const toggleTemplateSelection = (templateId) => {
    setSelectedTemplateIds((prev) => {
      // Kiểm tra xem template đã được chọn chưa
      const isSelected = prev.includes(templateId);

      if (isSelected) {
        // Nếu đã chọn, thì bỏ chọn
        return prev.filter((id) => id !== templateId);
      } else {
        // Nếu chưa chọn, thì thêm vào danh sách
        return [...prev, templateId];
      }
    });
  };

  // Xử lý thay đổi tên cho template đã chọn
  const handleTemplateNameChange = (templateId, name) => {
    setTemplateNames((prev) => ({
      ...prev,
      [templateId]: name,
    }));
  };

  // Xử lý thay đổi số dư cho template đã chọn
  const handleTemplateBalanceChange = (templateId, balance) => {
    setTemplateBalances((prev) => ({
      ...prev,
      [templateId]: balance,
    }));
  };

  // Xử lý thêm nhiều template vào danh sách đã chọn
  const handleAddTemplates = () => {
    if (selectedTemplateIds.length === 0) {
      Alert.alert("Thông báo", "Vui lòng chọn ít nhất một ví mẫu");
      return;
    }

    const templates = templateType === "bank" ? BANK_TEMPLATES : CASH_TEMPLATES;
    const newTemplates = selectedTemplateIds
      .map((id) => {
        const template = templates.find((t) => t.id === id);
        if (!template) return null;

        // Xác định tên - sử dụng giá trị đã nhập hoặc tên mặc định
        const name = templateNames[id] || template.name;

        // Xác định số dư - sử dụng giá trị đã nhập hoặc mặc định là 0
        const balance = templateBalances[id] || "0";

        return {
          id: template.id,
          name: name,
          balance: balance,
          icon: template.icon,
          color: template.color,
          type: template.type,
        };
      })
      .filter((t) => t !== null);

    // Thêm vào danh sách đã chọn (bỏ qua các mục đã tồn tại)
    const existingIds = selectedTemplates.map((t) => t.id);
    const uniqueNewTemplates = newTemplates.filter(
      (t) => !existingIds.includes(t.id)
    );

    setSelectedTemplates([...selectedTemplates, ...uniqueNewTemplates]);

    // Reset state
    setSelectedTemplateIds([]);
    setTemplateBalances({});
    setTemplateNames({});
    setShowTemplateModal(false);
  };

  // Xử lý xóa template đã chọn
  const handleRemoveTemplate = (templateId) => {
    setSelectedTemplates(selectedTemplates.filter((t) => t.id !== templateId));
  };

  // Xử lý lưu nhiều ví mẫu cùng lúc
  const handleSaveTemplates = async () => {
    if (selectedTemplates.length === 0) {
      Alert.alert("Thông báo", "Vui lòng chọn ít nhất một ví mẫu");
      return;
    }

    try {
      setIsLoading(true);
      let successCount = 0;

      // Tạo từng ví một
      for (const template of selectedTemplates) {
        try {
          // Kiểm tra tên ví - không được để trống
          if (!template.name.trim()) {
            continue; // Bỏ qua ví không có tên
          }

          // Xác định icon cho ví - nếu là file ảnh thì lưu tên file, nếu không thì lưu tên icon
          const walletIcon = template.icon.endsWith(".png")
            ? template.icon // Giữ nguyên tên file ảnh
            : template.icon; // Giữ nguyên tên icon

          // Parse balance từ định dạng có dấu chấm ngăn cách
          const balance = parseMoney(template.balance);

          // Tạo đối tượng ví mới
          const walletData = {
            name: template.name.trim(),
            balance: parseFloat(balance || "0"), // Sử dụng 0 nếu không có số dư
            type: template.type,
            color: template.color,
            icon: walletIcon,
          };

          // Lưu ví mới vào database
          await WalletModel.create(walletData);
          successCount++;
        } catch (error) {
          console.error(`Lỗi khi tạo ví ${template.name}:`, error);
        }
      }

      if (successCount > 0) {
        // Hiển thị thông báo thành công và quay lại
        Alert.alert(
          t("common.success"),
          `Đã thêm thành công ${successCount} ví mới!`,
          [
            {
              text: t("common.confirm"),
              onPress: () => {
                // Kiểm tra để quyết định điều hướng
                if (isFromSetup) {
                  router.push("/(setup)/create-categories");
                } else {
                  router.back(); // Hành vi mặc định là quay lại
                }
              },
            },
          ]
        );
      } else {
        Alert.alert(t("common.error"), "Không thể thêm ví. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("Lỗi khi tạo các ví mẫu:", error);
      Alert.alert(t("common.error"), t("wallets.addError"));
    } finally {
      setIsLoading(false);
    }
  };

  // Render ví mẫu trong modal
  const renderTemplateItem = ({ item }) => {
    const isSelected = selectedTemplateIds.includes(item.id);
    const isBankTemplate = isImageIcon(item.icon);

    return (
      <TouchableOpacity
        style={[
          styles.templateItem,
          {
            backgroundColor: themeColors.cardBackground,
            borderColor: isSelected
              ? item.color
              : themeColors.border,
            ...getShadowStyle('medium'),
            shadowColor: isSelected ? item.color : themeColors.shadowColor,
          },
        ]}
        onPress={() => toggleTemplateSelection(item.id)}
      >
        <View
          style={[
            styles.templateIcon,
            { 
              backgroundColor: item.color, 
              shadowColor: item.color,
              ...getShadowStyle('medium'),
            },
          ]}
        >
          {isBankTemplate ? (
            <Image
              source={BankLogos[item.icon]}
              style={{ width: 30, height: 30 }}
              contentFit="contain"
            />
          ) : (
            <Ionicons name={item.icon} size={24} color="white" />
          )}
        </View>
        <Text style={[styles.templateName, { color: themeColors.text }]}>
          {item.name}
        </Text>
        {isSelected && (
          <View style={styles.selectedCheck}>
            <Ionicons name="checkmark-circle" size={22} color={item.color} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Render ví mẫu đã chọn
  const renderSelectedTemplate = ({ item, index }) => {
    const isBankTemplate = isImageIcon(item.icon);

    return (
      <View
        style={[
          styles.selectedTemplateItem,
          {
            backgroundColor: themeColors.cardBackground,
            borderLeftColor: item.color,
            borderColor: themeColors.border,
            marginTop: index === 0 ? 0 : 10,
            shadowColor: item.color,
            ...getShadowStyle('medium'),
          },
        ]}
      >
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <View
            style={[
              styles.templateIconSmall,
              { 
                backgroundColor: item.color, 
                shadowColor: item.color,
                ...getShadowStyle('medium'),
              },
            ]}
          >
            {isBankTemplate ? (
              <Image
                source={BankLogos[item.icon]}
                style={{ width: 20, height: 20 }}
                contentFit="contain"
              />
            ) : (
              <Ionicons name={item.icon} size={20} color="white" />
            )}
          </View>

          <View style={{ marginLeft: 10, flex: 1 }}>
            {/* Trường nhập tên ví */}
            <TextInput
              style={{
                color: themeColors.text,
                fontWeight: "600",
                fontSize: 15,
                padding: 0,
                margin: 0,
                height: 24,
              }}
              value={item.name}
              onChangeText={(text) => {
                // Cập nhật tên ví trong danh sách đã chọn
                const updatedTemplates = selectedTemplates.map((template) =>
                  template.id === item.id
                    ? { ...template, name: text }
                    : template
                );
                setSelectedTemplates(updatedTemplates);
              }}
              placeholder="Tên ví"
              placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
            />

            <Text
              style={{ color: themeColors.secondaryText, fontSize: 13 }}
            >
              {getWalletTypeName(item.type, t)}
            </Text>
          </View>

          {/* Trường nhập số dư */}
          <TextInput
            style={{
              color: item.color,
              fontWeight: "600",
              textAlign: "right",
              width: 100,
            }}
            value={formatMoney(item.balance)}
            onChangeText={(text) => {
              // Loại bỏ tất cả ký tự không phải số
              const numericValue = text.replace(/[^\d]/g, "");

              // Cập nhật số dư trong danh sách đã chọn
              const updatedTemplates = selectedTemplates.map((template) =>
                template.id === item.id
                  ? { ...template, balance: numericValue }
                  : template
              );
              setSelectedTemplates(updatedTemplates);
            }}
            keyboardType="numeric"
            placeholder="0"
            placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
          />
        </View>

        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveTemplate(item.id)}
        >
          <Ionicons
            name="close-circle"
            size={22}
            color={themeColors.primary}
          />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView
      edges={["top"]}
      style={[styles.container, { backgroundColor: themeColors.background }]}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <View style={[styles.header, { backgroundColor: themeColors.background }]}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={themeColors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: themeColors.text }]}>
            {t("wallets.addWallet")}
          </Text>
          <TouchableOpacity
            onPress={
              addMode === "template" ? handleSaveTemplates : handleSaveCustom
            }
            style={[
              styles.saveButton,
              {
                backgroundColor:
                  addMode === "template"
                    ? selectedTemplates.length > 0 && !isLoading
                      ? themeColors.primary
                      : isDark
                      ? "#375980"
                      : "#BBDEFB"
                    : name && balance && !isLoading
                    ? themeColors.primary
                    : isDark
                    ? "#375980"
                    : "#BBDEFB",
                ...getShadowStyle('low')
              },
            ]}
            disabled={
              (addMode === "template"
                ? selectedTemplates.length === 0
                : !name || !balance) || isLoading
            }
          >
            <Text
              style={{
                color:
                  addMode === "template"
                    ? selectedTemplates.length > 0 && !isLoading
                      ? "#fff"
                      : isDark
                      ? "#7CB9F8"
                      : "#64B5F6"
                    : name && balance && !isLoading
                    ? "#fff"
                    : isDark
                    ? "#7CB9F8"
                    : "#64B5F6",
                fontSize: 15,
                fontWeight: "600",
              }}
            >
              {isLoading ? t("common.loading") : t("common.save")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab chuyển đổi giữa ví mẫu và tùy chỉnh */}
        <View
          style={[
            styles.tabContainer,
            {
              borderBottomColor: themeColors.border,
              backgroundColor: themeColors.background,
            },
          ]}
        >
          <TouchableOpacity
            style={[
              styles.tabButton,
              addMode === "template" && {
                borderBottomWidth: 3,
                borderBottomColor: themeColors.primary,
              },
            ]}
            onPress={() => setAddMode("template")}
          >
            <Ionicons
              name="copy-outline"
              size={18}
              color={
                addMode === "template"
                  ? themeColors.primary
                  : themeColors.primary
              }
              style={{ marginRight: 6 }}
            />
            <Text
              style={{
                color:
                  addMode === "template"
                    ? themeColors.primary
                    : themeColors.primary,
                fontWeight: addMode === "template" ? "600" : "normal",
              }}
            >
              Mẫu có sẵn
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              addMode === "custom" && {
                borderBottomWidth: 3,
                borderBottomColor: themeColors.primary,
              },
            ]}
            onPress={() => setAddMode("custom")}
          >
            <Ionicons
              name="create-outline"
              size={18}
              color={
                addMode === "custom"
                  ? themeColors.primary
                  : themeColors.primary
              }
              style={{ marginRight: 6 }}
            />
            <Text
              style={{
                color:
                  addMode === "custom"
                    ? themeColors.primary
                    : themeColors.primary,
                fontWeight: addMode === "custom" ? "600" : "normal",
              }}
            >
              Tùy chỉnh
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* --- PHẦN VÍ MẪU --- */}
          {addMode == "template" && (
            <View style={{ backgroundColor: "transparent" }}>
              {/* Nút chọn ví mẫu */}
              <TouchableOpacity
                style={[
                  styles.selectTemplateButton,
                  getCardStyle('medium')
                ]}
                onPress={() => setShowTemplateModal(true)}
              >
                <Ionicons
                  name="add-circle-outline"
                  size={24}
                  color={themeColors.primary}
                  style={{ marginRight: 8 }}
                />
                <Text style={{ color: themeColors.primary, fontWeight: "500" }}>
                  Chọn ví mẫu
                </Text>
              </TouchableOpacity>

              {/* Danh sách ví đã chọn */}
              {selectedTemplates.length > 0 ? (
                <FlatList
                  data={selectedTemplates}
                  renderItem={renderSelectedTemplate}
                  keyExtractor={(item) => item.id}
                  style={{ marginTop: 16 }}
                  scrollEnabled={false}
                />
              ) : (
                <View
                  style={[
                    styles.emptyTemplates,
                    getCardStyle('medium')
                  ]}
                >
                  <Ionicons
                    name="information-circle-outline"
                    size={24}
                    color={themeColors.primary}
                  />
                  <Text
                    style={{
                      color: themeColors.secondaryText,
                      marginTop: 10,
                    }}
                  >
                    Chưa có ví mẫu nào được chọn
                  </Text>
                  <Text
                    style={{
                      color: themeColors.primary,
                      fontSize: 13,
                      marginTop: 4,
                      textAlign: "center",
                    }}
                  >
                    Nhấn "Chọn ví mẫu" để thêm ví từ danh sách có sẵn
                  </Text>
                </View>
              )}

              {/* Nút lưu nhiều ví */}
              {selectedTemplates.length > 0 && (
                <TouchableOpacity
                  style={[
                    styles.createButton,
                    {
                      backgroundColor: !isLoading
                        ? themeColors.primary
                        : isDark
                        ? "#375980"
                        : "#BBDEFB",
                      marginTop: 20,
                      ...getShadowStyle('medium')
                    },
                  ]}
                  onPress={handleSaveTemplates}
                  disabled={isLoading}
                >
                  <Text
                    style={[
                      styles.createButtonText,
                      {
                        color: !isLoading
                          ? "#fff"
                          : isDark
                          ? "#7CB9F8"
                          : "#64B5F6",
                      },
                    ]}
                  >
                    {isLoading
                      ? t("common.loading")
                      : `Lưu ${selectedTemplates.length} ví đã chọn`}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* --- PHẦN VÍ TÙY CHỈNH --- */}
          {addMode === "custom" && (
            <View style={{ backgroundColor: "transparent" }}>
              {/* Preview */}
              <View
                style={[
                  styles.previewCard,
                  getCardStyle('high')
                ]}
              >
                <View
                  style={[
                    styles.walletIcon,
                    {
                      backgroundColor: selectedColor,
                      shadowColor: selectedColor,
                      ...getShadowStyle('medium')
                    },
                  ]}
                >
                  <Ionicons
                    name={
                      WALLET_TYPES.find((type) => type.id === selectedType)
                        ?.icon
                    }
                    size={36}
                    color="white"
                  />
                </View>
                <Text style={[styles.previewTitle, { color: themeColors.text }]}>
                  {name || t("wallets.walletName")}
                </Text>
                <View style={styles.balanceDisplay}>
                  <Text
                    style={[
                      styles.currencySymbol,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    VND
                  </Text>
                  <Text
                    style={[
                      styles.previewBalance,
                      { color: themeColors.secondaryText },
                    ]}
                  >
                    {balance ? parseFloat(balance).toLocaleString() : "0"}
                  </Text>
                </View>
              </View>

              {/* Form Fields */}
              <View
                style={[
                  styles.formSection,
                  getCardStyle('medium')
                ]}
              >
                {/* Wallet Name Field */}
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <Ionicons
                      name="wallet-outline"
                      size={18}
                      color={themeColors.primary}
                      style={styles.labelIcon}
                    />
                    <Text style={[styles.label, { color: themeColors.text }]}>
                      {t("wallets.walletName")}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.inputContainer,
                      {
                        backgroundColor: themeColors.iconBackground,
                        borderColor: themeColors.border,
                        ...getShadowStyle('low')
                      },
                    ]}
                  >
                    <TextInput
                      style={[styles.input, { color: themeColors.text }]}
                      placeholder={t("wallets.enterWalletName")}
                      placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                      value={name}
                      onChangeText={setName}
                    />
                  </View>
                </View>

                {/* Initial Balance Field */}
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <Ionicons
                      name="cash-outline"
                      size={18}
                      color={themeColors.primary}
                      style={styles.labelIcon}
                    />
                    <Text style={[styles.label, { color: themeColors.text }]}>
                      {t("wallets.initialBalance")}
                    </Text>
                  </View>
                  <View
                    style={[
                      styles.inputContainer,
                      {
                        backgroundColor: themeColors.iconBackground,
                        borderColor: themeColors.border,
                        ...getShadowStyle('low')
                      },
                    ]}
                  >
                    <TextInput
                      style={[styles.input, { color: themeColors.text }]}
                      placeholder={t("wallets.enterInitialBalance")}
                      placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
                      value={balance}
                      onChangeText={setBalance}
                      keyboardType="numeric"
                    />
                  </View>
                </View>

                {/* Wallet Type Selection */}
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <Ionicons
                      name="albums-outline"
                      size={18}
                      color={themeColors.primary}
                      style={styles.labelIcon}
                    />
                    <Text style={[styles.label, { color: themeColors.text }]}>
                      {t("wallets.walletType")}
                    </Text>
                  </View>
                  <View style={styles.typeOptions}>
                    {WALLET_TYPES.map((type) => (
                      <TouchableOpacity
                        key={type.id}
                        style={[
                          styles.typeOption,
                          {
                            backgroundColor:
                              selectedType === type.id
                                ? isDark
                                  ? `${selectedColor}30`
                                  : `${selectedColor}20`
                                : themeColors.iconBackground,
                            borderColor:
                              selectedType === type.id
                                ? selectedColor
                                : themeColors.border,
                            ...getShadowStyle(selectedType === type.id ? 'medium' : 'low')
                          },
                        ]}
                        onPress={() => setSelectedType(type.id)}
                      >
                        <Ionicons
                          name={type.icon}
                          size={22}
                          color={
                            selectedType === type.id
                              ? selectedColor
                              : themeColors.primary
                          }
                        />
                        <Text
                          style={[
                            styles.typeText,
                            {
                              color:
                                selectedType === type.id
                                  ? selectedColor
                                  : themeColors.text,
                              fontWeight:
                                selectedType === type.id ? "600" : "normal",
                            },
                          ]}
                        >
                          {getWalletTypeName(type.id, t)}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Color Selection */}
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <Ionicons
                      name="color-palette-outline"
                      size={18}
                      color={themeColors.primary}
                      style={styles.labelIcon}
                    />
                    <Text style={[styles.label, { color: themeColors.text }]}>
                      {t("categories.color")}
                    </Text>
                  </View>
                  <View style={styles.colorPalette}>
                    {WALLET_COLORS.map((color) => (
                      <TouchableOpacity
                        key={color}
                        style={[
                          styles.colorOption,
                          {
                            backgroundColor: color,
                            borderWidth: selectedColor === color ? 3 : 0,
                            borderColor: "white",
                            transform:
                              selectedColor === color
                                ? [{ scale: 1.1 }]
                                : [{ scale: 1 }],
                            shadowColor: color,
                            shadowOpacity: selectedColor === color ? 0.6 : 0.3,
                            shadowOffset: { width: 0, height: 2 },
                            shadowRadius: 3,
                            elevation: selectedColor === color ? 5 : 4,
                          },
                        ]}
                        onPress={() => setSelectedColor(color)}
                      >
                        {selectedColor === color && (
                          <Ionicons name="checkmark" size={16} color="white" />
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>

              {/* Save Button */}
              <TouchableOpacity
                style={[
                  styles.createButton,
                  {
                    backgroundColor:
                      name && balance && !isLoading
                        ? themeColors.primary
                        : isDark
                        ? "#375980"
                        : "#BBDEFB",
                    ...getShadowStyle('medium')
                  },
                ]}
                onPress={handleSaveCustom}
                disabled={!name || !balance || isLoading}
              >
                <Text
                  style={[
                    styles.createButtonText,
                    {
                      color:
                        name && balance && !isLoading
                          ? "#fff"
                          : isDark
                          ? "#7CB9F8"
                          : "#64B5F6",
                    },
                  ]}
                >
                  {isLoading ? t("common.loading") : t("wallets.addWallet")}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Modal chọn ví mẫu */}
      <Modal
        visible={showTemplateModal}
        transparent={false}
        animationType="slide"
        onRequestClose={() => setShowTemplateModal(false)}
        statusBarTranslucent={true}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: themeColors.background }}>
          <View
            style={[
              styles.modalHeader,
              {
                borderBottomColor: themeColors.border,
                paddingTop: 20, // Thêm padding cho phần tiêu đề để kéo xuống
                backgroundColor: "transparent",
              },
            ]}
          >
            <Text style={[styles.modalTitle, { color: themeColors.text }]}>
              Chọn ví mẫu
            </Text>
            <TouchableOpacity
              onPress={() => setShowTemplateModal(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>

          {/* Tab chọn loại ví mẫu */}
          <View
            style={[
              styles.templateTypeTabs,
              {
                borderBottomColor: themeColors.border,
                backgroundColor: "transparent",
              },
            ]}
          >
            <TouchableOpacity
              style={[
                styles.templateTypeTab,
                templateType === "bank" && {
                  borderBottomWidth: 3,
                  borderBottomColor: themeColors.primary,
                },
              ]}
              onPress={() => setTemplateType("bank")}
            >
              <Text
                style={{
                  color:
                    templateType === "bank"
                      ? themeColors.primary
                      : themeColors.primary,
                  fontWeight: templateType === "bank" ? "600" : "normal",
                }}
              >
                Ngân hàng / Ví điện tử
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.templateTypeTab,
                templateType === "cash" && {
                  borderBottomWidth: 3,
                  borderBottomColor: themeColors.primary,
                },
              ]}
              onPress={() => setTemplateType("cash")}
            >
              <Text
                style={{
                  color:
                    templateType === "cash"
                      ? themeColors.primary
                      : themeColors.primary,
                  fontWeight: templateType === "cash" ? "600" : "normal",
                }}
              >
                Tiền mặt
              </Text>
            </TouchableOpacity>
          </View>

          {/* Khung tìm kiếm ví mẫu */}
          <View
            style={[
              styles.searchContainer,
              {
                backgroundColor: themeColors.iconBackground,
                borderColor: themeColors.border,
              },
            ]}
          >
            <Ionicons
              name="search-outline"
              size={20}
              color={themeColors.primary}
              style={{ marginRight: 8 }}
            />
            <TextInput
              style={{
                flex: 1,
                color: themeColors.text,
                height: 40,
              }}
              placeholder="Tìm kiếm ví mẫu..."
              placeholderTextColor={isDark ? "#6D8AC3" : "#90CAF9"}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery("")}>
                <Ionicons
                  name="close-circle"
                  size={20}
                  color={themeColors.primary}
                />
              </TouchableOpacity>
            )}
          </View>

          <ScrollView
            style={styles.modalBody}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{ paddingBottom: 100 }}
          >
            {/* Danh sách ví mẫu */}
            <View style={{ marginBottom: 20, backgroundColor: "transparent" }}>
              <FlatList
                data={getFilteredTemplates(templateType, searchQuery)}
                renderItem={renderTemplateItem}
                keyExtractor={(item) => item.id}
                numColumns={2}
                columnWrapperStyle={styles.templateGrid}
                scrollEnabled={false}
                ListEmptyComponent={
                  <View
                    style={[
                      styles.emptySearchResults,
                      getCardStyle('medium')
                    ]}
                  >
                    <Ionicons
                      name="search-outline"
                      size={24}
                      color={themeColors.primary}
                    />
                    <Text
                      style={{
                        color: themeColors.secondaryText,
                        marginTop: 10,
                        textAlign: "center",
                      }}
                    >
                      Không tìm thấy ví mẫu phù hợp
                    </Text>
                  </View>
                }
              />
            </View>
          </ScrollView>

          <View
            style={[
              styles.modalFooter,
              {
                borderTopColor: themeColors.border,
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: themeColors.background,
              },
            ]}
          >
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => {
                setShowTemplateModal(false);
                setSelectedTemplateIds([]);
                setTemplateBalances({});
              }}
            >
              <Text style={{ color: themeColors.secondaryText }}>
                Hủy bỏ
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modalAddButton,
                {
                  backgroundColor:
                    selectedTemplateIds.length > 0
                      ? themeColors.primary
                      : isDark
                      ? "#375980"
                      : "#BBDEFB",
                  opacity: selectedTemplateIds.length > 0 ? 1 : 0.5,
                  ...getShadowStyle('low')
                },
              ]}
              onPress={handleAddTemplates}
              disabled={selectedTemplateIds.length === 0}
            >
              <Text
                style={{
                  color:
                    selectedTemplateIds.length > 0
                      ? "white"
                      : isDark
                      ? "#7CB9F8"
                      : "#64B5F6",
                  fontWeight: "600",
                }}
              >
                {selectedTemplateIds.length > 0
                  ? `Thêm ${selectedTemplateIds.length} ví`
                  : "Thêm ví"}
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  previewCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    alignItems: "center",
  },
  walletIcon: {
    width: 76,
    height: 76,
    borderRadius: 38,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 22,
    fontWeight: "600",
    marginBottom: 10,
  },
  balanceDisplay: {
    flexDirection: "row",
    alignItems: "flex-end",
    backgroundColor: "transparent",
  },
  currencySymbol: {
    fontSize: 16,
    marginRight: 4,
    marginBottom: 4,
    opacity: 0.7,
  },
  previewBalance: {
    fontSize: 28,
    fontWeight: "bold",
    paddingTop: 10,
  },
  formSection: {
    marginBottom: 24,
    borderRadius: 16,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
    backgroundColor: "transparent",
  },
  labelContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
    backgroundColor: "transparent",
  },
  labelIcon: {
    marginRight: 6,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
  },
  inputContainer: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: "hidden",
  },
  input: {
    height: 54,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  typeOptions: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  typeOption: {
    flex: 1,
    padding: 16,
    borderRadius: 14,
    alignItems: "center",
    borderWidth: 2,
    marginHorizontal: 4,
  },
  typeText: {
    marginTop: 10,
    fontSize: 14,
    textAlign: "center",
  },
  colorPalette: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    justifyContent: "space-between",
    backgroundColor: "transparent",
  },
  colorOption: {
    width: 46,
    height: 46,
    borderRadius: 23,
    margin: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  createButton: {
    height: 56,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 12,
  },
  createButtonText: {
    fontSize: 18,
    fontWeight: "600",
  },
  // Styles cho phần ví mẫu
  selectTemplateButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 16,
  },
  emptyTemplates: {
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
    marginTop: 16,
    borderRadius: 16,
  },
  selectedTemplateItem: {
    padding: 14,
    borderRadius: 16,
    borderLeftWidth: 3,
    position: "relative",
  },
  templateIconSmall: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  removeButton: {
    position: "absolute",
    top: 10,
    right: 10,
    padding: 2,
  },
  // Modal styles
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 8,
  },
  modalBody: {
    padding: 16,
    flex: 1,
  },
  modalFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    borderTopWidth: 1,
    paddingBottom: Platform.OS === "ios" ? 30 : 16, // Thêm padding cho iOS để tránh bị notch che
  },
  modalCancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  modalAddButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  templateGrid: {
    justifyContent: "space-between",
  },
  templateItem: {
    width: "48%",
    marginBottom: 16,
    padding: 12,
    borderRadius: 16,
    alignItems: "center",
    borderWidth: 2,
    position: "relative",
  },
  templateIcon: {
    width: 50,
    height: 50,
    borderRadius: 15,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  templateName: {
    textAlign: "center",
    fontSize: 14,
    fontWeight: "500",
  },
  selectedCheck: {
    position: "absolute",
    top: 5,
    right: 5,
  },
  templateTypeTabs: {
    flexDirection: "row",
    borderBottomWidth: 1,
  },
  templateTypeTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    borderWidth: 1,
    height: 44,
  },
  emptySearchResults: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    marginTop: 20,
    borderRadius: 16,
  },
});