{"expo": {"name": "AI Money", "slug": "MoneyUp", "owner": "v<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.3", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "buildNumber": "5", "bundleIdentifier": "com.hyusoft.MoneyUp", "infoPlist": {"NSSpeechRecognitionUsageDescription": "Cho phép AI Money sử dụng nhận dạng giọng nói để dễ dàng thêm giao dịch và các thao tác khác bằng giọng nói", "NSMicrophoneUsageDescription": "Cho phép AI Money sử dụng microphone để chuyển đổi giọng nói thành văn bản và nhập nhanh các giao dịch tài ch<PERSON>h", "NSCameraUsageDescription": "AI Money cần truy cập camera để bạn có thể chụp ảnh màn hình từ ứng dụng tài chính khác. Ảnh này sẽ được phân tích bằng AI để tự động trích xuất thông tin ví (tên và số dư), gi<PERSON><PERSON> bạn chuyển dữ liệu sang AI Money mà không cần nhập thủ công. Hình ảnh chỉ được sử dụng cho mục đích này và được xử lý an toàn.", "NSPhotoLibraryUsageDescription": "AI Money cần truy cập thư viện ảnh để bạn có thể chọn ảnh chụp màn hình từ ứng dụng tài chính khác. Chúng tôi sẽ phân tích ảnh để tự động nhận diện và tạo các ví tương ứng với tên và số dư hiển thị trong ảnh. Quyền này chỉ dùng để đọc ảnh do bạn chọn và không thu thập hoặc lưu trữ các ảnh khác từ thư viện của bạn.", "NSPhotoLibraryAddUsageDescription": "AI Money cần quyền để lưu ảnh phân tích vào thư viện của bạn khi bạn yêu cầu. Ảnh này có thể giúp bạn tham khảo thông tin đã nhập về các ví tài chính của mình.", "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "package": "com.hyusoft.MoneyUp", "versionCode": 4}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"backgroundColor": "#121212"}}], ["expo-image-picker", {"photosPermission": "AI Money cần truy cập thư viện ảnh để bạn có thể chọn ảnh chụp màn hình từ ứng dụng tài chính khác. Chúng tôi sẽ phân tích ảnh để tự động nhận diện và tạo các ví tương ứng với tên và số dư hiển thị trong ảnh. Quyền này chỉ dùng để đọc ảnh do bạn chọn và không thu thập hoặc lưu trữ các ảnh khác từ thư viện của bạn.", "cameraPermission": "AI Money cần truy cập camera để bạn có thể chụp ảnh màn hình từ ứng dụng tài chính khác. Ảnh này sẽ được phân tích bằng AI để tự động trích xuất thông tin ví (tên và số dư), gi<PERSON><PERSON> bạn chuyển dữ liệu sang AI Money mà không cần nhập thủ công. Hình ảnh chỉ được sử dụng cho mục đích này và được xử lý an toàn."}], ["expo-notifications", {"icon": "./assets/images/icon.png", "color": "#ffffff"}], "react-native-iap"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "openaiApiKey": "${OPENAI_API_KEY}", "eas": {"projectId": "20660b04-6c10-47df-9348-141cacb7156a"}}, "hooks": {"postPublish": [{"file": "sentry-expo/upload-sourcemaps", "config": {"organization": "your-sentry-org", "project": "your-sentry-project"}}]}}}