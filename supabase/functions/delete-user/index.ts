import { serve } from "https://deno.land/std@0.182.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.21.0";

// Set CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get("SUPABASE_URL") ?? "",
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      // Create client with Auth context of the user that called the function.
      // This way your row-level-security (RLS) policies are applied.
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      }
    );

    // Get the user who is making the request
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    if (!user) {
      throw new Error("User is not authenticated");
    }

    const userId = user.id;

    // Create an admin client to delete data with admin privileges
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      // Service role key has admin rights and can bypass RLS
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Delete user data in the correct order to maintain referential integrity

    // First, get the avatar URL to delete it from storage later
    const { data: profileData } = await supabaseClient
      .from("profiles")
      .select("avatar_url")
      .eq("id", userId)
      .single();

    // Delete the user's transactions
    const { error: transactionsError } = await supabaseAdmin
      .from("transactions")
      .delete()
      .eq("user_id", userId);

    if (transactionsError) {
      console.error("Error deleting transactions:", transactionsError);
      // Continue with deletion even if transaction removal fails
    }

    // Delete the user's wallets
    const { error: walletsError } = await supabaseAdmin
      .from("wallets")
      .delete()
      .eq("user_id", userId);

    if (walletsError) {
      console.error("Error deleting wallets:", walletsError);
      // Continue with deletion even if wallet removal fails
    }

    // Delete user profile
    const { error: profileError } = await supabaseAdmin
      .from("profiles")
      .delete()
      .eq("id", userId);

    if (profileError) {
      console.error("Error deleting profile:", profileError);
      throw profileError;
    }

    // Delete avatar from storage if it exists
    if (profileData?.avatar_url) {
      try {
        const filePath = profileData.avatar_url.split("/").pop();
        if (filePath) {
          await supabaseAdmin.storage
            .from("avatars")
            .remove([`avatars/${filePath}`]);
        }
      } catch (storageError) {
        console.error("Error deleting avatar:", storageError);
        // Continue with deletion even if avatar removal fails
      }
    }

    // Finally, delete the user account itself
    const { error: userDeletionError } =
      await supabaseAdmin.auth.admin.deleteUser(userId);

    if (userDeletionError) {
      throw userDeletionError;
    }

    return new Response(
      JSON.stringify({ success: true, message: "User deleted successfully" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error deleting user:", error);

    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      }
    );
  }
});
