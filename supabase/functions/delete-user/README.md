# User Delete Edge Function

This Edge Function securely handles user account deletion in Supabase. It's designed to run with admin privileges to properly delete user data and the user account itself.

## Why an Edge Function?

Deleting a user account requires the `service_role` key which should never be exposed in client applications. By using an edge function, we can securely perform the deletion on the server-side while authenticating the request through the user's session.

## How it works

1. The edge function verifies the user making the request
2. It uses admin privileges to delete the user's data in the proper order
3. Finally, it deletes the user account itself from auth.users

## Deployment

To deploy this edge function to your Supabase project:

```bash
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project (follow prompts)
supabase link --project-ref your-project-id

# Deploy the function
supabase functions deploy delete-user
```

## Security Considerations

- This function requires authentication to work
- It uses row level security (RLS) to verify the user is deleting their own account
- The service role key is kept secure on the server
