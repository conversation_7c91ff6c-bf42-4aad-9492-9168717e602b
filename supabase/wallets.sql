-- Tạo extension để sử dụng UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tạo bảng Wallets để lưu thông tin ví
CREATE TABLE wallets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  balance NUMERIC(15, 2) DEFAULT 0 NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('cash', 'bank', 'ewallet')),
  icon TEXT DEFAULT 'wallet-outline' NOT NULL,
  color TEXT NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- <PERSON><PERSON><PERSON> đặt RLS (Row Level Security) cho bảng wallets
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;

-- Chỉ cho phép xem/thêm/sửa/xóa wallet của chính user
CREATE POLICY wallet_select_policy ON wallets
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY wallet_insert_policy ON wallets
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY wallet_update_policy ON wallets
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY wallet_delete_policy ON wallets
  FOR DELETE USING (auth.uid() = user_id);

-- Tạo function để tự động cập nhật trường updated_at
CREATE OR REPLACE FUNCTION update_timestamp_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger cho wallets để tự động cập nhật trường updated_at
CREATE TRIGGER update_wallets_timestamp
BEFORE UPDATE ON wallets
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column(); 