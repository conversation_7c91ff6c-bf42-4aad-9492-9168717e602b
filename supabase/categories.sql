-- Tạo extension để sử dụng UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tạo bảng Categories để lưu danh mục chi tiêu và thu nhập
CREATE TABLE categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  icon TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Cài đặt RLS cho bảng categories
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Chỉ cho phép xem/thêm/sửa/xóa category của chính user
CREATE POLICY category_select_policy ON categories
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY category_insert_policy ON categories
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY category_update_policy ON categories
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY category_delete_policy ON categories
  FOR DELETE USING (auth.uid() = user_id);

-- Tạo hàm để thêm danh mục bỏ qua RLS (với quyền SECURITY DEFINER)
CREATE OR REPLACE FUNCTION insert_category(
  p_name TEXT,
  p_icon TEXT,
  p_color TEXT,
  p_type TEXT
) RETURNS UUID AS $$
DECLARE
  new_id UUID;
BEGIN
  INSERT INTO categories (id, user_id, name, icon, color, type)
  VALUES (
    uuid_generate_v4(),
    auth.uid(),
    p_name,
    p_icon,
    p_color,
    p_type
  )
  RETURNING id INTO new_id;
  
  RETURN new_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tạo hàm xóa tất cả danh mục của user hiện tại bỏ qua RLS
CREATE OR REPLACE FUNCTION delete_all_categories()
RETURNS VOID AS $$
BEGIN
  DELETE FROM categories WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tạo dữ liệu mặc định cho categories
CREATE OR REPLACE FUNCTION insert_default_categories()
RETURNS VOID AS $$
BEGIN
  INSERT INTO categories (id, user_id, name, type, icon, color) VALUES
    (uuid_generate_v4(), auth.uid(), 'Lương', 'income', 'cash-outline', '#4CAF50'),
    (uuid_generate_v4(), auth.uid(), 'Thưởng', 'income', 'gift-outline', '#2196F3'),
    (uuid_generate_v4(), auth.uid(), 'Đầu tư', 'income', 'trending-up-outline', '#673AB7'),
    (uuid_generate_v4(), auth.uid(), 'Khác', 'income', 'add-circle-outline', '#607D8B'),
    (uuid_generate_v4(), auth.uid(), 'Ăn uống', 'expense', 'restaurant-outline', '#F44336'),
    (uuid_generate_v4(), auth.uid(), 'Di chuyển', 'expense', 'car-outline', '#FF9800'),
    (uuid_generate_v4(), auth.uid(), 'Mua sắm', 'expense', 'cart-outline', '#E91E63'),
    (uuid_generate_v4(), auth.uid(), 'Nhà cửa', 'expense', 'home-outline', '#795548'),
    (uuid_generate_v4(), auth.uid(), 'Giải trí', 'expense', 'film-outline', '#9C27B0'),
    (uuid_generate_v4(), auth.uid(), 'Sức khỏe', 'expense', 'medkit-outline', '#00BCD4'),
    (uuid_generate_v4(), auth.uid(), 'Giáo dục', 'expense', 'school-outline', '#3F51B5'),
    (uuid_generate_v4(), auth.uid(), 'Khác', 'expense', 'add-circle-outline', '#607D8B');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tạo trigger cho categories để tự động cập nhật trường updated_at
CREATE TRIGGER update_categories_timestamp
BEFORE UPDATE ON categories
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column(); 