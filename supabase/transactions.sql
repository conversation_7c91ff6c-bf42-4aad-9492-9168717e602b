-- Tạo extension để sử dụng UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tạo bảng Transactions để lưu giao dịch
CREATE TABLE transactions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE NOT NULL,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  amount NUMERIC(15, 2) NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
  description TEXT,
  date TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- <PERSON><PERSON>i đặt RLS cho bảng transactions
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Chỉ cho phép xem/thêm/sửa/xóa transaction của chính user
CREATE POLICY transaction_select_policy ON transactions
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY transaction_insert_policy ON transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY transaction_update_policy ON transactions
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY transaction_delete_policy ON transactions
  FOR DELETE USING (auth.uid() = user_id);

-- Tạo function để cập nhật số dư ví khi tạo/cập nhật/xóa giao dịch
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
  IF (TG_OP = 'INSERT') THEN
    IF NEW.type = 'income' THEN
      UPDATE wallets SET balance = balance + NEW.amount WHERE id = NEW.wallet_id;
    ELSIF NEW.type = 'expense' THEN
      UPDATE wallets SET balance = balance - NEW.amount WHERE id = NEW.wallet_id;
    END IF;
  ELSIF (TG_OP = 'UPDATE') THEN
    IF OLD.type = 'income' THEN
      UPDATE wallets SET balance = balance - OLD.amount WHERE id = OLD.wallet_id;
    ELSIF OLD.type = 'expense' THEN
      UPDATE wallets SET balance = balance + OLD.amount WHERE id = OLD.wallet_id;
    END IF;
    
    IF NEW.type = 'income' THEN
      UPDATE wallets SET balance = balance + NEW.amount WHERE id = NEW.wallet_id;
    ELSIF NEW.type = 'expense' THEN
      UPDATE wallets SET balance = balance - NEW.amount WHERE id = NEW.wallet_id;
    END IF;
  ELSIF (TG_OP = 'DELETE') THEN
    IF OLD.type = 'income' THEN
      UPDATE wallets SET balance = balance - OLD.amount WHERE id = OLD.wallet_id;
    ELSIF OLD.type = 'expense' THEN
      UPDATE wallets SET balance = balance + OLD.amount WHERE id = OLD.wallet_id;
    END IF;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tạo triggers để gọi function update_wallet_balance
CREATE TRIGGER update_wallet_balance_on_insert
AFTER INSERT ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

CREATE TRIGGER update_wallet_balance_on_update
AFTER UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

CREATE TRIGGER update_wallet_balance_on_delete
AFTER DELETE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

-- Tạo trigger cho transactions để tự động cập nhật trường updated_at
CREATE TRIGGER update_transactions_timestamp
BEFORE UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column(); 