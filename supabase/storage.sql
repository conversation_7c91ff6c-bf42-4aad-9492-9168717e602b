-- Create storage bucket for profile images
INSERT INTO storage.buckets (id, name, public)
VALUES ('profiles', 'profiles', true);

-- Set up security policies for the profiles bucket

-- Allow public access to all profile images in the bucket
CREATE POLICY "Public can view all profile images" ON storage.objects
  FOR SELECT
  USING (bucket_id = 'profiles');

-- Allow authenticated users to upload any image to the bucket
CREATE POLICY "Authenticated users can upload images" ON storage.objects
  FOR INSERT
  WITH CHECK (bucket_id = 'profiles' AND auth.uid() IS NOT NULL);

-- Allow authenticated users to update any images in the bucket
CREATE POLICY "Authenticated users can update images" ON storage.objects
  FOR UPDATE
  USING (bucket_id = 'profiles' AND auth.uid() IS NOT NULL);

-- Allow authenticated users to delete any images in the bucket
CREATE POLICY "Authenticated users can delete images" ON storage.objects
  FOR DELETE
  USING (bucket_id = 'profiles' AND auth.uid() IS NOT NULL); 