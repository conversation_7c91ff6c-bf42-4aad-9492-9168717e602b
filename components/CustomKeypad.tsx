import { useTheme } from '@/context/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Dimensions, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface CustomKeypadProps {
  onKeyPress: (key: string) => void;
  onDone: () => void;
  onToggleType: (type: 'expense' | 'income') => void;
  visible: boolean;
}

const KEYS = [
  ['C', 'backspace', '+', '-'],
  ['7', '8', '9', '000'],
  ['4', '5', '6', ''],
  ['1', '2', '3', ''],
];

const { width } = Dimensions.get('window');
const PADDING = 15;
const GAP = 8; // nhỏ hơn
const BTN_W = (width - PADDING * 2 - GAP * 3) / 4;

const CustomKeypad: React.FC<CustomKeypadProps> = ({
  onKeyPress,
  onDone,
  onToggleType,
  visible,
}) => {
  const { themeColors, getShadowStyle } = useTheme();
  if (!visible) return null;

  const handlePress = (key: string) => {
    if (!key) return;
    if (key === '+') return onToggleType('income');
    if (key === '-') return onToggleType('expense');
    onKeyPress(key);
  };

  const renderBtn = (key: string) => {
    if (!key) return <View style={styles.keyButton} key={Math.random().toString()} />;
    let bg = themeColors.cardBackground, color = themeColors.text, style = [styles.keyButton];
    if (key === 'backspace' || key === 'C') { bg = themeColors.iconBackground; color = themeColors.danger; }
    else if (['+', '-'].includes(key)) { bg = themeColors.iconBackground; color = themeColors.primary; }
    return (
      <TouchableOpacity
        key={key}
        style={[
          ...style,
          { backgroundColor: bg, borderColor: themeColors.border, ...getShadowStyle('low') },
        ]}
        onPress={() => handlePress(key)}
        activeOpacity={0.85}
      >
        {key === 'backspace'
          ? <Ionicons name="backspace-outline" size={20} color={color} />
          : <Text style={[styles.keyText, { color }]}>{key}</Text>
        }
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: themeColors.background, borderTopColor: themeColors.border }]}>
      <View style={styles.keypad}>
        {KEYS.map((row, idx) => (
          <View key={idx} style={styles.row}>
            {row.map(renderBtn)}
          </View>
        ))}
        <View style={styles.row}>
          <View style={[styles.keyButton, { backgroundColor: themeColors.cardBackground, borderColor: themeColors.border, ...getShadowStyle('low') }]}>
            <TouchableOpacity
              style={{ flex: 1, justifyContent: 'center', alignItems: 'center', height: '100%' }}
              onPress={() => handlePress('0')}
              activeOpacity={0.85}
            >
              <Text style={[styles.keyText, { color: themeColors.text }]}>0</Text>
            </TouchableOpacity>
          </View>
          <View style={{ flex: 1, marginLeft: GAP }}>
            <TouchableOpacity
              style={[styles.keyButton, styles.doneButton, { backgroundColor: '#2196F3', borderColor: themeColors.border, ...getShadowStyle('low'), width: '100%' }]}
              onPress={onDone}
              activeOpacity={0.88}
            >
              <Text style={[styles.keyText, { color: 'white', fontSize: 18 }]}>Xong</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { borderTopWidth: 1, paddingTop: 10, paddingBottom: 12, paddingHorizontal: PADDING },
  keypad: { flexDirection: 'column', gap: 6 },
  row: { flexDirection: 'row', gap: GAP, marginBottom: 2 },
  keyButton: {
    width: BTN_W, height: 44, borderRadius: 14,
    justifyContent: 'center', alignItems: 'center',
    borderWidth: 1, backgroundColor: 'transparent',
  },
  doneButton: {
    width: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 14,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyText: { fontSize: 18, fontWeight: '700' },
});

export default CustomKeypad;
