// File: components/UpdateModal.js
// File này hiển thị modal thông báo khi có cập nhật ứng dụng
// File này liên quan đến: context/UpdateContext.js

import React from 'react';
import { 
  Modal, 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useUpdate } from '../context/UpdateContext';

// Component đơn giản
function UpdateModal({ visible, onClose }) {
  const { openAppStore } = useUpdate();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Ionicons name="arrow-up-circle" size={24} color="#2196F3" />
            </View>
            <Text style={styles.title}>
              Đ<PERSON> có phiên bản mới
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#999" />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <Text style={styles.message}>
              Đã có phiên bản mới của ứng dụng. Bạn nên cập nhật để có trải nghiệm tốt nhất và sử dụng các tính năng mới nhất.
            </Text>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity 
              style={styles.updateButton}
              onPress={openAppStore}
            >
              <Ionicons name="cloud-download-outline" size={20} color="white" />
              <Text style={styles.updateButtonText}>
                Cập nhật ngay
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.laterButton} 
              onPress={onClose}
            >
              <Text style={styles.laterButtonText}>
                Để sau
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginLeft: 10,
  },
  iconContainer: {
    marginRight: 5,
  },
  closeButton: {
    padding: 5,
  },
  content: {
    padding: 16,
  },
  message: {
    fontSize: 16,
    lineHeight: 22,
    color: '#333',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  updateButton: {
    backgroundColor: '#2196F3',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    flexDirection: 'row',
  },
  updateButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  laterButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  laterButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#999',
  },
});

export default UpdateModal;