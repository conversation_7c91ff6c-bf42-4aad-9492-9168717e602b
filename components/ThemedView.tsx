import { useColorScheme } from "@/hooks/useColorScheme";
import { View, type ViewProps } from "react-native";

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedView({
  style,
  lightColor,
  darkColor,
  ...otherProps
}: ThemedViewProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === "dark";

  // Mặc định, sử dụng màu nền cho giao diện sáng/tối
  const backgroundColor = isDark
    ? darkColor || "#1e1e1e"
    : lightColor || "#ffffff";

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
