// File: components/ReferralCodeInput.tsx
// File này liên quan đến: app/(tabs)/settings.tsx, lib/services/referral-code-service.ts

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/context/ThemeContext";
import { referralCodeService, ReferralCodeInfo, UserReferralInfo } from "@/lib/services/referral-code-service";

interface ReferralCodeInputProps {
  userId: string;
  onCodeUsed?: (referrerName: string) => void;
}

export default function ReferralCodeInput({ userId, onCodeUsed }: ReferralCodeInputProps) {
  const { themeColors, getCardStyle, getShadowStyle } = useTheme();
  const [inputCode, setInputCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [codeInfo, setCodeInfo] = useState<ReferralCodeInfo | null>(null);
  const [userReferralInfo, setUserReferralInfo] = useState<UserReferralInfo | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    loadUserReferralInfo();
  }, [userId]);

  const loadUserReferralInfo = async () => {
    try {
      setInitialLoading(true);
      const info = await referralCodeService.getUserReferralInfo(userId);
      setUserReferralInfo(info);
    } catch (error) {
      console.error("Error loading user referral info:", error);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleCodeChange = async (text: string) => {
    // Chỉ cho phép nhập số
    const numericText = text.replace(/[^0-9]/g, "").substring(0, 6);
    setInputCode(numericText);
    setCodeInfo(null);

    // Kiểm tra mã khi nhập đủ 6 số
    if (numericText.length === 6) {
      await checkCode(numericText);
    }
  };

  const checkCode = async (code: string) => {
    if (!referralCodeService.validateCodeFormat(code)) {
      setCodeInfo(null);
      return;
    }

    try {
      setChecking(true);
      
      // Kiểm tra không được dùng mã của chính mình
      const isOwnCode = await referralCodeService.isUserOwnCode(userId, code);
      if (isOwnCode) {
        Alert.alert("Lỗi", "Không thể sử dụng mã giới thiệu của chính mình");
        setInputCode("");
        setCodeInfo(null);
        return;
      }

      const info = await referralCodeService.checkReferralCode(code);
      setCodeInfo(info);

      if (!info.isValid) {
        Alert.alert("Mã không hợp lệ", "Mã giới thiệu không tồn tại. Vui lòng kiểm tra lại.");
        setInputCode("");
      }
    } catch (error) {
      console.error("Error checking code:", error);
      Alert.alert("Lỗi", "Không thể kiểm tra mã giới thiệu. Vui lòng thử lại.");
    } finally {
      setChecking(false);
    }
  };

  const handleConfirmCode = async () => {
    if (!codeInfo || !codeInfo.isValid) {
      Alert.alert("Lỗi", "Vui lòng nhập mã giới thiệu hợp lệ");
      return;
    }

    Alert.alert(
      "Xác nhận sử dụng mã",
      `Bạn có chắc muốn sử dụng mã giới thiệu của ${codeInfo.ownerName}?\n\nLưu ý: Bạn chỉ có thể sử dụng mã giới thiệu một lần duy nhất.`,
      [
        {
          text: "Hủy",
          style: "cancel",
        },
        {
          text: "Xác nhận",
          onPress: useCode,
        },
      ]
    );
  };

  const useCode = async () => {
    if (!codeInfo) return;

    try {
      setLoading(true);
      const success = await referralCodeService.useReferralCode(
        userId,
        codeInfo.code,
        codeInfo.ownerId
      );

      if (success) {
        Alert.alert(
          "Thành công!",
          `Bạn đã sử dụng mã giới thiệu của ${codeInfo.ownerName} thành công!`,
          [
            {
              text: "OK",
              onPress: () => {
                onCodeUsed?.(codeInfo.ownerName);
                loadUserReferralInfo(); // Reload thông tin
              },
            },
          ]
        );
      }
    } catch (error: any) {
      Alert.alert("Lỗi", error.message || "Không thể sử dụng mã giới thiệu");
    } finally {
      setLoading(false);
    }
  };

  const formatDisplayCode = (code: string) => {
    return code.replace(/(\d{3})(\d{3})/, "$1-$2");
  };

  if (initialLoading) {
    return (
      <View style={[styles.container, getCardStyle("medium"), { backgroundColor: themeColors.cardBackground }]}>
        <ActivityIndicator size="small" color={themeColors.primary} />
        <Text style={[styles.loadingText, { color: themeColors.secondaryText }]}>
          Đang tải thông tin...
        </Text>
      </View>
    );
  }

  // Nếu đã sử dụng mã rồi, hiển thị thông tin
  if (userReferralInfo?.hasUsedCode) {
    return (
      <View style={[styles.container, getCardStyle("medium"), { backgroundColor: themeColors.cardBackground }]}>
        <View style={styles.usedHeader}>
          <View style={[styles.successIcon, { backgroundColor: "#4CAF5020" }]}>
            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
          </View>
          <Text style={[styles.usedTitle, { color: themeColors.text }]}>
            Mã giới thiệu đã sử dụng
          </Text>
        </View>

        <View style={styles.referrerInfo}>
          <View style={styles.referrerRow}>
            <Text style={[styles.referrerLabel, { color: themeColors.secondaryText }]}>
              👤 Người giới thiệu:
            </Text>
            <Text style={[styles.referrerName, { color: themeColors.text }]}>
              {userReferralInfo.referrerName}
            </Text>
          </View>
          
          <View style={styles.referrerRow}>
            <Text style={[styles.referrerLabel, { color: themeColors.secondaryText }]}>
              🎯 Mã đã sử dụng:
            </Text>
            <Text style={[styles.referrerCode, { color: themeColors.primary }]}>
              {userReferralInfo.usedCode ? formatDisplayCode(userReferralInfo.usedCode) : ""}
            </Text>
          </View>

          {userReferralInfo.usedAt && (
            <View style={styles.referrerRow}>
              <Text style={[styles.referrerLabel, { color: themeColors.secondaryText }]}>
                📅 Thời gian:
              </Text>
              <Text style={[styles.referrerDate, { color: themeColors.secondaryText }]}>
                {new Date(userReferralInfo.usedAt).toLocaleDateString("vi-VN")}
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  }

  // Form nhập mã
  return (
    <View style={[styles.container, getCardStyle("medium"), { backgroundColor: themeColors.cardBackground }]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: themeColors.iconBackground }]}>
          <Ionicons name="gift" size={24} color={themeColors.primary} />
        </View>
        <View style={styles.headerText}>
          <Text style={[styles.title, { color: themeColors.text }]}>
            Mã giới thiệu
          </Text>
          <Text style={[styles.subtitle, { color: themeColors.secondaryText }]}>
            Nhập mã 6 chữ số để nhận ưu đãi
          </Text>
        </View>
      </View>

      <View style={styles.inputSection}>
        <View style={styles.inputContainer}>
          <TextInput
            style={[
              styles.codeInput,
              {
                backgroundColor: themeColors.iconBackground,
                color: themeColors.text,
                borderColor: codeInfo?.isValid ? "#4CAF50" : themeColors.border,
                borderWidth: 2,
              },
            ]}
            value={inputCode}
            onChangeText={handleCodeChange}
            placeholder="000000"
            placeholderTextColor={themeColors.secondaryText}
            keyboardType="numeric"
            maxLength={6}
            textAlign="center"
            fontSize={20}
            fontWeight="600"
            letterSpacing={4}
          />
          
          {checking && (
            <View style={styles.checkingIndicator}>
              <ActivityIndicator size="small" color={themeColors.primary} />
            </View>
          )}
        </View>

        {codeInfo?.isValid && (
          <View style={[styles.codePreview, { backgroundColor: "#4CAF5010", borderColor: "#4CAF50" }]}>
            <View style={styles.previewRow}>
              <Ionicons name="person" size={16} color="#4CAF50" />
              <Text style={[styles.previewText, { color: "#4CAF50" }]}>
                Mã của: {codeInfo.ownerName}
              </Text>
            </View>
          </View>
        )}

        <TouchableOpacity
          style={[
            styles.confirmButton,
            {
              backgroundColor: codeInfo?.isValid ? themeColors.primary : themeColors.iconBackground,
              ...getShadowStyle("low"),
            },
          ]}
          onPress={handleConfirmCode}
          disabled={!codeInfo?.isValid || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons 
                name="checkmark" 
                size={20} 
                color={codeInfo?.isValid ? "#fff" : themeColors.secondaryText} 
              />
              <Text
                style={[
                  styles.confirmText,
                  { color: codeInfo?.isValid ? "#fff" : themeColors.secondaryText },
                ]}
              >
                Xác nhận sử dụng
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      <View style={styles.noteSection}>
        <Text style={[styles.noteText, { color: themeColors.secondaryText }]}>
          💡 Bạn chỉ có thể sử dụng mã giới thiệu một lần duy nhất
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 16,
    marginVertical: 8,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    textAlign: "center",
  },
  usedHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  successIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  usedTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  referrerInfo: {
    marginTop: 8,
  },
  referrerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  referrerLabel: {
    fontSize: 14,
    width: 120,
  },
  referrerName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  referrerCode: {
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: 2,
    flex: 1,
  },
  referrerDate: {
    fontSize: 14,
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
  },
  inputSection: {
    marginBottom: 16,
  },
  inputContainer: {
    position: "relative",
    marginBottom: 12,
  },
  codeInput: {
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    fontSize: 20,
    fontWeight: "600",
    letterSpacing: 4,
  },
  checkingIndicator: {
    position: "absolute",
    right: 16,
    top: "50%",
    transform: [{ translateY: -10 }],
  },
  codePreview: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  previewRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  previewText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 6,
  },
  confirmButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 14,
    borderRadius: 12,
  },
  confirmText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  noteSection: {
    marginTop: 8,
  },
  noteText: {
    fontSize: 12,
    textAlign: "center",
    lineHeight: 16,
  },
});