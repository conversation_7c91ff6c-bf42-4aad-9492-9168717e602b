import { Text } from "@/components/Themed";
import { useCurrency } from "@/context/CurrencyContext";
import { TextProps } from "react-native";

interface FormattedCurrencyProps extends TextProps {
  value: number;
  style?: any;
}

export default function FormattedCurrency(props: FormattedCurrencyProps) {
  const { value, style, ...otherProps } = props;
  const { formatCurrency } = useCurrency();

  return (
    <Text style={style} {...otherProps}>
      {formatCurrency(value)}
    </Text>
  );
}
