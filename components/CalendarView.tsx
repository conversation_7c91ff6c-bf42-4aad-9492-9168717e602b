// File: components/CalendarView.tsx
// Component này cung cấp giao diện lịch có thể tái sử dụng trong toàn ứng dụng
// File này liên quan đến: app/(more)/calendar.tsx, context/ThemeContext.tsx

import React, { useState, useEffect, useMemo } from "react";
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/context/ThemeContext";

// Kiểu dữ liệu thể hiện thông tin của một ngày
export interface DayInfo {
  date: Date;
  totalAmount: number;
  hasTransactions: boolean;
  isPositive: boolean;
}

// Props cho component
interface CalendarViewProps {
  // Tháng hiện tại đang hiển thị
  currentMonth: Date;
  // Callback khi thay đổi tháng
  onMonthChange?: (newMonth: Date) => void;
  // Callback khi chọn một ngày
  onSelectDate?: (date: Date) => void;
  // Ngày đang được chọn
  selectedDate?: Date;
  // Dữ liệu cho mỗi ngày (tổng tiền, màu sắc, v.v.)
  dayInfoMap?: Map<string, DayInfo>;
  // Các style tùy chỉnh
  containerStyle?: ViewStyle;
  // Chế độ hiển thị (mặc định là tháng)
  viewMode?: "month" | "week" | "day";
  // Callback khi thay đổi chế độ xem
  onViewModeChange?: (mode: "month" | "week" | "day") => void;
  // Hiển thị đường chỉ báo cho mỗi ngày
  showDayIndicators?: boolean;
  // Hiển thị số tiền cho mỗi ngày
  showDayAmounts?: boolean;
}

// Chuyển đổi ngày thành chuỗi key để lưu trong Map
function dateToKey(date: Date): string {
  return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;
}

// Hàm định dạng tiền tệ
function formatCurrency(amount: number): string {
  return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Component Calendar chính
export default function CalendarView({
  currentMonth,
  onMonthChange,
  onSelectDate,
  selectedDate = new Date(),
  dayInfoMap = new Map(),
  containerStyle,
  viewMode = "month",
  onViewModeChange,
  showDayIndicators = true,
  showDayAmounts = true,
}: CalendarViewProps) {
  const { themeColors, isDark, getCardStyle } = useTheme();
  const today = new Date();

  // Tính toán các ngày trong tháng hiện tại
  const daysInMonth = useMemo(() => {
    return generateCalendarDays(currentMonth);
  }, [currentMonth]);

  // Tính toán các ngày trong tuần (nếu đang ở chế độ xem tuần)
  const daysInWeek = useMemo(() => {
    const startOfWeek = new Date(selectedDate);
    const day = selectedDate.getDay(); // 0 = CN, 1 = T2, ...
    startOfWeek.setDate(selectedDate.getDate() - day);
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      days.push(day);
    }
    return days;
  }, [selectedDate]);

  // Xử lý thay đổi tháng
  const handleMonthChange = (increment: number) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + increment);
    if (onMonthChange) {
      onMonthChange(newMonth);
    }
  };

  // Xử lý khi chọn một ngày
  const handleSelectDate = (date: Date) => {
    if (onSelectDate) {
      onSelectDate(date);
    }
  };

  // Lấy thông tin về một ngày từ Map
  const getDayInfo = (date: Date): DayInfo | undefined => {
    const key = dateToKey(date);
    return dayInfoMap.get(key);
  };

  // Tính toán màu cho ô ngày dựa vào thông tin
  const getDayColor = (date: Date) => {
    const info = getDayInfo(date);
    if (!info) return {};

    if (info.isPositive) {
      return { backgroundColor: "rgba(46, 204, 113, 0.2)" }; // Thu nhập
    } else {
      return { backgroundColor: "rgba(231, 76, 60, 0.2)" }; // Chi tiêu
    }
  };

  // Hiển thị chỉ báo cho mỗi ngày
  const renderDayIndicator = (date: Date) => {
    if (!showDayIndicators) return null;
    
    const info = getDayInfo(date);
    if (!info || info.totalAmount === 0) return null;

    const width = Math.min(Math.abs(info.totalAmount) / 1000000 * 100, 100); // Tối đa 100% chiều rộng
    const barColor = info.isPositive ? "#2ecc71" : "#e74c3c";

    return (
      <View style={styles.dayIndicator}>
        <View
          style={[
            styles.dayIndicatorBar,
            { width: `${width}%`, backgroundColor: barColor },
          ]}
        />
      </View>
    );
  };

  // Render header của lịch (tháng/năm và các nút điều hướng)
  const renderCalendarHeader = () => {
    const monthYear = `${currentMonth.toLocaleString("vi", {
      month: "long",
      year: "numeric",
    })}`;

    return (
      <View style={styles.calendarHeader}>
        <TouchableOpacity onPress={() => handleMonthChange(-1)}>
          <Ionicons name="chevron-back" size={24} color={themeColors.primary} />
        </TouchableOpacity>
        <Text style={[styles.monthTitle, { color: themeColors.text }]}>
          {monthYear}
        </Text>
        <TouchableOpacity onPress={() => handleMonthChange(1)}>
          <Ionicons name="chevron-forward" size={24} color={themeColors.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  // Render nút chuyển đổi chế độ xem
  const renderViewModeSelector = () => {
    if (!onViewModeChange) return null;

    return (
      <View style={styles.viewModeSelector}>
        {(["month", "week", "day"] as Array<"month" | "week" | "day">).map((mode) => (
          <TouchableOpacity
            key={mode}
            style={[
              styles.viewModeButton,
              viewMode === mode && {
                backgroundColor: themeColors.primary,
              },
            ]}
            onPress={() => onViewModeChange(mode)}
          >
            <Text
              style={[
                styles.viewModeText,
                {
                  color:
                    viewMode === mode
                      ? themeColors.buttonText
                      : themeColors.text,
                },
              ]}
            >
              {mode === "month"
                ? "Tháng"
                : mode === "week"
                ? "Tuần"
                : "Ngày"}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render tên các ngày trong tuần
  const renderWeekDayNames = () => {
    const weekDays = ["CN", "T2", "T3", "T4", "T5", "T6", "T7"];
    return (
      <View style={styles.weekDayRow}>
        {weekDays.map((day, index) => (
          <View key={index} style={styles.weekDayCell}>
            <Text
              style={[styles.weekDayText, { color: themeColors.secondaryText }]}
            >
              {day}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  // Render lịch theo chế độ tháng
  const renderMonthView = () => {
    const numRows = Math.ceil(daysInMonth.length / 7);
    const rows = [];

    for (let i = 0; i < numRows; i++) {
      const rowCells = [];
      for (let j = 0; j < 7; j++) {
        const dayIndex = i * 7 + j;
        if (dayIndex >= daysInMonth.length) {
          rowCells.push(<View key={`empty-${dayIndex}`} style={styles.dayCell} />);
          continue;
        }

        const day = daysInMonth[dayIndex];
        const isToday = isSameDay(day, today);
        const isSelected = isSameDay(day, selectedDate);
        const isCurrentMonth = day.getMonth() === currentMonth.getMonth();
        const dayInfo = getDayInfo(day);
        const hasAmount = dayInfo && dayInfo.totalAmount !== 0;
        const formattedAmount = hasAmount 
          ? formatCurrency(Math.abs(dayInfo.totalAmount)) 
          : "";

        rowCells.push(
          <TouchableOpacity
            key={dayIndex}
            style={[
              styles.dayCell,
              getDayColor(day),
              isToday && styles.todayCell,
              isSelected && styles.selectedCell,
              !isCurrentMonth && styles.otherMonthCell,
            ]}
            onPress={() => handleSelectDate(day)}
          >
            <Text
              style={[
                styles.dayNumber,
                {
                  color: isCurrentMonth
                    ? themeColors.text
                    : themeColors.disabledText,
                  fontWeight: isToday ? "bold" : "normal",
                },
              ]}
            >
              {day.getDate()}
            </Text>
            {showDayAmounts && hasAmount && (
              <Text
                style={[
                  styles.dayAmount,
                  { color: dayInfo.isPositive ? "#2ecc71" : "#e74c3c" },
                ]}
                numberOfLines={1}
              >
                {dayInfo.isPositive ? "+" : "-"}{formattedAmount}
              </Text>
            )}
            {renderDayIndicator(day)}
          </TouchableOpacity>
        );
      }

      rows.push(
        <View key={`row-${i}`} style={styles.calendarRow}>
          {rowCells}
        </View>
      );
    }

    return (
      <View style={styles.calendar}>
        {renderWeekDayNames()}
        {rows}
      </View>
    );
  };

  // Render lịch theo chế độ tuần
  const renderWeekView = () => {
    return (
      <View style={styles.calendar}>
        {renderWeekDayNames()}
        <View style={styles.calendarRow}>
          {daysInWeek.map((day, index) => {
            const isToday = isSameDay(day, today);
            const isSelected = isSameDay(day, selectedDate);
            const dayInfo = getDayInfo(day);
            const hasAmount = dayInfo && dayInfo.totalAmount !== 0;
            const formattedAmount = hasAmount 
              ? formatCurrency(Math.abs(dayInfo.totalAmount)) 
              : "";

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.weekDayCell,
                  getDayColor(day),
                  isToday && styles.todayCell,
                  isSelected && styles.selectedCell,
                ]}
                onPress={() => handleSelectDate(day)}
              >
                <Text style={[styles.weekDayLabel, { color: themeColors.secondaryText }]}>
                  {day.toLocaleDateString("vi", { weekday: "short" })}
                </Text>
                <Text
                  style={[
                    styles.dayNumber,
                    { color: themeColors.text, fontWeight: isToday ? "bold" : "normal" },
                  ]}
                >
                  {day.getDate()}
                </Text>
                {showDayAmounts && hasAmount && (
                  <Text
                    style={[
                      styles.dayAmount,
                      { color: dayInfo.isPositive ? "#2ecc71" : "#e74c3c" },
                    ]}
                  >
                    {dayInfo.isPositive ? "+" : "-"}{formattedAmount}
                  </Text>
                )}
                {renderDayIndicator(day)}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // Render chế độ xem ngày
  const renderDayView = () => {
    return (
      <View style={styles.dayViewContainer}>
        <Text style={[styles.dayViewText, { color: themeColors.text }]}>
          {selectedDate.toLocaleDateString("vi", {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </Text>
        
        {showDayAmounts && getDayInfo(selectedDate) && (
          <View style={styles.dayViewAmountContainer}>
            <Text style={[styles.dayViewAmount, { 
              color: getDayInfo(selectedDate)?.isPositive ? "#2ecc71" : "#e74c3c" 
            }]}>
              {getDayInfo(selectedDate)?.isPositive ? "+" : "-"}
              {formatCurrency(Math.abs(getDayInfo(selectedDate)?.totalAmount || 0))} đ
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {renderCalendarHeader()}
      {renderViewModeSelector()}
      
      {viewMode === "month" && renderMonthView()}
      {viewMode === "week" && renderWeekView()}
      {viewMode === "day" && renderDayView()}
    </View>
  );
}

// Các hàm tiện ích
function isSameDay(date1: Date, date2: Date) {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
}

function generateCalendarDays(monthDate: Date) {
  const year = monthDate.getFullYear();
  const month = monthDate.getMonth();

  // Ngày đầu tiên của tháng
  const firstDay = new Date(year, month, 1);
  // Ngày cuối cùng của tháng
  const lastDay = new Date(year, month + 1, 0);

  const days = [];

  // Thêm các ngày từ tháng trước để lấp đầy tuần đầu tiên
  const firstDayOfWeek = firstDay.getDay(); // 0 = Chủ nhật
  for (let i = firstDayOfWeek; i > 0; i--) {
    const prevMonthDay = new Date(year, month, 1 - i);
    days.push(prevMonthDay);
  }

  // Thêm các ngày trong tháng hiện tại
  for (let i = 1; i <= lastDay.getDate(); i++) {
    days.push(new Date(year, month, i));
  }

  // Thêm các ngày từ tháng tiếp theo để lấp đầy tuần cuối cùng
  const lastDayOfWeek = lastDay.getDay(); // 0 = Chủ nhật
  for (let i = 1; i < 7 - lastDayOfWeek; i++) {
    days.push(new Date(year, month + 1, i));
  }

  return days;
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: "hidden",
  },
  calendarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  monthTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  viewModeSelector: {
    flexDirection: "row",
    padding: 8,
    justifyContent: "center",
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  viewModeButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  viewModeText: {
    fontSize: 14,
    fontWeight: "500",
  },
  weekDayRow: {
    flexDirection: "row",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  weekDayCell: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  weekDayText: {
    fontSize: 12,
    fontWeight: "500",
  },
  calendarRow: {
    flexDirection: "row",
    height: 70,
  },
  dayCell: {
    flex: 1,
    padding: 4,
    alignItems: "center",
    borderWidth: 0.5,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  todayCell: {
    borderWidth: 1,
    borderColor: "#3498db",
  },
  selectedCell: {
    backgroundColor: "rgba(52, 152, 219, 0.1)",
  },
  otherMonthCell: {
    opacity: 0.6,
  },
  dayNumber: {
    fontSize: 14,
  },
  dayAmount: {
    fontSize: 10,
    marginTop: 2,
  },
  dayIndicator: {
    width: "100%",
    height: 3,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    marginTop: 4,
    borderRadius: 1.5,
  },
  dayIndicatorBar: {
    height: "100%",
    borderRadius: 1.5,
  },
  weekDayLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  dayViewContainer: {
    padding: 16,
    alignItems: "center",
  },
  dayViewText: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  dayViewAmountContainer: {
    marginTop: 8,
    padding: 8,
    borderRadius: 8,
    backgroundColor: "rgba(0, 0, 0, 0.05)",
  },
  dayViewAmount: {
    fontSize: 20,
    fontWeight: "bold",
  },
});