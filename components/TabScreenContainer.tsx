import { Colors } from "@/constants/Colors";
import { useTheme } from "@/context/ThemeContext";
import { Platform, StyleSheet, Text, View, ViewProps } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

interface TabScreenContainerProps extends ViewProps {
  children: React.ReactNode;
  title?: string;
}

export default function TabScreenContainer({
  children,
  style,
  title,
  ...rest
}: TabScreenContainerProps) {
  const { isDark } = useTheme();
  const theme = Colors[isDark ? "dark" : "light"];
  const insets = useSafeAreaInsets();

  const tabBarHeight = Platform.OS === "ios" ? 95 : 60;
  const paddingBottom = tabBarHeight;

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.background,
          paddingBottom: paddingBottom,
        },
        style,
      ]}
      {...rest}
    >
      {title && (
        <View style={[styles.header, { backgroundColor: theme.background }]}>
          <Text style={[styles.title, { color: theme.text }]}>{title}</Text>
        </View>
      )}
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 8,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
  },
});
