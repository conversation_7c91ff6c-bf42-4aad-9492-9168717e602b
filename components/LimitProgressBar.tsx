// File: components/LimitProgressBar.tsx
// Component hiển thị progress bar cho giới hạn chi tiêu
// File này liên quan đến: context/ThemeContext.tsx, lib/services/SpendingLimitService.ts

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/context/ThemeContext';
import { useCurrency } from '@/context/CurrencyContext';
import { LimitUsage, SpendingLimitService } from '@/lib/services/SpendingLimitService';

interface LimitProgressBarProps {
  usage: LimitUsage;
  showDetails?: boolean; // Hiển thị số tiền chi tiết
  size?: 'small' | 'medium' | 'large'; // Kích thước
  onPress?: () => void; // Callback khi nhấn vào progress bar
  style?: any; // Custom style
}

export const LimitProgressBar: React.FC<LimitProgressBarProps> = ({
  usage,
  showDetails = true,
  size = 'medium',
  onPress,
  style,
}) => {
  const { themeColors, isDark } = useTheme();
  const { symbol } = useCurrency();

  // Tính toán màu sắc theo trạng thái
  const getProgressColors = () => {
    switch (usage.status) {
      case 'exceeded':
        return {
          progressColor: '#FF5252', // Đỏ đậm
          backgroundColor: isDark ? '#2D1215' : '#FFEBEE',
          textColor: '#FF5252',
        };
      case 'danger':
        return {
          progressColor: '#FF7043', // Cam đỏ
          backgroundColor: isDark ? '#2D1A15' : '#FFF3E0',
          textColor: '#FF7043',
        };
      case 'warning':
        return {
          progressColor: '#FFB74D', // Vàng cam
          backgroundColor: isDark ? '#2D2415' : '#FFFDE7',
          textColor: '#FFB74D',
        };
      default: // safe
        return {
          progressColor: '#66BB6A', // Xanh lá
          backgroundColor: isDark ? '#1B2D1B' : '#F1F8E9',
          textColor: '#66BB6A',
        };
    }
  };

  // Lấy kích thước theo size
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 6,
          borderRadius: 3,
          containerPadding: 8,
          textSize: 12,
          iconSize: 14,
        };
      case 'large':
        return {
          height: 12,
          borderRadius: 6,
          containerPadding: 16,
          textSize: 16,
          iconSize: 20,
        };
      default: // medium
        return {
          height: 8,
          borderRadius: 4,
          containerPadding: 12,
          textSize: 14,
          iconSize: 16,
        };
    }
  };

  // Lấy icon theo trạng thái
  const getStatusIcon = () => {
    switch (usage.status) {
      case 'exceeded':
        return 'alert-circle';
      case 'danger':
        return 'warning';
      case 'warning':
        return 'warning-outline';
      default:
        return 'checkmark-circle-outline';
    }
  };

  // Format số tiền
  const formatAmount = (amount: number) => {
    return amount.toLocaleString('vi-VN') + symbol;
  };

  // Format phần trăm
  const formatPercentage = (percentage: number) => {
    return percentage.toFixed(1) + '%';
  };

  const colors = getProgressColors();
  const sizeStyles = getSizeStyles();
  const percentage = Math.min(usage.percentage, 100); // Giới hạn progress bar ở 100%

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: colors.backgroundColor,
          borderColor: isDark ? themeColors.border : colors.progressColor + '30',
          padding: sizeStyles.containerPadding,
        },
        style,
      ]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      {/* Header với icon và percentage */}
      <View style={[styles.header, { backgroundColor: 'transparent' }]}>
        <View style={[styles.statusContainer, { backgroundColor: 'transparent' }]}>
          <Ionicons
            name={getStatusIcon() as any}
            size={sizeStyles.iconSize}
            color={colors.textColor}
            style={{ marginRight: 6 }}
          />
          <Text
            style={[
              styles.percentageText,
              {
                color: colors.textColor,
                fontSize: sizeStyles.textSize,
                fontWeight: '600',
              },
            ]}
          >
            {formatPercentage(usage.percentage)}
          </Text>
        </View>

        {/* Period info */}
        <Text
          style={[
            styles.periodText,
            {
              color: themeColors.secondaryText,
              fontSize: sizeStyles.textSize - 2,
            },
          ]}
        >
          {SpendingLimitService.formatPeriod(usage.period)}
        </Text>
      </View>

      {/* Progress Bar */}
      <View
        style={[
          styles.progressContainer,
          {
            height: sizeStyles.height,
            borderRadius: sizeStyles.borderRadius,
            backgroundColor: isDark ? themeColors.border : '#E0E0E0',
            marginVertical: size === 'small' ? 4 : 8,
          },
        ]}
      >
        <View
          style={[
            styles.progressBar,
            {
              width: `${percentage}%`,
              height: sizeStyles.height,
              borderRadius: sizeStyles.borderRadius,
              backgroundColor: colors.progressColor,
            },
          ]}
        />
        
        {/* Indicator cho 80% và 100% nếu size không phải small */}
        {size !== 'small' && (
          <>
            {/* 80% line */}
            <View
              style={[
                styles.warningLine,
                {
                  left: '80%',
                  backgroundColor: isDark ? themeColors.secondaryText : '#FFB74D',
                },
              ]}
            />
            {/* 100% line */}
            <View
              style={[
                styles.dangerLine,
                {
                  right: 0,
                  backgroundColor: isDark ? themeColors.secondaryText : '#FF7043',
                },
              ]}
            />
          </>
        )}
      </View>

      {/* Details */}
      {showDetails && (
        <View style={[styles.details, { backgroundColor: 'transparent' }]}>
          <View style={[styles.amountContainer, { backgroundColor: 'transparent' }]}>
            <Text
              style={[
                styles.spentText,
                {
                  color: themeColors.text,
                  fontSize: sizeStyles.textSize,
                  fontWeight: '500',
                },
              ]}
            >
              Đã chi: {formatAmount(usage.currentSpent)}
            </Text>
            <Text
              style={[
                styles.limitText,
                {
                  color: themeColors.secondaryText,
                  fontSize: sizeStyles.textSize - 1,
                },
              ]}
            >
              / {formatAmount(usage.limitAmount)}
            </Text>
          </View>

          {/* Remaining amount */}
          <Text
            style={[
              styles.remainingText,
              {
                color: usage.status === 'exceeded' ? colors.textColor : themeColors.secondaryText,
                fontSize: sizeStyles.textSize - 1,
                fontWeight: usage.status === 'exceeded' ? '600' : '400',
              },
            ]}
          >
            {usage.status === 'exceeded'
              ? `Vượt: ${formatAmount(usage.currentSpent - usage.limitAmount)}`
              : `Còn lại: ${formatAmount(usage.remainingAmount)}`}
          </Text>
        </View>
      )}

      {/* Status message cho trạng thái warning/danger */}
      {(usage.status === 'warning' || usage.status === 'danger' || usage.status === 'exceeded') && 
       size !== 'small' && (
        <View
          style={[
            styles.statusMessage,
            {
              backgroundColor: colors.progressColor + '20',
              borderLeftColor: colors.progressColor,
            },
          ]}
        >
          <Text
            style={[
              styles.statusMessageText,
              {
                color: colors.textColor,
                fontSize: sizeStyles.textSize - 1,
              },
            ]}
          >
            {usage.status === 'exceeded' && 'Đã vượt giới hạn! Hãy cân nhắc điều chỉnh chi tiêu.'}
            {usage.status === 'danger' && 'Đã đạt giới hạn! Hãy thận trọng khi chi tiêu.'}
            {usage.status === 'warning' && 'Gần đạt giới hạn. Theo dõi chi tiêu cẩn thận.'}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  percentageText: {
    fontWeight: '600',
  },
  periodText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  progressContainer: {
    position: 'relative',
    backgroundColor: '#E0E0E0',
  },
  progressBar: {
    position: 'absolute',
    left: 0,
    top: 0,
  },
  warningLine: {
    position: 'absolute',
    top: -2,
    bottom: -2,
    width: 1,
    opacity: 0.7,
  },
  dangerLine: {
    position: 'absolute',
    top: -2,
    bottom: -2,
    width: 1,
    opacity: 0.7,
  },
  details: {
    marginTop: 4,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 2,
  },
  spentText: {
    fontWeight: '500',
  },
  limitText: {
    marginLeft: 4,
  },
  remainingText: {
    fontSize: 12,
  },
  statusMessage: {
    marginTop: 8,
    padding: 8,
    borderRadius: 6,
    borderLeftWidth: 3,
  },
  statusMessageText: {
    fontSize: 12,
    lineHeight: 16,
  },
});