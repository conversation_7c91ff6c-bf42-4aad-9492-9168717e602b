{"name": "moneyup", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "github:react-native-community/datetimepicker", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.4", "buffer": "^6.0.3", "expo": "~53.0.9", "expo-audio": "^0.4.5", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-file-system": "^18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "^16.1.5", "expo-media-library": "^17.1.6", "expo-notifications": "~0.31.2", "expo-router": "5.0.6", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "formik": "^2.4.6", "i18n-js": "^4.5.1", "openai": "^4.98.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-config": "^1.5.5", "react-native-gesture-handler": "~2.24.0", "react-native-iap": "^12.16.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "react-native-dotenv": "^3.4.11", "typescript": "~5.8.3"}, "private": true}