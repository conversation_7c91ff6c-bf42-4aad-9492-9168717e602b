import AsyncStorage from "@react-native-async-storage/async-storage";
import { useEffect, useState } from "react";
import {
  ColorSchemeName,
  useColorScheme as _useColorScheme,
} from "react-native";

// Khóa lưu trữ cài đặt theme trong AsyncStorage
const THEME_PREFERENCE_KEY = "@moneyup_theme_preference";

// Các giá trị có thể có của theme
type ThemePreference = "light" | "dark" | "system";

/**
 * Hook này mở rộng useColorScheme mặc định của React Native để cho phép
 * người dùng tùy chỉnh theme (sáng/tối) thay vì chỉ dựa vào cài đặt hệ thống.
 */
export function useColorScheme(): {
  colorScheme: ColorSchemeName;
  setColorScheme: (theme: ThemePreference) => void;
  themePreference: ThemePreference;
} {
  // Lấy theme hệ thống (sáng/tối)
  const systemColorScheme = _useColorScheme();

  // State lưu trữ tùy chọn theme của người dùng
  const [themePreference, setThemePreference] =
    useState<ThemePreference>("system");

  // Tính toán theme thực tế dựa trên cài đặt và theme hệ thống
  const colorScheme: ColorSchemeName =
    themePreference === "system" ? systemColorScheme : themePreference;

  // Đọc cài đặt theme đã lưu từ storage khi component được mount
  useEffect(() => {
    async function loadThemePreference() {
      try {
        const savedPreference = await AsyncStorage.getItem(
          THEME_PREFERENCE_KEY
        );

        // Nếu có giá trị đã lưu, sử dụng nó
        if (savedPreference) {
          setThemePreference(savedPreference as ThemePreference);
        }
      } catch (error) {
        console.error("Failed to load theme preference:", error);
      }
    }

    loadThemePreference();
  }, []);

  // Hàm để cập nhật theme và lưu vào storage
  const setColorScheme = async (newTheme: ThemePreference) => {
    try {
      setThemePreference(newTheme);
      await AsyncStorage.setItem(THEME_PREFERENCE_KEY, newTheme);
    } catch (error) {
      console.error("Failed to save theme preference:", error);
    }
  };

  return {
    colorScheme,
    setColorScheme,
    themePreference,
  };
}
