// File: hooks/useNotifications.ts
// Custom hook để quản lý notifications trong components
// File này liên quan đến: lib/services/NotificationService.ts

import { useEffect, useState } from 'react';
import * as Notifications from 'expo-notifications';
import { NotificationService, DebtNotificationData } from '@/lib/services/NotificationService';

export interface NotificationPermission {
  status: string;
  canSchedule: boolean;
  canReceive: boolean;
}

export const useNotifications = () => {
  const [permission, setPermission] = useState<NotificationPermission>({
    status: 'undetermined',
    canSchedule: false,
    canReceive: false,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Kiểm tra quyền thông báo khi hook mount
  useEffect(() => {
    checkPermission();
  }, []);

  const checkPermission = async () => {
    try {
      setIsLoading(true);
      const status = await NotificationService.getNotificationPermissionStatus();
      
      setPermission({
        status,
        canSchedule: status === 'granted',
        canReceive: status === 'granted',
      });
    } catch (error) {
      console.error('Lỗi kiểm tra quyền thông báo:', error);
      setPermission({
        status: 'denied',
        canSchedule: false,
        canReceive: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const requestPermission = async (): Promise<boolean> => {
    try {
      const initialized = await NotificationService.initialize();
      
      if (initialized) {
        await checkPermission();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Lỗi xin quyền thông báo:', error);
      return false;
    }
  };

  const scheduleDebtReminder = async (
    debtId: string,
    reminderDate: Date,
    data: DebtNotificationData
  ): Promise<boolean> => {
    try {
      if (!permission.canSchedule) {
        console.log('Không có quyền lên lịch thông báo');
        return false;
      }

      const notificationId = await NotificationService.scheduleDebtReminder(
        `debt_reminder_${debtId}`,
        reminderDate,
        data
      );

      return notificationId !== null;
    } catch (error) {
      console.error('Lỗi lên lịch thông báo nợ:', error);
      return false;
    }
  };

  const scheduleOverdueNotification = async (
    debtId: string,
    dueDate: Date,
    data: DebtNotificationData
  ): Promise<boolean> => {
    try {
      if (!permission.canSchedule) {
        console.log('Không có quyền lên lịch thông báo');
        return false;
      }

      const notificationId = await NotificationService.scheduleOverdueNotification(
        `debt_reminder_${debtId}`,
        dueDate,
        data
      );

      return notificationId !== null;
    } catch (error) {
      console.error('Lỗi lên lịch thông báo quá hạn:', error);
      return false;
    }
  };

  const cancelDebtNotifications = async (debtId: string): Promise<void> => {
    try {
      await NotificationService.cancelDebtNotifications(debtId);
    } catch (error) {
      console.error('Lỗi hủy thông báo nợ:', error);
    }
  };

  const sendImmediateNotification = async (
    title: string,
    body: string,
    data?: any
  ): Promise<boolean> => {
    try {
      if (!permission.canReceive) {
        console.log('Không có quyền gửi thông báo');
        return false;
      }

      const notificationId = await NotificationService.sendImmediateNotification(
        title,
        body,
        data
      );

      return Boolean(notificationId);
    } catch (error) {
      console.error('Lỗi gửi thông báo ngay:', error);
      return false;
    }
  };

  const getScheduledNotifications = async (): Promise<Notifications.NotificationRequest[]> => {
    try {
      return await NotificationService.getScheduledNotifications();
    } catch (error) {
      console.error('Lỗi lấy danh sách thông báo:', error);
      return [];
    }
  };

  const getDebtNotifications = async (debtId?: string): Promise<Notifications.NotificationRequest[]> => {
    try {
      return await NotificationService.getDebtNotifications(debtId);
    } catch (error) {
      console.error('Lỗi lấy thông báo nợ:', error);
      return [];
    }
  };

  const clearAllNotifications = async (): Promise<void> => {
    try {
      await NotificationService.clearAllNotifications();
    } catch (error) {
      console.error('Lỗi xóa tất cả thông báo:', error);
    }
  };

  return {
    // States
    permission,
    isLoading,
    
    // Actions
    checkPermission,
    requestPermission,
    scheduleDebtReminder,
    scheduleOverdueNotification,
    cancelDebtNotifications,
    sendImmediateNotification,
    getScheduledNotifications,
    getDebtNotifications,
    clearAllNotifications,
    
    // Computed
    canUseNotifications: permission.canSchedule && permission.canReceive,
    needsPermission: permission.status !== 'granted',
  };
};

// Hook để lắng nghe notifications trong component
export const useNotificationListener = (
  onNotificationReceived?: (notification: Notifications.Notification) => void,
  onNotificationTapped?: (response: Notifications.NotificationResponse) => void
) => {
  useEffect(() => {
    const receivedSubscription = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('📱 Notification received:', notification);
        onNotificationReceived?.(notification);
      }
    );

    const responseSubscription = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('👆 Notification response:', response);
        onNotificationTapped?.(response);
      }
    );

    return () => {
      receivedSubscription.remove();
      responseSubscription.remove();
    };
  }, [onNotificationReceived, onNotificationTapped]);
};

// Hook để hiển thị trạng thái notification permission
export const useNotificationPermissionStatus = () => {
  const [status, setStatus] = useState<string>('undetermined');

  useEffect(() => {
    const checkStatus = async () => {
      const currentStatus = await NotificationService.getNotificationPermissionStatus();
      setStatus(currentStatus);
    };

    checkStatus();

    // Check lại mỗi khi app become active
    const interval = setInterval(checkStatus, 30000); // 30 giây

    return () => clearInterval(interval);
  }, []);

  const getStatusMessage = (): string => {
    switch (status) {
      case 'granted':
        return 'Đã cấp quyền thông báo';
      case 'denied':
        return 'Đã từ chối quyền thông báo';
      case 'undetermined':
        return 'Chưa xin quyền thông báo';
      default:
        return 'Không xác định';
    }
  };

  const getStatusColor = (): string => {
    switch (status) {
      case 'granted':
        return '#4CAF50'; // Green
      case 'denied':
        return '#F44336'; // Red
      case 'undetermined':
        return '#FF9800'; // Orange
      default:
        return '#757575'; // Gray
    }
  };

  return {
    status,
    message: getStatusMessage(),
    color: getStatusColor(),
    isGranted: status === 'granted',
    isDenied: status === 'denied',
    isUndetermined: status === 'undetermined',
  };
};