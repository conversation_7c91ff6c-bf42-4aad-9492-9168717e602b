-- Create UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table for additional user information
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- <PERSON>reate trigger to automatically create profile when user registers
CREATE OR REPLACE FUNCTION create_profile_for_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO profiles(id, full_name)
  VALUES(NEW.id, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER create_profile_trigger
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION create_profile_for_user();

-- <PERSON>reate wallets table to store user wallets
CREATE TABLE wallets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  balance NUMERIC(15, 2) DEFAULT 0 NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('cash', 'bank', 'ewallet')),
  icon TEXT DEFAULT 'wallet-outline' NOT NULL,
  color TEXT NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for wallets table
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for wallets table
CREATE POLICY wallet_select_policy ON wallets
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY wallet_insert_policy ON wallets
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY wallet_update_policy ON wallets
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY wallet_delete_policy ON wallets
  FOR DELETE USING (auth.uid() = user_id);

-- Create categories table for expense and income categories
CREATE TABLE categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  icon TEXT NOT NULL,
  color TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for categories table
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for categories table
CREATE POLICY category_select_policy ON categories
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY category_insert_policy ON categories
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY category_update_policy ON categories
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY category_delete_policy ON categories
  FOR DELETE USING (auth.uid() = user_id);

-- Create transactions table to store financial transactions
CREATE TABLE transactions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE NOT NULL,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  amount NUMERIC(15, 2) NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense', 'transfer')),
  description TEXT,
  date TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable Row Level Security for transactions table
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for transactions table
CREATE POLICY transaction_select_policy ON transactions
  FOR SELECT USING (auth.uid() = user_id);
  
CREATE POLICY transaction_insert_policy ON transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);
  
CREATE POLICY transaction_update_policy ON transactions
  FOR UPDATE USING (auth.uid() = user_id);
  
CREATE POLICY transaction_delete_policy ON transactions
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update wallet balance when transactions change
CREATE OR REPLACE FUNCTION update_wallet_balance()
RETURNS TRIGGER AS $$
BEGIN
  -- When adding a new transaction
  IF (TG_OP = 'INSERT') THEN
    IF NEW.type = 'income' THEN
      UPDATE wallets SET balance = balance + NEW.amount WHERE id = NEW.wallet_id;
    ELSIF NEW.type = 'expense' THEN
      UPDATE wallets SET balance = balance - NEW.amount WHERE id = NEW.wallet_id;
    END IF;
    
  -- When updating a transaction
  ELSIF (TG_OP = 'UPDATE') THEN
    IF OLD.wallet_id = NEW.wallet_id THEN
      -- Same wallet, just update amount
      IF OLD.type = 'income' THEN
        UPDATE wallets SET balance = balance - OLD.amount + (CASE WHEN NEW.type = 'income' THEN NEW.amount ELSE -NEW.amount END) WHERE id = NEW.wallet_id;
      ELSIF OLD.type = 'expense' THEN
        UPDATE wallets SET balance = balance + OLD.amount + (CASE WHEN NEW.type = 'income' THEN NEW.amount ELSE -NEW.amount END) WHERE id = NEW.wallet_id;
      END IF;
    ELSE
      -- Different wallet, update both wallets
      IF OLD.type = 'income' THEN
        UPDATE wallets SET balance = balance - OLD.amount WHERE id = OLD.wallet_id;
      ELSIF OLD.type = 'expense' THEN
        UPDATE wallets SET balance = balance + OLD.amount WHERE id = OLD.wallet_id;
      END IF;
      
      IF NEW.type = 'income' THEN
        UPDATE wallets SET balance = balance + NEW.amount WHERE id = NEW.wallet_id;
      ELSIF NEW.type = 'expense' THEN
        UPDATE wallets SET balance = balance - NEW.amount WHERE id = NEW.wallet_id;
      END IF;
    END IF;
    
  -- When deleting a transaction
  ELSIF (TG_OP = 'DELETE') THEN
    IF OLD.type = 'income' THEN
      UPDATE wallets SET balance = balance - OLD.amount WHERE id = OLD.wallet_id;
    ELSIF OLD.type = 'expense' THEN
      UPDATE wallets SET balance = balance + OLD.amount WHERE id = OLD.wallet_id;
    END IF;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers to call update_wallet_balance function
CREATE TRIGGER update_wallet_balance_on_insert
AFTER INSERT ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

CREATE TRIGGER update_wallet_balance_on_update
AFTER UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

CREATE TRIGGER update_wallet_balance_on_delete
AFTER DELETE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_wallet_balance();

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update updated_at field
CREATE TRIGGER update_profiles_timestamp
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column();

CREATE TRIGGER update_wallets_timestamp
BEFORE UPDATE ON wallets
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column();

CREATE TRIGGER update_categories_timestamp
BEFORE UPDATE ON categories
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column();

CREATE TRIGGER update_transactions_timestamp
BEFORE UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_timestamp_column(); 