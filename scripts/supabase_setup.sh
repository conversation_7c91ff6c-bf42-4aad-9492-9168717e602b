#!/bin/bash

# This script sets up the Supabase database and storage for AI Money

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting Supabase setup for AI Money...${NC}"

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo -e "${RED}Error: Supabase CLI is not installed.${NC}"
    echo "Please install it first: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Get current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Apply database migrations
echo -e "${YELLOW}Applying database migrations...${NC}"
cat "$ROOT_DIR/supabase/profiles.sql" | supabase db sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully applied profiles migration.${NC}"
else
    echo -e "${RED}Error applying profiles migration.${NC}"
    exit 1
fi

cat "$ROOT_DIR/supabase/migrations.sql" | supabase db sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully applied main migrations.${NC}"
else
    echo -e "${RED}Error applying main migrations.${NC}"
    exit 1
fi

# Set up storage buckets and policies
echo -e "${YELLOW}Setting up storage buckets and policies...${NC}"
cat "$ROOT_DIR/supabase/storage.sql" | supabase db sql
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully set up storage.${NC}"
else
    echo -e "${RED}Error setting up storage.${NC}"
    exit 1
fi

echo -e "${GREEN}AI Money Supabase setup completed successfully!${NC}"
echo -e "${YELLOW}Don't forget to enable Storage in your Supabase project dashboard.${NC}" 