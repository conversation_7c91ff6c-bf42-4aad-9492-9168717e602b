#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}<PERSON>ang cập nhật bảng profiles...${NC}"

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo -e "${RED}Lỗi: Chưa cài đặt Supabase CLI.${NC}"
    echo "Vui lòng cài đặt tại: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Fix các bản ghi có avatar_url = NULL bằng cách tìm ảnh trong storage
echo -e "${YELLOW}Đang sửa lỗi avatar_url...${NC}"

cat << EOF | supabase db sql
-- Kiểm tra nếu profiles đã có avatar_url
DO \$\$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'avatar_url'
    ) THEN
        ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
    END IF;
END \$\$;

-- Lấy danh sách các avatars trong storage
WITH avatar_files AS (
    SELECT 
        obj.name,
        obj.id,
        storage.foldername(obj.name) as folder,
        'https://wfswtpktlloldkhlyclf.supabase.co/storage/v1/object/public/profiles/' || obj.name as url
    FROM storage.objects obj
    WHERE 
        obj.bucket_id = 'profiles' AND
        (storage.foldername(obj.name))[1] = 'avatars'
    ORDER BY obj.created_at DESC
)
-- Cập nhật các profile chưa có avatar_url
UPDATE profiles
SET avatar_url = (
    SELECT url FROM avatar_files LIMIT 1
)
WHERE avatar_url IS NULL;
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Đã cập nhật bảng profiles thành công.${NC}"
else
    echo -e "${RED}Lỗi khi cập nhật bảng profiles.${NC}"
    exit 1
fi

echo -e "${GREEN}Hoàn tất cập nhật!${NC}"
echo -e "${YELLOW}Đã sửa lỗi avatar_url trong bảng profiles.${NC}" 