import { I18n } from "i18n-js";
import en from "../translations/en";
import vi from "../translations/vi";

// Create an i18n instance
const i18n = new I18n({
  en,
  vi,
});

// Export function to set locale for this formatter
export function setDateFormatterLocale(locale: string) {
  i18n.locale = locale;
}

/**
 * Format a date for display in the UI
 * Shows today, yesterday, or date with time
 */
export function formatDate(date: Date): string {
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  // Format hours and minutes with leading zeros
  const hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const timeStr = `${hours}:${minutes}`;

  if (isSameDay(date, today)) {
    return `${timeStr} - ${i18n.t("dateTime.today")}`;
  } else if (isSameDay(date, yesterday)) {
    return `${timeStr} - ${i18n.t("dateTime.yesterday")}`;
  } else {
    return `${timeStr} - ${date.getDate()}/${date.getMonth() + 1}`;
  }
}

/**
 * Format a date for input fields in the format: DD/MM/YYYY
 */
export function formatDateForInput(date: Date): string {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();

  return `${day}/${month}/${year}`;
}

/**
 * Check if two dates are the same day
 */
function isSameDay(date1: Date, date2: Date): boolean {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
}
