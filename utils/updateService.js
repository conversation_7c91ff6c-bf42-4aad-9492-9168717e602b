// File: utils/updateService.js
// File này cung cấp các hàm tiện ích để tự động kiểm tra cập nhật ứng dụng từ App Store
// File này liên quan đến: app/_layout.js, app/(tabs)/settings.js, app/(tabs)/settings/other-apps.js

import Constants from 'expo-constants';
import * as Linking from 'expo-linking';
import { Alert } from 'react-native';

// ID ứng dụng trên App Store
const APP_STORE_ID = '6745965337';
const APP_STORE_URL = `https://apps.apple.com/app/id${APP_STORE_ID}`;

// ID của nhà phát triển Manh Vu trên App Store
const DEVELOPER_ID = '1812796322';
const DEVELOPER_URL = `https://apps.apple.com/us/developer/manh-vu/id${DEVELOPER_ID}`;

// L<PERSON>y phiên bản hiện tại của ứng dụng
export const getCurrentVersion = () => {
  return Constants.expoConfig?.version || '1.0.0';
};

// So sánh hai chuỗi phiên bản (1.0.0 vs 1.0.1)
export const compareVersions = (v1, v2) => {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  
  return 0;
};

// Kiểm tra cập nhật trực tiếp từ App Store
export const checkForUpdates = async () => {
  try {
    // Lấy phiên bản hiện tại
    const currentVersion = getCurrentVersion();
    
    // Lấy thông tin ứng dụng từ API công khai của Apple
    const response = await fetch(`https://itunes.apple.com/lookup?id=${APP_STORE_ID}`);
    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      console.log('Không tìm thấy thông tin ứng dụng trên App Store');
      return { hasUpdate: false };
    }
    
    // Thông tin phiên bản mới nhất từ App Store
    const appStoreInfo = data.results[0];
    const storeVersion = appStoreInfo.version;
    
    // Các thông tin cập nhật khác
    const releaseNotes = appStoreInfo.releaseNotes || 'Không có thông tin cập nhật';
    
    // So sánh phiên bản
    const hasNewVersion = compareVersions(currentVersion, storeVersion) < 0;
    
    return {
      hasUpdate: hasNewVersion,
      isRequired: false, // App Store không cung cấp thông tin này
      latestVersion: storeVersion,
      releaseNotes: releaseNotes,
      updateUrl: APP_STORE_URL
    };
  } catch (error) {
    console.error('Lỗi khi kiểm tra cập nhật:', error);
    return { hasUpdate: false, isRequired: false };
  }
};

// Mở App Store để cập nhật
export const openAppStore = async () => {
  try {
    await Linking.openURL(APP_STORE_URL);
    return true;
  } catch (error) {
    console.error('Lỗi khi mở App Store:', error);
    return false;
  }
};

// Mở trang App Store của nhà phát triển để xem các ứng dụng khác
export const openDeveloperApps = async () => {
  try {
    // URL chính xác của nhà phát triển Manh Vu trên App Store
    const developerUrl = `https://apps.apple.com/us/developer/manh-vu/id${DEVELOPER_ID}`;
    await Linking.openURL(developerUrl);
    return true;
  } catch (error) {
    console.error('Lỗi khi mở trang nhà phát triển:', error);
    return false;
  }
};

// Hàm xử lý kiểm tra cập nhật để sử dụng trong settings.js
export const handleCheckUpdate = async (setCheckingUpdate) => {
  try {
    // Bắt đầu hiển thị trạng thái đang kiểm tra
    if (setCheckingUpdate) {
      setCheckingUpdate(true);
    }
    
    // Kiểm tra cập nhật
    const updateInfo = await checkForUpdates();
    
    // Kết thúc hiển thị trạng thái đang kiểm tra
    if (setCheckingUpdate) {
      setCheckingUpdate(false);
    }
    
    if (updateInfo.hasUpdate) {
      // Có phiên bản mới, hiển thị thông báo
      Alert.alert(
        'Phiên bản mới',
        `Đã có phiên bản mới ${updateInfo.latestVersion}.\n\nThông tin cập nhật:\n${updateInfo.releaseNotes}`,
        [
          {
            text: 'Để sau',
            style: 'cancel'
          },
          {
            text: 'Cập nhật ngay',
            onPress: async () => {
              await openAppStore();
            }
          }
        ]
      );
    } else {
      // Không có cập nhật mới
      Alert.alert(
        'Kiểm tra cập nhật',
        'Bạn đang sử dụng phiên bản mới nhất.',
        [{ text: 'OK' }]
      );
    }
    
    return updateInfo;
  } catch (error) {
    // Kết thúc hiển thị trạng thái đang kiểm tra nếu có lỗi
    if (setCheckingUpdate) {
      setCheckingUpdate(false);
    }
    
    console.error('Lỗi khi kiểm tra cập nhật:', error);
    Alert.alert(
      'Lỗi',
      'Không thể kiểm tra cập nhật. Vui lòng thử lại sau.',
      [{ text: 'OK' }]
    );
    
    return { hasUpdate: false, error: error.message };
  }
};