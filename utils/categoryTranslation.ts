import { useLocalization } from "@/context/LocalizationContext";
import en from "../translations/en";

/**
 * Hook để dịch tên danh mục dựa vào key tiếng Việt
 * @param categoryName Tên danh mục gốc (tiếng Việt)
 * @returns Tên danh mục đã được dịch theo ngôn ngữ hiện tại
 */
export function useCategoryTranslation() {
  const { t, locale } = useLocalization();

  const translateCategoryName = (categoryName: string): string => {
    // Nếu không có tên danh mục hoặc đang sử dụng tiếng Việt, trả về tên gốc
    if (!categoryName || locale === "vi") {
      return categoryName;
    }

    // Kiểm tra xem danh mục có tồn tại trong bản dịch tiếng Anh không
    if (
      locale === "en" &&
      en.categoryNames &&
      categoryName in en.categoryNames
    ) {
      return t(`categoryNames.${categoryName}`);
    }

    // Trường hợp không có bản dịch, tr<PERSON> về tên gốc
    return categoryName;
  };

  return translateCategoryName;
}

/**
 * Kiểm tra xem danh mục có phải là danh mục thu nhập hay không
 * @param categoryName Tên danh mục
 * @returns true nếu là danh mục thu nhập
 */
export function isIncomeCategory(categoryName: string): boolean {
  const incomeCategories = ["Lương", "Thưởng", "Đầu tư", "Khác"];
  return incomeCategories.includes(categoryName);
}

/**
 * Kiểm tra xem danh mục có phải là danh mục chi tiêu hay không
 * @param categoryName Tên danh mục
 * @returns true nếu là danh mục chi tiêu
 */
export function isExpenseCategory(categoryName: string): boolean {
  const expenseCategories = [
    "Ăn uống",
    "Di chuyển",
    "Mua sắm",
    "Hóa đơn",
    "Nhà cửa",
    "Giải trí",
    "Sức khỏe",
    "Giáo dục",
    "Khác",
  ];
  return expenseCategories.includes(categoryName);
}
