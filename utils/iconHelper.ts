/**
 * Get appropriate icon name for wallet type
 */
export function getIconForWalletType(type: string): string {
  switch (type.toLowerCase()) {
    case "cash":
      return "cash-outline";
    case "bank":
      return "card-outline";
    case "ewallet":
      return "wallet-outline";
    default:
      return "wallet-outline";
  }
}

/**
 * Get appropriate icon name for category
 * This is not needed since we store the icon name in the database
 * But we can use it to provide default icons based on category names
 */
export function getIconForCategory(categoryName: string): string {
  const name = categoryName.toLowerCase();

  if (name.includes("ăn") || name.includes("uống") || name.includes("food")) {
    return "restaurant-outline";
  }

  if (
    name.includes("di chuyển") ||
    name.includes("taxi") ||
    name.includes("xe")
  ) {
    return "car-outline";
  }

  if (name.includes("mua sắm") || name.includes("shopping")) {
    return "cart-outline";
  }

  if (
    name.includes("thu nhập") ||
    name.includes("lương") ||
    name.includes("income")
  ) {
    return "cash-outline";
  }

  if (name.includes("giải trí") || name.includes("entertainment")) {
    return "film-outline";
  }

  if (
    name.includes("học") ||
    name.includes("sách") ||
    name.includes("education")
  ) {
    return "book-outline";
  }

  if (name.includes("sức khỏe") || name.includes("health")) {
    return "medkit-outline";
  }

  if (
    name.includes("nhà") ||
    name.includes("thuê") ||
    name.includes("housing")
  ) {
    return "home-outline";
  }

  // Default icon
  return "pricetag-outline";
}
