// Test file for IAP functionality
// Run this to test IAP service

import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet, ScrollView } from 'react-native';
import { 
  initIAP, 
  getSubscriptions, 
  requestSubscription, 
  getCurrentPurchases,
  restorePurchases,
  hasActiveSubscription,
  SubscriptionProduct 
} from './lib/services/iap-service';

export default function TestIAP() {
  const [products, setProducts] = useState<SubscriptionProduct[]>([]);
  const [purchases, setPurchases] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPremium, setIsPremium] = useState(false);

  useEffect(() => {
    testInitIAP();
  }, []);

  const testInitIAP = async () => {
    try {
      setLoading(true);
      console.log('🧪 Testing IAP initialization...');
      
      const result = await initIAP();
      console.log('Init result:', result);
      
      if (result) {
        await testGetSubscriptions();
        await testGetCurrentPurchases();
        await testHasActiveSubscription();
      }
    } catch (error) {
      console.error('Test init error:', error);
      Alert.alert('Error', 'Failed to initialize IAP');
    } finally {
      setLoading(false);
    }
  };

  const testGetSubscriptions = async () => {
    try {
      console.log('🧪 Testing get subscriptions...');
      const subs = await getSubscriptions();
      console.log('Subscriptions:', subs);
      setProducts(subs);
      
      if (subs.length === 0) {
        Alert.alert('Info', 'No subscription products found. This is normal in development mode.');
      } else {
        Alert.alert('Success', `Found ${subs.length} subscription products`);
      }
    } catch (error) {
      console.error('Test get subscriptions error:', error);
    }
  };

  const testGetCurrentPurchases = async () => {
    try {
      console.log('🧪 Testing get current purchases...');
      const currentPurchases = await getCurrentPurchases();
      console.log('Current purchases:', currentPurchases);
      setPurchases(currentPurchases);
    } catch (error) {
      console.error('Test get current purchases error:', error);
    }
  };

  const testHasActiveSubscription = async () => {
    try {
      console.log('🧪 Testing has active subscription...');
      const hasActive = await hasActiveSubscription();
      console.log('Has active subscription:', hasActive);
      setIsPremium(hasActive);
    } catch (error) {
      console.error('Test has active subscription error:', error);
    }
  };

  const testRequestSubscription = async (sku: string) => {
    try {
      setLoading(true);
      console.log('🧪 Testing request subscription:', sku);
      
      const result = await requestSubscription(sku);
      console.log('Purchase result:', result);
      
      if (result) {
        Alert.alert('Success', 'Purchase completed successfully!');
        await testGetCurrentPurchases();
        await testHasActiveSubscription();
      }
    } catch (error: any) {
      console.error('Test request subscription error:', error);
      
      if (error.code === 'E_USER_CANCELLED') {
        Alert.alert('Cancelled', 'Purchase was cancelled by user');
      } else {
        Alert.alert('Error', `Purchase failed: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const testRestorePurchases = async () => {
    try {
      setLoading(true);
      console.log('🧪 Testing restore purchases...');
      
      const restored = await restorePurchases();
      console.log('Restored purchases:', restored);
      
      if (restored.length > 0) {
        Alert.alert('Success', `Restored ${restored.length} purchases`);
        await testHasActiveSubscription();
      } else {
        Alert.alert('Info', 'No purchases to restore');
      }
    } catch (error) {
      console.error('Test restore purchases error:', error);
      Alert.alert('Error', 'Failed to restore purchases');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>IAP Test Screen</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status</Text>
        <Text>Loading: {loading ? 'Yes' : 'No'}</Text>
        <Text>Premium: {isPremium ? 'Yes' : 'No'}</Text>
        <Text>Products found: {products.length}</Text>
        <Text>Current purchases: {purchases.length}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={testInitIAP}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Re-initialize IAP</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={testGetSubscriptions}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Get Subscriptions</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.button} 
          onPress={testRestorePurchases}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Restore Purchases</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Products</Text>
        {products.map((product, index) => (
          <View key={index} style={styles.productItem}>
            <Text style={styles.productTitle}>{product.title}</Text>
            <Text>ID: {product.productId}</Text>
            <Text>Price: {product.localizedPrice}</Text>
            <TouchableOpacity 
              style={styles.purchaseButton}
              onPress={() => testRequestSubscription(product.productId)}
              disabled={loading}
            >
              <Text style={styles.buttonText}>Test Purchase</Text>
            </TouchableOpacity>
          </View>
        ))}
        
        {products.length === 0 && (
          <Text style={styles.emptyText}>
            No products found. In development mode, mock data should be displayed.
          </Text>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Purchases</Text>
        {purchases.map((purchase, index) => (
          <View key={index} style={styles.purchaseItem}>
            <Text>Product ID: {purchase.productId}</Text>
            <Text>Transaction ID: {purchase.transactionId}</Text>
            <Text>Date: {new Date(purchase.transactionDate).toLocaleDateString()}</Text>
          </View>
        ))}
        
        {purchases.length === 0 && (
          <Text style={styles.emptyText}>No current purchases found.</Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  section: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 15,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 6,
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
  productItem: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    marginBottom: 10,
    borderRadius: 6,
  },
  productTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  purchaseButton: {
    backgroundColor: '#34C759',
    padding: 8,
    borderRadius: 4,
    marginTop: 5,
  },
  purchaseItem: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    marginBottom: 10,
    borderRadius: 6,
    backgroundColor: '#f9f9f9',
  },
  emptyText: {
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
    padding: 20,
  },
});
