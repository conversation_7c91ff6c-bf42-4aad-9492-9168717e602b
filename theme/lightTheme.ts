// <PERSON><PERSON><PERSON> ngh<PERSON><PERSON> các màu sắc cho light theme
const lightTheme = {
  // <PERSON><PERSON><PERSON> nền
  background: {
    primary: "#ffffff",
    secondary: "#f8f9fa",
    card: "#ffffff",
    input: "#f5f5f5",
  },

  // <PERSON><PERSON><PERSON> chữ
  text: {
    primary: "#333333",
    secondary: "#666666",
    hint: "#999999",
    disabled: "#dddddd",
  },

  // Màu viền
  border: {
    light: "#eeeeee",
    medium: "#dddddd",
    dark: "#cccccc",
  },

  // M<PERSON>u nút
  button: {
    primary: "#007AFF",
    secondary: "#f8f9fa",
    disabled: "#e0e0e0",
  },

  // Màu danh mục
  category: {
    income: "#4CAF50",
    expense: "#F44336",
    food: "#FF9800",
    transport: "#9C27B0",
    shopping: "#F44336",
    bills: "#2196F3",
    home: "#FF5722",
    entertainment: "#673AB7",
    health: "#4CAF50",
    education: "#009688",
  },

  // <PERSON><PERSON><PERSON> giao diện
  ui: {
    success: "#4CAF50",
    warning: "#FFC107",
    error: "#F44336",
    info: "#2196F3",
  },

  // Độ tương phản
  alpha: {
    low: "rgba(0, 0, 0, 0.05)",
    medium: "rgba(0, 0, 0, 0.1)",
    high: "rgba(0, 0, 0, 0.15)",
  },
};

export default lightTheme;
