// <PERSON><PERSON><PERSON> nghĩa các màu sắc cho dark theme
const darkTheme = {
  // <PERSON><PERSON><PERSON> nền
  background: {
    primary: "#121212",
    secondary: "#1e1e1e",
    card: "#2a2a2a",
    input: "#333333",
  },

  // <PERSON><PERSON><PERSON> chữ
  text: {
    primary: "#ffffff",
    secondary: "#aaaaaa",
    hint: "#666666",
    disabled: "#555555",
  },

  // Màu viền
  border: {
    light: "#333333",
    medium: "#444444",
    dark: "#666666",
  },

  // M<PERSON><PERSON> nút
  button: {
    primary: "#007AFF",
    secondary: "#2a2a2a",
    disabled: "#444444",
  },

  // Màu danh mục
  category: {
    income: "#4CAF50",
    expense: "#F44336",
    food: "#FF9800",
    transport: "#9C27B0",
    shopping: "#F44336",
    bills: "#2196F3",
    home: "#FF5722",
    entertainment: "#673AB7",
    health: "#4CAF50",
    education: "#009688",
  },

  // <PERSON><PERSON><PERSON> giao diện
  ui: {
    success: "#4CAF50",
    warning: "#FFC107",
    error: "#F44336",
    info: "#2196F3",
  },

  // Độ tương phản
  alpha: {
    low: "rgba(255, 255, 255, 0.1)",
    medium: "rgba(255, 255, 255, 0.2)",
    high: "rgba(255, 255, 255, 0.3)",
  },
};

export default darkTheme;
