import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

// Keys for AsyncStorage
const CURRENCY_CODE_KEY = "@moneyup_currency_code";
const CURRENCY_SYMBOL_KEY = "@moneyup_currency_symbol";

// Định nghĩa interface cho context
interface CurrencyContextType {
  currency: string;
  symbol: string;
  setCurrency: (code: string, symbol: string) => void;
  formatCurrency: (amount: number) => string;
}

// Tạo context với giá trị mặc định
const CurrencyContext = createContext<CurrencyContextType>({
  currency: "VND",
  symbol: "₫",
  setCurrency: () => {},
  formatCurrency: () => "",
});

// Hook để sử dụng currency context
export const useCurrency = () => useContext(CurrencyContext);

// Props cho provider
interface CurrencyProviderProps {
  children: ReactNode;
}

// Provider component
export function CurrencyProvider({ children }: CurrencyProviderProps) {
  const [currency, setCurrencyState] = useState("VND");
  const [symbol, setSymbolState] = useState("₫");
  const [isLoading, setIsLoading] = useState(true);

  // Load currency từ AsyncStorage khi component được mount
  useEffect(() => {
    const loadSavedCurrency = async () => {
      try {
        const savedCurrencyCode = await AsyncStorage.getItem(CURRENCY_CODE_KEY);
        const savedCurrencySymbol = await AsyncStorage.getItem(
          CURRENCY_SYMBOL_KEY
        );

        if (savedCurrencyCode) {
          setCurrencyState(savedCurrencyCode);
        }

        if (savedCurrencySymbol) {
          setSymbolState(savedCurrencySymbol);
        }
      } catch (error) {
        console.error("Không thể tải cài đặt tiền tệ:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSavedCurrency();
  }, []);

  // Function để thay đổi đơn vị tiền tệ và lưu vào AsyncStorage
  const setCurrency = async (code: string, currencySymbol: string) => {
    try {
      // Lưu vào state
      setCurrencyState(code);
      setSymbolState(currencySymbol);

      // Lưu vào AsyncStorage
      await AsyncStorage.setItem(CURRENCY_CODE_KEY, code);
      await AsyncStorage.setItem(CURRENCY_SYMBOL_KEY, currencySymbol);
    } catch (error) {
      console.error("Không thể lưu cài đặt tiền tệ:", error);
    }
  };

  // Function định dạng tiền tệ
  const formatCurrency = (amount: number) => {
    // Định dạng dựa trên loại tiền tệ
    switch (currency) {
      case "VND":
        return new Intl.NumberFormat("vi-VN", {
          style: "currency",
          currency: "VND",
          maximumFractionDigits: 0,
        }).format(amount);
      case "USD":
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount);
      case "EUR":
        return new Intl.NumberFormat("de-DE", {
          style: "currency",
          currency: "EUR",
        }).format(amount);
      default:
        // Trường hợp khác chỉ dùng symbol
        return `${symbol}${amount.toLocaleString()}`;
    }
  };

  const value = {
    currency,
    symbol,
    setCurrency,
    formatCurrency,
  };

  // Nếu đang tải dữ liệu từ AsyncStorage, có thể trả về loading state hoặc giá trị mặc định
  if (isLoading) {
    return (
      <CurrencyContext.Provider value={value}>
        {children}
      </CurrencyContext.Provider>
    );
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
}

export default CurrencyContext;
