// File: context/PremiumContext.tsx

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  hasActiveSubscription, 
  getCurrentPurchases, 
  restorePurchases,
  initIAP,
  endIAP 
} from '@/lib/services/iap-service';

interface PremiumContextType {
  isPremium: boolean;
  isLoading: boolean;
  checkPremiumStatus: () => Promise<void>;
  restoreUserPurchases: () => Promise<boolean>;
  setPremiumStatus: (status: boolean) => void;
  premiumFeatures: {
    noAds: boolean;
    advancedReports: boolean;
    cloudBackup: boolean;
    smartNotifications: boolean;
    advancedSecurity: boolean;
    trendPrediction: boolean;
  };
}

const PremiumContext = createContext<PremiumContextType | undefined>(undefined);

interface PremiumProviderProps {
  children: ReactNode;
}

export const PremiumProvider: React.FC<PremiumProviderProps> = ({ children }) => {
  const [isPremium, setIsPremium] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Premium features object
  const premiumFeatures = {
    noAds: isPremium,
    advancedReports: isPremium,
    cloudBackup: isPremium,
    smartNotifications: isPremium,
    advancedSecurity: isPremium,
    trendPrediction: isPremium,
  };

  useEffect(() => {
    initializePremium();
    
    // Cleanup on unmount
    return () => {
      endIAP();
    };
  }, []);

  const initializePremium = async () => {
    try {
      setIsLoading(true);
      
      // Initialize IAP first
      await initIAP();
      
      // Check premium status
      await checkPremiumStatus();
    } catch (error) {
      console.error('Error initializing premium:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkPremiumStatus = async () => {
    try {
      console.log('🔍 Checking premium status...');
      
      // First check locally stored status
      const localPremiumStatus = await AsyncStorage.getItem('user_premium_status');
      if (localPremiumStatus === 'true') {
        console.log('✅ Local premium status: true');
        setIsPremium(true);
      }

      // Then verify with IAP
      const hasActiveSub = await hasActiveSubscription();
      
      if (hasActiveSub) {
        console.log('✅ Active subscription found');
        setIsPremium(true);
        await AsyncStorage.setItem('user_premium_status', 'true');
      } else {
        console.log('❌ No active subscription');
        setIsPremium(false);
        await AsyncStorage.setItem('user_premium_status', 'false');
      }
      
    } catch (error) {
      console.error('❌ Error checking premium status:', error);
      
      // Fallback to local status if IAP check fails
      const localStatus = await AsyncStorage.getItem('user_premium_status');
      setIsPremium(localStatus === 'true');
    }
  };

  const restoreUserPurchases = async (): Promise<boolean> => {
    try {
      console.log('🔄 Restoring user purchases...');
      setIsLoading(true);
      
      const restoredPurchases = await restorePurchases();
      
      if (restoredPurchases.length > 0) {
        console.log('✅ Purchases restored successfully');
        setIsPremium(true);
        await AsyncStorage.setItem('user_premium_status', 'true');
        return true;
      } else {
        console.log('❌ No purchases to restore');
        return false;
      }
    } catch (error) {
      console.error('❌ Error restoring purchases:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const setPremiumStatus = async (status: boolean) => {
    try {
      setIsPremium(status);
      await AsyncStorage.setItem('user_premium_status', status.toString());
      console.log(`✅ Premium status set to: ${status}`);
    } catch (error) {
      console.error('❌ Error setting premium status:', error);
    }
  };

  const value: PremiumContextType = {
    isPremium,
    isLoading,
    checkPremiumStatus,
    restoreUserPurchases,
    setPremiumStatus,
    premiumFeatures,
  };

  return (
    <PremiumContext.Provider value={value}>
      {children}
    </PremiumContext.Provider>
  );
};

export const usePremium = (): PremiumContextType => {
  const context = useContext(PremiumContext);
  if (context === undefined) {
    throw new Error('usePremium must be used within a PremiumProvider');
  }
  return context;
};

// Helper hooks for specific features
export const useNoAds = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.noAds;
};

export const useAdvancedReports = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.advancedReports;
};

export const useCloudBackup = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.cloudBackup;
};

export const useSmartNotifications = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.smartNotifications;
};

export const useAdvancedSecurity = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.advancedSecurity;
};

export const useTrendPrediction = () => {
  const { premiumFeatures } = usePremium();
  return premiumFeatures.trendPrediction;
};
