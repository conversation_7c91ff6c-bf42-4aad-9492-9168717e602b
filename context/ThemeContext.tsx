// File: context/ThemeContext.tsx
// File này được cập nhật với màu nền đen hoàn toàn và màu card sáng hơn
// File này liên quan đến: tất cả các file UI trong ứng dụng

import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from "@/hooks/useColorScheme";
import { getTheme, Theme } from "@/theme";

// Key for AsyncStorage
const THEME_PREFERENCE_KEY = "@moneyup_theme_preference";

// Định nghĩa các loại theme
export type ThemeType = 'light' | 'dark';

// Định nghĩa cấu trúc màu sắc cho theme
export interface ThemeColors {
  // Màu nền chính
  background: string;
  // M<PERSON>u chủ đạo
  primary: string;
  // <PERSON><PERSON>u nền cho thẻ card
  cardBackground: string;
  // <PERSON><PERSON>u văn bản chính
  text: string;
  // Màu viền
  border: string;
  // Màu văn bản phụ
  secondaryText: string;
  // Màu nền cho icon container
  iconBackground: string;
  // Màu bóng đổ
  shadowColor: string;
  // Màu cảnh báo/danger
  danger: string;
  // Màu nền cho nút danger
  dangerBackground: string;
  // Màu viền cho nút danger
  dangerBorder: string;
}

// Định nghĩa các theme - Đã cập nhật thành màu đen hoàn toàn cho dark theme và màu card sáng hơn
export const themes: Record<ThemeType, ThemeColors> = {
  light: {
    background: '#E3F2FD',
    primary: '#2196F3',
    cardBackground: 'white',
    text: '#333333',
    border: '#D1E4FD',
    secondaryText: '#0D47A1',
    iconBackground: '#F5F9FF',
    shadowColor: '#1565C0',
    danger: '#F44336',
    dangerBackground: '#FFEBEE',
    dangerBorder: '#FFCDD2',
  },
  dark: {
    // Màu nền chính đen hoàn toàn
    background: '#000000',
    // Màu chủ đạo sáng để tương phản tốt với nền đen
    primary: '#4FC3F7',
    // Màu nền card sáng hơn một chút (từ #121212 thành #1A1A1A)
    cardBackground: '#1A1A1A',
    // Văn bản trắng để tương phản tốt với nền tối
    text: '#FFFFFF',
    // Màu viền tối nhưng vẫn nhìn thấy được, sáng hơn một chút
    border: '#2A2A2A',
    // Màu văn bản phụ trắng
    secondaryText: '#FFFFFF',
    // Màu nền icon tối, sáng hơn một chút
    iconBackground: '#222222',
    // Màu bóng đổ đen hoàn toàn
    shadowColor: '#000000',
    // Màu danger sáng để tạo tương phản tốt với nền đen
    danger: '#FF8A80',
    // Màu nền cho danger tối đậm
    dangerBackground: '#2D1215',
    dangerBorder: '#3E1A1F',
  },
};

// Định nghĩa style cho shadow
export const getShadowStyle = (isDark: boolean, intensity: 'low' | 'medium' | 'high' = 'medium') => {
  const shadowIntensity = {
    low: {
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDark ? 0.3 : 0.2,
      shadowRadius: 3,
      elevation: 2,
    },
    medium: {
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: isDark ? 0.35 : 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    high: {
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.4 : 0.3,
      shadowRadius: 5,
      elevation: 6,
    },
  };

  return {
    shadowColor: isDark ? themes.dark.shadowColor : themes.light.shadowColor,
    ...shadowIntensity[intensity],
  };
};

// Định nghĩa cấu trúc card style
export const getCardStyle = (isDark: boolean, intensity: 'low' | 'medium' | 'high' = 'medium') => {
  const currentTheme = isDark ? themes.dark : themes.light;
  
  return {
    backgroundColor: currentTheme.cardBackground,
    borderWidth: 1,
    borderColor: currentTheme.border,
    borderRadius: 16,
    ...getShadowStyle(isDark, intensity),
  };
};

// Định nghĩa cấu trúc icon container style
export const getIconContainerStyle = (isDark: boolean) => {
  const currentTheme = isDark ? themes.dark : themes.light;
  
  return {
    backgroundColor: currentTheme.iconBackground,
    borderWidth: 1,
    borderColor: currentTheme.border,
    ...getShadowStyle(isDark, 'low'),
  };
};

// Tạo context cho theme
// Mở rộng interface ThemeContextType với các helpers mới
interface ThemeContextType {
  theme: Theme; // Giữ nguyên loại Theme từ getTheme cho tương thích ngược
  isDark: boolean;
  toggleTheme: () => void;
  // Thêm các thuộc tính mới
  themeColors: ThemeColors; // Theme colors mới
  getCardStyle: (intensity?: 'low' | 'medium' | 'high') => Record<string, any>;
  getIconContainerStyle: () => Record<string, any>;
  getShadowStyle: (intensity?: 'low' | 'medium' | 'high') => Record<string, any>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider component
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { colorScheme, setColorScheme, themePreference } = useColorScheme();

  // Kiểm tra nếu đang ở chế độ tối
  const isDark = colorScheme === "dark";

  // Lấy theme tương ứng từ getTheme
  const theme = getTheme(isDark);

  // Lấy theme colors tương ứng từ themes mới
  const themeColors = isDark ? themes.dark : themes.light;

  // Lưu theme preference vào AsyncStorage khi thay đổi
  useEffect(() => {
    const saveThemePreference = async () => {
      try {
        await AsyncStorage.setItem(THEME_PREFERENCE_KEY, themePreference);
      } catch (error) {
        console.error("Error saving theme preference:", error);
      }
    };

    saveThemePreference();
  }, [themePreference]);

  // Hàm chuyển đổi theme
  const toggleTheme = () => {
    const newTheme = themePreference === "dark" ? "light" : "dark";
    setColorScheme(newTheme);
  };

  // Các hàm helper để lấy style
  const currentGetCardStyle = (intensity: 'low' | 'medium' | 'high' = 'medium') => 
    getCardStyle(isDark, intensity);
  
  const currentGetIconContainerStyle = () => 
    getIconContainerStyle(isDark);
  
  const currentGetShadowStyle = (intensity: 'low' | 'medium' | 'high' = 'medium') => 
    getShadowStyle(isDark, intensity);

  return (
    <ThemeContext.Provider value={{ 
      theme, 
      isDark, 
      toggleTheme, 
      themeColors, 
      getCardStyle: currentGetCardStyle,
      getIconContainerStyle: currentGetIconContainerStyle,
      getShadowStyle: currentGetShadowStyle
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook để sử dụng theme
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};