// File này ở context/AuthContext.tsx
// Những file liên quan tới file này: register.tsx, login.tsx, forgot-password.tsx

import { UserModel } from "@/lib/models/user";
import NetInfo from "@react-native-community/netinfo";
import { Session, User } from "@supabase/supabase-js";
import { useRouter } from "expo-router";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { Alert } from "react-native";
import { supabase, safeAuthListener, cleanupAuthListeners } from "../lib/supabase";
import { useLocalization } from "./LocalizationContext";

// Types
type AuthContextType = {
  avatar: string;
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  checkEmailExists: (email: string) => Promise<boolean>;
  setAvatar: (url: string) => void;
  refreshSession: () => Promise<boolean>;
};

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  avatar: "",
  user: null,
  session: null,
  isLoading: true,
  signUp: async () => {},
  signIn: async () => {},
  signOut: async () => {},
  forgotPassword: async () => {},
  checkEmailExists: async () => false,
  setAvatar: () => {},
  refreshSession: async () => false,
});

// Provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [avatar, setAvatar] = useState("");
  const { t } = useLocalization();
  const sessionRefreshTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isAuthStateChanging = useRef(false);
  const isNavigating = useRef(false);

  const router = useRouter();

  // Cleanup function to avoid memory leaks
  const cleanup = () => {
    // Clear timer
    if (sessionRefreshTimerRef.current) {
      clearInterval(sessionRefreshTimerRef.current);
      sessionRefreshTimerRef.current = null;
    }
    
    // Clean up all auth listeners
    cleanupAuthListeners();
  };

  // Set up periodic session refresh (every 10 minutes)
  useEffect(() => {
    if (session) {
      // Clear any existing timer
      if (sessionRefreshTimerRef.current) {
        clearInterval(sessionRefreshTimerRef.current);
        sessionRefreshTimerRef.current = null;
      }

      // Set up new timer to refresh session every 10 minutes
      sessionRefreshTimerRef.current = setInterval(async () => {
        console.log("Attempting to refresh session...");
        await manualRefreshSession();
      }, 10 * 60 * 1000); // 10 minutes
    }

    return () => {
      if (sessionRefreshTimerRef.current) {
        clearInterval(sessionRefreshTimerRef.current);
        sessionRefreshTimerRef.current = null;
      }
    };
  }, [session]);

  // Listen for auth state changes
  useEffect(() => {
    // Cleanup previous resources
    cleanup();
    
    // Set up auth state change listener
    const setupAuthListener = () => {
      try {
        console.log("[Auth] Setting up auth state change listener");
        
        // Đăng ký listener bằng helper function từ supabase.ts
        safeAuthListener(async (event, currentSession) => {
          console.log("[Auth] Auth state changed:", event);
          
          // Skip if already processing an auth state change to avoid race conditions
          if (isAuthStateChanging.current) {
            console.log("[Auth] Already processing an auth state change, skipping");
            return;
          }
          
          isAuthStateChanging.current = true;
          
          try {
            switch (event) {
              case "INITIAL_SESSION":
                if (currentSession) {
                  console.log("[Auth] Phiên đăng nhập ban đầu hợp lệ");
                  setSession(currentSession);
                  setUser(currentSession.user);

                  try {
                    const userProfile = await UserModel.getCurrentProfile();
                    if (userProfile) {
                      setAvatar(userProfile.avatar_url || "");
                    }
                  } catch (error) {
                    console.error("[Auth] Lỗi khi tải thông tin người dùng:", error);
                  }
                } else {
                  console.log(
                    "[Auth] Không có phiên đăng nhập ban đầu, chuyển hướng đến trang đăng nhập"
                  );
                  if (!isNavigating.current) {
                    navigateToScreen("/(auth)/login");
                  }
                }
                setIsLoading(false);
                break;

              case "SIGNED_IN":
                console.log("[Auth] Đăng nhập thành công");
                if (currentSession) {
                  setSession(currentSession);
                  setUser(currentSession.user);

                  try {
                    const userProfile = await UserModel.getCurrentProfile();
                    if (userProfile) {
                      setAvatar(userProfile.avatar_url || "");
                    }
                  } catch (error) {
                    console.error("[Auth] Lỗi khi tải thông tin người dùng:", error);
                  }
                }
                setIsLoading(false);
                break;

              case "SIGNED_OUT":
                console.log("[Auth] Đã đăng xuất");
                setSession(null);
                setUser(null);
                setAvatar("");
                if (!isNavigating.current) {
                  navigateToScreen("/(auth)/login");
                }
                setIsLoading(false);
                break;

              case "TOKEN_REFRESHED":
                console.log("[Auth] Token đã được làm mới");
                if (currentSession) {
                  setSession(currentSession);
                  setUser(currentSession.user);
                }
                setIsLoading(false);
                break;

              case "USER_UPDATED":
                console.log("[Auth] Thông tin người dùng đã được cập nhật");
                if (currentSession) {
                  setSession(currentSession);
                  setUser(currentSession.user);

                  try {
                    const userProfile = await UserModel.getCurrentProfile();
                    if (userProfile) {
                      setAvatar(userProfile.avatar_url || "");
                    }
                  } catch (error) {
                    console.error("[Auth] Lỗi khi tải thông tin người dùng:", error);
                  }
                }
                setIsLoading(false);
                break;

              default:
                // Xử lý mặc định cho các sự kiện khác
                setSession(currentSession);
                setUser(currentSession?.user ?? null);
                setIsLoading(false);

                if (!currentSession || !currentSession.user) {
                  // Chỉ chuyển hướng đến trang đăng nhập nếu không phải là sự kiện INITIAL_SESSION
                  if (event !== "INITIAL_SESSION" && !isNavigating.current) {
                    console.warn(
                      "[Auth] Phiên đăng nhập hết hạn. Chuyển hướng đến trang đăng nhập."
                    );
                    navigateToScreen("/(auth)/login");
                  }
                }
                break;
            }
          } finally {
            isAuthStateChanging.current = false;
          }
        });
      } catch (error) {
        console.error("[Auth] Error setting up auth listener:", error);
        setIsLoading(false);
      }
    };

    // Chỉ thiết lập listener nếu đang loading
    if (isLoading) {
      setupAuthListener();
      checkUser();
    }

    // Cleanup function
    return cleanup;
  }, []);

  // Safe navigation function to prevent multiple navigations
  const navigateToScreen = (screen: string) => {
    if (isNavigating.current) {
      console.log("[Auth] Already navigating, skipping to:", screen);
      return;
    }
    
    isNavigating.current = true;
    
    // Small delay to prevent navigation issues
    setTimeout(() => {
      try {
        router.replace(screen);
      } catch (error) {
        console.error("[Auth] Navigation error:", error);
      } finally {
        // Reset navigation flag after a slight delay
        setTimeout(() => {
          isNavigating.current = false;
        }, 500);
      }
    }, 100);
  };

  // Check if user is logged in
  const checkUser = async () => {
    try {
      console.log("[Auth] Checking current user session...");
      
      // Kiểm tra phiên đăng nhập hiện tại
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        throw error;
      }
      
      const currentSession = data?.session;

      if (currentSession && currentSession.user) {
        console.log("[Auth] Phiên đăng nhập hợp lệ được tìm thấy");

        // Kiểm tra xem token có gần hết hạn không (còn dưới 1 ngày)
        const expiresAt = currentSession.expires_at || 0;
        const now = Math.floor(Date.now() / 1000); // Thời gian hiện tại tính bằng giây
        const timeUntilExpiry = expiresAt - now;

        console.log(`[Auth] Token hết hạn sau ${timeUntilExpiry} giây`);

        // Nếu token sắp hết hạn (còn dưới 1 ngày) hoặc đã hết hạn, làm mới nó
        if (timeUntilExpiry < 86400 || timeUntilExpiry <= 0) {
          // 86400 giây = 1 ngày
          console.log("[Auth] Token sắp hết hạn, đang làm mới...");
          await manualRefreshSession();
        } else {
          // Sử dụng phiên hiện tại
          setSession(currentSession);
          setUser(currentSession.user);
        }

        // Tải thông tin người dùng
        try {
          const userProfile = await UserModel.getCurrentProfile();
          if (userProfile) {
            setAvatar(userProfile.avatar_url || "");
          }
        } catch (profileError) {
          console.error(
            "[Auth] Lỗi khi tải thông tin người dùng:",
            profileError
          );
        }
      } else {
        console.warn(
          "[Auth] Không tìm thấy phiên đăng nhập hợp lệ. Chuyển hướng đến trang đăng nhập."
        );
        navigateToScreen("/(auth)/login");
      }
    } catch (error) {
      console.error("[Auth] Lỗi khi kiểm tra trạng thái xác thực:", error);
      navigateToScreen("/(auth)/login");
    } finally {
      setIsLoading(false);
    }
  };

  // Check if an email already exists
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false, // Don't create a new user
        },
      });

      // If there's no error when requesting OTP for a non-creating signin,
      // it means the email exists
      return !error;
    } catch (error) {
      // If there's an error, the email likely doesn't exist
      console.log("[Auth] Lỗi khi kiểm tra email:", error);
      return false;
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, name: string) => {
    try {
      // First check if email already exists
      const emailExists = await checkEmailExists(email);
      if (emailExists) {
        Alert.alert(t("common.error"), t("auth.emailAlreadyExists"));
        return;
      }

      // Xóa listeners hiện tại trước khi đăng ký để tránh race conditions
      cleanupAuthListeners();

      const { error, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: { full_name: name },
          emailRedirectTo: null, // Tắt redirect để tránh lỗi deep linking
        },
      });

      if (error) throw error;

      // Handle newly created user
      if (data.user) {
        // If user is created, save info and update session
        if (data.session) {
          setSession(data.session);
          setUser(data.user);
          
          // Trì hoãn việc navigation để tránh race condition
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Chuyển đến trang setup ngay lập tức sau khi đăng ký thành công
          navigateToScreen("/(setup)/welcome");
        } else {
          // Nếu không có session (có thể do cần xác nhận email)
          Alert.alert(
            t("auth.registerSuccess"),
            t("auth.checkEmailForConfirmation")
          );
          navigateToScreen("/(auth)/login");
        }
      } else {
        Alert.alert(
          t("auth.registerSuccess"),
          t("auth.registrationCompleted")
        );
      }
    } catch (error: any) {
      console.error("[Auth] Lỗi đăng ký:", error);
      Alert.alert(t("common.error"), error.message || t("auth.registerError"));
      throw error;
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      // Xóa listeners hiện tại trước khi đăng nhập để tránh race conditions
      cleanupAuthListeners();
      
      const { error, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Handle specific error cases
        if (error.message.includes("Invalid login credentials")) {
          Alert.alert(t("auth.loginFailed"), t("auth.invalidCredentials"));
        } else {
          Alert.alert(
            t("auth.loginFailed"),
            error.message || t("auth.loginError")
          );
        }
        throw error;
      }

      if (!data || !data.user) {
        Alert.alert(t("auth.loginFailed"), t("auth.loginError"));
        return;
      }

      // Kiểm tra xem người dùng đã có categories nào chưa
      // để quyết định điều hướng đến setup hay tabs
      try {
        const { data: categories, error: categoriesError } = await supabase
          .from("categories")
          .select("id")
          .limit(1);

        if (categoriesError) {
          console.error("Lỗi khi kiểm tra categories:", categoriesError);
          navigateToScreen("/(tabs)");
          return;
        }

        // Nếu không có categories nào, chuyển đến trang setup
        // Ngược lại, chuyển đến trang tabs (trang chính)
        setTimeout(() => {
          if (!categories || categories.length === 0) {
            console.log("Chưa có categories, chuyển đến trang setup");
            navigateToScreen("/(setup)/welcome");
          } else {
            console.log("Đã có categories, chuyển đến trang chính");
            navigateToScreen("/(tabs)");
          }
        }, 300);
      } catch (error) {
        console.error("Lỗi khi kiểm tra và chuyển hướng:", error);
        navigateToScreen("/(tabs)");
      }
    } catch (error: any) {
      // General error handling is now done in the specific error block above
      throw error;
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      // Clear any existing timer
      if (sessionRefreshTimerRef.current) {
        clearInterval(sessionRefreshTimerRef.current);
        sessionRefreshTimerRef.current = null;
      }
      
      // Xóa listeners hiện tại trước khi đăng xuất để tránh race conditions
      cleanupAuthListeners();
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear local state
      setSession(null);
      setUser(null);
      setAvatar("");
      
      // Navigate to login
      navigateToScreen("/(auth)/login");
    } catch (error: any) {
      Alert.alert(t("common.error"), error.message || t("auth.logoutError"));
      throw error;
    }
  };

  // Forgot password
  const forgotPassword = async (email: string) => {
    try {
      // Xóa listeners hiện tại trước khi thực hiện forgot password
      cleanupAuthListeners();
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: "moneyup://reset-password",
      });
      if (error) throw error;
      Alert.alert(t("common.success"), t("auth.passwordResetEmailSent"));
    } catch (error: any) {
      Alert.alert(
        t("common.error"),
        error.message || t("auth.passwordResetError")
      );
      throw error;
    }
  };

  // Manual session refresh function
  const manualRefreshSession = async (): Promise<boolean> => {
    try {
      // Check network status first
      const networkState = await NetInfo.fetch();
      const isConnected = networkState.isConnected ?? false;

      if (!isConnected) {
        console.warn(
          "[Auth] Không thể làm mới phiên đăng nhập: Không có kết nối mạng"
        );
        return false;
      }

      console.log("[Auth] Đang làm mới phiên đăng nhập...");

      // Kiểm tra phiên hiện tại trước khi làm mới
      const { data: sessionData } = await supabase.auth.getSession();
      if (sessionData.session) {
        const expiresAt = sessionData.session.expires_at || 0;
        const now = Math.floor(Date.now() / 1000);
        console.log(
          `[Auth] Phiên hiện tại hết hạn sau ${expiresAt - now} giây`
        );
      } else {
        console.log("[Auth] Không có phiên đăng nhập hiện tại");
      }

      // Tạm thời xóa listener để tránh lỗi
      cleanupAuthListeners();

      // Refresh session
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error("[Auth] Lỗi khi làm mới phiên đăng nhập:", error);
        return false;
      }

      if (data.session) {
        const expiresAt = data.session.expires_at || 0;
        const now = Math.floor(Date.now() / 1000);
        console.log(
          `[Auth] Phiên đăng nhập đã được làm mới thành công, hết hạn sau ${
            expiresAt - now
          } giây`
        );
        setSession(data.session);
        setUser(data.session.user);
        return true;
      } else {
        console.log("[Auth] Không nhận được phiên đăng nhập mới");
      }

      return false;
    } catch (error) {
      console.error(
        "[Auth] Lỗi trong quá trình làm mới phiên đăng nhập:",
        error
      );
      return false;
    }
  };

  // Provide auth context values
  const value = {
    user,
    session,
    isLoading,
    signUp,
    signIn,
    signOut,
    forgotPassword,
    checkEmailExists,
    avatar,
    setAvatar,
    refreshSession: manualRefreshSession,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};