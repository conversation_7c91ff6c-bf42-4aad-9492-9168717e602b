// File: context/UpdateContext.js
// File này quản lý trạng thái kiểm tra cập nhật ứng dụng

import React, { createContext, useContext } from 'react';
import Constants from 'expo-constants';
import * as Linking from 'expo-linking';

// L<PERSON><PERSON> phiên bản từ Constants
const appVersion = Constants.expoConfig?.version || '1.0.0';

// Tạo context với giá trị mặc định
const UpdateContext = createContext({
  currentVersion: appVersion,
  checkingUpdate: false,
  hasUpdate: false,
  isRequired: false,
  updateVersion: '',
  releaseNotes: '',
  lastChecked: null,
  checkForUpdate: () => {},
  skipCurrentUpdate: () => {},
  openAppStore: () => {}
});

// Component Provider
export function UpdateProvider({ children }) {
  // Chỉ cần chứa children, không trả về text trực tiếp
  
  // Hàm mở App Store
  const openAppStore = async () => {
    try {
      const url = 'https://apps.apple.com/app/id6745965337';
      await Linking.openURL(url);
    } catch (error) {
      console.log('Không thể mở URL App Store:', error);
    }
  };

  // Giá trị context 
  const value = {
    currentVersion: appVersion,
    checkingUpdate: false,
    hasUpdate: false,
    isRequired: false,
    updateVersion: '',
    releaseNotes: '',
    lastChecked: null,
    checkForUpdate: () => {},
    skipCurrentUpdate: () => {},
    openAppStore
  };

  return (
    <UpdateContext.Provider value={value}>
      {children}
    </UpdateContext.Provider>
  );
}

// Hook để sử dụng context
export function useUpdate() {
  return useContext(UpdateContext);
}