import AsyncStorage from "@react-native-async-storage/async-storage";
import * as Localization from "expo-localization";
import { I18n } from "i18n-js";
import React, { createContext, useContext, useEffect, useState } from "react";

// Import translation files
import en from "../translations/en";
import vi from "../translations/vi";

// Create an i18n instance
const i18n = new I18n({
  en,
  vi,
});

// Set the default locale based on device
i18n.defaultLocale = "vi";

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = "@moneyup_language";

// Context type
type LocalizationContextType = {
  t: (scope: string, options?: object) => string;
  locale: string;
  setLocale: (locale: string) => void;
  isRTL: boolean;
  locales: { code: string; name: string }[];
};

// Create context
const LocalizationContext = createContext<LocalizationContextType>({
  t: () => "",
  locale: "vi",
  setLocale: () => {},
  isRTL: false,
  locales: [
    { code: "en", name: "English" },
    { code: "vi", name: "Tiếng Việt" },
  ],
});

// Provider component
export const LocalizationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [locale, setLocale] = useState("vi");

  // Load saved language on app start
  useEffect(() => {
    (async () => {
      try {
        const savedLocale = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
        if (savedLocale) {
          // Cập nhật cả i18n.locale và state
          i18n.locale = savedLocale;
          setLocale(savedLocale);
        } else {
          // Use device locale if no saved preference
          const deviceLocale = Localization.locale.split("-")[0];
          if (["en", "vi"].includes(deviceLocale)) {
            i18n.locale = deviceLocale;
            setLocale(deviceLocale);
          }
        }
      } catch (error) {
        console.error("Error loading language preference:", error);
      }
    })();
  }, []);

  // Function to change the locale
  const setLocaleWrapper = async (newLocale: string) => {
    // Cập nhật i18n locale trước
    i18n.locale = newLocale;

    // Sau đó cập nhật state và lưu vào AsyncStorage
    setLocale(newLocale);

    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, newLocale);
    } catch (error) {
      console.error("Error saving language preference:", error);
    }
  };

  // Text direction (for RTL languages if needed in the future)
  const isRTL = false;

  // Translation function
  const t = (scope: string, options?: object) => {
    return i18n.t(scope, options);
  };

  // Available locales
  const locales = [
    { code: "en", name: "English" },
    { code: "vi", name: "Tiếng Việt" },
  ];

  return (
    <LocalizationContext.Provider
      value={{
        t,
        locale,
        setLocale: setLocaleWrapper,
        isRTL,
        locales,
      }}
    >
      {children}
    </LocalizationContext.Provider>
  );
};

// Custom hook to use localization
export const useLocalization = () => useContext(LocalizationContext);
