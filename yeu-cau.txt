
 YÊU CẦU DỰ ÁN ỨNG DỤNG QUẢN LÝ CHI TIÊU
1. Tổng quan
Xây dựng ứng dụng mobile quản lý chi tiêu cá nhân sử dụng Expo (React Native). Ứng dụng cho phép người dùng:

<PERSON><PERSON> lại thu/chi hàng ngày

Quản lý nhiều ví khác nhau (ví tiền mặt, ngân hàng, ví điện tử,…)

Thống kê thu/chi

Hỗ trợ chế độ sáng/tối (Dark mode)

Lưu trữ dữ liệu trên Supabase


2. Tính năng chính
2.1. <PERSON><PERSON><PERSON> nhập / Đăng ký
Đăng nhập email + password (Supabase Auth)

Đăng ký người dùng mới

Xác thực qua OTP (email)

2.2. Quản lý ví (Wallets)
Tạo ví mới: tên ví, số dư ban đầu, lo<PERSON><PERSON> ví (Tiền mặt, <PERSON><PERSON>, <PERSON><PERSON>,…), mà<PERSON> sắc biểu tượng

Chỉnh sửa / Xo<PERSON> ví

Chọn ví mặc định

Liên kết mỗi giao dịch với 1 ví cụ thể

2.3. Ghi chép giao dịch
Tạo giao dịch mới (thu hoặc chi)

Số tiền

Danh mục (ăn uống, đi lại, lương,…)

Ghi chú

Ngày giờ

Ví sử dụng

Chỉnh sửa / Xoá giao dịch

2.4. Thống kê
Tổng thu/chi trong ngày, tuần, tháng

Biểu đồ tròn thể hiện tỷ lệ các loại chi tiêu

Lọc theo ví / thời gian

2.5. Cài đặt
Chọn ngôn ngữ (tùy chọn)

Chế độ giao diện: Sáng / Tối / Theo hệ thống

Đăng xuất