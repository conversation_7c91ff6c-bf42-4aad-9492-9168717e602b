// File: services/UpdateService.ts
// File này xử lý việc kiểm tra cập nhật ứng dụng 
// File này liên quan đến: context/UpdateContext.tsx, app/(tabs)/settings.tsx

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Key lưu trạng thái đã kiểm tra
const LAST_UPDATE_CHECK_KEY = '@moneyup_last_update_check';
const SKIP_VERSION_KEY = '@moneyup_skip_version';

// URL Store
const APP_STORE_URL = 'https://apps.apple.com/app/id6745965337';
const PLAY_STORE_URL = 'https://play.google.com/store/apps/details?id=vn.moneyup';

/**
 * <PERSON><PERSON><PERSON> phiên bản hiện tại của ứng dụng
 */
export const getCurrentVersion = () => {
  return Constants.expoConfig?.version || '1.0.0';
};

/**
 * So sánh hai chuỗi phiên bản (1.0.0 vs 1.0.1)
 */
export const compareVersions = (v1, v2) => {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  
  return 0;
};

/**
 * Kiểm tra cập nhật từ server
 */
export const checkForUpdates = async () => {
  try {
    // Lưu thời gian kiểm tra
    const now = Date.now().toString();
    await AsyncStorage.setItem(LAST_UPDATE_CHECK_KEY, now);
    
    // Phiên bản hiện tại
    const currentVersion = getCurrentVersion();
    
    // Mock dữ liệu server response
    const mockData = {
      latestVersion: '1.0.3',
      minVersion: '1.0.0',
      updateUrl: Platform.OS === 'ios' ? APP_STORE_URL : PLAY_STORE_URL,
      releaseNotes: 'Phiên bản mới với các tính năng: \n- Cải thiện hiệu suất\n- Sửa lỗi nhỏ\n- Cải thiện giao diện người dùng',
      isRequired: false
    };
    
    // So sánh phiên bản
    const hasNewVersion = compareVersions(currentVersion, mockData.latestVersion) < 0;
    const isRequired = compareVersions(currentVersion, mockData.minVersion) < 0 || mockData.isRequired;
    
    // Kiểm tra xem đã bỏ qua phiên bản này chưa
    let skipVersionResult = false;
    if (hasNewVersion && !isRequired) {
      const skippedVersion = await AsyncStorage.getItem(SKIP_VERSION_KEY);
      skipVersionResult = skippedVersion === mockData.latestVersion;
    }
    
    return {
      hasUpdate: hasNewVersion && !skipVersionResult,
      isRequired: isRequired,
      latestVersion: mockData.latestVersion,
      updateUrl: mockData.updateUrl,
      releaseNotes: mockData.releaseNotes,
      lastChecked: new Date()
    };
  } catch (error) {
    console.error('Lỗi khi kiểm tra cập nhật:', error);
    return {
      hasUpdate: false,
      isRequired: false,
      latestVersion: '',
      updateUrl: '',
      releaseNotes: '',
      lastChecked: null
    };
  }
};

/**
 * Đánh dấu phiên bản hiện tại là đã bỏ qua
 */
export const skipVersion = async (version) => {
  try {
    await AsyncStorage.setItem(SKIP_VERSION_KEY, version);
    return true;
  } catch (error) {
    console.error('Lỗi khi lưu phiên bản bỏ qua:', error);
    return false;
  }
};