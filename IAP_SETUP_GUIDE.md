# In-App Purchase Setup Guide

## Tình trạng hiện tại
✅ `react-native-iap` đã được cài đặt (v12.16.2)  
✅ Plugin IAP đã được thêm vào app.json  
✅ IAP service đã được cải thiện với error handling và logging  
✅ Premium Context đã được tạo  
✅ UI upgrade-premium đã hoàn chỉnh  

## Những gì cần làm để test IAP

### 1. Setup trên App Store Connect (iOS)

1. **Tạo App trên App Store Connect**
   - Bundle ID phải khớp với `com.hyusoft.MoneyUp` trong app.json
   - App phải ở trạng thái "Waiting for Review" hoặc đã được approve

2. **Tạo Subscription Products**
   - Vào App Store Connect > Your App > Features > In-App Purchases
   - Tạo 2 subscription products:
     - `premium_monthly` - G<PERSON><PERSON> tháng
     - `premium_yearly` - Gó<PERSON> năm
   - Set giá và mô tả cho từng gói
   - Submit for review

3. **Tạo Sandbox Test User**
   - Vào App Store Connect > Users and Access > Sandbox Testers
   - Tạo test user với email và password
   - Sử dụng user này để test purchases

### 2. Setup trên Google Play Console (Android)

1. **Tạo App trên Google Play Console**
   - Package name phải khớp với `com.hyusoft.MoneyUp`
   - Upload APK/AAB để activate IAP

2. **Tạo Subscription Products**
   - Vào Google Play Console > Your App > Monetize > Products > Subscriptions
   - Tạo 2 subscription products với cùng ID:
     - `premium_monthly`
     - `premium_yearly`

3. **Setup Test Accounts**
   - Thêm test accounts vào license testing
   - Hoặc sử dụng internal testing track

### 3. Testing trên Device

#### iOS Testing:
```bash
# Build development version
npx expo run:ios --device

# Hoặc build với EAS
eas build --platform ios --profile development
```

**Lưu ý iOS:**
- Phải test trên device thật, không được dùng simulator
- Phải sign out khỏi App Store trên device
- Khi test, sẽ được prompt để sign in với sandbox account

#### Android Testing:
```bash
# Build development version  
npx expo run:android --device

# Hoặc build với EAS
eas build --platform android --profile development
```

**Lưu ý Android:**
- Có thể test trên emulator hoặc device thật
- Phải sử dụng account được thêm vào license testing

### 4. Debug IAP Issues

#### Kiểm tra Console Logs:
```javascript
// Trong IAP service, tất cả logs đều có emoji để dễ tìm:
// 🚀 - Initialization
// 📦 - Getting products  
// 💳 - Making purchases
// ✅ - Success
// ❌ - Errors
// ⚠️ - Warnings
```

#### Common Issues và Solutions:

1. **"No products found"**
   - Kiểm tra Bundle ID/Package name khớp với store
   - Đảm bảo products đã được approve trên store
   - Kiểm tra product IDs chính xác

2. **"E_ITEM_UNAVAILABLE"**
   - Products chưa được approve
   - Bundle ID không khớp
   - Test trên wrong environment (production vs sandbox)

3. **"E_NETWORK_ERROR"**
   - Kiểm tra internet connection
   - Store servers có thể đang down

4. **"E_USER_CANCELLED"**
   - User đã cancel purchase - đây là normal behavior

### 5. Test với Mock Data

Trong development mode, IAP service sẽ return mock data nếu không get được products thật:

```javascript
// Mock products sẽ được return nếu __DEV__ = true
const mockProducts = [
  {
    productId: 'premium_monthly',
    price: '49000',
    currency: 'VND',
    localizedPrice: '49.000đ',
    title: 'AI Money Premium Monthly',
    description: 'Monthly subscription to AI Money Premium features',
  },
  {
    productId: 'premium_yearly', 
    price: '299000',
    currency: 'VND',
    localizedPrice: '299.000đ',
    title: 'AI Money Premium Yearly',
    description: 'Yearly subscription to AI Money Premium features',
  }
];
```

### 6. Test File

Sử dụng file `test-iap.tsx` để test IAP functionality:

1. Import test component vào app
2. Navigate đến test screen
3. Test từng function một cách riêng biệt
4. Kiểm tra console logs để debug

### 7. Production Checklist

Trước khi release:

- [ ] Products đã được approve trên cả iOS và Android
- [ ] Test purchases thành công trên cả platforms  
- [ ] Test restore purchases
- [ ] Test subscription status checking
- [ ] Remove test code và mock data
- [ ] Test với production builds

### 8. Troubleshooting Commands

```bash
# Clear Metro cache
npx expo start --clear

# Clear iOS build cache
cd ios && xcodebuild clean && cd ..

# Clear Android build cache  
cd android && ./gradlew clean && cd ..

# Reset Expo cache
npx expo install --fix
```

## Next Steps

1. **Test IAP service ngay bây giờ:**
   - Chạy app trên device thật
   - Navigate đến upgrade-premium screen
   - Kiểm tra console logs
   - Xem có get được products không

2. **Nếu không get được products:**
   - Kiểm tra mock data có hiển thị không
   - Setup products trên App Store Connect/Google Play Console
   - Test lại với real products

3. **Khi IAP hoạt động:**
   - Test purchase flow
   - Test premium features
   - Test restore purchases
   - Integrate với backend nếu cần

## Support

Nếu gặp vấn đề, hãy:
1. Kiểm tra console logs với emoji filters
2. Verify store setup (Bundle ID, Product IDs)
3. Test với mock data trước
4. Check device/emulator requirements
